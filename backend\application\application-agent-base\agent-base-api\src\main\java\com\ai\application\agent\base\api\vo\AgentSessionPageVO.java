package com.ai.application.agent.base.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AgentSessionPageVO {
    private String runId;

    @Schema(description = "agent类型")
    private Integer agentType;

    @Schema(description = "执行时间")
    private String runTime;

    @Schema(description = "回复内容")
    private Object replayContent;

    @Schema(description = "请求内容")
    private Object requestContent;
}
