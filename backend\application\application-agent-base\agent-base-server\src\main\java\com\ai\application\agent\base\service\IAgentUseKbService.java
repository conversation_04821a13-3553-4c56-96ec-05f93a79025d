package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.dto.AgentUseKbAddDTO;
import com.ai.application.agent.base.api.dto.AgentUseKbListDTO;
import com.ai.application.agent.base.api.dto.AgentUseKbUpdateDTO;
import com.ai.application.agent.base.api.vo.AgentUseKbListVO;

import java.util.List;

public interface IAgentUseKbService {
    /**
     * 列表
     *
     * @param queryDto
     * @return
     */
    List<AgentUseKbListVO> list(AgentUseKbListDTO queryDto);

    /**
     * 保存
     *
     * @param dto
     */
    void add(AgentUseKbAddDTO dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(AgentUseKbUpdateDTO dto);

    /**
     * TO Agent详情
     * @param versionId
     * @return
     */
    List<Integer> toDetail(Integer versionId);

}
