package com.ai.application.agent.base.api.mapstruct;
import com.ai.application.agent.base.api.dto.AgentUseWorkflowUpdateDTO;
import com.ai.application.agent.base.api.entity.AgentUseWorkflow;
import com.ai.application.agent.base.api.dto.AgentUseWorkflowAddDTO;
import com.ai.application.agent.base.api.vo.AgentUseWorkflowListVO;
import com.ai.application.agent.base.api.vo.AgentUseWorkflowVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 智能体工作流表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-07
 */

@Mapper(componentModel = "spring")
public interface AgentUseWorkflowMapstruct {

    AgentUseWorkflow toAddEntity(AgentUseWorkflowAddDTO dto);
    AgentUseWorkflow toUpdateEntity(AgentUseWorkflowUpdateDTO dto);
    List<AgentUseWorkflowListVO> toVoList(List<AgentUseWorkflow> entities);
    AgentUseWorkflowVO toVo(AgentUseWorkflow dto);
}
