package com.ai.application.task.api.enums;

import lombok.Getter;

/**
 * 任务状态:1-启用,0-禁用,2-暂停,-1-删除
 */
@Getter
public enum TaskStatusEnum {
    ENABLE(1, "启用"),
    DISABLE(0, "禁用"),
    PAUSE(2, "暂停"),
    DELETE(-1, "删除"),
    ;

    private final Integer code;
    private final String desc;

    TaskStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
