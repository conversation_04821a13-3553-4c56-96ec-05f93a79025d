package com.ai.application.skill.mcp.api.feign;


import com.ai.application.skill.mcp.api.vo.McpToolVO;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.vo.ResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(value = ServiceConstant.SKILL_MCP, contextId = "IMcpToolFeignClient")
@Component
public interface IMcpToolFeignClient {
    String API_PREFIX = "/v1/operate/feign";

    @GetMapping(API_PREFIX+"/detail/{mcpToolId}")
    public ResultVo<McpToolVO> getSkillDetail(@PathVariable("mcpToolId") Integer mcpToolId);


}
