package com.ai.application.base.model.api.mapstruct;

import com.ai.application.base.model.api.dto.ModelInfoDTO;
import com.ai.application.base.model.api.entity.ModelInfo;
import com.ai.application.base.model.api.vo.ModelBaseVO;
import com.ai.application.base.model.api.vo.ModelInfoVO;
import com.ai.application.base.model.api.vo.ModelSimpleVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-14T11:00:31+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class ModelInfoMapstructImpl implements ModelInfoMapstruct {

    @Override
    public ModelInfo toEntity(ModelInfoDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ModelInfo modelInfo = new ModelInfo();

        modelInfo.setModelId( dto.getModelId() );
        modelInfo.setModelSn( dto.getModelSn() );
        modelInfo.setModelName( dto.getModelName() );
        modelInfo.setModelEngine( dto.getModelEngine() );
        modelInfo.setModelType( dto.getModelType() );
        modelInfo.setModelToolcall( dto.getModelToolcall() );
        modelInfo.setModelDesc( dto.getModelDesc() );
        modelInfo.setModelConfig( dto.getModelConfig() );
        modelInfo.setModelStatus( dto.getModelStatus() );
        modelInfo.setSupplierId( dto.getSupplierId() );
        modelInfo.setCreateTime( dto.getCreateTime() );
        modelInfo.setUpdateTime( dto.getUpdateTime() );

        return modelInfo;
    }

    @Override
    public List<ModelInfo> toEntityList(List<ModelInfoDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<ModelInfo> list = new ArrayList<ModelInfo>( dtolist.size() );
        for ( ModelInfoDTO modelInfoDTO : dtolist ) {
            list.add( toEntity( modelInfoDTO ) );
        }

        return list;
    }

    @Override
    public ModelInfoVO toVo(ModelInfo entity) {
        if ( entity == null ) {
            return null;
        }

        ModelInfoVO modelInfoVO = new ModelInfoVO();

        modelInfoVO.setModelId( entity.getModelId() );
        modelInfoVO.setModelSn( entity.getModelSn() );
        modelInfoVO.setModelName( entity.getModelName() );
        modelInfoVO.setModelEngine( entity.getModelEngine() );
        modelInfoVO.setModelType( entity.getModelType() );
        modelInfoVO.setModelToolcall( entity.getModelToolcall() );
        modelInfoVO.setModelDesc( entity.getModelDesc() );
        modelInfoVO.setModelConfig( entity.getModelConfig() );
        modelInfoVO.setModelStatus( entity.getModelStatus() );
        modelInfoVO.setSupplierId( entity.getSupplierId() );
        modelInfoVO.setCreateTime( entity.getCreateTime() );
        modelInfoVO.setUpdateTime( entity.getUpdateTime() );

        return modelInfoVO;
    }

    @Override
    public List<ModelInfoVO> toVoList(List<ModelInfo> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ModelInfoVO> list = new ArrayList<ModelInfoVO>( entities.size() );
        for ( ModelInfo modelInfo : entities ) {
            list.add( toVo( modelInfo ) );
        }

        return list;
    }

    @Override
    public List<ModelSimpleVO> toSimpleVoList(List<ModelInfo> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ModelSimpleVO> list = new ArrayList<ModelSimpleVO>( entities.size() );
        for ( ModelInfo modelInfo : entities ) {
            list.add( modelInfoToModelSimpleVO( modelInfo ) );
        }

        return list;
    }

    @Override
    public List<ModelBaseVO> toBaseVoList(List<ModelInfoVO> voList) {
        if ( voList == null ) {
            return null;
        }

        List<ModelBaseVO> list = new ArrayList<ModelBaseVO>( voList.size() );
        for ( ModelInfoVO modelInfoVO : voList ) {
            list.add( modelInfoVOToModelBaseVO( modelInfoVO ) );
        }

        return list;
    }

    protected ModelSimpleVO modelInfoToModelSimpleVO(ModelInfo modelInfo) {
        if ( modelInfo == null ) {
            return null;
        }

        ModelSimpleVO modelSimpleVO = new ModelSimpleVO();

        modelSimpleVO.setModelSn( modelInfo.getModelSn() );
        modelSimpleVO.setModelName( modelInfo.getModelName() );

        return modelSimpleVO;
    }

    protected ModelBaseVO modelInfoVOToModelBaseVO(ModelInfoVO modelInfoVO) {
        if ( modelInfoVO == null ) {
            return null;
        }

        ModelBaseVO modelBaseVO = new ModelBaseVO();

        modelBaseVO.setModelSn( modelInfoVO.getModelSn() );
        modelBaseVO.setModelName( modelInfoVO.getModelName() );
        modelBaseVO.setModelEngine( modelInfoVO.getModelEngine() );
        modelBaseVO.setModelType( modelInfoVO.getModelType() );
        modelBaseVO.setModelToolcall( modelInfoVO.getModelToolcall() );
        modelBaseVO.setModelDesc( modelInfoVO.getModelDesc() );
        modelBaseVO.setModelConfig( modelInfoVO.getModelConfig() );
        modelBaseVO.setModelStatus( modelInfoVO.getModelStatus() );
        modelBaseVO.setSupplierId( modelInfoVO.getSupplierId() );
        modelBaseVO.setTestStatus( modelInfoVO.getTestStatus() );
        modelBaseVO.setCreateTime( modelInfoVO.getCreateTime() );
        modelBaseVO.setUpdateTime( modelInfoVO.getUpdateTime() );

        return modelBaseVO;
    }
}
