package com.ai.application.skill.mcp.service.impl;

import com.ai.application.skill.mcp.api.config.McpServersWrapper;
import com.ai.application.skill.mcp.api.config.McpSseServerConfig;
import com.ai.application.skill.mcp.api.config.McpSseServersWrapper;
import com.ai.application.skill.mcp.api.config.McpStutioServerConfig;
import com.ai.application.skill.mcp.api.dto.McpServerDTO;
import com.ai.application.skill.mcp.api.dto.McpServerTestDTO;
import com.ai.application.skill.mcp.api.dto.query.McpServerQueryDTO;
import com.ai.application.skill.mcp.api.entity.McpServer;
import com.ai.application.skill.mcp.api.entity.McpSupplier;
import com.ai.application.skill.mcp.api.entity.McpTool;
import com.ai.application.skill.mcp.api.mapstruct.McpServerMapstruct;
import com.ai.application.skill.mcp.api.vo.McpServerVO;
import com.ai.application.skill.mcp.mapper.McpServerMapper;
import com.ai.application.skill.mcp.mapper.McpSupplierMapper;
import com.ai.application.skill.mcp.mapper.McpToolMapper;
import com.ai.application.skill.mcp.service.IMcpServerService;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.list.CollectionUtils;
import com.ai.framework.core.util.string.StringUtil;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.google.gson.Gson;
import dev.langchain4j.agent.tool.ToolExecutionRequest;
import dev.langchain4j.agent.tool.ToolSpecification;
import dev.langchain4j.mcp.client.DefaultMcpClient;
import dev.langchain4j.mcp.client.McpClient;
import dev.langchain4j.mcp.client.transport.McpTransport;
import dev.langchain4j.mcp.client.transport.http.HttpMcpTransport;
import dev.langchain4j.mcp.client.transport.stdio.StdioMcpTransport;

import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;

import javax.naming.Context;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * MCP服务器表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Service
@Slf4j
public class McpServerServiceImpl implements IMcpServerService {
    public static final String KEY = "key";
    public static final String ARGS = "args";
    public static final String URL = "url";
    public static final String CMD = "cmd";
    public static final String C = "/c";
    public static final String SERVER_URL = "无";
    public static final String OAUTH = "oauth";
    public static final String OS_NAME = "os.name";
    @Resource
    private McpServerMapper mcpServerMapper;

    @Resource
    private McpServerMapstruct mcpServerMapstruct;

    @Resource
    private McpToolMapper mcpToolMapper;

    @Resource
    private McpSupplierMapper mcpSupplierMapper;


    Gson gson = new Gson();

    @Transactional(readOnly = true)
    @Override
    public PageInfo<McpServerVO> page(McpServerQueryDTO queryDto) {
        QueryWrapper<McpServer> queryWrapper = this.buildQuery(queryDto);
        Page<McpServer> page = new Page<>(queryDto.getPageNo(), queryDto.getPageSize());
        Page<McpServer> result = this.mcpServerMapper.selectPage(page, queryWrapper);
        List<McpServerVO> voList = mcpServerMapstruct.toVoList(result.getRecords());
        if (CollectionUtils.isEmpty(voList)) {
            return PageInfo.of(new ArrayList<>());
        }
        // 1. 先收集所有需要查询的供应商ID
        Set<Integer> supplierIds = voList.stream()
                .map(McpServerVO::getSupplierId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (!supplierIds.isEmpty()) {
            // 2. 一次性批量查询所有供应商信息
            Map<Integer, McpSupplier> supplierMap = mcpSupplierMapper.selectList(
                    new LambdaQueryWrapper<McpSupplier>()
                            .in(McpSupplier::getSupplierId, supplierIds)
            ).stream().collect(Collectors.toMap(McpSupplier::getSupplierId, Function.identity()));

            // 3. 填充供应商名称
            voList.forEach(vo -> {
                McpSupplier supplier = supplierMap.get(vo.getSupplierId());
                if (supplier != null) {
                    vo.setSupplierName(supplier.getSupplierName());
                }
            });
        }
        PageInfo<McpServerVO> toolVOPageInfo = PageInfo.of(voList);
        toolVOPageInfo.setTotal(result.getTotal());
        return toolVOPageInfo;
    }

    @Transactional(readOnly = true)
    @Override
    public List<McpServerVO> list(McpServerQueryDTO queryDto) {
        QueryWrapper<McpServer> queryWrapper = this.buildQuery(queryDto);
        return mcpServerMapstruct.toVoList(this.mcpServerMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(McpServerDTO dto) {

        String supplierSn = UUIDUtil.randomUUID();
        McpSupplier mcpSupplier = new McpSupplier();
        mcpSupplier.setSupplierName(dto.getSupplierName());
        mcpSupplier.setSupplierSn(supplierSn);
        mcpSupplier.setSupplierStatus(1);
        mcpSupplier.setSupplierWeight(1);
        //供应商数据插入
        mcpSupplierMapper.insert(mcpSupplier);
        Timestamp now = new Timestamp(System.currentTimeMillis());
        dto.setServerId(null);
        McpServer entity = mcpServerMapstruct.toEntity(dto);
        entity.setCreateTime(now);
        if (dto.getConnectionConfig().contains(ARGS)) {
            //如果是通过命令行方式连接mcp服务
            entity.setTransportType(10);
        } else if (dto.getConnectionConfig().contains(URL)) {
            //如果是通过sse方式连接mcp服务
            entity.setTransportType(20);
        } else {
            throw new RuntimeException("不支持的连接方式");
        }
        entity.setSupplierId(mcpSupplier.getSupplierId());
        if (Objects.isNull(entity.getServerUrl())) {
            entity.setServerUrl(SERVER_URL);
        }
        if (dto.getConnectionConfig().contains(KEY)) {
            entity.setAuthType(20);
        } else if (dto.getConnectionConfig().contains(OAUTH)) {
            entity.setAuthType(30);
        } else {
            entity.setAuthType(10);
        }
        Integer userId = UserContext.getUserId();
        if (Objects.nonNull(userId)) {
            entity.setCreateUserId(userId);
            entity.setUpdateUserId(userId);
        } else {
            entity.setCreateUserId(100002);
            entity.setUpdateUserId(100002);
        }


        mcpServerMapper.insert(entity);


        // mcp服务数据已处理，接下来处理mcp工具
        handelTools(dto.getConnectionConfig(), entity);
    }

    public String test(McpServerTestDTO dto) {

        //先查询服务信息
        LambdaQueryWrapper<McpServer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(McpServer::getServerSn, dto.getServerSn());
        McpServer mcpServer = mcpServerMapper.selectOne(queryWrapper);
        Map<String, McpClient> mcpClientMap = testToolsGetClient(mcpServer.getConnectionConfig(), mcpServer);
        String server = mcpClientMap.keySet().iterator().next();
        log.info("测试工具Server: {}", gson.toJson(dto.getParams()));
        ToolExecutionRequest request = ToolExecutionRequest.builder()
                .name(dto.getToolName())
                .arguments(gson.toJson(dto.getParams()))
                .build();
        McpClient mcpClient = mcpClientMap.get(server);
        // 执行工具调用
        String result = mcpClient.executeTool(request);
        return result;
    }


    public Flux<String> getStreamResult(McpServerTestDTO dto) {
        return Flux.defer(() -> {
            try {
                // 1. 查询服务信息（阻塞操作）
                LambdaQueryWrapper<McpServer> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(McpServer::getServerSn, dto.getServerSn());
                McpServer mcpServer = mcpServerMapper.selectOne(queryWrapper);

                if (mcpServer == null) {
                    return Flux.error(new RuntimeException("Server not found for SN: " + dto.getServerSn()));
                }

                // 2. 获取客户端连接
                Map<String, McpClient> mcpClientMap = testToolsGetClient(mcpServer.getConnectionConfig(), mcpServer);
                String server = mcpClientMap.keySet().iterator().next();
                McpClient mcpClient = mcpClientMap.get(server);

                // 3. 构建请求
                ToolExecutionRequest request = ToolExecutionRequest.builder()
                        .name(dto.getToolName())
                        .arguments(JsonUtils.toJsonString(dto.getParams()))
                        .build();

                // 4. 执行工具调用并流式返回结果
                String result = mcpClient.executeTool(request);

                // 5. 将结果拆分为行流式返回
                return Flux.fromStream(Arrays.stream(result.split("\\r?\\n")));

            } catch (Exception e) {
                return Flux.error(e);
            }
        }).subscribeOn(Schedulers.boundedElastic()); // 使用弹性线程池执行阻塞操作
    }


    @Transactional(
            isolation = Isolation.READ_COMMITTED,
            timeout = 60,  // 单位秒
            rollbackFor = {Exception.class}
    )
    public void handelTools(String json, McpServer mcpServer) {
        String comm = "/bin/sh";
        String ctm = "-c";
        String os = System.getProperty(OS_NAME).toLowerCase();
        if (os.contains("win")) {
            comm = CMD;
            ctm = C;

        } else {
            log.info("当前操作系统是：" + os);
            comm = "/bin/sh";
            ctm="-c";
        }
        try {
            Timestamp now = new Timestamp(System.currentTimeMillis());
            ObjectMapper mapper = new ObjectMapper();
            try {
                if (json.contains(ARGS)) {
                    // 解析 JSON
                    McpServersWrapper wrapper = mapper.readValue(json, McpServersWrapper.class);

                    // 遍历所有 MCP 服务配置
                    for (Map.Entry<String, McpStutioServerConfig> entry : wrapper.getMcpServers().entrySet()) {
                        String serviceName = entry.getKey(); // "amap-maps" 或 "baidu-maps"
                        McpStutioServerConfig config = entry.getValue();
                        //需要请求mcp服务 拿到服务工具
                        List<String> command = new ArrayList();
//                        command.add(comm);
//                        command.add(ctm);
                        if (StringUtil.isNotEmpty(config.getCommand())) {
                            command.add(config.getCommand());
                        }
                        if (CollectionUtils.isNotEmpty(config.getArgs())) {
                            command.addAll(config.getArgs());
                        }
                        Map<String, String> env = config.getEnv();

                        // 2.构建MCP服务传输方式  有sse和stdio两种， 这里演示的是stdio
                        McpTransport transport = new StdioMcpTransport.Builder()
                                .command(command)
                                .environment(env)
                                .logEvents(true)
                                .build();

                        // 3.构建MCP客户端， 指定传输方式
                        McpClient mcpClient = new DefaultMcpClient.Builder()
                                .transport(transport)
                                .build();
                        List<ToolSpecification> toolSpecifications = mcpClient.listTools();
                        log.info("mcp服务工具处理开始{},toolSpecifications:{}", gson.toJson(env), gson.toJson(toolSpecifications));

                        toolSpecifications.forEach(tool -> {
                            McpTool mcpTool = McpTool.builder().
                                    serverId(mcpServer.getServerId()).
                                    toolName(tool.name()).
                                    toolDesc(tool.description()).
                                    inputSchema(gson.toJson(tool.parameters())).
                                    createTime(now).
                                    updateTime(now).
                                    build();
                            mcpToolMapper.insert(mcpTool);
                        });
                        mcpClient.close();
                        log.info("mcp服务工具处理完成");
                    }
                }
                if (json.contains(URL) && !json.contains(ARGS)) {
                    McpSseServersWrapper wrapper = mapper.readValue(json, McpSseServersWrapper.class);
                    // 遍历所有 MCP 服务配置
                    for (Map.Entry<String, McpSseServerConfig> entry : wrapper.getMcpServers().entrySet()) {
                        String serviceName = entry.getKey(); // "amap-maps" 或 "baidu-maps"
                        McpSseServerConfig config = entry.getValue();
                        //需要请求mcp服务 拿到服务工具
                        String url = config.getUrl();
                        // 2.构建MCP服务传输方式  有sse和stdio两种
                        McpTransport transport = new HttpMcpTransport.Builder().sseUrl(url)
                                .build();
                        // 3.构建MCP客户端， 指定传输方式
                        McpClient mcpClient = new DefaultMcpClient.Builder()
                                .transport(transport)
                                .build();
                        List<ToolSpecification> toolSpecifications = mcpClient.listTools();
                        toolSpecifications.forEach(tool -> {
                            McpTool mcpTool = McpTool.builder().
                                    serverId(mcpServer.getServerId()).
                                    toolName(tool.name()).
                                    toolDesc(tool.description()).
                                    inputSchema(gson.toJson(tool.parameters())).
                                    createTime(now).
                                    updateTime(now).
                                    build();
                            mcpToolMapper.insert(mcpTool);
                        });
                        mcpClient.close();
                        log.info("mcp服务工具处理完成");
                    }
                }
            }catch (Exception e) {
                log.info("mcp服务工具处理失败{}报错异常{}",  e.getMessage(),e);
                throw new RuntimeException("MCP工具处理失败", e);
            }
        } catch (Exception e) {
            log.info("mcp服务工具处理失败{}{}",  e.getMessage(),e);
        }

    }


    public void updateTools(String json, McpServer mcpServer) {
        try {
            String comm = "/bin/sh";
            String ctm = "-c";
            String os = System.getProperty(OS_NAME).toLowerCase();
            if (os.contains("win")) {
                comm = CMD;
                ctm = C;
            } else {
                log.info("当前操作系统是：" + os);
                comm = "/bin/sh";
                ctm="-c";
            }
            Timestamp now = new Timestamp(System.currentTimeMillis());
            ObjectMapper mapper = new ObjectMapper();
            try {
                if (json.contains(ARGS)) {
                    // 解析 JSON
                    McpServersWrapper wrapper = mapper.readValue(json, McpServersWrapper.class);
                    // 遍历所有 MCP 服务配置
                    for (Map.Entry<String, McpStutioServerConfig> entry : wrapper.getMcpServers().entrySet()) {
                        String serviceName = entry.getKey(); // "amap-maps" 或 "baidu-maps"
                        McpStutioServerConfig config = entry.getValue();
                        //需要请求mcp服务 拿到服务工具
                        List<String> command = new ArrayList();
                        command.add(comm);
                        command.add(ctm);
                        if (StringUtil.isNotEmpty(config.getCommand())) {
                            command.add(config.getCommand());
                        }
                        if (CollectionUtils.isNotEmpty(config.getArgs())) {
                            command.addAll(config.getArgs());
                        }
                        Map<String, String> env = config.getEnv();

                        // 2.构建MCP服务传输方式  有sse和stdio两种， 这里演示的是stdio
                        McpTransport transport = new StdioMcpTransport.Builder()
                                .command(command)
                                .environment(env)
                                .logEvents(true)
                                .build();

                        // 3.构建MCP客户端， 指定传输方式
                        McpClient mcpClient = new DefaultMcpClient.Builder()
                                .transport(transport)
                                .build();
                        List<ToolSpecification> toolSpecifications = mcpClient.listTools();
                        log.info("mcp服务更新工具处理开始{},toolSpecifications:{}", gson.toJson(env), gson.toJson(toolSpecifications));

                        //执行工具更新操作
                        extracted(mcpServer, toolSpecifications, now);
                        mcpClient.close();
                        log.info("mcp服务工具处理完成");
                    }
                }
                if (json.contains(URL) && !json.contains(ARGS)) {
                    McpSseServersWrapper wrapper = mapper.readValue(json, McpSseServersWrapper.class);
                    // 遍历所有 MCP 服务配置
                    for (Map.Entry<String, McpSseServerConfig> entry : wrapper.getMcpServers().entrySet()) {
                        String serviceName = entry.getKey(); // "amap-maps" 或 "baidu-maps"
                        McpSseServerConfig config = entry.getValue();
                        //需要请求mcp服务 拿到服务工具
                        String url = config.getUrl();
                        // 2.构建MCP服务传输方式  有sse和stdio两种
                        McpTransport transport = new HttpMcpTransport.Builder().sseUrl(url)
                                .build();
                        // 3.构建MCP客户端， 指定传输方式
                        McpClient mcpClient = new DefaultMcpClient.Builder()
                                .transport(transport)
                                .build();
                        List<ToolSpecification> toolSpecifications = mcpClient.listTools();
                        //执行工具更新操作
                        extracted(mcpServer, toolSpecifications, now);
                        mcpClient.close();
                        log.info("mcp服务工具处理完成");
                    }
                }
            } catch (IOException e) {
                log.info("mcp服务工具处理失败{}{}", e, e.getMessage());
            } catch (Exception e) {
                log.info("mcp服务工具处理失败{}{}", e, e.getMessage());
                throw new RuntimeException(e);
            }
        } catch (Exception e) {
            log.info("mcp服务工具处理失败{}{}", e, e.getMessage());
        }

    }

    private void extracted(McpServer mcpServer, List<ToolSpecification> toolSpecifications, Timestamp now) {
        // 1. 首先获取数据库中现有的工具列表
        List<McpTool> existingTools = mcpToolMapper.selectList(
                new LambdaQueryWrapper<McpTool>()
                        .eq(McpTool::getServerId, mcpServer.getServerId())
        );

        // 2. 创建工具名称到数据库实体的映射
        Map<String, McpTool> existingToolMap = existingTools.stream()
                .collect(Collectors.toMap(McpTool::getToolName, Function.identity()));

        // 3. 处理从MCP服务获取的新工具列表
        Set<String> processedToolNames = new HashSet<>();
        toolSpecifications.forEach(newTool -> {
            processedToolNames.add(newTool.name());

            McpTool existingTool = existingToolMap.get(newTool.name());
            if (existingTool != null) {
                // 更新逻辑：检查是否有变化
                if (!Objects.equals(existingTool.getToolDesc(), newTool.description()) ||
                        !Objects.equals(existingTool.getInputSchema(), gson.toJson(newTool.parameters()))) {

                    existingTool.setToolDesc(newTool.description());
                    existingTool.setInputSchema(gson.toJson(newTool.parameters()));
                    existingTool.setUpdateTime(now);
                    mcpToolMapper.updateById(existingTool);
                    log.info("更新工具: {}", newTool.name());
                }
            } else {
                // 新增逻辑
                McpTool mcpTool = McpTool.builder()
                        .serverId(mcpServer.getServerId())
                        .toolName(newTool.name())
                        .toolDesc(newTool.description())
                        .inputSchema(gson.toJson(newTool.parameters()))
                        .createTime(now)
                        .updateTime(now)
                        .build();
                mcpToolMapper.insert(mcpTool);
                log.info("新增工具: {}", newTool.name());
            }
        });

        // 4. 删除逻辑：找出数据库中存在但服务端不存在的工具
        existingToolMap.keySet().stream()
                .filter(toolName -> !processedToolNames.contains(toolName))
                .forEach(toolName -> {
                    McpTool toDelete = existingToolMap.get(toolName);
                    toDelete.setToolStatus(-1);
                    mcpToolMapper.updateById(toDelete);

                    log.info("删除工具: {}", toolName);
                });
    }

    public Map<String, McpClient> testToolsGetClient(String json, McpServer mcpServer) {

        Timestamp now = new Timestamp(System.currentTimeMillis());
        ObjectMapper mapper = new ObjectMapper();
        String comm = "/bin/sh";
        String ctm = "-c";
        String os = System.getProperty(OS_NAME).toLowerCase();
        if (os.contains("win")) {
            comm = CMD;
            ctm = C;
        } else {
            log.info("当前操作系统是：" + os);
            comm = "/bin/sh";
            ctm="-c";
        }
        try {
            if (json.contains(ARGS)) {
                // 解析 JSON
                McpServersWrapper wrapper = mapper.readValue(json, McpServersWrapper.class);
                // 遍历所有 MCP 服务配置
                for (Map.Entry<String, McpStutioServerConfig> entry : wrapper.getMcpServers().entrySet()) {
                    String serviceName = entry.getKey(); // "amap-maps" 或 "baidu-maps"
                    McpStutioServerConfig config = entry.getValue();
                    //需要请求mcp服务 拿到服务工具
                    List<String> command = new ArrayList();
                    command.add(comm);
                    command.add(ctm);
                    if (StringUtil.isNotEmpty(config.getCommand())) {
                        command.add(config.getCommand());
                    }
                    if (CollectionUtils.isNotEmpty(config.getArgs())) {
                        command.addAll(config.getArgs());
                    }
                    Map<String, String> env = config.getEnv();

                    // 2.构建MCP服务传输方式  有sse和stdio两种， 这里演示的是stdio
                    McpTransport transport = new StdioMcpTransport.Builder()
                            .command(command)
                            .environment(env)
                            .logEvents(true)
                            .build();

                    // 3.构建MCP客户端， 指定传输方式
                    McpClient mcpClient = new DefaultMcpClient.Builder()
                            .transport(transport)
                            .build();

                    Map<String, McpClient> params = new HashMap<>();
                    params.put(serviceName, mcpClient);
                    return params;
                }
            } else if (json.contains(URL) && !json.contains(ARGS)) {
                McpSseServersWrapper wrapper = mapper.readValue(json, McpSseServersWrapper.class);
                // 遍历所有 MCP 服务配置
                for (Map.Entry<String, McpSseServerConfig> entry : wrapper.getMcpServers().entrySet()) {
                    String serviceName = entry.getKey(); // "amap-maps" 或 "baidu-maps"
                    McpSseServerConfig config = entry.getValue();
                    //需要请求mcp服务 拿到服务工具
                    String url = config.getUrl();
                    // 2.构建MCP服务传输方式  有sse和stdio两种
                    McpTransport transport = new HttpMcpTransport.Builder().sseUrl(url)
                            .build();
                    // 3.构建MCP客户端， 指定传输方式
                    McpClient mcpClient = new DefaultMcpClient.Builder()
                            .transport(transport)
                            .build();
                    Map<String, McpClient> params = new HashMap<>();
                    params.put(serviceName, mcpClient);
                    return params;
                }
            }
        } catch (IOException e) {
            log.info("mcp服务工具处理失败{}{}", e, e.getMessage());
        } catch (Exception e) {
            log.info("mcp服务工具处理失败{}{}", e, e.getMessage());
            throw new RuntimeException(e);
        }
        return Collections.emptyMap();
    }

    private static void handleAmapMaps(McpStutioServerConfig config) {
        System.out.println("Handling AMAP Maps service...");
        // 调用高德地图 API 或执行命令
    }

    private static void handleBaiduMaps(McpStutioServerConfig config) {
        System.out.println("Handling Baidu Maps service...");
        // 调用百度地图 API 或执行命令
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(McpServerDTO dto) {
        if (dto.getType() == 2) {
            Timestamp now = new Timestamp(System.currentTimeMillis());
            BusinessAssertUtil.notNull(dto.getServerId(), "ServerId不能为空");
            LambdaQueryWrapper<McpServer> lambdaQueryWrapper = getMcpServerLambdaQueryWrapper(dto);
            // TODO 唯一性字段校验
            McpServer entity = mcpServerMapper.selectOne(lambdaQueryWrapper);
            BusinessAssertUtil.notNull(entity, "找不到ServerId为 " + dto.getServerSn() + " 的记录");
            McpServer entityList = mcpServerMapstruct.toEntity(dto);
            entityList.setUpdateTime(now);
            entity.setConnectionConfig(dto.getConnectionConfig());
            entity.setServerDesc(dto.getServerDesc());
            mcpServerMapper.updateById(entity);
            McpSupplier mcpSupplier = mcpSupplierMapper.selectOne(new LambdaQueryWrapper<McpSupplier>().eq(McpSupplier::getSupplierId, entity.getSupplierId()));
            mcpSupplier.setSupplierName(dto.getSupplierName());
            if (!Objects.equals(mcpSupplier.getSupplierName(), dto.getServerName())) {
                //供应商数据插入
                mcpSupplierMapper.updateById(mcpSupplier);
            }

        } else if (dto.getType() == 1) {
            //状态更新
            LambdaQueryWrapper<McpServer> lambdaQueryWrapper = getMcpServerLambdaQueryWrapper(dto);
            // TODO 唯一性字段校验
            McpServer entity = mcpServerMapper.selectOne(lambdaQueryWrapper);
            BusinessAssertUtil.notNull(entity, "找不到ServerId为 " + dto.getServerSn() + " 的记录");
            entity.setServerStatus(dto.getServerStatus());
            entity.setUpdateTime(null);
            mcpServerMapper.updateById(entity);
        } else if (dto.getType() == 0) {
            //查询
            LambdaQueryWrapper<McpServer> lambdaQueryWrapper = getMcpServerLambdaQueryWrapper(dto);
            McpServer entity = mcpServerMapper.selectOne(lambdaQueryWrapper);
            BusinessAssertUtil.notNull(entity, "找不到ServerId为 " + dto.getServerSn() + " 的记录");
            // 更新工具
            updateTools(entity.getConnectionConfig(), entity);
        }
    }


    private static LambdaQueryWrapper<McpServer> getMcpServerLambdaQueryWrapper(McpServerDTO dto) {
        LambdaQueryWrapper<McpServer> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(McpServer::getServerSn, dto.getServerSn());
        return lambdaQueryWrapper;
    }

    @Transactional(readOnly = true)
    @Override
    public McpServerVO get(Integer id) {
        BusinessAssertUtil.notNull(id, "ServerId不能为空");

        McpServer entity = mcpServerMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到ServerId为 " + id + " 的记录");

        return mcpServerMapstruct.toVo(entity);
    }

    private QueryWrapper<McpServer> buildQuery(McpServerQueryDTO queryDto) {
        QueryWrapper<McpServer> queryWrapper = new QueryWrapper<>();
        return queryWrapper;
    }
}