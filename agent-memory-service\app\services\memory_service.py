# -*- coding: utf-8 -*-
"""
记忆管理服务模块

智能体记忆管理的核心业务逻辑，提供：
- 记忆的增删查改操作
- 向量相似度计算和检索
- 混合条件过滤（时间、类别、用户等）
- 记忆统计分析
- 服务健康检查
- 与Elasticsearch和向量服务的集成

Project: Agent Memory Service
Author: 张小龙 <<EMAIL>>
Created: 2025-06-01
Version: 1.0.0
License: private
"""

from datetime import datetime, timedelta
from typing import List, Optional, Tuple
from loguru import logger

from app.models.schemas import (
    AddMemoryRequest, SearchMemoryRequest, ClearMemoryRequest,
    MemoryItem, MemoryDocument, SortOrder
)
from app.services.elasticsearch_service import es_service
from app.services.embedding_service import embedding_service
from app.config.settings import settings


class MemoryService:
    """记忆管理服务"""
    
    def __init__(self):
        self.es_service = es_service
        self.embedding_service = embedding_service
    
    async def initialize(self):
        """初始化服务"""
        try:
            # 初始化Elasticsearch服务
            await self.es_service.initialize()
            
            # 初始化向量编码服务
            await self.embedding_service.initialize()
            
            logger.info("记忆管理服务初始化完成")
            
        except Exception as e:
            logger.error(f"记忆管理服务初始化失败: {e}")
            raise
    
    async def add_memory(self, request: AddMemoryRequest) -> str:
        """
        添加记忆
        
        Args:
            request: 添加记忆请求
            
        Returns:
            记忆ID
        """
        try:
            logger.info(f"开始添加记忆: agent_id={request.agent_id}, user_id={request.user_id}")
            
            # 对用户问题进行向量编码
            question_vector = await self.embedding_service.encode_text(request.user_question)
            
            # 创建记忆文档
            current_time = datetime.now()
            memory_doc = MemoryDocument(
                agent_id=request.agent_id,
                user_id=request.user_id,
                category=request.category,
                user_question=request.user_question,
                question_reply=request.question_reply,
                question_time=request.question_time,
                question_vector=question_vector,
                created_at=current_time,
                updated_at=current_time
            )
            
            # 存储到Elasticsearch
            doc_id = await self.es_service.add_memory(memory_doc)
            
            logger.info(f"记忆添加成功: doc_id={doc_id}")
            return doc_id
            
        except Exception as e:
            logger.error(f"添加记忆失败: {e}")
            raise
    
    async def search_memories(self, request: SearchMemoryRequest) -> Tuple[List[MemoryItem], int]:
        """
        搜索记忆
        
        Args:
            request: 搜索记忆请求
            
        Returns:
            记忆列表和总数
        """
        try:
            logger.info(f"开始搜索记忆: agent_id={request.agent_id}, user_id={request.user_id}")
            
            # 如果有用户问题，进行向量编码
            question_vector = None
            if request.user_question:
                question_vector = await self.embedding_service.encode_text(request.user_question)
            
            # 获取相似度阈值，使用配置的默认值或请求中的值
            similarity_threshold = request.similarity_threshold
            if similarity_threshold is None:
                similarity_threshold = settings.memory.default_similarity_threshold
            
            # 搜索记忆
            memories, total = await self.es_service.search_memories(
                agent_id=request.agent_id,
                user_id=request.user_id,
                category=request.category,
                question_vector=question_vector,
                similarity_threshold=similarity_threshold,
                start_time=request.start_time,
                end_time=request.end_time,
                sort_by=request.sort_by,
                limit=request.limit
            )
            
            logger.info(f"搜索记忆完成: 返回{len(memories)}条，总计{total}条")
            return memories, total
            
        except Exception as e:
            logger.error(f"搜索记忆失败: {e}")
            raise
    
    async def clear_memories(self, request: ClearMemoryRequest) -> int:
        """
        清除记忆
        
        Args:
            request: 清除记忆请求
            
        Returns:
            删除的记录数
        """
        try:
            logger.info(f"开始清除记忆: agent_id={request.agent_id}, user_id={request.user_id}")
            
            # 检查是否有任何过滤条件，避免误删全部数据
            if not request.has_conditions():
                raise ValueError("清除记忆必须指定至少一个过滤条件")
            
            # 清除记忆
            deleted_count = await self.es_service.clear_memories(
                agent_id=request.agent_id,
                user_id=request.user_id,
                category=request.category,
                start_time=request.start_time,
                end_time=request.end_time
            )
            
            logger.info(f"清除记忆完成: 删除{deleted_count}条记录")
            return deleted_count
            
        except Exception as e:
            logger.error(f"清除记忆失败: {e}")
            raise
    
    async def get_memory_by_id(self, memory_id: str) -> Optional[MemoryItem]:
        """
        根据ID获取记忆
        
        Args:
            memory_id: 记忆ID
            
        Returns:
            记忆项
        """
        try:
            logger.info(f"获取记忆: memory_id={memory_id}")
            
            memory = await self.es_service.get_memory_by_id(memory_id)
            
            if memory:
                logger.info(f"记忆获取成功: {memory_id}")
            else:
                logger.warning(f"记忆未找到: {memory_id}")
            
            return memory
            
        except Exception as e:
            logger.error(f"获取记忆失败: {e}")
            raise
    
    async def search_similar_memories(
        self, 
        agent_id: str,
        user_id: str,
        question: str,
        similarity_threshold: Optional[float] = None,
        limit: int = 10,
        category: Optional[str] = None
    ) -> List[MemoryItem]:
        """
        搜索相似记忆
        
        Args:
            agent_id: 智能体ID
            user_id: 用户ID
            question: 查询问题
            similarity_threshold: 相似度阈值
            limit: 返回数量限制
            category: 记忆类别
            
        Returns:
            相似记忆列表
        """
        try:
            logger.info(f"搜索相似记忆: agent_id={agent_id}, user_id={user_id}")
            
            # 对查询问题进行向量编码
            question_vector = await self.embedding_service.encode_text(question)
            
            # 使用默认相似度阈值
            if similarity_threshold is None:
                similarity_threshold = settings.memory.default_similarity_threshold
            
            # 搜索相似记忆
            memories, _ = await self.es_service.search_memories(
                agent_id=agent_id,
                user_id=user_id,
                category=category,
                question_vector=question_vector,
                similarity_threshold=similarity_threshold,
                sort_by=SortOrder.SIMILARITY_DESC,
                limit=limit
            )
            
            logger.info(f"相似记忆搜索完成: 返回{len(memories)}条记录")
            return memories
            
        except Exception as e:
            logger.error(f"搜索相似记忆失败: {e}")
            raise
    
    async def get_recent_memories(
        self,
        agent_id: str,
        user_id: str,
        days: int = 7,
        limit: int = 10,
        category: Optional[str] = None
    ) -> List[MemoryItem]:
        """
        获取最近的记忆
        
        Args:
            agent_id: 智能体ID
            user_id: 用户ID
            days: 天数
            limit: 返回数量限制
            category: 记忆类别
            
        Returns:
            最近记忆列表
        """
        try:
            logger.info(f"获取最近记忆: agent_id={agent_id}, user_id={user_id}, days={days}")
            
            # 计算开始时间
            start_time = datetime.now() - timedelta(days=days)
            
            # 搜索最近记忆
            memories, _ = await self.es_service.search_memories(
                agent_id=agent_id,
                user_id=user_id,
                category=category,
                start_time=start_time,
                sort_by=SortOrder.TIME_DESC,
                limit=limit
            )
            
            logger.info(f"最近记忆获取完成: 返回{len(memories)}条记录")
            return memories
            
        except Exception as e:
            logger.error(f"获取最近记忆失败: {e}")
            raise
    
    async def get_memory_statistics(self, agent_id: str, user_id: str) -> dict:
        """
        获取记忆统计信息
        
        Args:
            agent_id: 智能体ID
            user_id: 用户ID
            
        Returns:
            统计信息
        """
        try:
            logger.info(f"获取记忆统计: agent_id={agent_id}, user_id={user_id}")
            
            # 获取总数
            total_memories, _ = await self.es_service.search_memories(
                agent_id=agent_id,
                user_id=user_id,
                limit=0
            )
            
            # 通过聚合查询获取所有类别的统计
            category_stats = await self.es_service.get_category_statistics(
                agent_id=agent_id,
                user_id=user_id
            )
            
            # 最近7天的记忆数量
            recent_start = datetime.now() - timedelta(days=7)
            recent_memories, recent_total = await self.es_service.search_memories(
                agent_id=agent_id,
                user_id=user_id,
                start_time=recent_start,
                limit=0
            )
            
            statistics = {
                "total_memories": len(total_memories),
                "category_stats": category_stats,
                "recent_7_days": recent_total
            }
            
            logger.info(f"记忆统计获取完成: {statistics}")
            return statistics
            
        except Exception as e:
            logger.error(f"获取记忆统计失败: {e}")
            raise
    
    async def health_check(self) -> dict:
        """健康检查"""
        try:
            # 检查Elasticsearch服务
            es_health = await self.es_service.health_check()
            
            # 检查向量编码服务
            embedding_health = {
                "status": "ready" if self.embedding_service.is_initialized() else "not_ready"
            }
            
            overall_status = "healthy"
            if es_health.get("status") != "connected" or embedding_health.get("status") != "ready":
                overall_status = "unhealthy"
            
            health_info = {
                "status": overall_status,
                "elasticsearch": es_health,
                "embedding": embedding_health,
                "timestamp": datetime.now().isoformat()
            }
            
            return health_info
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def close(self):
        """关闭服务"""
        try:
            await self.es_service.close()
            await self.embedding_service.close()
            logger.info("记忆管理服务已关闭")
        except Exception as e:
            logger.error(f"关闭记忆管理服务失败: {e}")


# 全局记忆管理服务实例
memory_service = MemoryService() 