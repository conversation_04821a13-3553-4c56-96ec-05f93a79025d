spring:
  application:
    name: base-file-service
  cloud:
    nacos:
      server-addr: 172.28.0.212:8080
      username: nacos
      password: nacos
      discovery:  # 这里是和注册相关的配置
        group: dev # 往哪个组注册
        namespace: dev
      config:
        group: dev # 往哪个组注册
        namespace: dev
        file-extension: yaml  # 文件类型
        extension-configs:
          - data-id: ai-common-config.yaml
            group: dev
            refresh: true