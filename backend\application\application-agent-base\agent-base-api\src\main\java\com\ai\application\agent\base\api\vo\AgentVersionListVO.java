package com.ai.application.agent.base.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Schema(name = "版本号表DTO")
@Data
public class AgentVersionListVO {
    /**
     * 智能体版本sn
     */
    @Schema(description = "智能体版本sn")
    private String versionSn;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建人")
    private String updateBy;

    /**
     * 智能体版本号
     */
    @Schema(description = "智能体版本号")
    private String versionNumber;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;
}
