package com.ai.application.app.api.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 应用表
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Data
@Schema(name = "")
public class AppVO {
    /**
     * 应用id:100智能体平台,500管理系统
     */
    @Schema(description = "应用id:100智能体平台,500管理系统")
    private Integer appId;
    /**
     * 应用名称
     */
    @Schema(description = "应用名称")
    private String appName;
    /**
     * 入口url
     */
    @Schema(description = "入口url")
    private String appUrl;
    /**
     * 应用描述
     */
    @Schema(description = "应用描述")
    private String appDesc;
    /**
     * 应用状态 0:禁用,1:启用,-1:删除
     */
    @Schema(description = "应用状态 0:禁用,1:启用,-1:删除")
    private Integer appStatus;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}