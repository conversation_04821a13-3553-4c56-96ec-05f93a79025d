package com.ai.application.agent.base.api.enums;

import lombok.Getter;

@Getter
public enum AgentTypeEnum {
    MASTER(30, "智能规划"),
    WORK_FLOW(20, "工作流"),
    CHAT_AGENT(10, "对话流")
    ;

    private final Integer code;
    private final String desc;

    AgentTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AgentTypeEnum ofCode(Integer value) {
        for (AgentTypeEnum sessionStatusEnum : AgentTypeEnum.values()) {
            if (sessionStatusEnum.code.equals(value)) {
                return sessionStatusEnum;
            }
        }
        return null;
    }
}
