<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ai.application</groupId>
        <artifactId>application-app</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>app-api</artifactId>
    <name>${project.artifactId}</name>
    <version>${revision}</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.ai.framework.core</groupId>
            <artifactId>framework-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ai.framework.swagger</groupId>
            <artifactId>framework-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ai.framework.mybatis</groupId>
            <artifactId>framework-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ai.framework.web</groupId>
            <artifactId>framework-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>
