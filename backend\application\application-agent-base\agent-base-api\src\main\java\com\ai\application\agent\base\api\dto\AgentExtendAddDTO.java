package com.ai.application.agent.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * agent扩展信息
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@Schema(name = "agent扩展信息DTO")
public class AgentExtendAddDTO {
    /**
     * 参数名称
     */
    @Schema(description = "参数名称")
    private String itemName;

    /**
     * 参数值
     */
    @Schema(description = "参数值")
    private String itemValue;

    /**
     * 智能体id
     */
    @Schema(description = "智能体id")
    private Integer agentId;
}