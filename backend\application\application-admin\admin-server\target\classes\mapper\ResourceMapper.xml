<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.admin.mapper.ResourceMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.admin.api.entity.Resource">
                    <id column="resource_id" property="resourceId" />
                    <result column="resource_type" property="resourceType" />
                    <result column="resource_object_id" property="resourceObjectId" />
                    <result column="resource_tenant_id" property="resourceTenantId" />
                    <result column="resource_status" property="resourceStatus" />
                    <result column="resource_scope" property="resourceScope" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        resource_id, resource_type, resource_object_id, resource_tenant_id, resource_status, resource_scope, create_time, update_time
    </sql>

    <select id="queryAgentList" resultType="com.ai.application.admin.api.vo.AdminResourceVO">
        SELECT A.resource_id,A.resource_object_id object_id,B.agent_name object_name FROM `resource` A
        LEFT JOIN agent B ON(A.resource_object_id=B.agent_id)
        WHERE A.resource_status=1 AND A.resource_type=10
    </select>

    <select id="queryGrantAgentList" resultType="com.ai.application.admin.api.vo.AdminResourceVO">
        SELECT A.grant_id,A.resource_object_id object_id,B.agent_name object_name FROM `resource_grant` A
        LEFT JOIN agent B ON(A.resource_object_id=B.agent_id)
        WHERE A.grant_status=1 AND A.resource_type=10 and A.grant_object_type=40 and A.grant_object_id=#{tenantId}
    </select>

    <select id="queryModelList" resultType="com.ai.application.admin.api.vo.AdminResourceVO">
        SELECT A.resource_id,A.resource_object_id object_id,B.model_name object_name FROM `resource` A
        LEFT JOIN model_info B ON(A.resource_object_id=B.model_id)
        WHERE A.resource_status=1 AND A.resource_type=70
    </select>
    <select id="queryGrantModelList" resultType="com.ai.application.admin.api.vo.AdminResourceVO">
        SELECT A.grant_id,A.resource_object_id object_id,B.model_name object_name FROM `resource_grant` A
        LEFT JOIN model_info B ON(A.resource_object_id=B.model_id)
        WHERE A.grant_status=1 AND B.model_status=1 AND A.resource_type=70 and A.grant_object_type=40 and A.grant_object_id=#{tenantId}
    </select>
</mapper>