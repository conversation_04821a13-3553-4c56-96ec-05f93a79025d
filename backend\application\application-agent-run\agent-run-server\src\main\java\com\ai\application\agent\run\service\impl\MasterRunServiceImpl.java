package com.ai.application.agent.run.service.impl;

import bo.MasterContentBO;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import com.ai.application.agent.run.service.IMasterRunService;
import com.ai.application.base.model.api.dto.ModelFactoryChatDTO;
import com.ai.application.base.model.api.feign.IModelFactoryClient;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.reactive.ReactiveFeignClientFactory;
import com.ai.framework.core.util.date.DateUtil;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.vo.ResultVo;
import dto.MasterRunDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import vo.MasterRunVO;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class MasterRunServiceImpl implements IMasterRunService {
    @Resource
    private ReactiveFeignClientFactory factory;

    @Override
    public Flux<ResultVo<MasterRunVO>> run(MasterRunDTO dto) {
        MasterRunVO masterRunVO = new MasterRunVO();
        masterRunVO.setRunId(1);
        masterRunVO.setSessionSn(UUIDUtil.genRandomSn("session"));
        masterRunVO.setMessageTime(DateUtil.dateToStr(new Date()));
        masterRunVO.setMessageStatus("success");
        return this.getStreamDoc(masterRunVO).map(ResultVo::data);
    }

    // 模拟不同的响应流
    private Flux<MasterRunVO> getStreamDoc(MasterRunVO masterRunVO) {
        IModelFactoryClient modelClient = factory.createClient(IModelFactoryClient.class,
                ServiceConstant.BASE_MODEL);

        MasterContentBO masterContentBO = new MasterContentBO();
        MasterContentBO.Step step = new MasterContentBO.Step();
        step.setType(20);
        step.setStepName("知识搜索中");
        masterContentBO.setSteps(List.of(step));
        masterRunVO.setContent(masterContentBO);

        Flux<MasterRunVO> startFlux = Flux.just(masterRunVO);

        ModelFactoryChatDTO modelFactoryChatDTO = new ModelFactoryChatDTO();
        modelFactoryChatDTO.setModelSn(UUIDUtil.genRandomSn("model"));
        modelFactoryChatDTO.setMessage("你是谁");
        Flux<String> chatStreamFlux = modelClient.chatStream(modelFactoryChatDTO);
        Flux<MasterRunVO> docFlux = chatStreamFlux.map(str -> {
            MasterContentBO masterContentBONew = new MasterContentBO();
            masterContentBONew.setText(str);
            masterRunVO.setContent(masterContentBONew);
            return masterRunVO;
        });

        return Flux.concat(startFlux, docFlux, toKnowledge(masterRunVO));
    }

    public  Flux<MasterRunVO> toKnowledge(MasterRunVO masterRun) {
        MasterRunVO masterRunVO = new MasterRunVO();
        BeanUtil.copyProperties(masterRun, masterRunVO);
        MasterContentBO masterContentBO = new MasterContentBO();

        masterRunVO.setRunId(RandomUtil.randomInt(10000, 100000));

        MasterContentBO.Knowledge<MasterContentBO.Doc> docKnowledge = new MasterContentBO.Knowledge<>();
        docKnowledge.setFileName("测试PDF");
        docKnowledge.setUrl("http://172.28.0.147:8001/public-agent/AskXbot%20%E4%BD%BF%E7%94%A8%E5%B9%B3%E5%8F%B0%20(1).pdf");
        docKnowledge.setDomain("example.com");
        docKnowledge.setFileType("pdf");
        MasterContentBO.Doc doc = new MasterContentBO.Doc();
        doc.setScore(0.2D);
        doc.setPageContent("当前，世界百年未有之大变局加速演进，世界之变、时代之变、历史之变正以前所未有的方式展开。本期“访谈录”邀请嘉宾就如何深刻认识和科学理解构建人类命运共同体理");
        doc.setPageNo(10);
        MasterContentBO.Doc doc1 = new MasterContentBO.Doc();
        doc1.setScore(0.3D);
        doc1.setPageContent("中国推动联大设立文明对话国际日，旨在充分发挥文明对话对于消除歧视偏见、增进理解信任、促进民心相通、加强团结合作的重要作用，为人类社会团结应对共同挑战注入正能量。");
        doc1.setPageNo(11);
        docKnowledge.setPage(List.of(doc, doc1));

        MasterContentBO.Knowledge<MasterContentBO.Doc> docKnowledge1 = new MasterContentBO.Knowledge<>();
        docKnowledge1.setFileName("测试DOC");
        docKnowledge1.setUrl("http://172.28.0.147:8001/public-agent/xbot.doc");
        docKnowledge1.setDomain("example.com");
        docKnowledge1.setFileType("doc");
        MasterContentBO.Doc doc3 = new MasterContentBO.Doc();
        doc3.setScore(0.3D);
        doc3.setPageContent("中国推动联大设立文明对话国际日，旨在充分发挥文明对话对于消除歧视偏见、增进理解信任、促进民心相通、加强团结合作的重要作用，为人类社会团结应对共同挑战注入正能量。");
        doc3.setPageNo(11);
        docKnowledge1.setPage(List.of(doc3));
        masterContentBO.setDocs(List.of(docKnowledge, docKnowledge1));

        MasterContentBO.Knowledge<MasterContentBO.Image> docKnowledge3 = new MasterContentBO.Knowledge<>();
        docKnowledge3.setFileName("测试图片");
        docKnowledge3.setUrl("https://img2.baidu.com/it/u=2765228315,1630846063&fm=253&fmt=auto&app=120&f=JPEG?w=667&h=500");
        docKnowledge3.setDomain("example.com");
        docKnowledge3.setFileType("jpg");
        MasterContentBO.Image image = new MasterContentBO.Image();
        image.setScore(0.2D);
        image.setImageName("C罗倒挂");
        image.setPageNo(10);
        image.setImageSn(UUIDUtil.genRandomSn("image"));
        image.setDownloadUrl("https://img2.baidu.com/it/u=2765228315,1630846063&fm=253&fmt=auto&app=120&f=JPEG?w=667&h=500");
        docKnowledge3.setPage(List.of(image));
        masterContentBO.setImages(List.of(docKnowledge3));

        masterRunVO.setContent(masterContentBO);
        return Flux.just(masterRunVO);
    }

}