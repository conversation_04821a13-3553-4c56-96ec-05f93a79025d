package com.ai.application.knowledge.table.service;

import com.ai.application.knowledge.table.dto.TableDataCreateDto;
import com.ai.application.knowledge.table.dto.TableDataListDto;
import com.ai.application.knowledge.table.entity.KnowledgeTable;
import com.ai.application.knowledge.table.entity.KnowledgeTableData;
import com.ai.application.knowledge.table.vo.TableDataImportVo;
import com.ai.application.knowledge.table.vo.TableDataListVo;
import com.ai.framework.core.vo.ResultVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 智能表格数据表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public interface IKnowledgeTableDataService extends IService<KnowledgeTableData> {

    ResultVo<Integer> create(TableDataCreateDto dto);

    ResultVo<String> createFeign(TableDataCreateDto dto);

    ResultVo<String> update(TableDataCreateDto dto);

    ResultVo<PageInfo<TableDataListVo>> list(TableDataListDto dto);

    ResultVo<List<TableDataListVo>> noPageList(TableDataListDto dto);

    ResultVo<String> delete(String tableSn, Integer rowId);

    ResponseEntity<byte[]> downloadTemplate(String intelligentTableSn);

    ResponseEntity<byte[]> export(TableDataListDto dto);

    ResultVo<TableDataImportVo> importExcel(MultipartFile file, String tableSn, Integer overwrite);

    void arithmeticBatchUpdate(List<KnowledgeTableData> list, KnowledgeTable knowledgeTable);
}
