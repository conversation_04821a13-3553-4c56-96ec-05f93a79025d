package com.ai.application.agent.run.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 文档知识检索枚举
 */
public interface DocKnowledgeSearchEnum {

    /**
     * 检索类型
     */
    @Getter
    @AllArgsConstructor
    enum DocKnowledgeSearchTypeEnum {

        RULE("rule", "规则检索"),
        //语义检索
        EMBEDDING("embedding", "语义检索"),
        //关键词检索
        KEYWORD("keywords", "关键词检索"),

        ALL("all", "全部"),
        ;
        private final String type;
        private final String desc;
    }

    /**
     * 输入字段枚举类
     */
    @Getter
    @AllArgsConstructor
    enum DocKnowledgeSearchInputFieldEnum {

        KNOWLEDGE_INVENTORY_SN("knowledgeInventorySn", "知识库Sn", DocKnowledgeSearchTypeEnum.ALL.getType(), null),
        TYPE("type", "类型", DocKnowledgeSearchTypeEnum.ALL.getType(), null),
        CONDITIONS("conditions", "条件集", DocKnowledgeSearchTypeEnum.ALL.getType(), null),
        SEARCH_CONTENT("searchContent", "语义检索 检索内容 文档名称 文档内容 知识标签", DocKnowledgeSearchTypeEnum.ALL.getType(), null),
        SEARCH_KNOWLEDGE_CONTENT("searchKnowledgeContent", "语义检索：内容", DocKnowledgeSearchTypeEnum.ALL.getType(), null),
        TARGET("target", "目标工作流id", DocKnowledgeSearchTypeEnum.ALL.getType(), CONDITIONS.field),
        EXPRESSION("expression", "表达式", DocKnowledgeSearchTypeEnum.RULE.getType(), CONDITIONS.field),
        LOGIC("logic", "逻辑", DocKnowledgeSearchTypeEnum.RULE.getType(), null),
        TOP_K("topK", "输出文档数量", DocKnowledgeSearchTypeEnum.ALL.getType(), null),
        ;

        private final String field;
        private final String desc;
        private final String searchType;
        private final String parentField;
    }

    /**
     * 输出字段枚举类
     */
    @Getter
    @AllArgsConstructor
    enum DocKnowledgeSearchOutputFieldEnum {
        TARGET("target", "目标工作流id"),
        DOC_FILE("docFile", "文件"),
        MATCH_RESULT("matchResult", "匹配结果")
        ;
        private final String field;
        private final String desc;
    }

    /**
     * 映射表字段
     */
    @Getter
    @AllArgsConstructor
    enum DocKnowledgeSearchMappingFieldEnum {
        DOCUMENT_NAME("documentName", "文档名称", "file_name"),
        DOCUMENT_SUFFIX("documentSuffix", "文档后缀", "doc_type"),
        DOCUMENT_TAG("documentTag", "知识标签", "tags"),
        DOCUMENT_SUMMARY("documentSummary", "知识摘要", "summary"),
        NONE("none", "无", "none"),
        ;
        private final String field;
        private final String desc;
        private final String mapping;
    }

    @Getter
    @AllArgsConstructor
    enum DocKnowledgeSearchMappingEmbeddingFieldEnum {
        DOCUMENT_NAME("documentName", "文档名称", "title"),
        DOCUMENT_CONTENT("documentContent", "文档内容", "content"),
        DOCUMENT_SUMMARY("documentSummary", "文档摘要", "summary"),
        NONE("none", "无", "none"),
        ;
        private final String field;
        private final String desc;
        private final String mapping;
    }

    @Getter
    @AllArgsConstructor
    enum DocKnowledgeSearchMappingKeywordFieldEnum {
        DOCUMENT_NAME("documentName", "文档名称", "file_name"),
        DOCUMENT_CONTENT("documentTag", "知识标签", "tags"),
        NONE("none", "无", "none"),
        ;
        private final String field;
        private final String desc;
        private final String mapping;
    }

    static DocKnowledgeSearchMappingFieldEnum getMappingByField(String field) {
        for (DocKnowledgeSearchMappingFieldEnum value : DocKnowledgeSearchMappingFieldEnum.values()) {
            if (value.getField().equals(field)) {
                return value;
            }
        }
        return DocKnowledgeSearchMappingFieldEnum.NONE;
    }

    static DocKnowledgeSearchMappingEmbeddingFieldEnum getEmbeddingMappingByField(String field) {
        for (DocKnowledgeSearchMappingEmbeddingFieldEnum value : DocKnowledgeSearchMappingEmbeddingFieldEnum.values()) {
            if (value.getField().equals(field)) {
                return value;
            }
        }
        return DocKnowledgeSearchMappingEmbeddingFieldEnum.NONE;
    }

    static DocKnowledgeSearchMappingKeywordFieldEnum getKeywordMappingByField(String field) {
        for (DocKnowledgeSearchMappingKeywordFieldEnum value : DocKnowledgeSearchMappingKeywordFieldEnum.values()) {
            if (value.getField().equals(field)) {
                return value;
            }
        }
        return DocKnowledgeSearchMappingKeywordFieldEnum.NONE;
    }
}
