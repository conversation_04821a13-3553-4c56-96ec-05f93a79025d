#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Nacos导入配置测试脚本

测试Nacos SDK的导入和配置状态，提供：
- loguru日志库导入验证
- Nacos SDK v2.0和v1.0导入测试
- nacos_client模块功能检查
- 依赖包兼容性验证
- 导入失败问题诊断和解决建议

Usage:
    python test/test_nacos_imports.py

Project: Agent Memory Service
Author: 张小龙 <<EMAIL>>
Created: 2025-06-01
Version: 1.0.0
License: private
"""

import sys
from pathlib import Path

def test_loguru_import():
    """测试loguru导入"""
    try:
        from loguru import logger
        print("[SUCCESS] loguru导入成功")
        return True
    except ImportError as e:
        print(f"[ERROR] loguru导入失败: {e}")
        return False

def test_nacos_v2_import():
    """测试Nacos SDK v2.0导入"""
    try:
        from nacos import NacosClient
        print("[SUCCESS] Nacos SDK v2.0导入成功")
        return True
    except ImportError as e:
        print(f"[ERROR] Nacos SDK v2.0导入失败: {e}")
        return False

def test_nacos_v1_import():
    """测试Nacos SDK v1.0导入"""
    try:
        import nacos
        print("[SUCCESS] Nacos SDK v1.0导入成功")
        return True
    except ImportError as e:
        print(f"[ERROR] Nacos SDK v1.0导入失败: {e}")
        return False

def test_nacos_client_import():
    """测试nacos_client模块导入"""
    try:
        # 添加项目路径
        project_root = Path(__file__).parent.parent
        sys.path.insert(0, str(project_root))
        
        from app.config.nacos_client import nacos_manager
        print("[SUCCESS] nacos_client模块导入成功")
        print(f"[INFO] 协议信息: {nacos_manager.protocol_info}")
        return True
    except ImportError as e:
        print(f"[ERROR] nacos_client模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"[ERROR] nacos_client模块导入异常: {e}")
        return False

def main():
    """主函数"""
    print("[START] Nacos导入配置测试")
    print("=" * 50)
    
    tests = [
        ("loguru库", test_loguru_import),
        ("Nacos SDK v2.0", test_nacos_v2_import),
        ("Nacos SDK v1.0", test_nacos_v1_import),
        ("nacos_client模块", test_nacos_client_import)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[TEST] 测试{test_name}...")
        if test_func():
            passed += 1
        else:
            print(f"[WARNING] {test_name}测试失败")
    
    print("\n" + "=" * 50)
    print(f"[REPORT] 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("[SUCCESS] 所有依赖导入正常")
    else:
        print(f"[WARNING] 有{total - passed}个依赖导入失败")
        print("\n[SUGGESTION] 解决建议:")
        print("1. 检查依赖是否已安装: pip install nacos-sdk-python")
        print("2. 检查Python环境配置")
        print("3. 检查项目路径配置")

if __name__ == "__main__":
    main() 