package com.ai.application.agent.run.controller;

import com.ai.application.agent.run.service.IChatFlowRunService;
import com.ai.framework.core.vo.ResultVo;
import dto.ChatFlowRunDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import vo.ChatFlowRunVO;

@Tag(name = "对话流", description = "对话流")
@RestController
@RequestMapping("/v1/chatFlow")
@AllArgsConstructor
public class ChatFlowController {
    private final IChatFlowRunService chatFlowSessionService;

    /**
     * 智能规划执行
     * @return
     */
    @Operation(summary = "对话流会话")
    @PostMapping(produces = "text/event-stream")
    public Flux<ResultVo<ChatFlowRunVO>> run(@Validated @RequestBody ChatFlowRunDTO dto) {
        return chatFlowSessionService.run(dto);
    }
}