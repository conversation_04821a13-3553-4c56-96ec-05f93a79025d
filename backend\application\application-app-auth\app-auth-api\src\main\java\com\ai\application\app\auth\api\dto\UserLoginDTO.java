package com.ai.application.app.auth.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "授权登录传参")
public class UserLoginDTO {
    /**
     * 用户账号
     */
    @Schema(description  = "用户登录名")
    private String loginName;

    /**
     * 用户密码
     */
    @Schema(description  = "用户密码")
    private String password;

    /**
     * 应用ID
     */
    @Schema(description  = "应用ID")
    private Integer appId;

    @Schema(description = "租户编码")
    private String tenantSn;
}
