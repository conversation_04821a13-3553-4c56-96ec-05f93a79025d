package com.ai.application.skill.mcp.feign;



import com.ai.application.skill.mcp.api.feign.IMcpToolFeignClient;
import com.ai.application.skill.mcp.api.vo.McpToolVO;
import com.ai.application.skill.mcp.service.IMcpToolService;
import com.ai.framework.core.vo.ResultVo;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import org.springframework.web.bind.annotation.RestController;


import java.io.IOException;
import java.util.List;

/**
 * 文件服务
 */
@RestController
@Slf4j
@AllArgsConstructor
public class McpToolFeignClient implements IMcpToolFeignClient {

    @Resource
    private IMcpToolService mcpToolService;


    @GetMapping("/detail/{mcpToolId}")
    public ResultVo<McpToolVO> getSkillDetail(@PathVariable("mcpToolId") Integer mcpToolId) {
         McpToolVO mcpToolVO = mcpToolService.get(mcpToolId);
        return ResultVo.data(mcpToolVO);
    }


}
