package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.bo.MasterDictBO;
import com.ai.application.agent.base.api.dto.AgentUseDictAddDTO;
import com.ai.application.agent.base.api.dto.AgentUseDictListDTO;
import com.ai.application.agent.base.api.dto.AgentUseDictUpdateDTO;
import com.ai.application.agent.base.api.vo.AgentUseDictListVO;
import java.util.List;

public interface IAgentUseDictService {
    /**
     * 列表
     *
     * @param queryDto
     * @return
     */
    List<AgentUseDictListVO> list(AgentUseDictListDTO queryDto);

    /**
     * 保存
     *
     * @param dto
     */
    void add(AgentUseDictAddDTO dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(AgentUseDictUpdateDTO dto);

    /**
     * TO Agent详情
     * @param versionId
     * @return
     */
    List<MasterDictBO> toDetail(Integer versionId);
}
