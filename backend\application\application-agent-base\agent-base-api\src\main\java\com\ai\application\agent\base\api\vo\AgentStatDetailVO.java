package com.ai.application.agent.base.api.vo;

import com.ai.application.agent.base.api.bo.AgentMetadataBO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Schema(name = "智体统计详情VO")
@Data
public class AgentStatDetailVO {
    /**
     * 智能体sn
     */
    @Schema(description = "智能体sn")
    private String agentSn;

    /**
     * 智能体名称
     */
    @Schema(description = "智能体名称")
    private String agentName;

    /**
     * 智能体描述
     */
    @Schema(description = "智能体描述")
    private String agentDesc;

    /**
     * 智能体类型: 10:对话流, 20:工作流, 30:master
     */
    @Schema(description = "智能体类型: 10:对话流, 20:工作流, 30:master")
    private Integer agentType;

    /**
     * 创建人名
     */
    @Schema(description = "创建人名")
    private String createBy;

    /**
     * 状态 0停用 1开发 5发布
     */
    @Schema(description = "状态 0停用 1开发 5发布")
    private Integer agentStatus;

    /**
     * 智能体启用版本id
     */
    @Schema(description = "智能体启用版本id")
    private Integer versionId;

    /**
     * 智能体启用版本名称
     */
    @Schema(description = "智能体启用版本名称")
    private String versionName;
}
