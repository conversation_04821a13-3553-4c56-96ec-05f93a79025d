package com.ai.application.agent.run.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 记忆添加请求DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "MemoryAddRequestDTO")
public class MemoryAddRequestDTO {

    /**
     * 智能体ID
     */
    @JsonProperty("agent_id")
    @Schema(description = "智能体ID")
    private String agentId;

    /**
     * 用户ID
     */
    @JsonProperty("user_id")
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 记忆类别
     */
    @JsonProperty("category")
    @Schema(description = "记忆类别")
    private String category;

    /**
     * 用户问题
     */
    @JsonProperty("user_question")
    @Schema(description = "用户问题")
    private String userQuestion;

    /**
     * 问题回复
     */
    @JsonProperty("question_reply")
    @Schema(description = "问题回复")
    private String questionReply;

    /**
     * 问题时间
     */
    @JsonProperty("question_time")
    @Schema(description = "问题时间")
    private LocalDateTime questionTime;
}
