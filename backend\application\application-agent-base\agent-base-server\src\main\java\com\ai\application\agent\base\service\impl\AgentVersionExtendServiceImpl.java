package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.dto.AgentVersionExtendDTO;
import com.ai.application.agent.base.api.dto.query.AgentVersionExtendQueryDTO;
import com.ai.application.agent.base.api.mapstruct.AgentVersionExtendMapstruct;
import com.ai.application.agent.base.api.vo.AgentVersionExtendVO;
import com.github.pagehelper.PageInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ai.application.agent.base.mapper.AgentVersionExtendMapper;
import com.ai.application.agent.base.api.entity.AgentVersionExtend;
import com.ai.application.agent.base.service.IAgentVersionExtendService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Date;

/**
 * agent版本扩展信息-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Service
public class AgentVersionExtendServiceImpl implements IAgentVersionExtendService{

    @Resource
    private AgentVersionExtendMapper agentVersionExtendMapper;

    @Resource
    private AgentVersionExtendMapstruct agentVersionExtendMapstruct;

    @Transactional(readOnly = true)
    @Override
    public List<AgentVersionExtendVO> list(AgentVersionExtendQueryDTO queryDto) {
        QueryWrapper<AgentVersionExtend> queryWrapper = this.buildQuery(queryDto);
        return agentVersionExtendMapstruct.toVoList(this.agentVersionExtendMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(AgentVersionExtendDTO dto) {
        dto.setItemId(null);
        AgentVersionExtend entity = agentVersionExtendMapstruct.toEntity(dto);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        agentVersionExtendMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void setItem(Integer agentId, Integer versionId, String itemName, String itemValue) {
        AgentVersionExtendDTO agentVersionExtendDTO = new AgentVersionExtendDTO();
        agentVersionExtendDTO.setVersionId(versionId);
        agentVersionExtendDTO.setAgentId(agentId);
        agentVersionExtendDTO.setItemName(itemName);
        agentVersionExtendDTO.setItemValue(itemValue);
        agentVersionExtendDTO.setItemStatus(1);
        this.add(agentVersionExtendDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AgentVersionExtendDTO dto) {
        BusinessAssertUtil.notNull(dto.getItemId(), "${cfg.primaryKeyColumnName}不能为空");

        // TODO 唯一性字段校验
        AgentVersionExtend entity = agentVersionExtendMapper.selectById(dto.getItemId());
        BusinessAssertUtil.notNull(entity, "找不到${cfg.primaryKeyColumnName}为 " + dto.getItemId() + " 的记录");

        AgentVersionExtend entityList = agentVersionExtendMapstruct.toEntity(dto);
        entityList.setUpdateTime(new Date());
        agentVersionExtendMapper.updateById(entityList);
    }

    @Transactional(readOnly = true)
    @Override
    public AgentVersionExtendVO get(Integer id) {
        BusinessAssertUtil.notNull(id, "${cfg.primaryKeyColumnName}不能为空");

        AgentVersionExtend entity = agentVersionExtendMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到${cfg.primaryKeyColumnName}为 " + id + " 的记录");

        return agentVersionExtendMapstruct.toVo(entity);
    }

    private QueryWrapper<AgentVersionExtend> buildQuery(AgentVersionExtendQueryDTO queryDto) {
        QueryWrapper<AgentVersionExtend> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(CollectionUtils.isNotEmpty(queryDto.getAgentIds()),AgentVersionExtend::getAgentId, queryDto.getAgentIds());
        if(StringUtils.isNotBlank(queryDto.getItemValue())){
            queryWrapper.lambda().eq(AgentVersionExtend::getItemName, queryDto.getItemName());
            queryWrapper.lambda().eq(AgentVersionExtend::getItemValue, queryDto.getItemValue());
        }
        return queryWrapper;
    }

}