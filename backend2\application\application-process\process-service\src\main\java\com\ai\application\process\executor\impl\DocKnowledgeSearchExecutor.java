package com.ai.application.process.executor.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.ai.application.agent.base.api.dto.DocSearchByKeywordDto;
import com.ai.application.agent.base.api.dto.DocumentRetrieverDto;
import com.ai.application.agent.doc.api.feign.IAgentDocClient;
import com.ai.application.process.api.entity.ProcessVariable;
import com.ai.application.process.api.enums.DataTypeEnum;
import com.ai.application.process.enums.DocKnowledgeSearchEnum;
import com.ai.application.process.enums.ProcessErrorCodeEnum;
import com.ai.application.process.executor.BaseExecutor;
import com.ai.application.process.executor.ExecutionContext;
import com.ai.application.process.service.KnowledgeService;
import com.ai.application.skill.file.api.dto.DocFileDto;
import com.ai.application.skill.file.api.dto.ExpressionVo;
import com.ai.application.skill.file.api.dto.VocabularyWordDto;
import com.ai.application.skill.file.api.entity.DocFile;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.vo.ResultVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Slf4j
@Component
public class DocKnowledgeSearchExecutor implements BaseExecutor {

    @Resource
    private IAgentDocClient agentDocClient;

    @Resource
    private KnowledgeService knowledgeService;


    @Override
    public Map<String, Object> execute(ExecutionContext executionContext) {

        Map<String, Object> parameters = executionContext.getParameters();
        Object knowledgeInventorySnObj = parameters.get(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.KNOWLEDGE_INVENTORY_SN.getField());
        String type = executionContext.getParameterAsString(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.TYPE.getField());
        Object conditions = parameters.get(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.CONDITIONS.getField());
        String searchContent = executionContext.getParameterAsString(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.SEARCH_CONTENT.getField());
        String searchKnowledgeContent = executionContext.getParameterAsString(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.SEARCH_KNOWLEDGE_CONTENT.getField());
        String logic = executionContext.getParameterAsString(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.LOGIC.getField());
        String topK = executionContext.getParameterAsString(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.TOP_K.getField());

        // 兼容自定义变量 知识库 知识库数组
        String knowledgeInventorySn = null;
        List<String> knowledgeInventoryList = Lists.newArrayList();
        if (knowledgeInventorySnObj != null) {
            if (knowledgeInventorySnObj instanceof List) {
                knowledgeInventoryList = (List<String>) knowledgeInventorySnObj;
            } else {
                knowledgeInventorySn = String.valueOf(knowledgeInventorySnObj);
            }
        } else {
            throw new ServiceException(ProcessErrorCodeEnum.DOC_KNOWLEDGE_SEARCH_KNOWLEDGE_INVENTORY_IS_BLANK);
        }

        Map<String, Object> result;
        topK=StringUtils.isBlank(topK)?"999":topK;
        if (DocKnowledgeSearchEnum.DocKnowledgeSearchTypeEnum.RULE.getType().equals(type)) {
            result = this.docKnowledgeSearchByRule(conditions, knowledgeInventorySn, knowledgeInventoryList,logic, executionContext.getVariables(), topK);
        } else if (DocKnowledgeSearchEnum.DocKnowledgeSearchTypeEnum.EMBEDDING.getType().equals(type)) {
            result = this.docKnowledgeSearchByEmbedding(conditions, knowledgeInventorySn, knowledgeInventoryList,searchContent, searchKnowledgeContent, topK);
        } else if (DocKnowledgeSearchEnum.DocKnowledgeSearchTypeEnum.KEYWORD.getType().equals(type)) {
            result = this.docKnowledgeSearchByKeyword(conditions, knowledgeInventorySn, searchContent,searchKnowledgeContent, topK);
        } else {
            throw new ServiceException(ProcessErrorCodeEnum.DOC_KNOWLEDGE_SEARCH_TYPE_ERROR, type);
        }

        return result;
    }

    private Map<String, Object> docKnowledgeSearchByKeyword(Object conditions, String knowledgeInventorySn, String searchContent, String searchKnowledgeContent, String topK) {

        ServiceException.throwIf(StringUtils.isBlank(searchKnowledgeContent), ProcessErrorCodeEnum.DOC_KNOWLEDGE_SEARCH_CONTENT_IS_BLANK);
        List<VocabularyWordDto> list;
        try {
            list = JSONArray.parseArray(searchKnowledgeContent, VocabularyWordDto.class);
        } catch (Exception e) {
            throw new ServiceException(ProcessErrorCodeEnum.DOC_KNOWLEDGE_SEARCH_CONTENT_IS_NOT_JSON);
        }
        DocSearchByKeywordDto dto = new DocSearchByKeywordDto(list, List.of(knowledgeInventorySn), Integer.parseInt(topK),
                DocKnowledgeSearchEnum.getKeywordMappingByField(searchContent).getMapping());
        ResultVo<List<DocFile>> resultVo = agentDocClient.searchByKeyword(dto);

        return this.resultMap(conditions, resultVo);
    }

    private Map<String, Object> docKnowledgeSearchByEmbedding(Object conditions, String knowledgeInventorySn,
                                                              List<String> knowledgeInventoryList, String searchContent,
                                                              String searchKnowledgeContent, String topK) {

        ServiceException.throwIf(StringUtils.isBlank(searchKnowledgeContent), ProcessErrorCodeEnum.DOC_KNOWLEDGE_SEARCH_CONTENT_IS_BLANK);

        String modelSn;
        DocumentRetrieverDto dto = new DocumentRetrieverDto();
        dto.setTopK(Integer.parseInt(topK));
        if (knowledgeInventorySn == null) {
            modelSn = knowledgeService.getModelSnByInventorySn(knowledgeInventoryList.get(0));
            dto.setDatasetIds(knowledgeInventoryList);
        } else {
            modelSn = knowledgeService.getModelSnByInventorySn(knowledgeInventorySn);
            dto.setDatasetIds(List.of(knowledgeInventorySn));
        }
        dto.setRetrieveMode(DocKnowledgeSearchEnum.getEmbeddingMappingByField(searchContent).getMapping());
        dto.setOriginQuery(searchKnowledgeContent);
        if ("localModel".equals(modelSn) ||
                "embedding::localModel".equals(modelSn)) {
            dto.setModelSn("embedding::localModel");
        } else {
            dto.setModelSn("embedding::defaultModel");
        }
        ResultVo<List<DocFile>> resultVo = agentDocClient.documentRetrieverDto(dto);
        if (resultVo.getCode() != 0) {
            throw new ServiceException(resultVo.getCode(), resultVo.getMessage());
        }
        return this.resultMap(conditions, resultVo);
    }

    @Nullable
    private Map<String, Object> resultMap(Object conditions, ResultVo<List<DocFile>> resultVo) {
        Map<String, Object> result = new HashMap<>();
        JSONArray object = JSONArray.from(conditions);
        if (resultVo.getData() != null && resultVo.getData().isEmpty()) {
            Object o = object.get(1);
            if (o instanceof JSONObject jsonObject) {
                String target = jsonObject.getString(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.TARGET.getField());
                if (StringUtils.isNotBlank(target)) {
                    result.put(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.TARGET.getField(), target);
                }
                result.put(DocKnowledgeSearchEnum.DocKnowledgeSearchOutputFieldEnum.DOC_FILE.getField(), List.of());
                result.put(DocKnowledgeSearchEnum.DocKnowledgeSearchOutputFieldEnum.MATCH_RESULT.getField(), "False");
                return result;
            }
        }
        Object o = object.get(0);
        if (o instanceof JSONObject jsonObject) {
            String target = jsonObject.getString(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.TARGET.getField());
            if (StringUtils.isNotBlank(target)) {
                result.put(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.TARGET.getField(), target);
            }
            result.put(DocKnowledgeSearchEnum.DocKnowledgeSearchOutputFieldEnum.DOC_FILE.getField(), resultVo.getData().stream().map(fileInfo -> String.format("%s::%s::%s", fileInfo.getFileId(), fileInfo.getFileName(), DataTypeEnum.KNOWLEDGE)).collect(Collectors.toList()));
            result.put(DocKnowledgeSearchEnum.DocKnowledgeSearchOutputFieldEnum.MATCH_RESULT.getField(), "True");
            return result;
        }
        return null;
    }

    private Map<String, Object> docKnowledgeSearchByRule(Object conditions, String knowledgeInventorySn,
                                                         List<String> knowledgeInventoryList, String logic,
                                                         List<ProcessVariable> variables, String topK) {

        JSONArray object = JSONArray.from(conditions);
        // 获取第一个目标 true
        Object condition = object.get(0);
        Boolean isTrue = false;
        String target = "";
        List<String> docFiles = List.of();
        if (condition instanceof JSONObject jsonObject) {
            Map<String, String> collect = variables.stream().collect(Collectors.toMap(ProcessVariable::getId, e -> String.valueOf(e.generateCurrentValue()), (e1, e2) -> e1));
            target = jsonObject.getString(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.TARGET.getField());
            //表达式是否为空
            boolean isEmptyExpression = jsonObject.containsKey(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.EXPRESSION.getField())
                    && jsonObject.getString(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.EXPRESSION.getField()).isEmpty();
            ServiceException.throwIf(isEmptyExpression, ProcessErrorCodeEnum.DOC_KNOWLEDGE_SEARCH_EXPRESSION_IS_BLANK);
            List<ExpressionVo.Expression> expressions = jsonObject.getList(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.EXPRESSION.getField(), ExpressionVo.Expression.class);
            expressions.forEach(e -> {
                e.setField(DocKnowledgeSearchEnum.getMappingByField(e.getField()).getMapping());
                String value = collect.get(this.getValueByVariables(e.getValue()));
                if (StringUtils.isNotBlank(value)) {
                    e.setValue(value);
                }
            });

            ExpressionVo vo = new ExpressionVo(knowledgeInventorySn, knowledgeInventoryList,expressions, logic, Integer.valueOf(topK));
            ResultVo<List<DocFileDto>> resultVo = agentDocClient.ruleRetrieverDto(vo);
            if (resultVo.getCode() != 0) {
                throw new ServiceException(resultVo.getCode(), resultVo.getMessage());
            }

            List<DocFileDto> files = resultVo.getData();
            docFiles = files.stream().map(fileInfo -> String.format("%s::%s::%s", fileInfo.getFileId(), fileInfo.getFileName(), DataTypeEnum.KNOWLEDGE)).collect(Collectors.toList());
            isTrue = !docFiles.isEmpty();
        }
        if (!isTrue) {
            //获取文档为空是 走未获取文档的输出
            Object nDoc = object.get(1);
            if (nDoc instanceof JSONObject jsonObject) {
                target = jsonObject.getString(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.TARGET.getField());
            }
        }
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isNotBlank(target)) {
            result.put(DocKnowledgeSearchEnum.DocKnowledgeSearchOutputFieldEnum.TARGET.getField(), target);
        }
        result.put(DocKnowledgeSearchEnum.DocKnowledgeSearchOutputFieldEnum.DOC_FILE.getField(), docFiles);
        result.put(DocKnowledgeSearchEnum.DocKnowledgeSearchOutputFieldEnum.MATCH_RESULT.getField(), isTrue ? "True" : "False");
        return result;
    }

    private String getValueByVariables(String value) {
        String regex = "\\{\\{([^}]+)}}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(value);
        while (matcher.find()) {
            return matcher.group(1);
        }
        return value;
    }

    @Override
    public String getId() {
        return "DOCUMENT_KNOWLEDGE_SEARCH";
    }
}
