package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.bo.DataStatRangeBO;
import com.ai.application.agent.base.api.dto.AgentStatDTO;
import com.ai.application.agent.base.api.enums.AgentPageSourceEnum;
import com.ai.application.agent.base.api.mapstruct.AgentMapstruct;
import com.ai.application.agent.base.api.vo.AgentStatIndexVO;
import com.ai.application.agent.base.api.vo.AgentStatResultVO;
import com.ai.application.agent.base.api.vo.AgentTokensStatisticsDetailVO;
import com.ai.application.agent.base.api.vo.AgentUseDetailVO;
import com.ai.application.agent.base.mapper.AgentMapper;
import com.ai.application.agent.base.mapper.AgentRunSessionMapper;
import com.ai.application.agent.base.mapper.AgentRunWorkflowMapper;
import com.ai.application.agent.base.service.IAgentCommService;
import com.ai.application.agent.base.service.IAgentService;
import com.ai.application.agent.base.service.ITenantBoardService;
import com.ai.application.agent.base.utils.DateStatUtils;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.util.json.JsonUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Slf4j
@AllArgsConstructor
@Service
public class TenantBoardServiceImpl implements ITenantBoardService {
    private final AgentRunSessionMapper agentRunSessionMapper;
    private final IAgentService agentService;
    private final AgentMapper agentMapper;
    private final IAgentCommService agentCommService;
    private final AgentMapstruct agentMapstruct;
    private final AgentRunWorkflowMapper agentRunWorkflowMapper;

    @Override
    public AgentStatResultVO statCountMetrics(AgentStatDTO dto) {
        dto.setTenantId(UserContext.getTenantId());
        log.info("租户看板-数量统计,dto={}", JsonUtils.toJsonString(dto));
        AgentStatResultVO res = new AgentStatResultVO();

        //获取日期区间
        DataStatRangeBO dateStatRange = DateStatUtils.getDateStatRange(dto.getIndex());
        //当前日期查询条件dto
        AgentStatDTO agentStatDTO = new AgentStatDTO();
        agentStatDTO.setStartDate(dateStatRange.getStartDate());
        agentStatDTO.setEndDate(dateStatRange.getEndDate());
        agentStatDTO.setSessionStatus(1);
        agentStatDTO.setTenantId(UserContext.getTenantId());
        agentStatDTO.setUserId(dto.getUserId());

        //更早日期查询dto
        AgentStatDTO preAgentStatDTO = new AgentStatDTO();
        agentStatDTO.setSessionStatus(1);
        agentStatDTO.setStartDate(dateStatRange.getPreStartDate());
        agentStatDTO.setEndDate(dateStatRange.getPreEndDate());
        agentStatDTO.setTenantId(UserContext.getTenantId());
        agentStatDTO.setUserId(dto.getUserId());


        //===活跃应用数量===
        //智能体总数量
        List<Integer> agentIds = agentCommService.queryGrantAgentIdList(AgentPageSourceEnum.MANAGE.getCode());
        Integer lastActiveAgents = Optional.ofNullable(agentRunSessionMapper.getAgentTotalSessions(agentStatDTO)).orElse(0);
        //更早7/30统计
        Integer preActiveAgents = Optional.ofNullable(agentRunSessionMapper.getAgentTotalSessions(preAgentStatDTO)).orElse(0);
        //计算增长率
        int diff = 0;
        String rate = "0%";
        if (preActiveAgents == 0) {
            rate = "--";
        } else {
            diff = lastActiveAgents - preActiveAgents;
            if (diff > 0) {
                rate = new BigDecimal(diff).divide(new BigDecimal(preActiveAgents), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).intValue() + "%";
            }
        }
        res.setAgentMetrics(AgentStatIndexVO.builder()
                .totalCount(agentIds.size())
                .count(lastActiveAgents)
                .rate(rate).build());

        //===总会话量===
        //最近7/30统计
        Integer lastAgentSessions = Optional.ofNullable(agentRunSessionMapper.getAgentTotalSessions(agentStatDTO)).orElse(0);
        //更早7/30统计
        Integer preAgentSessions = Optional.ofNullable(agentRunSessionMapper.getAgentTotalSessions(preAgentStatDTO)).orElse(0);
        //计算增长率
        diff = lastAgentSessions - preAgentSessions;
        rate = "0%";
        if (preAgentSessions == 0) {
            rate = "--";
        } else {
            if (diff > 0) {
                rate = new BigDecimal(diff).divide(new BigDecimal(preAgentSessions), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).intValue() + "%";
            }
        }
        res.setSessionMetrics(AgentStatIndexVO.builder().count(lastAgentSessions).rate(rate).build());

        //===工作流调用次数===
        Integer lastWorkflows = Optional.ofNullable(agentRunWorkflowMapper.getWorkflowCount(agentStatDTO)).orElse(0);
        //更早7/30统计
        Integer preWorkflows = Optional.ofNullable(agentRunWorkflowMapper.getWorkflowCount(preAgentStatDTO)).orElse(0);
        //计算增长率
        diff = lastWorkflows - preWorkflows;
        rate = "0%";
        if (preWorkflows == 0) {
            rate = "--";
        } else {
            if (diff > 0) {
                rate = new BigDecimal(diff).divide(new BigDecimal(preWorkflows), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).intValue() + "%";
            }
        }
        res.setWorkFlowMetrics(AgentStatIndexVO.builder().count(lastWorkflows).rate(rate).build());

        //===Token总消耗量统计===
        //最近7/30统计
        Integer lastAgentTokens = Optional.ofNullable(agentMapper.getAgentTotalTokens(agentStatDTO)).orElse(0);
        //更早7/30统计
        Integer preAgentTokens = Optional.ofNullable(agentMapper.getAgentTotalTokens(preAgentStatDTO)).orElse(0);
        //计算增长率
        diff = lastAgentTokens - preAgentTokens;
        rate = "0%";
        if (preAgentTokens == 0) {
            rate = "--";
        } else {
            if (diff > 0) {
                rate = new BigDecimal(diff).divide(new BigDecimal(preAgentTokens), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).intValue() + "%";
            }
        }
        res.setTokenMetrics(AgentStatIndexVO.builder().count(lastAgentTokens).rate(rate).build());

        return res;
    }

    /**
     * 高频智能体tokens消耗
     *
     * @param dto
     * @return
     */
    @Override
    public List<AgentTokensStatisticsDetailVO> queryTokensFrequentAgent(AgentStatDTO dto) {
        log.info("租户看板-高频tokens统计,dto={}", JsonUtils.toJsonString(dto));
        //获取日期区间
        DataStatRangeBO dateStatRange = DateStatUtils.getDateStatRange(dto.getIndex());
        //当前日期查询条件dto
        dto.setStartDate(dateStatRange.getStartDate());
        dto.setEndDate(dateStatRange.getEndDate());
        dto.setTenantId(UserContext.getTenantId());
        dto.setUserId(dto.getUserId());
        return agentMapper.queryTokensFrequentAgent(dto);
    }

    /**
     * 智能体最近消耗tokens消耗趋势
     *
     * @param dto
     * @return
     */
    @Override
    public List<AgentTokensStatisticsDetailVO> queryLastTokensAgent(AgentStatDTO dto) {
        log.info("租户看板-最近tokens统计,dto={}", JsonUtils.toJsonString(dto));
        //获取日期区间
        DataStatRangeBO dateStatRange = DateStatUtils.getDateStatRange(dto.getIndex());

        dto.setTenantId(UserContext.getTenantId());
        dto.setUserId(dto.getUserId());
        dto.setStartDate(dateStatRange.getStartDate());
        dto.setEndDate(dateStatRange.getEndDate());
        dto.setStatFlag(dateStatRange.getStatFlag());
        List<AgentTokensStatisticsDetailVO> list;
        //统计标记(1:按周,2:按月,3:按季,4:按年)
        List<Integer> statdays = Arrays.asList(0, 3, 4);
        int cnt = 0;
        if (statdays.contains(dateStatRange.getStatFlag())) {
            list = agentMapper.queryLastTokensAgentByMonthly(dto);
        } else {
            list = agentMapper.queryLastTokensAgentByDaily(dto);
        }

        if (dateStatRange.getStatFlag() == 0) {
            cnt = list.size();
        } else {
            Period between = Period.between(dateStatRange.getStartDate(), dateStatRange.getEndDate());
            //设置统计数量
            cnt = switch (dateStatRange.getStatFlag()) {
                case 1 -> 7;
                case 2 -> between.getDays();
                case 3 -> 3;
                case 4 -> 12;
                default -> cnt;
            };
        }

        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }

        List<AgentTokensStatisticsDetailVO> resList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        AgentTokensStatisticsDetailVO vo = new AgentTokensStatisticsDetailVO();
        String statDate = dateStatRange.getStartDate().format(formatter);
        vo.setStatLabel(statDate);
        vo.setStatValue(0);
        Optional<AgentTokensStatisticsDetailVO> first = list.stream().filter(a -> a.getStatLabel().equals(statDate)).findFirst();
        if (first.isPresent()) {
            vo.setStatValue(first.hashCode());
        }
        resList.add(vo);

        for (int i = 1; i < cnt; i++) {
            LocalDate date = dateStatRange.getStartDate().plusDays(i);
            if (statdays.contains(dateStatRange.getStatFlag())) {
                date = dateStatRange.getStartDate().plusMonths(i);
            }
            vo = new AgentTokensStatisticsDetailVO();
            String finalStatDate = date.format(formatter);
            vo.setStatLabel(date.format(formatter));
            vo.setStatValue(0);
            first = list.stream().filter(a -> a.getStatLabel().equals(finalStatDate)).findFirst();
            if (first.isPresent()) {
                vo.setStatValue(first.hashCode());
            }
            resList.add(vo);
        }
        return resList;
    }

    @Override
    public List<AgentUseDetailVO> queryAgentUseDetail(AgentStatDTO dto) {
        log.info("租户看板-智能体使用详情,dto={}", JsonUtils.toJsonString(dto));
        //获取日期区间
        DataStatRangeBO dateStatRange = DateStatUtils.getDateStatRange(dto.getIndex());

        dto.setTenantId(UserContext.getTenantId());
        dto.setUserId(dto.getUserId());
        dto.setStartDate(dateStatRange.getStartDate());
        dto.setEndDate(dateStatRange.getEndDate());
        //智能体使用tokens
        List<AgentUseDetailVO> list = agentMapper.queryAgentUseDetail(dto);

        //工作流平均调用次数
        List<AgentUseDetailVO> agentUseWorkflowCounts = agentRunWorkflowMapper.queryWorkflowCountByAgent(dto);

        //最近7日调用次数
        AgentStatDTO agentStatDTO = new AgentStatDTO();
        agentStatDTO.setTenantId(UserContext.getTenantId());
        agentStatDTO.setUserId(dto.getUserId());
        agentStatDTO.setDays(7);
        List<AgentUseDetailVO> listAgentRunCount7Days = agentMapper.queryAgentRunCounts(agentStatDTO);

        //最近30日调用次数
        agentStatDTO.setDays(30);
        List<AgentUseDetailVO> listAgentRunCount30Days = agentMapper.queryAgentRunCounts(agentStatDTO);

        //数据整合
        list.forEach(agent -> {
            Optional<AgentUseDetailVO> first = agentUseWorkflowCounts.stream().filter(b -> b.getAgentSn().equals(agent.getAgentSn())).findFirst();
            if (first.isPresent()) {
                agent.setWorkFlowMetrics(first.get().getWorkFlowMetrics());
            }
            Optional<AgentUseDetailVO> first2 = listAgentRunCount7Days.stream().filter(b -> b.getAgentSn().equals(agent.getAgentSn())).findFirst();
            if (first2.isPresent()) {
                agent.setRunCount7Days(first2.get().getRunCount());
            }
            Optional<AgentUseDetailVO> first3 = listAgentRunCount30Days.stream().filter(b -> b.getAgentSn().equals(agent.getAgentSn())).findFirst();
            if (first3.isPresent()) {
                agent.setRunCount30Days(first3.get().getRunCount());
            }
        });
        return list;
    }

}
