# -*- coding: utf-8 -*-
"""
Elasticsearch服务模块

负责与Elasticsearch集群的交互，提供：
- Elasticsearch客户端初始化和连接管理
- 索引创建和映射配置（支持IK分词器）
- 记忆文档的CRUD操作
- 向量相似度搜索和混合查询
- 聚合统计查询
- 健康检查和连接状态监控
- 批量操作和数据迁移

Project: Agent Memory Service
Author: 张小龙 <<EMAIL>>
Created: 2025-06-01
Version: 1.0.0
License: private
"""

import json
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from elasticsearch import AsyncElasticsearch
from elasticsearch.exceptions import NotFoundError, RequestError
from loguru import logger

from app.config.settings import settings
from app.models.schemas import MemoryDocument, MemoryItem, SortOrder


class ElasticsearchService:
    """Elasticsearch服务"""
    
    def __init__(self):
        self.client: Optional[AsyncElasticsearch] = None
        self.index_name = settings.elasticsearch.index_name
        self._is_connected = False
    
    async def initialize(self):
        """初始化Elasticsearch客户端"""
        try:
            es_config = settings.elasticsearch
            
            # 构建连接参数 - 适配 elasticsearch 9.x
            client_kwargs = {
                'hosts': es_config.hosts,
                'request_timeout': es_config.timeout,  # elasticsearch 9.x 使用 request_timeout
                'max_retries': es_config.max_retries,
                'retry_on_timeout': es_config.retry_on_timeout
            }
            
            # 添加认证配置 - elasticsearch 9.x 使用 basic_auth 参数
            if es_config.username and es_config.password:
                client_kwargs['basic_auth'] = (es_config.username, es_config.password)
                logger.info(f"使用基础认证: {es_config.username}")
            
            # 添加SSL配置
            if es_config.use_ssl:
                client_kwargs['verify_certs'] = es_config.verify_certs
                
                if es_config.ca_certs:
                    client_kwargs['ca_certs'] = es_config.ca_certs
                if es_config.client_cert:
                    client_kwargs['client_cert'] = es_config.client_cert
                if es_config.client_key:
                    client_kwargs['client_key'] = es_config.client_key
                    
                logger.info("启用SSL连接")
            
            self.client = AsyncElasticsearch(**client_kwargs)
            
            # 测试连接
            await self._test_connection()
            self._is_connected = True
            
            # 创建索引（如果不存在）
            await self._create_index_if_not_exists()
            
            logger.info("Elasticsearch客户端初始化成功")
            
        except Exception as e:
            logger.error(f"Elasticsearch客户端初始化失败: {e}")
            self._is_connected = False
            raise
    
    async def _test_connection(self):
        """测试Elasticsearch连接"""
        if not self.client:
            raise Exception("Elasticsearch客户端未初始化")
        
        try:
            health = await self.client.cluster.health()
            logger.info(f"Elasticsearch集群状态: {health['status']}")
        except Exception as e:
            logger.error(f"Elasticsearch连接测试失败: {e}")
            raise
    
    async def _create_index_if_not_exists(self):
        """创建索引（如果不存在）"""
        try:
            # 检查索引是否存在
            exists = await self.client.indices.exists(index=self.index_name)
            
            if not exists:
                # 先检查是否支持IK分词器
                ik_available = await self._check_ik_analyzer()
                
                # 定义索引映射
                mapping = {
                    "mappings": {
                        "properties": {
                            "agent_id": {
                                "type": "keyword"
                            },
                            "user_id": {
                                "type": "keyword"
                            },
                            "category": {
                                "type": "keyword"
                            },
                            "user_question": {
                                "type": "text",
                                "analyzer": "ik_max_word" if ik_available else "standard",
                                "search_analyzer": "ik_smart" if ik_available else "standard"
                            },
                            "question_reply": {
                                "type": "text",
                                "analyzer": "ik_max_word" if ik_available else "standard",
                                "search_analyzer": "ik_smart" if ik_available else "standard"
                            },
                            "question_time": {
                                "type": "date",
                                "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd'T'HH:mm:ss||epoch_millis"
                            },
                            "question_vector": {
                                "type": "dense_vector",
                                "dims": settings.embedding.dimensions,  # 从配置中读取向量维度
                                "index": True,
                                "similarity": "cosine"
                            },
                            "created_at": {
                                "type": "date",
                                "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd'T'HH:mm:ss||epoch_millis"
                            },
                            "updated_at": {
                                "type": "date",
                                "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd'T'HH:mm:ss||epoch_millis"
                            }
                        }
                    },
                    "settings": {
                        "number_of_shards": 1,
                        "number_of_replicas": 0
                    }
                }
                
                # 只在支持IK分词器时添加分析器配置
                if ik_available:
                    mapping["settings"]["analysis"] = {
                        "analyzer": {
                            "ik_max_word": {
                                "type": "ik_max_word"
                            },
                            "ik_smart": {
                                "type": "ik_smart"
                            }
                        }
                    }
                    logger.info("使用IK分词器创建索引")
                else:
                    logger.warning("IK分词器不可用，使用标准分词器")
                
                # 创建索引
                await self.client.indices.create(
                    index=self.index_name,
                    body=mapping
                )
                
                logger.info(f"索引创建成功: {self.index_name}")
            else:
                logger.info(f"索引已存在: {self.index_name}")
                
        except Exception as e:
            logger.error(f"创建索引失败: {e}")
            raise
    
    async def _check_ik_analyzer(self) -> bool:
        """检查是否支持IK分词器"""
        try:
            # 尝试创建一个临时索引来测试IK分词器
            test_mapping = {
                "mappings": {
                    "properties": {
                        "test_field": {
                            "type": "text",
                            "analyzer": "ik_smart"
                        }
                    }
                },
                "settings": {
                    "analysis": {
                        "analyzer": {
                            "ik_smart": {
                                "type": "ik_smart"
                            }
                        }
                    }
                }
            }
            
            test_index = f"{self.index_name}_ik_test"
            
            # 创建测试索引
            await self.client.indices.create(
                index=test_index,
                body=test_mapping
            )
            
            # 删除测试索引
            await self.client.indices.delete(index=test_index)
            
            logger.info("IK分词器可用")
            return True
            
        except Exception as e:
            logger.warning(f"IK分词器不可用: {e}")
            return False
    
    async def add_memory(self, memory_doc: MemoryDocument) -> str:
        """
        添加记忆文档
        
        Args:
            memory_doc: 记忆文档
            
        Returns:
            文档ID
        """
        try:
            if not self._is_connected:
                await self.initialize()
            
            # 准备文档数据
            doc_data = {
                "agent_id": memory_doc.agent_id,
                "user_id": memory_doc.user_id,
                "category": memory_doc.category,
                "user_question": memory_doc.user_question,
                "question_reply": memory_doc.question_reply,
                "question_time": memory_doc.question_time.isoformat(),
                "question_vector": memory_doc.question_vector,
                "created_at": memory_doc.created_at.isoformat(),
                "updated_at": memory_doc.updated_at.isoformat()
            }
            
            # 索引文档
            response = await self.client.index(
                index=self.index_name,
                body=doc_data
            )
            
            doc_id = response['_id']
            logger.info(f"记忆文档添加成功: {doc_id}")
            return doc_id
            
        except Exception as e:
            logger.error(f"添加记忆文档失败: {e}")
            raise
    
    async def search_memories(
        self,
        agent_id: Optional[str] = None,
        user_id: Optional[str] = None,
        category: Optional[str] = None,
        question_vector: Optional[List[float]] = None,
        similarity_threshold: float = 0.7,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        sort_by: SortOrder = SortOrder.SIMILARITY_DESC,
        limit: int = 10
    ) -> Tuple[List[MemoryItem], int]:
        """
        搜索记忆
        
        Args:
            agent_id: 智能体ID
            user_id: 用户ID
            category: 记忆类别
            question_vector: 查询向量
            similarity_threshold: 相似度阈值
            start_time: 开始时间
            end_time: 结束时间
            sort_by: 排序方式
            limit: 返回数量限制
            
        Returns:
            记忆列表和总数
        """
        try:
            if not self._is_connected:
                await self.initialize()
            
            # 构建查询
            query = self._build_search_query(
                agent_id=agent_id,
                user_id=user_id,
                category=category,
                question_vector=question_vector,
                similarity_threshold=similarity_threshold,
                start_time=start_time,
                end_time=end_time
            )
            
            # 构建排序
            sort_config = self._build_sort_config(sort_by)
            
            # 执行搜索
            response = await self.client.search(
                index=self.index_name,
                body={
                    "query": query,
                    "sort": sort_config,
                    "size": limit,
                    "_source": True
                }
            )
            
            # 解析结果
            memories = []
            hits = response['hits']['hits']
            
            for hit in hits:
                source = hit['_source']
                
                # 计算相似度分数
                similarity_score = None
                if question_vector and 'question_vector' in source:
                    from app.services.embedding_service import embedding_service
                    similarity_score = embedding_service.calculate_cosine_similarity(
                        question_vector, source['question_vector']
                    )
                
                memory_item = MemoryItem(
                    id=hit['_id'],
                    agent_id=source['agent_id'],
                    user_id=source['user_id'],
                    category=source['category'],
                    user_question=source['user_question'],
                    question_reply=source.get('question_reply'),
                    question_time=datetime.fromisoformat(source['question_time']),
                    similarity_score=similarity_score,
                    created_at=datetime.fromisoformat(source['created_at']),
                    updated_at=datetime.fromisoformat(source['updated_at'])
                )
                memories.append(memory_item)
            
            total = response['hits']['total']['value']
            
            logger.info(f"搜索记忆完成，返回 {len(memories)} 条记录，总计 {total} 条")
            return memories, total
            
        except Exception as e:
            logger.error(f"搜索记忆失败: {e}")
            raise
    
    def _build_search_query(
        self,
        agent_id: Optional[str] = None,
        user_id: Optional[str] = None,
        category: Optional[str] = None,
        question_vector: Optional[List[float]] = None,
        similarity_threshold: float = 0.7,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """构建搜索查询"""
        
        # 构建过滤条件（不计算分数，对于must查询性能更优）
        filter_clauses = []
        
        # 添加精确匹配过滤条件
        if agent_id:
            filter_clauses.append({"term": {"agent_id": agent_id}})
        
        if user_id:
            filter_clauses.append({"term": {"user_id": user_id}})
        
        if category:
            filter_clauses.append({"term": {"category": category}})
        
        # 添加时间范围过滤
        if start_time or end_time:
            time_range = {}
            if start_time:
                time_range["gte"] = start_time.isoformat()
            if end_time:
                time_range["lte"] = end_time.isoformat()
            
            filter_clauses.append({
                "range": {
                    "question_time": time_range
                }
            })
        
        # 构建查询
        if question_vector:
            # 向量相似度查询 - 使用filter优化
            query = {
                "bool": {
                    "filter": filter_clauses,  # 精确匹配条件使用filter
                    "must": [  # 只有需要计算分数的查询使用must
                        {
                            "script_score": {
                                "query": {"match_all": {}},
                                "script": {
                                    "source": "cosineSimilarity(params.query_vector, 'question_vector') + 1.0",
                                    "params": {
                                        "query_vector": question_vector
                                    }
                                },
                                "min_score": similarity_threshold + 1.0
                            }
                        }
                    ]
                }
            }
        else:
            # 普通查询 - 全部使用filter
            if filter_clauses:
                query = {
                    "bool": {
                        "filter": filter_clauses
                    }
                }
            else:
                query = {"match_all": {}}
        
        return query
    
    def _build_sort_config(self, sort_by: SortOrder) -> List[Dict[str, Any]]:
        """构建排序配置"""
        
        sort_config = []
        
        if sort_by == SortOrder.SIMILARITY_DESC:
            sort_config.append({"_score": {"order": "desc"}})
        elif sort_by == SortOrder.SIMILARITY_ASC:
            sort_config.append({"_score": {"order": "asc"}})
        elif sort_by == SortOrder.TIME_DESC:
            sort_config.append({"question_time": {"order": "desc"}})
        elif sort_by == SortOrder.TIME_ASC:
            sort_config.append({"question_time": {"order": "asc"}})
        
        # 添加默认排序
        sort_config.append({"_id": {"order": "desc"}})
        
        return sort_config
    
    async def clear_memories(
        self,
        agent_id: Optional[str] = None,
        user_id: Optional[str] = None,
        category: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> int:
        """
        清除记忆
        
        Args:
            agent_id: 智能体ID
            user_id: 用户ID
            category: 记忆类别
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            删除的记录数
        """
        try:
            if not self._is_connected:
                await self.initialize()
            
            # 构建删除查询
            query = self._build_delete_query(
                agent_id=agent_id,
                user_id=user_id,
                category=category,
                start_time=start_time,
                end_time=end_time
            )
            
            # 执行删除
            response = await self.client.delete_by_query(
                index=self.index_name,
                body={"query": query},
                conflicts="proceed"
            )
            
            deleted_count = response['deleted']
            logger.info(f"清除记忆完成，删除 {deleted_count} 条记录")
            return deleted_count
            
        except Exception as e:
            logger.error(f"清除记忆失败: {e}")
            raise
    
    def _build_delete_query(
        self,
        agent_id: Optional[str] = None,
        user_id: Optional[str] = None,
        category: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """构建删除查询"""
        
        # 构建过滤条件（删除操作不需要计算分数）
        filter_clauses = []
        
        # 添加精确匹配过滤条件
        if agent_id:
            filter_clauses.append({"term": {"agent_id": agent_id}})
        
        if user_id:
            filter_clauses.append({"term": {"user_id": user_id}})
        
        if category:
            filter_clauses.append({"term": {"category": category}})
        
        # 添加时间范围过滤
        if start_time or end_time:
            time_range = {}
            if start_time:
                time_range["gte"] = start_time.isoformat()
            if end_time:
                time_range["lte"] = end_time.isoformat()
            
            filter_clauses.append({
                "range": {
                    "question_time": time_range
                }
            })
        
        # 构建查询
        if filter_clauses:
            query = {
                "bool": {
                    "filter": filter_clauses
                }
            }
        else:
            query = {"match_all": {}}
        
        return query
    
    async def get_memory_by_id(self, doc_id: str) -> Optional[MemoryItem]:
        """
        根据ID获取记忆
        
        Args:
            doc_id: 文档ID
            
        Returns:
            记忆项
        """
        try:
            if not self._is_connected:
                await self.initialize()
            
            response = await self.client.get(
                index=self.index_name,
                id=doc_id
            )
            
            source = response['_source']
            
            memory_item = MemoryItem(
                id=response['_id'],
                agent_id=source['agent_id'],
                user_id=source['user_id'],
                category=source['category'],
                user_question=source['user_question'],
                question_reply=source.get('question_reply'),
                question_time=datetime.fromisoformat(source['question_time']),
                similarity_score=None,
                created_at=datetime.fromisoformat(source['created_at']),
                updated_at=datetime.fromisoformat(source['updated_at'])
            )
            
            return memory_item
            
        except NotFoundError:
            logger.warning(f"记忆文档未找到: {doc_id}")
            return None
        except Exception as e:
            logger.error(f"获取记忆文档失败: {e}")
            raise
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            if not self.client:
                return {"status": "disconnected", "details": "客户端未初始化"}
            
            # 检查集群健康状态
            health = await self.client.cluster.health()
            
            # 检查索引状态
            index_exists = await self.client.indices.exists(index=self.index_name)
            
            return {
                "status": "connected" if self._is_connected else "disconnected",
                "cluster_status": health['status'],
                "index_exists": index_exists,
                "details": health
            }
            
        except Exception as e:
            logger.error(f"Elasticsearch健康检查失败: {e}")
            return {"status": "error", "details": str(e)}
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self._is_connected
    
    async def get_category_statistics(self, agent_id: str, user_id: str) -> Dict[str, int]:
        """
        获取各类别记忆统计
        
        Args:
            agent_id: 智能体ID
            user_id: 用户ID
            
        Returns:
            类别统计字典，格式为 {category: count}
        """
        try:
            if not self.client:
                raise RuntimeError("Elasticsearch客户端未初始化")
            
            # 构建查询条件 - 使用filter优化性能
            query = {
                "bool": {
                    "filter": [
                        {"term": {"agent_id.keyword": agent_id}},
                        {"term": {"user_id.keyword": user_id}}
                    ]
                }
            }
            
            # 构建聚合查询
            aggs = {
                "categories": {
                    "terms": {
                        "field": "category.keyword",
                        "size": 100  # 默认类别数量最多统计100
                    }
                }
            }
            
            # 执行聚合查询
            response = await self.client.search(
                index=self.index_name,
                body={
                    "query": query,
                    "aggs": aggs,
                    "size": 0  # 不需要返回具体文档，只要聚合结果
                }
            )
            
            # 解析聚合结果
            category_stats = {}
            if "aggregations" in response and "categories" in response["aggregations"]:
                buckets = response["aggregations"]["categories"]["buckets"]
                for bucket in buckets:
                    category_stats[bucket["key"]] = bucket["doc_count"]
            
            logger.info(f"类别统计查询完成: agent_id={agent_id}, user_id={user_id}, 结果={category_stats}")
            return category_stats
            
        except Exception as e:
            logger.error(f"获取类别统计失败: {e}")
            raise
    
    async def close(self):
        """关闭客户端"""
        try:
            if self.client:
                await self.client.close()
            self._is_connected = False
            logger.info("Elasticsearch客户端已关闭")
        except Exception as e:
            logger.error(f"关闭Elasticsearch客户端失败: {e}")


# 全局Elasticsearch服务实例
es_service = ElasticsearchService() 