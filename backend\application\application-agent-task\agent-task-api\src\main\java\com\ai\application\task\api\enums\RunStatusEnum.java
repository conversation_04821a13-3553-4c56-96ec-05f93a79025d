package com.ai.application.task.api.enums;

import lombok.Getter;

/**
 * 执行状态:1-等待,2-执行中,3-成功,4-失败,5-超时,6-中断
 */
@Getter
public enum RunStatusEnum {
    WAIT(1, "等待"),
    RUN(2, "执行中"),
    SUCCESS(3, "成功"),
    FAIL(4, "失败"),
    TIMEOUT(5, "超时"),
    STOP(6, "中断"),
    ;

    private final Integer code;
    private final String desc;

    RunStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
