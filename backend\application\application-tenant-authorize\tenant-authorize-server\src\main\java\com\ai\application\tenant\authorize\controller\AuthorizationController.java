package com.ai.application.tenant.authorize.controller;

import com.ai.application.tenant.authorize.api.dto.UserAuthorizationReqDTO;
import com.ai.application.tenant.authorize.api.dto.query.UserAuthorizationQueryDTO;
import com.ai.application.tenant.authorize.vo.UserAuthorizationResultVO;
import com.ai.application.tenant.authorize.service.IAuthorizationService;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "授权通用接口", description = "授权通用接口")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1/authorize")
@AllArgsConstructor
public class AuthorizationController {
    private final IAuthorizationService authorizationService;

    @Operation(summary = "资源授权列表查询", description = "资源授权列表查询")
    @ApiResponse(responseCode = "0", description = "成功",
            content = @Content(schema = @Schema(implementation = UserAuthorizationResultVO.class)))
    @PostMapping("/list")
    public ResultVo<List<UserAuthorizationResultVO>> list(@Validated @RequestBody UserAuthorizationQueryDTO queryDto){
        return ResultVo.data(authorizationService.list(queryDto));
    }

    @Operation(summary = "资源授权", description = "资源授权")
    @PostMapping("")
    public ResultVo<List<UserAuthorizationResultVO>> authorization(@Validated @RequestBody UserAuthorizationReqDTO dto){
        authorizationService.authorization(dto);
        return ResultVo.success("授权成功");
    }
}
