package com.ai.application.agent.run.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 记忆搜索请求DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "MemorySearchRequestDTO")
public class MemorySearchRequestDTO {

    /**
     * 智能体ID
     */
    @JsonProperty("agent_id")
    @Schema(description = "智能体ID")
    private String agentId;

    /**
     * 用户ID
     */
    @JsonProperty("user_id")
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 记忆类别
     */
    @JsonProperty("category")
    @Schema(description = "记忆类别")
    private String category;

    /**
     * 用户问题（用于向量检索）
     */
    @JsonProperty("user_question")
    @Schema(description = "用户问题（用于向量检索）")
    private String userQuestion;

    /**
     * 相似度阈值
     */
    @JsonProperty("similarity_threshold")
    @Schema(description = "相似度阈值")
    private Double similarityThreshold;

    /**
     * 开始时间
     */
    @JsonProperty("start_time")
    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonProperty("end_time")
    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    /**
     * 排序方式
     */
    @JsonProperty("sort_by")
    @Schema(description = "排序方式")
    private String sortBy;

    /**
     * 返回结果数量
     */
    @JsonProperty("limit")
    @Schema(description = "返回结果数量")
    private Integer limit;
}
