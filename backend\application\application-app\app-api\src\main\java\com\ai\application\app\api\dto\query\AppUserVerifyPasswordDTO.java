package com.ai.application.app.api.dto.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 应用用户表 查询条件
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(name = "校验用户密码DTO")
public class AppUserVerifyPasswordDTO {
    /**
     * appId
     */
    @Schema(description = "appId")
    private Integer appId;

    /**
     * 租户sn
     */
    @Schema(description = "租户sn")
    private String tenantSn;

    /**
     * 登录名
     */
    @Schema(description = "登录名")
    private String loginName;

    /**
     * 密码
     */
    @Schema(description = "密码")
    private String password;
}