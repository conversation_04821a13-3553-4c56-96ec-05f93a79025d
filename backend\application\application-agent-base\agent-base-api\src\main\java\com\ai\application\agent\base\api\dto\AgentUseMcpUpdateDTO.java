package com.ai.application.agent.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 智能体MCP工具关联表
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Data
@Schema(name = "智能体MCP工具关联表DTO")
public class AgentUseMcpUpdateDTO {
    /**
     * 关联id
     */
    @Schema(description = "关联id")
    private Integer amcId;

    /**
     * 关联状态:1-启用,0-禁用,-1-删除
     */
    @Schema(description = "关联状态:1-启用,0-禁用,-1-删除")
    private Integer amcStatus;

    /**
     * 智能体id
     */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
     * 智能体版本id
     */
    @Schema(description = "智能体版本id")
    private Integer versionId;

    @Schema(description = "mcp扩展信息")
    private String mcpExtend;

    /**
     * MCP工具id
     */
    @Schema(description = "MCP工具id")
    private Integer mcpToolId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}