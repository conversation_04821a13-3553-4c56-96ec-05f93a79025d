package com.ai.application.knowledge.table.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class TableListVo {


    @Schema(description = "智能表格id")
    private Integer tableId;

    @Schema(description = "智能表格sn")
    private String tableSn;

    @Schema(description = "智能表格名称")
    private String tableName;

    @Schema(description = "智能表格描述")
    private String tableDesc;

    @Schema(description = "智能表格类型:10-公用数据表,20-内嵌临时表")
    private Integer tableType;

    @Schema(description = "状态:1-有效,0-无效,-1-删除")
    private Integer tableStatus;

    @Schema(description = "关联智能体数量")
    private Integer tableAgents;

    @Schema(description = "租户id")
    private Integer tenantId;

    @Schema(description = "创建用户id")
    private Integer createUserId;

    @Schema(description = "更新用户id")
    private Integer updateUserId;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "创建人姓名")
    private String creator;

    @Schema(description = "更新人姓名")
    private String updater;
}
