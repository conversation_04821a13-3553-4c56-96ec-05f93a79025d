package com.ai.application.agent.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

@Data
@Schema(name = "智能体统计DTO")
public class AgentStatDTO {
    @Schema(description = "查询天数")
    private Integer days = 7;

    //内部用变量
    @Schema(description = "租户id",hidden = true)
    private Integer tenantId;
    @Schema(description = "用户id",hidden = true)
    private Integer userId;
    @Schema(description = "检索开始日期",hidden = true)
    private LocalDate startDate;
    @Schema(description = "检索结束日期",hidden = true)
    private LocalDate endDate;
    @Schema(description = "会话状态",hidden = true)
    private Integer sessionStatus;
}
