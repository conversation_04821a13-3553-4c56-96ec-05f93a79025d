package com.ai.develop.generation.service;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.po.TableFill;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import java.util.*;

@SuppressWarnings("all")
public class GeneratorSystem {
    /**
     * 需要生成的表名后缀，多个用逗号隔开 ,,,,,,,,,,,,,,,,,,,,
     */
    public static String TABLE_SUFFIX = "agent_use_dict,agent_use_kb,agent_use_mcp,agent_use_table,agent_use_tool,agent_use_workflow";

    // 服务包名
    public static String appName = "agent.base";

    /**
     * 指定具体项目模块
     */
    public static String AUTHOR_NAME = "thor";

    /**
     * 数据源信息
     */
    public static String URL = "*******************************************************************************************************************************************************************************************";
    public static String DRIVER_NAME = "com.mysql.cj.jdbc.Driver";
    public static String USERNAME = "root";
    public static String PASSWORD = "LanmaAdmin.27";

    /**
     * 包设置
     */

    public static String PARENT_PACKAGE = "com.ai.application." + appName;
    public static String MAPPER_PATH = "/src/main/resources/mapper/";
    public static String AppPath = "/develop/code-generation/target/" + appName;

    /**
     * DTO/VO/QueryDTO 包路径
     */
    public static String PARENT_ENTITY_PATH = "\\src\\main\\java\\com\\api\\entity\\";
    public static String PARENT_DTO_PATH = "\\src\\main\\java\\com\\api\\dto\\";
    public static String PARENT_VO_PATH = "\\src\\main\\java\\com\\api\\vo\\";
    public static String PARENT_MAPPING_PATH = "\\src\\main\\java\\com\\api\\mapstruct\\";
    public static String PARENT_Query_DTO_PATH = "\\src\\main\\java\\com\\api\\dto\\query\\";

    /**
     * DTO/VO/QueryDTO 包名
     */
    public static String PARENT_ENTITY_PACKAGE = PARENT_PACKAGE + ".api.entity";
    public static String PARENT_DTO_PACKAGE = PARENT_PACKAGE + ".api.dto";
    public static String PARENT_VO_PACKAGE = PARENT_PACKAGE + ".api.vo";
    public static String PARENT_MAPPING_PACKAGE = PARENT_PACKAGE + ".api.mapstruct";
    public static String PARENT_Query_DTO_PACKAGE = PARENT_PACKAGE + ".api.dto.query";

    /**
     * DTO/VO/QueryDTO 自定义模板路径
     */
    public static String TEMPLATE_CONTROLLER_PATH = "/vm/java/controller.java";
    public static String TEMPLATE_SERVICE_PATH = "/vm/java/service.java";
    public static String TEMPLATE_SERVICEIMPL_PATH = "/vm/java/serviceImpl.java";
    public static String TEMPLATE_MAPPER_PATH = "/vm/java/mapper.java";
    public static String TEMPLATE_MAPPER_XML_PATH = "/vm/xml/mapper.xml.vm";

    /**
     * DTO/VO/QueryDTO 自定义模板路径
     */
    public static String TEMPLATE_ENTITY_PATH = "/vm/java/entity.java.vm";
    public static String TEMPLATE_DTO_PATH = "/vm/java/dto.java.vm";
    public static String TEMPLATE_VO_PATH = "/vm/java/vo.java.vm";
    public static String TEMPLATE_MAPPING_PATH = "/vm/java/mapstruct.java.vm";
    public static String TEMPLATE_QUERY_DTO_PATH = "/vm/java/queryDto.java.vm";

    /**
     * controller 层请求路径前缀
     */
    public static String REQUEST_PATH_PREFIX = "/";

    /**
     * 表前缀,生成实体类时，会自动去除表前缀，如：table: tb_task, class: Task
     */
    public static String TABLE_PREFIX_ENTITY = "";

    /**
     * 需要生成的表名前缀,若为空，则默认是需要生成的表名是 TABLE_SUFFIX
     */
    public static String TABLE_PREFIX = "";

    /**
     * DTO 忽略的字段
     */
    public static String DTO_IGNORE_FIELD = "";

    public static void main(String[] args) {
        if ("".equals(TABLE_SUFFIX)) {
            System.out.println("----->>>需要生成的表名为空");
            return;
        }

        //表拼接
        String[] tables;
        if ("".equals(TABLE_PREFIX)) {
            tables = TABLE_SUFFIX.split(",");
        } else {
            List<String> tableList = Arrays.asList(TABLE_SUFFIX.split(","));
            tables = tableList.stream().map(GeneratorSystem::apply).toArray(String[]::new);

        }
        System.out.println("表：");
        Arrays.stream(tables).forEach(System.out::println);

        // 代码生成器
        AutoGenerator mpg = new AutoGenerator();

        // 全局配置
        GlobalConfig gc = new GlobalConfig();
        String projectPath = System.getProperty("user.dir") + AppPath;
        System.out.println("项目路径：" + projectPath);
        gc.setOutputDir(projectPath + "/src/main/java");//设置代码生成路径

        gc.setFileOverride(true);//是否覆盖以前文件
        gc.setOpen(false);//是否打开生成目录
        gc.setAuthor(AUTHOR_NAME);//设置项目作者名称
        // gc.setIdType(IdType.AUTO);//设置主键策略
        gc.setIdType(IdType.ASSIGN_ID);//设置主键策略
        gc.setBaseResultMap(true);//生成基本ResultMap
        gc.setBaseColumnList(true);//生成基本ColumnList
        gc.setServiceName("I%sService");//去掉服务默认前缀
        gc.setDateType(DateType.ONLY_DATE);//设置时间类型
//        gc.setSwagger2(true);
        mpg.setGlobalConfig(gc);

        // 数据源配置
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setUrl(URL);
        dsc.setDriverName(DRIVER_NAME);
        dsc.setUsername(USERNAME);
        dsc.setPassword(PASSWORD);
        mpg.setDataSource(dsc);

        // 包配置
        PackageConfig pc = new PackageConfig();
        pc.setParent(PARENT_PACKAGE);
        pc.setMapper("mapper");

        //不生成实体类
//        pc.setEntity("model");
        pc.setService("service");
        pc.setServiceImpl("service.impl");
        pc.setController("controller");
        mpg.setPackageInfo(pc);

        //自定义配置，配置自定义属性注入
        InjectionConfig cfg = new InjectionConfig() {
            //在.ftl(或者是.vm)模板中，通过${cfg.abc}获取属性
            @Override
            public void initMap() {
                //自定义生成模板参数
                Map<String, Object> map = new HashMap<>();
                map.put("abc", this.getConfig().getGlobalConfig().getAuthor() + "-mp");

                // ------------------- 添加主键信息 -------------------
                List<TableInfo> tableInfoList = this.getConfig().getTableInfoList();
                if (!tableInfoList.isEmpty()) {
                    TableInfo tableInfo = tableInfoList.get(0); // 获取第一个表的信息
                    if (tableInfo.isHavePrimaryKey()) {
                        // 注入主键字段名（数据库字段名）
                        map.put("primaryKeyColumnName", underlineToCamel(tableInfo.getFields().get(0).getName()));
                    } else {
                        map.put("primaryKeyColumnName", "无主键");
                    }
                }

                //模板中获取值：${cfg.requestPathPrefix}
                map.put("requestPathPrefix", REQUEST_PATH_PREFIX);
                map.put("entityPackage", PARENT_ENTITY_PACKAGE);
                map.put("dtoPackage", PARENT_DTO_PACKAGE);
                map.put("voPackage", PARENT_VO_PACKAGE);
                map.put("mappingPackage", PARENT_MAPPING_PACKAGE);
                map.put("queryDtoPackage", PARENT_Query_DTO_PACKAGE);
                map.put("dtoIgnoreFields", DTO_IGNORE_FIELD);
                this.setMap(map);
            }
        };

        // 如果模板引擎是 freemarker
        // String templatePath = "/templates/mapper.xml.vm.ftl";
        // 如果模板引擎是 velocity：
        // String templatePath = "/templates/mapper.xml.vm";

        //自定义输出配置
        List<FileOutConfig> focList = new ArrayList<>();
        // 自定义配置会被优先输出
        focList.add(new FileOutConfig(TEMPLATE_MAPPER_XML_PATH) {
            @Override
            public String outputFile(TableInfo tableInfo) {
                // 自定义输出文件名 ， 如果你 Entity 设置了前后缀、此处注意 xml 的名称会跟着发生变化！！
                return projectPath + "/" + MAPPER_PATH + tableInfo.getEntityName() + "Mapper" + StringPool.DOT_XML;
            }
        });

        //自定义生成类DTO/VO/QueryDOT
        focList.add(new FileOutConfig(TEMPLATE_ENTITY_PATH) {
            @Override
            public String outputFile(TableInfo tableInfo) {
                // 自定义输出文件名 ， 如果你 Entity 设置了前后缀、此处注意 xml 的名称会跟着发生变化！！
                return projectPath + "/" + PARENT_ENTITY_PATH + tableInfo.getEntityName() + StringPool.DOT_JAVA;
            }
        });

        focList.add(new FileOutConfig(TEMPLATE_DTO_PATH) {
            @Override
            public String outputFile(TableInfo tableInfo) {
                // 自定义输出文件名 ， 如果你 Entity 设置了前后缀、此处注意 xml 的名称会跟着发生变化！！
                return projectPath + "/" + PARENT_DTO_PATH + tableInfo.getEntityName() + "DTO" + StringPool.DOT_JAVA;
            }
        });

        focList.add(new FileOutConfig(TEMPLATE_VO_PATH) {
            @Override
            public String outputFile(TableInfo tableInfo) {
                // 自定义输出文件名 ， 如果你 Entity 设置了前后缀、此处注意 xml 的名称会跟着发生变化！！
                return projectPath + "/" + PARENT_VO_PATH + tableInfo.getEntityName() + "VO" + StringPool.DOT_JAVA;
            }
        });

        focList.add(new FileOutConfig(TEMPLATE_MAPPING_PATH) {
            @Override
            public String outputFile(TableInfo tableInfo) {
                // 自定义输出文件名 ， 如果你 Entity 设置了前后缀、此处注意 xml 的名称会跟着发生变化！！
                return projectPath + "/" + PARENT_MAPPING_PATH + tableInfo.getEntityName() + "Mapstruct" + StringPool.DOT_JAVA;
            }
        });

        focList.add(new FileOutConfig(TEMPLATE_QUERY_DTO_PATH) {
            @Override
            public String outputFile(TableInfo tableInfo) {
                // 自定义输出文件名 ， 如果你 Entity 设置了前后缀、此处注意 xml 的名称会跟着发生变化！！
                return projectPath + "/" + PARENT_Query_DTO_PATH + tableInfo.getEntityName() + "QueryDTO" + StringPool.DOT_JAVA;
            }
        });
        cfg.setFileOutConfigList(focList);
        mpg.setCfg(cfg);

        // 自定义模板
        TemplateConfig templateConfig = new TemplateConfig();
        //控制:不生java下成xml
        templateConfig.setXml(null);
        templateConfig.setEntity(null);

        // 配置自定义输出模板
        //指定自定义模板路径，注意不要带上.ftl/.vm, 会根据使用的模板引擎自动识别
//        templateConfig.setEntity("templates/entity2.java");
        templateConfig.setController(TEMPLATE_CONTROLLER_PATH);
        templateConfig.setService(TEMPLATE_SERVICE_PATH);
        templateConfig.setServiceImpl(TEMPLATE_SERVICEIMPL_PATH);
        templateConfig.setMapper(TEMPLATE_MAPPER_PATH);
        mpg.setTemplate(templateConfig);

        // 策略配置
        StrategyConfig sc = new StrategyConfig();
        sc.setNaming(NamingStrategy.underline_to_camel);
        sc.setColumnNaming(NamingStrategy.underline_to_camel);
        sc.setEntityLombokModel(true);//自动lombok
        sc.setRestControllerStyle(true);
        sc.setControllerMappingHyphenStyle(true);
//        sc.setLogicDeleteFieldName("delFlag");//设置逻辑删除

        //设置自动填充配置
        TableFill gmt_create = new TableFill("create_time", FieldFill.INSERT);
        TableFill gmt_modified = new TableFill("update_time", FieldFill.INSERT_UPDATE);
        ArrayList<TableFill> tableFills = new ArrayList<>();
        tableFills.add(gmt_create);
        tableFills.add(gmt_modified);
        sc.setTableFillList(tableFills);

        //乐观锁
        sc.setVersionFieldName("version");
        sc.setRestControllerStyle(true);//驼峰命名

        sc.setTablePrefix(TABLE_PREFIX_ENTITY); //设置表名前缀
        sc.setInclude(tables);//表名，多个英文逗号分割
        mpg.setStrategy(sc);

        // 生成代码
        mpg.execute();
    }

    // 工具方法：将下划线命名转为小驼峰
    private static String underlineToCamel(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        StringBuilder result = new StringBuilder();
        String[] parts = input.toLowerCase().split("_");

        for (int i = 0; i < parts.length; i++) {
            String part = parts[i];
            result.append(part.substring(0, 1).toUpperCase());
            result.append(part.substring(1));
        }
        return result.toString();
    }

    private static String apply(String t) {
        return TABLE_PREFIX + t;
    }
}
