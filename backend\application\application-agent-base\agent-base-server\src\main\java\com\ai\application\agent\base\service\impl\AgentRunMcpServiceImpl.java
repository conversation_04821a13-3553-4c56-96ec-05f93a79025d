package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.dto.AgentRunMcpListDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ai.application.agent.base.mapper.AgentRunMcpMapper;
import com.ai.application.agent.base.api.entity.AgentRunMcp;
import com.ai.application.agent.base.service.IAgentRunMcpService;
import com.ai.application.agent.base.api.dto.AgentRunMcpDTO;
import com.ai.application.agent.base.api.vo.AgentRunMcpVO;
import com.ai.application.agent.base.api.mapstruct.AgentRunMcpMapstruct;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Date;

/**
 * 智能体工具执行mcp工具记录表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
public class AgentRunMcpServiceImpl implements IAgentRunMcpService{

    @Resource
    private AgentRunMcpMapper agentRunMcpMapper;

    @Resource
    private AgentRunMcpMapstruct agentRunMcpMapstruct;

    @Transactional(readOnly = true)
    @Override
    public List<AgentRunMcpVO> list(AgentRunMcpListDTO queryDto) {
        LambdaQueryWrapper<AgentRunMcp> queryWrapper = this.buildQuery(queryDto);
        return agentRunMcpMapstruct.toVoList(this.agentRunMcpMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(AgentRunMcpDTO dto) {
        dto.setRunId(null);
        AgentRunMcp entity = agentRunMcpMapstruct.toEntity(dto);
        entity.setCreateTime(new Date());

        agentRunMcpMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AgentRunMcpDTO dto) {
        BusinessAssertUtil.notNull(dto.getRunId(), "RunId不能为空");

        // TODO 唯一性字段校验
        AgentRunMcp entity = agentRunMcpMapper.selectById(dto.getRunId());
        BusinessAssertUtil.notNull(entity, "找不到RunId为 " + dto.getRunId() + " 的记录");

        AgentRunMcp entityList = agentRunMcpMapstruct.toEntity(dto);
        entityList.setUpdateTime(new Date());
        agentRunMcpMapper.updateById(entityList);
    }

    @Transactional(readOnly = true)
    @Override
    public AgentRunMcpVO get(Integer id) {
        BusinessAssertUtil.notNull(id, "RunId不能为空");

        AgentRunMcp entity = agentRunMcpMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到RunId为 " + id + " 的记录");

        return agentRunMcpMapstruct.toVo(entity);
    }

    private LambdaQueryWrapper<AgentRunMcp> buildQuery(AgentRunMcpListDTO queryDto) {
        LambdaQueryWrapper<AgentRunMcp> queryWrapper = new LambdaQueryWrapper<>();
        return queryWrapper;
    }
}