package com.ai.application.base.log.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class MailConfigBO {
    @Schema(description = "邮件服务器地址")
    private String host;

    @Schema(description = "邮件服务器端口")
    private String port;

    @Schema(description = "邮箱地址")
    private String email;

    @Schema(description = "发件人显示名")
    private String showName;

    @Schema(description = "邮件服务器用户名")
    private String username;

    @Schema(description = "邮件服务器密码")
    private String password;



}
