package com.ai.application.base.log.api.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

/**
 * 通知模板配置表
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@Schema(name = "通知模板配置表DTO")
public class NotificationTemplateDTO {
    /**
     * 通知模板id
     */
    @Schema(description = "通知模板id")
    private Integer ntplId;

    /**
     * 分类:10-异常,20-一般通知,30-成功
     */
    @Schema(description = "分类:10-异常,20-一般通知,30-成功")
    private Integer ntplType;

    /**
     * 模板名称
     */
    @Schema(description = "模板名称")
    private String ntplName;

    /**
     * 模板内容
     */
    @Schema(description = "模板内容")
    private String ntplContent;

    /**
     * 模板状态:1-启用,0-禁用,-1-删除
     */
    @Schema(description = "模板状态:1-启用,0-禁用,-1-删除")
    private Integer ntplStatus;

    /**
     * 模板配置参数
     */
    @Schema(description = "模板配置参数")
    private String ntplConfig;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Integer tenantId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}