package com.ai.application.app.service.impl;
import com.ai.application.app.api.dto.AppRoleDTO;
import com.ai.application.app.api.dto.query.AppRoleQueryDTO;
import com.ai.application.app.api.entity.AppRole;
import com.ai.application.app.api.entity.AppRoleFunction;
import com.ai.application.app.api.mapstruct.AppRoleMapstruct;
import com.ai.application.app.api.vo.AppRoleFunctionVO;
import com.ai.application.app.api.vo.AppRoleListVO;
import com.ai.application.app.api.vo.AppRoleSimpleVO;
import com.ai.application.app.api.vo.AppRoleVO;
import com.ai.application.app.mapper.AppRoleFunctionMapper;
import com.ai.application.app.mapper.AppRoleMapper;
import com.ai.application.app.service.IAppRoleService;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.util.BusinessAssertUtil;
import com.ai.framework.core.util.validator.AssertUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 应用角色表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
@AllArgsConstructor
public class AppRoleServiceImpl implements IAppRoleService {

    private final AppRoleMapper appRoleMapper;
    private final AppRoleMapstruct appRoleMapstruct;
    private final AppRoleFunctionMapper appRoleFunctionMapper;

    @Transactional(readOnly = true)
    @Override
    public PageInfo<AppRoleVO> page(AppRoleQueryDTO queryDto) {
        QueryWrapper<AppRole> queryWrapper = this.buildQuery(queryDto);
        Page<AppRole> page = new Page<>(queryDto.getPageNo(), queryDto.getPageSize());
        Page<AppRole> result = this.appRoleMapper.selectPage(page, queryWrapper);
        return PageInfo.of(appRoleMapstruct.toVoList(result.getRecords()));
    }

    @Transactional(readOnly = true)
    @Override
    public List<AppRoleVO> list(AppRoleQueryDTO queryDto) {
        QueryWrapper<AppRole> queryWrapper = this.buildQuery(queryDto);
        return appRoleMapstruct.toVoList(this.appRoleMapper.selectList(queryWrapper));
    }

    @Transactional(readOnly = true)
    @Override
    public List<AppRoleSimpleVO> simpleList(AppRoleQueryDTO queryDto) {
        QueryWrapper<AppRole> queryWrapper = this.buildQuery(queryDto);
        return appRoleMapstruct.toSimpleVoList(this.appRoleMapper.selectList(queryWrapper));
    }

    @Transactional(readOnly = true)
    @Override
    public List<AppRoleListVO> queryRoles() {
        QueryWrapper<AppRole> queryWrapper = this.buildQuery(new AppRoleQueryDTO());
        List<AppRole> appRoles = this.appRoleMapper.selectList(queryWrapper);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(appRoles)){
            return Lists.newArrayList();
        }
        List<AppRoleListVO> roleVoList = appRoleMapstruct.toRoleVoList(appRoles);
        //根据角色查询功能
        List<Integer> roleIds = appRoles.stream().map(AppRole::getRoleId).toList();
        List<AppRoleFunctionVO> appRoleFunctions = appRoleFunctionMapper.queryFunctionListByRoleIds(roleIds);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(appRoleFunctions)){
            return roleVoList;
        }

        roleVoList.forEach(roleVo -> {
            List<AppRoleFunctionVO> list = appRoleFunctions.stream().filter(a -> a.getRoleId().equals(roleVo.getRoleId())).toList();
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(list)){
                roleVo.setFunctionIds(list.stream().map(AppRoleFunctionVO::getFunId).toList());
            }
        });

        return roleVoList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(AppRoleDTO dto) {
        // TODO 唯一性字段校验
        dto.setRoleId(null);
        AppRole entity = appRoleMapstruct.toEntity(dto);
        entity.setCreateTime(new Date());
        appRoleMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AppRoleDTO dto) {
        AssertUtil.isNotNull(dto.getRoleId(), "id不能为空");
        // TODO 唯一性字段校验
        AppRole entity = appRoleMapper.selectById(dto.getRoleId());
        BusinessAssertUtil.notNull(entity, "找不到id为 " + dto.getRoleId() + " 的记录");
        AppRole entitys = appRoleMapstruct.toEntity(dto);
        entitys.setUpdateTime(new Date());
        appRoleMapper.updateById(entitys);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Set<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            appRoleMapper.deleteBatchIds(ids);
        }
    }

    @Transactional(readOnly = true)
    @Override
    public AppRoleVO get(Long id) {
        BusinessAssertUtil.notNull(id, "id不能为空");
        AppRole entity = appRoleMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到id为 " + id + " 的记录");
        return appRoleMapstruct.toVo(entity);
    }

    private QueryWrapper<AppRole> buildQuery(AppRoleQueryDTO queryDto) {
        QueryWrapper<AppRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AppRole::getTenantId, UserContext.getTenantId());
        queryWrapper.lambda().eq(AppRole::getRoleStatus, 1);
        queryWrapper.lambda().eq(StringUtils.isNoneBlank(queryDto.getRoleCode()),AppRole::getRoleCode, queryDto.getRoleCode());
        queryWrapper.lambda().eq(org.apache.commons.collections4.CollectionUtils.isNotEmpty(queryDto.getRoleIds()),AppRole::getRoleId, queryDto.getRoleIds());
        return queryWrapper;
    }
}