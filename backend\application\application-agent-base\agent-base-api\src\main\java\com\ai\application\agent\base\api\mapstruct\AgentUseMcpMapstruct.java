package com.ai.application.agent.base.api.mapstruct;
import com.ai.application.agent.base.api.dto.AgentUseMcpUpdateDTO;
import com.ai.application.agent.base.api.entity.AgentUseMcp;
import com.ai.application.agent.base.api.dto.AgentUseMcpAddDTO;
import com.ai.application.agent.base.api.vo.AgentUseMcpListVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 智能体MCP工具关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-07
 */

@Mapper(componentModel = "spring")
public interface AgentUseMcpMapstruct {

    AgentUseMcp toAddEntity(AgentUseMcpAddDTO dto);
    AgentUseMcp toUpdateEntity(AgentUseMcpUpdateDTO dto);
    AgentUseMcpListVO toVo(AgentUseMcp entity);
    List<AgentUseMcpListVO> toVoList(List<AgentUseMcp> entities);
}
