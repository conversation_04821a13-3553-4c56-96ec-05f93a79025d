package ${package.Controller};

import com.github.pagehelper.PageInfo;
import ${package.Service}.${table.serviceName};
import ${cfg.dtoPackage}.${entity}DTO;
import ${cfg.queryDtoPackage}.${entity}QueryDTO;
import ${cfg.voPackage}.${entity}VO;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * $!{table.comment}-前端控制器
 *
 * <AUTHOR>
 * @since ${date}
 */
#set($pathPrefix = "${cfg.requestPathPrefix}${table.entityPath}")
@Tag(name = "$!{table.comment}", description = "$!{table.comment}-相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("$pathPrefix")
public class ${table.controllerName} {

    @Resource
    private ${table.serviceName}  ${table.entityPath}Service;

    /**
     * 分页查询
     *
     * @param queryDto
     * @return
     */
    @Operation(summary = "$!{table.comment}-分页查询", description = "查询所有$!{table.comment} 信息")
    @PostMapping("/page")
    public ResultVo<PageInfo<${entity}VO>> page(@Validated @RequestBody ${entity}QueryDTO queryDto){
        return ResultVo.data(${table.entityPath}Service.page(queryDto));
    }

    /**
     * 保存
     *
     * @param dto
     * @return
     */
    @Operation(summary = "$!{table.comment}-新增")
    @PostMapping("/add")
    public ResultVo<Void> add(@Validated @RequestBody ${entity}DTO dto){
        ${table.entityPath}Service.add(dto);
        return ResultVo.success("保存成功");
    }
    
    /**
     * 修改
     *
     * @param dto
     * @return
     */
    @Operation(summary = "$!{table.comment}-修改")
    @PostMapping(value = "/update")
    public ResultVo<Void> update(@Validated @RequestBody ${entity}DTO dto){
        ${table.entityPath}Service.update(dto);
        return ResultVo.success("修改成功");
    }
}