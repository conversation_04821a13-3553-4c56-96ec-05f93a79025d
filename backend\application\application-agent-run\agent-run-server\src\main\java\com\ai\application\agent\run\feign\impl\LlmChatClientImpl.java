package com.ai.application.agent.run.feign.impl;

import com.ai.application.agent.run.dto.ProcessChatDTO;
import com.ai.application.agent.run.feign.ILlmChatClient;
import com.ai.application.agent.run.service.ILlmChatService;
import com.ai.framework.core.vo.ResultVo;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

/**
 * LLM 聊天客户端实现类
 */
@RestController
@AllArgsConstructor
public class LlmChatClientImpl implements ILlmChatClient {

    private final ILlmChatService llmChatService;

    @Override
    public ResultVo<String> chat(ProcessChatDTO dto, String authorization) {
        return llmChatService.chat(dto);
    }
}
