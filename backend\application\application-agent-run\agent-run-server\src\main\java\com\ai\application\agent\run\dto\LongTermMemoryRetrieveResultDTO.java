package com.ai.application.agent.run.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 长期记忆检索结果DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "LongTermMemoryRetrieveResultDTO")
public class LongTermMemoryRetrieveResultDTO {

    /**
     * 是否成功
     */
    @Schema(description = "是否成功")
    private Boolean success;

    /**
     * 检索结果列表JSON（匹配backend2的jsonList输出）
     */
    @Schema(description = "检索结果列表JSON")
    private List<String> jsonList;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 记忆总数
     */
    @Schema(description = "记忆总数")
    private Integer totalCount;

    /**
     * 返回的记忆数量
     */
    @Schema(description = "返回的记忆数量")
    private Integer returnCount;

    /**
     * 详细记忆列表（原始数据）
     */
    @Schema(description = "详细记忆列表")
    private List<LongTermMemoryItemDTO> memoryDetails;

    /**
     * 长期记忆项DTO
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class LongTermMemoryItemDTO {
        /**
         * 记忆内容（Map格式）
         */
        @Schema(description = "记忆内容")
        private Map<String, Object> content;

        /**
         * 记忆时间戳
         */
        @Schema(description = "记忆时间戳")
        private String timestamp;

        /**
         * 记忆时间（添加到content中）
         */
        @Schema(description = "记忆时间")
        private String memoryTime;
    }
}
