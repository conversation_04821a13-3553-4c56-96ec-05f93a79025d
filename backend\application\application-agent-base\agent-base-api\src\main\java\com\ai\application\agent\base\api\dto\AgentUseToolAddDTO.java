package com.ai.application.agent.base.api.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 智能体使用工具表
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Data
@Schema(name = "智能体使用工具表DTO")
public class AgentUseToolAddDTO {

    /**
     * 状态 0失效 1有效
     */
    @Schema(description = "状态 0失效 1有效")
    private Integer atlStatus;

    /**
     * 智能体id
     */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
     * 智能体版本id
     */
    @Schema(description = "智能体版本id")
    private Integer versionId;

    /**
     * 工具id
     */
    @Schema(description = "工具id")
    private Integer toolId;

    /**
     * 额外属性
     */
    @Schema(description = "额外属性")
    private String toolExtend;
}