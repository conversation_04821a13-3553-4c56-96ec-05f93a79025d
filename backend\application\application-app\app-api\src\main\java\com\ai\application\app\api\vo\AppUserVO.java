package com.ai.application.app.api.vo;

import com.ai.framework.core.sensitive.Sensitive;
import com.ai.framework.core.sensitive.SensitiveType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 应用用户表
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(name = "")
public class AppUserVO {
    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private Integer userId;
    /**
     * 用户sn
     */
    @Schema(description = "用户sn")
    private String userSn;
    /**
     * 登录账号
     */
    @Schema(description = "登录账号")
    private String userAccount;
    /**
     * 用户名称
     */
    @Schema(description = "用户名称")
    private String userName;
    /**
     * 登录密码
     */
    @Schema(description = "登录密码")
    private String userPassword;
    /**
     * 用户手机号
     */
    @Schema(description = "用户手机号")
    @Sensitive(type = SensitiveType.MOBILE)
    private String userMobile;
    /**
     * 用户邮箱
     */
    @Schema(description = "用户邮箱")
    private String userEmail;
    /**
     * 头像链接
     */
    @Schema(description = "头像链接")
    private String userAvatar;
    /**
     * 工号
     */
    @Schema(description = "工号")
    private String userStaffSn;
    /**
     * 用户状态 1-启用 0-禁用 -1-删除
     */
    @Schema(description = "用户状态 1-启用 0-禁用 -1-删除")
    private Integer userStatus;
    /**
     * 应用id
     */
    @Schema(description = "应用id")
    private Integer appId;
    /**
     * 角色id
     */
    @Schema(description = "角色id")
    private Integer roleId;

    @Schema(description = "是否管理员（false:否,true:是）")
    private Boolean isAdmin = false;
    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Integer tenantId;
    /**
     * 部门id
     */
    @Schema(description = "部门id")
    private Integer deptId;
    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Schema(description = "角色列表")
    private List<AppRoleSimpleVO> roles;
}