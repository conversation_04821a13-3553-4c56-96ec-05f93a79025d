package com.ai.application.base.log.api.mapstruct;
import com.ai.application.base.log.api.dto.ErrorDeliveryConfigSaveDTO;
import com.ai.application.base.log.api.entity.ErrorDeliveryConfig;
import com.ai.application.base.log.api.dto.ErrorDeliveryConfigDTO;
import com.ai.application.base.log.api.vo.ErrorDeliveryConfigVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 错误通知配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-07
 */

@Mapper(componentModel = "spring")
public interface ErrorDeliveryConfigMapstruct {

    ErrorDeliveryConfig toEntity(ErrorDeliveryConfigDTO dto);
    ErrorDeliveryConfig toEntity(ErrorDeliveryConfigSaveDTO dto);
    List<ErrorDeliveryConfig> toEntityList(List<ErrorDeliveryConfigDTO> dtolist);
    ErrorDeliveryConfigVO toVo(ErrorDeliveryConfig entity);
    List<ErrorDeliveryConfigVO> toVoList(List<ErrorDeliveryConfig> entities);
}
