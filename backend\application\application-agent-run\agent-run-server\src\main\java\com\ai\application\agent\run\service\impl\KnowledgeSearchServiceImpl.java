package com.ai.application.agent.run.service.impl;

import cn.hutool.json.JSONUtil;
import com.ai.application.agent.run.dto.DocumentSearchRequestDTO;
import com.ai.application.agent.run.dto.DocumentSearchResultDTO;
import com.ai.application.agent.run.dto.MultiRetrieverRequestDTO;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.service.IKnowledgeSearchService;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 知识片段检检索服务实现
 */
@Slf4j
@Service
public class KnowledgeSearchServiceImpl implements IKnowledgeSearchService {

    @Override
    public ResultVo<DocumentSearchResultDTO> executeDocumentSearch(DocumentSearchRequestDTO request, String authorization) {
        log.info("KnowledgeSearchService executeDocumentSearch start, request: {}", JsonUtils.toJsonString(request));

        try {
            // 参数校验
            validateKnowledgeSearchRequest(request);

            DocumentSearchResultDTO result;
            String searchModel = request.getSearchModel();

            // 根据backend2的KnowledgeSearchExecutor逻辑：
            // expand 基于片段检索 content 基于内容检索 all返回全部片段
            if ("expand".equals(searchModel)) {
                // 扩展检索
                result = executeExpandSearch(request, authorization);
            } else if ("all".equals(searchModel)) {
                // 手动检索器
                result = executeManualRetriever(request, authorization);
            } else {
                // 内容检索（默认模式）- 使用多检索器
                result = executeContentSearch(request, authorization);
            }

            log.info("KnowledgeSearchService executeDocumentSearch success, result: {}", JsonUtils.toJsonString(result));
            return ResultVo.data(result);

        } catch (Exception e) {
            log.error("KnowledgeSearchService executeDocumentSearch error", e);
            return ResultVo.fail(e.getMessage());
        }
    }

    @Override
    public ResultVo<List<DocumentSearchResultDTO.DocumentFragmentDTO>> multiRetrieverSearch(MultiRetrieverRequestDTO request, String authorization) {
        log.info("DocumentSearchService multiRetrieverSearch start, request: {}", JsonUtils.toJsonString(request));

        try {
            // TODO: 调用实际的多检索器服务
            // 这里需要根据实际的服务接口进行实现
            List<DocumentSearchResultDTO.DocumentFragmentDTO> mockResult = mockMultiRetrieverSearch(request);
            
            log.info("DocumentSearchService multiRetrieverSearch success");
            return ResultVo.data(mockResult);

        } catch (Exception e) {
            log.error("DocumentSearchService multiRetrieverSearch error", e);
            return ResultVo.fail(e.getMessage());
        }
    }

    @Override
    public ResultVo<List<DocumentSearchResultDTO.DocumentFragmentDTO>> expandSearch(DocumentSearchRequestDTO.ExpandSearchParamsDTO expandParams, String authorization) {
        log.info("DocumentSearchService expandSearch start, expandParams: {}", JsonUtils.toJsonString(expandParams));

        try {
            // 参数校验
            if (StringUtils.isBlank(expandParams.getFragmentType())) {
                throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "片段类型不能为空");
            }

            // TODO: 调用实际的扩展检索服务
            // 这里需要根据实际的服务接口进行实现
            List<DocumentSearchResultDTO.DocumentFragmentDTO> mockResult = mockExpandSearch(expandParams);
            
            log.info("DocumentSearchService expandSearch success");
            return ResultVo.data(mockResult);

        } catch (Exception e) {
            log.error("DocumentSearchService expandSearch error", e);
            return ResultVo.fail(e.getMessage());
        }
    }

    @Override
    public ResultVo<List<DocumentSearchResultDTO.DocumentFragmentDTO>> manualRetriever(Object searchKnowledge, String authorization) {
        log.info("DocumentSearchService manualRetriever start, searchKnowledge: {}", searchKnowledge);

        try {
            // TODO: 调用实际的手动检索服务
            // 这里需要根据实际的服务接口进行实现
            List<DocumentSearchResultDTO.DocumentFragmentDTO> mockResult = mockManualRetriever(searchKnowledge);
            
            log.info("DocumentSearchService manualRetriever success");
            return ResultVo.data(mockResult);

        } catch (Exception e) {
            log.error("DocumentSearchService manualRetriever error", e);
            return ResultVo.fail(e.getMessage());
        }
    }

    @Override
    public DocumentSearchRequestDTO prepareSearchRequest(String type, String knowledgeInventorySn, Object knowledgeSn) {
        log.info("DocumentSearchService prepareSearchRequest start, type: {}, knowledgeInventorySn: {}", type, knowledgeInventorySn);

        DocumentSearchRequestDTO request = new DocumentSearchRequestDTO();
        List<String> knowledge = new ArrayList<>();

        if ("knowledgeInventorySn".equals(type)) {
            request.setKnowledgeInventorySn(knowledgeInventorySn);
            request.setKnowledgeSn(knowledge);
        } else if ("knowledgeSn".equals(type)) {
            if (knowledgeSn == null) {
                throw new ServiceException(ExecutorError.KNOWLEDGE_SN_IS_NULL);
            }
            // TODO: 根据知识地址查询知识库相关配置
            // String inventorySn = getKnowledgeAndAddKnowList(knowledgeSn, knowledge);
            // request.setKnowledgeInventorySn(inventorySn);
            request.setKnowledgeSn(knowledge);
        } else {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "不支持的检索类型: " + type);
        }

        return request;
    }

    @Override
    public DocumentSearchRequestDTO prepareSearchRequestForKnowledgeTree(String type, List<String> knowledgeInventorySnList, Object knowledgeSn) {
        log.info("DocumentSearchService prepareSearchRequestForKnowledgeTree start, type: {}, knowledgeInventorySnList: {}", type, knowledgeInventorySnList);

        DocumentSearchRequestDTO request = new DocumentSearchRequestDTO();
        List<String> knowledge = new ArrayList<>();

        if ("knowledgeInventorySn".equals(type)) {
            request.setKnowledgeInventorySnList(knowledgeInventorySnList);
            request.setKnowledgeSn(knowledge);
        } else if ("knowledgeSn".equals(type)) {
            if (knowledgeSn == null) {
                throw new ServiceException(ExecutorError.KNOWLEDGE_SN_IS_NULL);
            }
            List<String> inventorySnList = getKnowledgeTreeAndAddKnowList(knowledgeSn, knowledge);
            request.setKnowledgeInventorySnList(inventorySnList);
            request.setKnowledgeSn(knowledge);
        } else {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "不支持的检索类型: " + type);
        }

        return request;
    }

    @Override
    public List<String> getKnowledgeTreeAndAddKnowList(Object knowledgeSn, List<String> knowledgeList) {
        log.info("DocumentSearchService getKnowledgeTreeAndAddKnowList start, knowledgeSn: {}", knowledgeSn);

        try {
            // TODO: 实现获取知识树的逻辑
            // 这里需要根据实际的业务逻辑进行实现
            List<String> inventorySnList = new ArrayList<>();
            
            if (knowledgeSn instanceof List) {
                List<?> snList = (List<?>) knowledgeSn;
                for (Object sn : snList) {
                    if (sn != null) {
                        inventorySnList.add(sn.toString());
                    }
                }
            } else if (knowledgeSn != null) {
                inventorySnList.add(knowledgeSn.toString());
            }

            log.info("DocumentSearchService getKnowledgeTreeAndAddKnowList success, result: {}", inventorySnList);
            return inventorySnList;

        } catch (Exception e) {
            log.error("DocumentSearchService getKnowledgeTreeAndAddKnowList error", e);
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "获取知识树失败: " + e.getMessage());
        }
    }

    @Override
    public List<String> getFileSnByFileId(List<String> fileIds) {
        log.info("DocumentSearchService getFileSnByFileId start, fileIds: {}", fileIds);

        try {
            // TODO: 实现根据文件ID获取文件编号的逻辑
            // 这里需要根据实际的服务接口进行实现
            List<String> fileSnList = new ArrayList<>();
            for (String fileId : fileIds) {
                // 模拟转换逻辑
                fileSnList.add("file_sn_" + fileId);
            }

            log.info("DocumentSearchService getFileSnByFileId success, result: {}", fileSnList);
            return fileSnList;

        } catch (Exception e) {
            log.error("DocumentSearchService getFileSnByFileId error", e);
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "获取文件编号失败: " + e.getMessage());
        }
    }

    @Override
    public KnowledgeInventoryInfo checkKnowledgeInventory(String knowledgeInventorySn) {
        log.info("DocumentSearchService checkKnowledgeInventory start, knowledgeInventorySn: {}", knowledgeInventorySn);

        try {
            // TODO: 实现检查知识库的逻辑
            // 这里需要根据实际的服务接口进行实现
            KnowledgeInventoryInfo info = new KnowledgeInventoryInfo();
            info.setInventorySn(knowledgeInventorySn);
            info.setModelSn("localModel");
            info.setName("测试知识库");
            info.setStatus(1);

            log.info("DocumentSearchService checkKnowledgeInventory success");
            return info;

        } catch (Exception e) {
            log.error("DocumentSearchService checkKnowledgeInventory error", e);
            throw new ServiceException(ExecutorError.KNOWLEDGE_INVENTORY_NOT_FOUND.getCode(), "知识库不存在: " + e.getMessage());
        }
    }

    /**
     * 校验知识片段检索请求
     */
    private void validateKnowledgeSearchRequest(DocumentSearchRequestDTO request) {
        String searchModel = request.getSearchModel();

        // 对于content和expand模式，需要检索内容
        if (!"all".equals(searchModel)) {
            if (StringUtils.isBlank(request.getSearchContent()) && StringUtils.isBlank(request.getSearchKnowledgeContent())) {
                throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "检索内容不能为空");
            }
        }

        // 根据类型校验相应参数
        String type = request.getType();
        if ("knowledgeInventorySn".equals(type)) {
            if (StringUtils.isBlank(request.getKnowledgeInventorySn()) && CollectionUtils.isEmpty(request.getKnowledgeInventorySnList())) {
                throw new ServiceException(ExecutorError.KNOWLEDGE_INVENTORY_SN_IS_NULL);
            }
        } else if ("knowledgeSn".equals(type)) {
            if (CollectionUtils.isEmpty(request.getKnowledgeSn())) {
                throw new ServiceException(ExecutorError.KNOWLEDGE_SN_IS_NULL);
            }
        }
    }

    /**
     * 执行扩展检索
     */
    private DocumentSearchResultDTO executeExpandSearch(DocumentSearchRequestDTO request, String authorization) {
        log.info("Execute expand search");

        DocumentSearchRequestDTO.ExpandSearchParamsDTO expandParams = request.getExpandParams();
        if (expandParams == null) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "扩展检索参数不能为空");
        }

        ResultVo<List<DocumentSearchResultDTO.DocumentFragmentDTO>> result = expandSearch(expandParams, authorization);
        if (result.getCode() != 0) {
            throw new ServiceException(result.getCode(), result.getMessage());
        }

        return DocumentSearchResultDTO.builder()
                .output(CollectionUtils.isEmpty(result.getData()) ? "" : JSONUtil.toJsonStr(result.getData()))
                .fragments(result.getData())
                .success(true)
                .totalCount(result.getData() != null ? result.getData().size() : 0)
                .build();
    }

    /**
     * 执行手动检索
     */
    private DocumentSearchResultDTO executeManualRetriever(DocumentSearchRequestDTO request, String authorization) {
        log.info("Execute manual retriever");

        Object searchKnowledge = request.getSearchKnowledge();
        if (searchKnowledge == null) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "手动检索知识不能为空");
        }

        ResultVo<List<DocumentSearchResultDTO.DocumentFragmentDTO>> result = manualRetriever(searchKnowledge, authorization);
        if (result.getCode() != 0) {
            throw new ServiceException(result.getCode(), result.getMessage());
        }

        return DocumentSearchResultDTO.builder()
                .output(CollectionUtils.isEmpty(result.getData()) ? "" : JSONUtil.toJsonStr(result.getData()))
                .fragments(result.getData())
                .success(true)
                .totalCount(result.getData() != null ? result.getData().size() : 0)
                .build();
    }

    /**
     * 执行内容检索 - 基于backend2的KnowledgeSearchExecutor逻辑
     */
    private DocumentSearchResultDTO executeContentSearch(DocumentSearchRequestDTO request, String authorization) {
        log.info("Execute content search");

        // 根据backend2逻辑：改成多级目录
        DocumentSearchRequestDTO searchRequest = prepareSearchRequestForKnowledgeTree(
                request.getType(), request.getKnowledgeInventorySnList(), request.getKnowledgeSn());

        // 使用knowledgeInventorySn获取知识库的modelSn，用于知识检索来召回知识片段
        // 这里只要模型sn 由于无论是知识库目录 还是知识，只能同模型，取第一个查询
        KnowledgeInventoryInfo inventory = checkKnowledgeInventory(searchRequest.getKnowledgeInventorySnList().get(0));

        // 构建多检索器请求
        MultiRetrieverRequestDTO multiRetrieverRequest = buildMultiRetrieverRequest(request, searchRequest, inventory);

        // 执行多检索器检索
        ResultVo<List<DocumentSearchResultDTO.DocumentFragmentDTO>> result = multiRetrieverSearch(multiRetrieverRequest, authorization);
        if (result.getCode() != 0) {
            throw new ServiceException(result.getCode(), result.getMessage());
        }

        return DocumentSearchResultDTO.builder()
                .output(CollectionUtils.isEmpty(result.getData()) ? "[]" : JSONUtil.toJsonStr(result.getData()))
                .fragments(result.getData())
                .success(true)
                .totalCount(result.getData() != null ? result.getData().size() : 0)
                .build();
    }

    /**
     * 构建多检索器请求 - 基于backend2的KnowledgeSearchExecutor逻辑
     */
    private MultiRetrieverRequestDTO buildMultiRetrieverRequest(DocumentSearchRequestDTO request, DocumentSearchRequestDTO searchRequest, KnowledgeInventoryInfo inventory) {
        MultiRetrieverRequestDTO multiRetrieverRequest = new MultiRetrieverRequestDTO();
        multiRetrieverRequest.setLlmSn(inventory.getModelSn());
        multiRetrieverRequest.setOriginQuery(StringUtils.isNotBlank(request.getSearchContent()) ? request.getSearchContent() : request.getSearchKnowledgeContent());

        // 根据backend2逻辑设置数据集和文件
        String type = request.getType();
        if ("knowledgeInventorySn".equals(type)) {
            multiRetrieverRequest.setDatasetIds(searchRequest.getKnowledgeInventorySnList());
            multiRetrieverRequest.setFileIds(new ArrayList<>());
        } else if ("knowledgeSn".equals(type)) {
            // 根据backend2逻辑：从knowledgeSn中提取fileId
            List<String> knowledgeSnList = request.getKnowledgeSn();
            List<String> fileIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(knowledgeSnList)) {
                for (String knowledge : knowledgeSnList) {
                    // backend2逻辑：knowledgeSn格式为 "fileId::fileName"
                    if (knowledge.contains("::")) {
                        String fileId = knowledge.split("::")[0];
                        fileIds.add(fileId);
                    }
                }
            }

            // 转换fileId为fileSn
            List<String> fileSnList = getFileSnByFileId(fileIds);
            multiRetrieverRequest.setDatasetIds(searchRequest.getKnowledgeInventorySnList());
            multiRetrieverRequest.setFileIds(fileSnList);
        }

        // 构建检索计划 - 完全按照backend2逻辑
        List<MultiRetrieverRequestDTO.RetrievalPlanDTO> retrievalPlanList = new ArrayList<>();

        // 语义检索
        MultiRetrieverRequestDTO.RetrievalPlanDTO embeddingPlan = new MultiRetrieverRequestDTO.RetrievalPlanDTO();
        embeddingPlan.setMethod("embedding");
        embeddingPlan.setTopK(request.getEmbedding() != null ? request.getEmbedding() : 0);
        retrievalPlanList.add(embeddingPlan);

        // 关键词检索
        MultiRetrieverRequestDTO.RetrievalPlanDTO keywordPlan = new MultiRetrieverRequestDTO.RetrievalPlanDTO();
        keywordPlan.setMethod("keywords");
        keywordPlan.setTopK(request.getKeyword() != null ? request.getKeyword() : 0);
        retrievalPlanList.add(keywordPlan);

        // 文本词检索
        MultiRetrieverRequestDTO.RetrievalPlanDTO textPlan = new MultiRetrieverRequestDTO.RetrievalPlanDTO();
        textPlan.setMethod("text");
        textPlan.setTopK(request.getText() != null ? request.getText() : 0);
        retrievalPlanList.add(textPlan);

        // 兼容历史数据 - backend2逻辑
        String searchModel = request.getSearchModel();
        if (searchModel == null) {
            embeddingPlan.setTopK(10);
            keywordPlan.setTopK(10);
            textPlan.setTopK(10);
        }

        multiRetrieverRequest.setRetrievalPlan(retrievalPlanList);
        return multiRetrieverRequest;
    }

    /**
     * 模拟多检索器检索
     */
    private List<DocumentSearchResultDTO.DocumentFragmentDTO> mockMultiRetrieverSearch(MultiRetrieverRequestDTO request) {
        // TODO: 这里需要调用实际的多检索器服务
        List<DocumentSearchResultDTO.DocumentFragmentDTO> fragments = new ArrayList<>();
        
        for (int i = 0; i < 3; i++) {
            fragments.add(DocumentSearchResultDTO.DocumentFragmentDTO.builder()
                    .fragmentId("fragment_" + i)
                    .documentId("doc_" + i)
                    .fileName("test_document_" + i + ".pdf")
                    .fileSn("file_sn_" + i)
                    .content("这是第" + i + "个文档片段的内容，包含了相关的知识信息。")
                    .score(0.9 - i * 0.1)
                    .pageNumber(i + 1)
                    .method("embedding")
                    .createTime(System.currentTimeMillis())
                    .build());
        }
        
        return fragments;
    }

    /**
     * 模拟扩展检索
     */
    private List<DocumentSearchResultDTO.DocumentFragmentDTO> mockExpandSearch(DocumentSearchRequestDTO.ExpandSearchParamsDTO expandParams) {
        // TODO: 这里需要调用实际的扩展检索服务
        List<DocumentSearchResultDTO.DocumentFragmentDTO> fragments = new ArrayList<>();
        
        fragments.add(DocumentSearchResultDTO.DocumentFragmentDTO.builder()
                .fragmentId("expand_fragment_1")
                .documentId("expand_doc_1")
                .fileName("expanded_document.pdf")
                .content("这是扩展检索返回的文档片段内容。")
                .score(0.95)
                .pageNumber(1)
                .method("expand")
                .createTime(System.currentTimeMillis())
                .build());
        
        return fragments;
    }

    /**
     * 模拟手动检索
     */
    private List<DocumentSearchResultDTO.DocumentFragmentDTO> mockManualRetriever(Object searchKnowledge) {
        // TODO: 这里需要调用实际的手动检索服务
        List<DocumentSearchResultDTO.DocumentFragmentDTO> fragments = new ArrayList<>();
        
        fragments.add(DocumentSearchResultDTO.DocumentFragmentDTO.builder()
                .fragmentId("manual_fragment_1")
                .documentId("manual_doc_1")
                .fileName("manual_document.pdf")
                .content("这是手动检索返回的文档片段内容。")
                .score(1.0)
                .pageNumber(1)
                .method("manual")
                .createTime(System.currentTimeMillis())
                .build());
        
        return fragments;
    }
}
