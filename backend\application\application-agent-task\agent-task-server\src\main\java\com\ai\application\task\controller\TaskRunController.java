package com.ai.application.task.controller;

import com.github.pagehelper.PageInfo;
import com.ai.application.task.service.ITaskRunService;
import com.ai.application.task.api.dto.TaskRunDTO;
import com.ai.application.task.api.dto.query.TaskRunQueryDTO;
import com.ai.application.task.api.vo.TaskRunVO;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 任务执行记录表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Tag(name = "任务执行记录表", description = "任务执行记录表-相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/taskRun")
public class TaskRunController {

    @Resource
    private ITaskRunService  taskRunService;

    /**
     * 分页查询
     *
     * @param queryDto
     * @return
     */
    @Operation(summary = "任务执行记录表-分页查询", description = "查询所有任务执行记录表 信息")
    @PostMapping("/page")
    public ResultVo<PageInfo<TaskRunVO>> page(@Validated @RequestBody TaskRunQueryDTO queryDto){
        return ResultVo.data(taskRunService.page(queryDto));
    }

    /**
     * 保存
     *
     * @param dto
     * @return
     */
    @Operation(summary = "任务执行记录表-新增")
    @PostMapping("/add")
    public ResultVo<Void> add(@Validated @RequestBody TaskRunDTO dto){
        taskRunService.add(dto);
        return ResultVo.success("保存成功");
    }
    
    /**
     * 修改
     *
     * @param dto
     * @return
     */
    @Operation(summary = "任务执行记录表-修改")
    @PostMapping(value = "/update")
    public ResultVo<Void> update(@Validated @RequestBody TaskRunDTO dto){
        taskRunService.update(dto);
        return ResultVo.success("修改成功");
    }
}