package com.ai.application.skill.tool.service;

import com.ai.application.skill.tool.api.dto.ApiSkillTestDto;
import com.ai.application.skill.tool.api.dto.SkillExecuteDto;
import com.ai.application.skill.tool.api.dto.SkillSaveDto;
import com.ai.application.skill.tool.api.dto.ToolDTO;
import com.ai.application.skill.tool.api.dto.query.ToolQueryDTO;
import com.ai.application.skill.tool.api.vo.SkillResultVo;
import com.ai.application.skill.tool.api.vo.SkillSaveVo;
import com.ai.application.skill.tool.api.vo.ToolDetailVo;
import com.ai.application.skill.tool.api.vo.ToolVO;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Set;

/**
 * 工具资源表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
public interface IToolService {

        /**
         * 分页
         *
         * @param queryDto
         * @return
         */
        PageInfo<ToolVO> page(ToolQueryDTO queryDto);

        /**
         * 列表
         *
         * @param sort
         * @param queryDto
         * @return
         */
//        List<ToolVO> list(ToolQueryDTO queryDto);

        /**
         * 保存
         *
         * @param dto
         */
//        void save(ToolDTO dto);

        /**
         * 更新
         *
         * @param dto
         */
//        void update(ToolDTO dto);

        /**
         * 查看
         *
         * @param id
         * @return
         */
//        ToolVO get(Long id);

        /**
         * 删除
         *
         * @param ids
         */
        void delete (String skillSn);

        ToolDetailVo getSkillDetail(String skillSn);
        ToolDetailVo getSkillDetail(Integer toolId);


        SkillSaveVo saveSkill(SkillSaveDto dto);

        SkillSaveVo publish(SkillSaveDto skillSaveDto);

        SkillResultVo testSkill(String skillSn, SkillExecuteDto executeDto);

        SkillResultVo apiTest(ApiSkillTestDto dto);

}