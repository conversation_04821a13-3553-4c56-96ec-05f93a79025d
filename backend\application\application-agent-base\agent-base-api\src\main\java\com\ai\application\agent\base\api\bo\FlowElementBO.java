package com.ai.application.agent.base.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class FlowElementBO {
    @NotEmpty(message = "元素定义不能为空")
    @Schema(description = "元素名称")
    private String element;

    @NotEmpty(message = "工作流元素id不能为空")
    @Size(max = 36, message = "工作流元素id过长")
    @Schema(description = "工作流元素ID")
    private String id;

    @Schema(description = "元素名称")
    private String name;

    @Schema(description = "目录")
    private String target;

    @Schema(description = "类型")
    private String type;

    /**
     * 输入参数 定义为HashMap fastjson可以配置不添加 @type info
     * key prompt value {{prompt}}
     */
    @Schema(description = "输入参数")
    private HashMap<String, Object> inputParameters;

    /**
     * 输出参数映射 key 参数名 value 变量名  定义为HashMap fastjson可以配置不添加 @type info
     * key prompt value prompt (没有双大括号)
     */
    @Schema(description = "输出参数")
    private HashMap<String, Object> outputParameters;

    /**
     * 按节点的输出映射
     */
    @Schema(description = "输出参数")
    private HashMap<String, Map<String, String>> outputParametersByNode;

    /**
     * 循环属性
     */
    private LoopCharacteristic loopCharacteristic;

    /**
     * 流程参数
     */
    @Schema(description = "流程参数")
    private List<String> parameters;

    /**
     * 是否忽略异常
     */
    @Schema(description = "是否忽略异常")
    private boolean ignoreError = false;

    /**
     * 网关条件
     */
    @Schema(description = "网关条件")
    private List<GatewayCondition> conditions;

    /**
     * 当节点为并行节点子的字节点时，该字段为当前并行节点的ID。其它情况为空
     */
    @Schema(description = "当节点为并行节点子的字节点时，该字段为当前并行节点的ID。其它情况为空")
    private String parallelProcessId;

    /**
     * 当节点为并行节点子的字节点时，该字段为当前并行分支Index
     */
    @Schema(description = "当节点为并行节点子的字节点时，该字段为当前并行分支Index")
    private int parallelBranchIndex;

    /** 用户自定义变量id列表 */
    @Schema(description = "用户自定义变量id列表")
    private List<String> promptParams;

    /**
     * 节点重要性，默认false
     */
    @Schema(description = "节点重要性")
    private Boolean resultOutputted;

    @Data
    public static class GatewayCondition {
        @Schema(description = "条件")
        private String expression;

        @Schema(description = "目标")
        @NotNull(message = "condition target must not be null")
        private String target;
    }

    @Data
    public static class LoopCharacteristic {
        private static final String DEFAULT_INPUT_ITEM = "_item";

        @Schema(description = "输入参数")
        @NotEmpty(message = "inputCollection must not be empty")
        private String inputCollection;

        @Schema(description = "输入参数")
        private String inputItem;

        @Schema(description = "输出集合")
        private List<String> outputCollections;
    }

}
