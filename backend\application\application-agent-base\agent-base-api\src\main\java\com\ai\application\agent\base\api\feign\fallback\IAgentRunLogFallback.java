package com.ai.application.agent.base.api.feign.fallback;

import com.ai.application.agent.base.api.feign.IAgentRunLogClient;
import com.ai.application.agent.base.api.vo.AgentRunMcpVO;
import com.ai.application.agent.base.api.vo.AgentRunToolVO;
import com.ai.framework.core.vo.ResultVo;
import java.util.List;

public class IAgentRunLogFallback implements IAgentRunLogClient {
    @Override
    public ResultVo<List<AgentRunMcpVO>> mcpLogs(Integer mcpId) {
        return null;
    }

    @Override
    public ResultVo<List<AgentRunToolVO>> toolLogs(Integer toolId) {
        return null;
    }
}