package ${package.ServiceImpl};

import com.github.pagehelper.PageInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import ${package.Mapper}.${entity}Mapper;
import ${cfg.entityPackage}.${entity};
import ${package.Service}.I${entity}Service;
import ${cfg.dtoPackage}.${entity}DTO;
import ${cfg.queryDtoPackage}.${entity}QueryDTO;
import ${cfg.voPackage}.${entity}VO;
import ${cfg.mappingPackage}.${entity}Mapstruct;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Date;

/**
 * $!{table.comment}-服务实现类
 *
 * <AUTHOR>
 * @since ${date}
 */
@Service
public class ${table.serviceImplName} implements ${table.serviceName}{

    @Resource
    private ${table.mapperName} ${table.entityPath}Mapper;

    @Resource
    private ${entity}Mapstruct ${table.entityPath}Mapstruct;

    @Transactional(readOnly = true)
    @Override
    public PageInfo<${entity}VO> page(${entity}QueryDTO queryDto) {
        QueryWrapper<${entity}> queryWrapper = this.buildQuery(queryDto);
        Page<${entity}> page = new Page<>(queryDto.getPageNo(), queryDto.getPageSize());
        Page<${entity}> result = this.${table.entityPath}Mapper.selectPage(page, queryWrapper);
        return PageInfo.of(${table.entityPath}Mapstruct.toVoList(result.getRecords()));
    }

    @Transactional(readOnly = true)
    @Override
    public List<${entity}VO> list(${entity}QueryDTO queryDto) {
        QueryWrapper<${entity}> queryWrapper = this.buildQuery(queryDto);
        return ${table.entityPath}Mapstruct.toVoList(this.${table.entityPath}Mapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(${entity}DTO dto) {
        dto.set${cfg.primaryKeyColumnName}(null);
        ${entity} entity = ${table.entityPath}Mapstruct.toEntity(dto);
        entity.setCreateTime(new Date());

        ${table.entityPath}Mapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(${entity}DTO dto) {
        BusinessAssertUtil.notNull(dto.get${cfg.primaryKeyColumnName}(), "${cfg.primaryKeyColumnName}不能为空");

        // TODO 唯一性字段校验
        ${entity} entity = ${table.entityPath}Mapper.selectById(dto.get${cfg.primaryKeyColumnName}());
        BusinessAssertUtil.notNull(entity, "找不到${cfg.primaryKeyColumnName}为 " + dto.get${cfg.primaryKeyColumnName}() + " 的记录");

        ${entity} entityList = ${table.entityPath}Mapstruct.toEntity(dto);
        entityList.setUpdateTime(new Date());
        ${table.entityPath}Mapper.updateById(entityList);
    }

    @Transactional(readOnly = true)
    @Override
    public ${entity}VO get(Integer id) {
        BusinessAssertUtil.notNull(id, "${cfg.primaryKeyColumnName}不能为空");

        ${entity} entity = ${table.entityPath}Mapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到${cfg.primaryKeyColumnName}为 " + id + " 的记录");

        return ${table.entityPath}Mapstruct.toVo(entity);
    }

    private QueryWrapper<${entity}> buildQuery(${entity}QueryDTO queryDto) {
        QueryWrapper<${entity}> queryWrapper = new QueryWrapper<>();
        return queryWrapper;
    }
}