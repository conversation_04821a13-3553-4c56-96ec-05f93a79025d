package com.ai.application.agent.base.mapper;

import com.ai.application.agent.base.api.dto.query.AgentUseMcpQueryDTO;
import com.ai.application.agent.base.api.entity.AgentUseMcp;
import com.ai.application.agent.base.api.vo.AgentUseMcpQueryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 智能体MCP工具关联表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface AgentUseMcpMapper extends BaseMapper<AgentUseMcp> {
    IPage<AgentUseMcpQueryVO> selectUseMcpByPage(IPage<AgentUseMcpQueryVO> page, @Param("params") AgentUseMcpQueryDTO dto);
}
