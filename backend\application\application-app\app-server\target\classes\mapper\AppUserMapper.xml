<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.app.mapper.AppUserMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.app.api.entity.AppUser">
                    <id column="user_id" property="userId" />
                    <result column="user_sn" property="userSn" />
                    <result column="user_account" property="userAccount" />
                    <result column="user_name" property="userName" />
                    <result column="user_password" property="userPassword" />
                    <result column="user_mobile" property="userMobile" />
                    <result column="user_email" property="userEmail" />
                    <result column="user_avatar" property="userAvatar" />
                    <result column="user_staff_sn" property="userStaffSn" />
                    <result column="user_status" property="userStatus" />
                    <result column="app_id" property="appId" />
                    <result column="role_id" property="roleId" />
                    <result column="tenant_id" property="tenantId" />
                    <result column="dept_id" property="deptId" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        user_id, user_sn, user_account, user_name, user_password, user_mobile, user_email, user_avatar, user_staff_sn, user_status, app_id, role_id, tenant_id, dept_id, create_time, update_time
    </sql>

    <select id="selectAppUserList" resultType="com.ai.application.app.api.vo.AppUserVO">
        select
        <include refid="com.ai.application.app.mapper.AppUserMapper.Base_Column_List"></include>
        from app_user
        order by create_time desc limit 10;
    </select>

    <select id="queryAppUserList" resultType="com.ai.application.app.api.vo.AppUserVO">
        select
        <include refid="com.ai.application.app.mapper.AppUserMapper.Base_Column_List"></include>
        from app_user
        <where>
        tenant_id = #{tenantId}
        and user_status = 1
        <if test="userSnList != null and userSnList.size &gt; 0">
            and user_sn in
            <foreach close=")" collection="userSnList" item="userSn" open="(" separator=",">
                #{userSn}
            </foreach>
        </if>
        </where>
    </select>
</mapper>