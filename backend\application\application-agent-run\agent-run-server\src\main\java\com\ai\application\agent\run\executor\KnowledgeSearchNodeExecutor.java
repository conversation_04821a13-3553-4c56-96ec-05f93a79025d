package com.ai.application.agent.run.executor;

import com.ai.application.agent.run.dto.DocumentSearchRequestDTO;
import com.ai.application.agent.run.dto.DocumentSearchResultDTO;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.service.IKnowledgeSearchService;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import com.ai.framework.workflow.excutor.NodeExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 知识片段检索节点执行器
 * 用于在工作流中执行知识片段检索功能
 */
@Slf4j
public class KnowledgeSearchNodeExecutor implements NodeExecutor {

    private IKnowledgeSearchService documentSearchService;

    @Override
    public void execute(WorkflowContext context) {
        String nodeKey = context.getCurrentNodeKey();
        NodeContext nodeCtx = context.getNodeContexts().get(nodeKey);
        Map<String, Object> nodeDef = nodeCtx.getNodeDefinition();

        log.info("KnowledgeSearchNodeExecutor execute start, nodeKey: {}, nodeDef: {}", nodeKey, JsonUtils.toJsonString(nodeDef));

        try {
            // 设置节点状态为运行中
            nodeCtx.setStatus(NodeStatus.RUNNING);

            // 初始化节点输出
            if (nodeCtx.getOutput() == null) {
                nodeCtx.setOutput(new HashMap<>());
            }

            // 从节点定义中获取输入参数
            Map<String, Object> inputParameters = (Map<String, Object>) nodeDef.get("inputParameters");
            if (inputParameters == null) {
                throw new ServiceException(ExecutorError.NODE_DEFINITION_IS_NULL);
            }

            // 构建知识片段检索请求
            DocumentSearchRequestDTO request = buildKnowledgeSearchRequest(inputParameters, context);

            // 执行知识片段检索
            String authorization = (String) context.getGlobalVars().get("authorization");
            ResultVo<DocumentSearchResultDTO> result = documentSearchService.executeDocumentSearch(request, authorization);

            if (result.getCode() != 0) {
                throw new ServiceException(result.getCode(), result.getMessage());
            }

            DocumentSearchResultDTO searchResult = result.getData();
            if (searchResult == null) {
                throw new ServiceException(ExecutorError.DOCUMENT_SEARCH_RESULT_IS_NULL);
            }

            // 构建输出结果
            Map<String, Object> outputResult = buildOutputResult(searchResult);

            // 将结果写入输出参数
            writeOutputParameters(nodeDef, context, outputResult);

            // 设置节点输出
            nodeCtx.getOutput().putAll(outputResult);

            // 设置节点状态为成功
            nodeCtx.setStatus(NodeStatus.SUCCESS);
            nodeCtx.setEndTime(java.time.LocalDateTime.now());

            log.info("KnowledgeSearchNodeExecutor execute success, result: {}", JsonUtils.toJsonString(outputResult));

        } catch (Exception e) {
            log.error("KnowledgeSearchNodeExecutor execute error", e);
            nodeCtx.setStatus(NodeStatus.FAILED);
            nodeCtx.setErrorMsg("知识片段检索执行失败: " + e.getMessage());
            nodeCtx.setEndTime(java.time.LocalDateTime.now());
            throw e;
        }
    }

    /**
     * 构建知识片段检索请求
     */
    private DocumentSearchRequestDTO buildKnowledgeSearchRequest(Map<String, Object> inputParameters, WorkflowContext context) {
        // 获取参数值，支持变量替换
        String searchModel = getParameterValue(inputParameters, "searchModel", context);
        String content = getParameterValue(inputParameters, "content", context);
        String type = getParameterValue(inputParameters, "type", context);
        String embeddingStr = getParameterValue(inputParameters, "embedding", context);
        String keywordStr = getParameterValue(inputParameters, "keyword", context);
        String textStr = getParameterValue(inputParameters, "text", context);
        String fragmentType = getParameterValue(inputParameters, "fragmentType", context);
        String forwardStr = getParameterValue(inputParameters, "forward", context);
        String backwardStr = getParameterValue(inputParameters, "backward", context);
        String lengthStr = getParameterValue(inputParameters, "length", context);

        // 获取知识库编号列表
        Object knowledgeInventorySnObj = inputParameters.get("knowledgeInventorySn");
        List<String> knowledgeInventorySnList = parseStringList(knowledgeInventorySnObj, context);

        // 获取知识编号列表
        Object knowledgeSnObj = inputParameters.get("knowledgeSn");
        List<String> knowledgeSn = parseStringList(knowledgeSnObj, context);

        // 获取扩展内容
        Object expand = getParameterObject(inputParameters, "expand", context);
        
        // 获取手动检索知识
        Object searchKnowledge = getParameterObject(inputParameters, "searchKnowledge", context);

        // 参数校验
        validateKnowledgeSearchParameters(content, type, knowledgeInventorySnList, knowledgeSn, searchModel);

        // 构建扩展参数
        DocumentSearchRequestDTO.ExpandSearchParamsDTO expandParams = null;
        if ("expand".equals(searchModel)) {
            expandParams = DocumentSearchRequestDTO.ExpandSearchParamsDTO.builder()
                    .fragmentType(fragmentType)
                    .forward(parseInteger(forwardStr))
                    .backward(parseInteger(backwardStr))
                    .length(parseInteger(lengthStr))
                    .expand(expand)
                    .build();
        }

        return DocumentSearchRequestDTO.builder()
                .searchModel(searchModel)
                .searchContent(content)
                .searchKnowledgeContent(content)
                .type(type)
                .knowledgeInventorySnList(knowledgeInventorySnList)
                .knowledgeSn(knowledgeSn)
                .embedding(parseInteger(embeddingStr))
                .keyword(parseInteger(keywordStr))
                .text(parseInteger(textStr))
                .expandParams(expandParams)
                .searchKnowledge(searchKnowledge)
                .build();
    }

    /**
     * 参数校验
     */
    private void validateKnowledgeSearchParameters(String content, String type, List<String> knowledgeInventorySnList, 
                                                 List<String> knowledgeSn, String searchModel) {
        // 对于content和expand模式，需要检索内容
        if (!"all".equals(searchModel) && StringUtils.isBlank(content)) {
            throw new ServiceException(ExecutorError.DOCUMENT_SEARCH_CONTENT_IS_BLANK);
        }
        
        // 根据类型校验相应参数
        if ("knowledgeInventorySn".equals(type)) {
            if (CollectionUtils.isEmpty(knowledgeInventorySnList)) {
                throw new ServiceException(ExecutorError.KNOWLEDGE_INVENTORY_SN_IS_NULL);
            }
        } else if ("knowledgeSn".equals(type)) {
            if (CollectionUtils.isEmpty(knowledgeSn)) {
                throw new ServiceException(ExecutorError.KNOWLEDGE_SN_IS_NULL);
            }
        }
    }

    /**
     * 解析字符串列表
     */
    private List<String> parseStringList(Object obj, WorkflowContext context) {
        if (obj == null) {
            return new ArrayList<>();
        }

        List<String> result = new ArrayList<>();
        
        if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            for (Object item : list) {
                if (item != null) {
                    String value = item.toString();
                    // 支持变量替换
                    if (value.startsWith("$")) {
                        String varName = value.substring(1);
                        Object varValue = context.getGlobalVars().get(varName);
                        if (varValue != null) {
                            result.add(varValue.toString());
                        }
                    } else {
                        result.add(value);
                    }
                }
            }
        } else {
            String value = obj.toString();
            // 支持变量替换
            if (value.startsWith("$")) {
                String varName = value.substring(1);
                Object varValue = context.getGlobalVars().get(varName);
                if (varValue != null) {
                    result.add(varValue.toString());
                }
            } else {
                result.add(value);
            }
        }

        return result;
    }

    /**
     * 获取参数值，支持变量替换
     */
    private String getParameterValue(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        String strValue = value.toString();
        
        // 如果是变量引用（以$开头），从全局变量中获取
        if (strValue.startsWith("$")) {
            String varName = strValue.substring(1);
            Object varValue = context.getGlobalVars().get(varName);
            return varValue != null ? varValue.toString() : null;
        }
        
        return strValue;
    }

    /**
     * 获取参数对象，支持变量替换
     */
    private Object getParameterObject(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        if (value instanceof String) {
            String strValue = value.toString();
            // 如果是变量引用（以$开头），从全局变量中获取
            if (strValue.startsWith("$")) {
                String varName = strValue.substring(1);
                return context.getGlobalVars().get(varName);
            }
        }
        
        return value;
    }

    /**
     * 解析整数
     */
    private Integer parseInteger(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        try {
            return Integer.parseInt(str);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 构建输出结果
     */
    private Map<String, Object> buildOutputResult(DocumentSearchResultDTO searchResult) {
        Map<String, Object> result = new HashMap<>();
        
        // 设置主要输出 - 知识片段检索的输出就是output字段
        result.put("output", searchResult.getOutput() != null ? searchResult.getOutput() : "[]");
        
        // 设置片段信息（可选）
        if (searchResult.getFragments() != null) {
            result.put("fragments", searchResult.getFragments());
        } else {
            result.put("fragments", Collections.emptyList());
        }
        
        // 设置执行状态
        result.put("success", searchResult.getSuccess() != null ? searchResult.getSuccess() : false);
        result.put("errorMessage", searchResult.getErrorMessage());
        result.put("totalCount", searchResult.getTotalCount() != null ? searchResult.getTotalCount() : 0);

        return result;
    }

    /**
     * 写入输出参数
     */
    private void writeOutputParameters(Map<String, Object> nodeDef, WorkflowContext context, Map<String, Object> result) {
        Map<String, Object> outputParameters = (Map<String, Object>) nodeDef.get("outputParameters");
        if (outputParameters != null) {
            for (Map.Entry<String, Object> entry : outputParameters.entrySet()) {
                String outputKey = entry.getKey();
                String variableName = entry.getValue().toString();
                
                Object resultValue = result.get(outputKey);
                if (resultValue != null) {
                    context.setVar(variableName, resultValue);
                    log.info("Set variable {} = {}", variableName, resultValue);
                }
            }
        }
    }

    @Override
    public String getType() {
        return "KNOWLEDGE_SEARCH";
    }
}
