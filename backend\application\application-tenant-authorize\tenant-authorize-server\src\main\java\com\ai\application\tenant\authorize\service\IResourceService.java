package com.ai.application.tenant.authorize.service;

import com.ai.application.tenant.authorize.api.dto.ResourceDTO;
import com.ai.application.tenant.authorize.api.dto.query.ResourceQueryDTO;
import com.ai.application.tenant.authorize.api.vo.ResourceVO;
import com.github.pagehelper.PageInfo;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 资源定义表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface IResourceService {

    /**
     * 分页
     *
     * @param queryDto
     * @return
     */
    PageInfo<ResourceVO> page(ResourceQueryDTO queryDto);

    /**
     * 列表
     *
     * @param sort
     * @param queryDto
     * @return
     */
    List<ResourceVO> list(ResourceQueryDTO queryDto);


    /**
     * 保存
     *
     * @param dto
     */
    void save(ResourceDTO dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(ResourceDTO dto);

    /**
     * 查看
     *
     * @param id
     * @return
     */
    ResourceVO get(Long id);

}