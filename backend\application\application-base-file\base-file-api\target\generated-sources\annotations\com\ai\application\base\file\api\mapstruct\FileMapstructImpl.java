package com.ai.application.base.file.api.mapstruct;

import com.ai.application.base.file.api.dto.DocFileDto;
import com.ai.application.base.file.api.entity.AppFile;
import com.ai.application.base.file.api.entity.AppFile.AppFileBuilder;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T09:54:00+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class FileMapstructImpl implements FileMapstruct {

    @Override
    public List<AppFile> toEntityList(List<DocFileDto> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<AppFile> list = new ArrayList<AppFile>( dtolist.size() );
        for ( DocFileDto docFileDto : dtolist ) {
            list.add( docFileDtoToAppFile( docFileDto ) );
        }

        return list;
    }

    @Override
    public List<DocFileDto> toDtoList(List<AppFile> appFiles) {
        if ( appFiles == null ) {
            return null;
        }

        List<DocFileDto> list = new ArrayList<DocFileDto>( appFiles.size() );
        for ( AppFile appFile : appFiles ) {
            list.add( appFileToDocFileDto( appFile ) );
        }

        return list;
    }

    protected AppFile docFileDtoToAppFile(DocFileDto docFileDto) {
        if ( docFileDto == null ) {
            return null;
        }

        AppFileBuilder appFile = AppFile.builder();

        appFile.fileId( docFileDto.getFileId() );
        appFile.fileSn( docFileDto.getFileSn() );
        appFile.fileName( docFileDto.getFileName() );
        appFile.fileExt( docFileDto.getFileExt() );
        appFile.fileMime( docFileDto.getFileMime() );
        appFile.filePath( docFileDto.getFilePath() );
        appFile.fileType( docFileDto.getFileType() );
        appFile.fileFrom( docFileDto.getFileFrom() );
        appFile.fileSize( docFileDto.getFileSize() );
        appFile.fileWidth( docFileDto.getFileWidth() );
        appFile.fileHeight( docFileDto.getFileHeight() );
        appFile.fileDuration( docFileDto.getFileDuration() );
        appFile.fileHash( docFileDto.getFileHash() );
        appFile.fileSource( docFileDto.getFileSource() );
        appFile.fileStatus( docFileDto.getFileStatus() );
        appFile.appId( docFileDto.getAppId() );
        appFile.tenantId( docFileDto.getTenantId() );
        appFile.userId( docFileDto.getUserId() );

        return appFile.build();
    }

    protected DocFileDto appFileToDocFileDto(AppFile appFile) {
        if ( appFile == null ) {
            return null;
        }

        DocFileDto docFileDto = new DocFileDto();

        docFileDto.setFileId( appFile.getFileId() );
        docFileDto.setFileSn( appFile.getFileSn() );
        docFileDto.setFileName( appFile.getFileName() );
        docFileDto.setFileExt( appFile.getFileExt() );
        docFileDto.setFileMime( appFile.getFileMime() );
        docFileDto.setFilePath( appFile.getFilePath() );
        docFileDto.setFileType( appFile.getFileType() );
        docFileDto.setFileFrom( appFile.getFileFrom() );
        docFileDto.setFileSize( appFile.getFileSize() );
        docFileDto.setFileWidth( appFile.getFileWidth() );
        docFileDto.setFileHeight( appFile.getFileHeight() );
        docFileDto.setFileDuration( appFile.getFileDuration() );
        docFileDto.setFileHash( appFile.getFileHash() );
        docFileDto.setFileSource( appFile.getFileSource() );
        docFileDto.setFileStatus( appFile.getFileStatus() );
        docFileDto.setAppId( appFile.getAppId() );
        docFileDto.setTenantId( appFile.getTenantId() );
        docFileDto.setUserId( appFile.getUserId() );

        return docFileDto;
    }
}
