package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.dto.AgentVersionExtendDTO;
import com.ai.application.agent.base.api.dto.query.AgentVersionExtendQueryDTO;
import com.ai.application.agent.base.api.vo.AgentVersionExtendVO;

import java.util.List;

/**
 * agent版本扩展信息-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface IAgentVersionExtendService {

        /**
         * 列表
         *
         * @param queryDto
         * @return
         */
        List<AgentVersionExtendVO> list(AgentVersionExtendQueryDTO queryDto);

        /**
         * 保存
         *
         * @param dto
         */
        void add(AgentVersionExtendDTO dto);

        /**
         * 更新
         *
         * @param dto
         */
        void update(AgentVersionExtendDTO dto);

        /**
         * 查看
         *
         * @param id
         * @return
         */
        AgentVersionExtendVO get(Integer id);

        /**
         * 设置
         * @param agentId
         * @param versionId
         * @param itemName
         * @param itemValue
         */
        void setItem(Integer agentId, Integer versionId, String itemName, String itemValue);
}