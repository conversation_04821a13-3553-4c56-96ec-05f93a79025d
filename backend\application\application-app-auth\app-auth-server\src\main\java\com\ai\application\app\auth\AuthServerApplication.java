package com.ai.application.app.auth;

import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.launch.AiApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

@EnableDiscoveryClient
@ComponentScan(basePackages = {"com.ai"})
@SpringBootApplication(exclude= {DataSourceAutoConfiguration.class})
@EnableFeignClients("com.ai")
public class AuthServerApplication {
    public static void main(String[] args) {
        AiApplication.run(ServiceConstant.APP_AUTH, AuthServerApplication.class, args);
    }
}