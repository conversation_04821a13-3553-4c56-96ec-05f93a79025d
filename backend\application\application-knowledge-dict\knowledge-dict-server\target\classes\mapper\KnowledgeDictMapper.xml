<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.knowledge.dict.mapper.KnowledgeDictMapper">

    <select id="list" resultType="com.ai.application.knowledge.dict.vo.DictListVo"
            parameterType="com.ai.application.knowledge.dict.dto.DictListDto">
        select
        dict_id as dictId,
        dict_sn as dictSn,
        dict_name as dictName,
        dict_desc as dictDesc,
        dict_type as dictType,
        dict_status as dictStatus,
        tenant_id as tenantId,
        dict_agents as dictAgents,
        create_user_id as createUserId,
        update_user_id as updateUserId,
        create_time as createTime,
        update_time as updateTime
        from knowledge_dict
        where tenant_id = #{tenantId}
        and dict_status = 1
        <if test="keyword != null and keyword != ''">
            and (dict_name like concat('%', #{keyword}, '%')
            or dict_desc like concat('%', #{keyword}, '%'))
        </if>
        <if test="dictType != null">
            and dict_type = #{dictType}
        </if>
        <if test="createTimeStart != null and createTimeEnd != null">
            AND create_time BETWEEN #{createTimeStart} AND #{createTimeEnd}
        </if>
        <if test="updateTimeStart != null and updateTimeEnd != null">
            AND update_time BETWEEN #{updateTimeStart} AND #{updateTimeEnd}
        </if>
        order by create_time desc
    </select>
</mapper>
