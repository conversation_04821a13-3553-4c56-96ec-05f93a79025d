package com.ai.application.task.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

@Slf4j
public class TaskTemplateUtils {

    public static Pair<String, byte[]> generateTemplateInfo(String name, String ver, List<String> headers) {
        try (ByteArrayOutputStream output = new ByteArrayOutputStream()) {
            var fileName = String.format("%s_%s.xlsx", name, null == ver ? "" : ver);
            generateTemplate(headers, output);
            return Pair.of(fileName, output.toByteArray());
        } catch (IOException e) {
            log.error("模版生成失败：{} {}", name, ver, e);
            throw new RuntimeException("模版生成失败");
        }
    }

    private static void generateTemplate(List<String> headers, ByteArrayOutputStream outputStream) {
        ExcelWriterBuilder writerBuilder = EasyExcel.write(outputStream);
        WriteSheet writeSheet = new WriteSheet();
        writeSheet.setSheetName("Sheet1");
        WriteTable writeTable = new WriteTable();
        writeTable.setTableNo(1);
        writeTable.setHead(createHeadWithParent(Pair.of("输入（数组类请换行输入新元素）", headers)));

        try (var writer = writerBuilder.build()) {
            writer.write(new ArrayList<>() , writeSheet, writeTable);
        }
    }

    private static List<List<String>> createHeadWithParent(Pair<String, Collection<String>>... headersGroups) {
        List<List<String>> excelHead = new ArrayList<>();
        for (var group : headersGroups) {
            for (String header : group.getRight()) {
                List<String> head = new ArrayList<>();
                // 父标题
                head.add(group.getLeft());
                // 子标题
                head.add(header);
                excelHead.add(head);
            }
        }

        return excelHead;
    }

    public static void downloadResponse(HttpServletResponse response, String fileName, InputStream input) {
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8)
                .replaceAll("\\+", "%20");
        response.addHeader("Content-Disposition", "attachment;filename=" + encodedFileName);
        response.setContentType("application/octet-stream");
        try (var output = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = input.read(buffer)) != -1) {
                output.write(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
