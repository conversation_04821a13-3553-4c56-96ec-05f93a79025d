#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能体记忆模块启动脚本

应用程序启动脚本，负责：
- 启动前的系统检查（模型文件、Elasticsearch、Nacos等）
- 服务健康状态验证
- 应用程序启动和配置
- 错误处理和故障排除

Project: Agent Memory Service
Author: 张小龙 <<EMAIL>>
Created: 2025-06-01
Version: 1.0.0
License: private
"""

import os
import sys
import uvicorn
import multiprocessing
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.setdefault("PYTHONPATH", str(project_root))


async def check_embedding_model():
    """检查embedding模型文件"""
    from app.config.settings import settings
    
    print("检查embedding模型文件...")
    model_path = Path(settings.embedding.model_path)
    
    if model_path.exists() and model_path.is_dir():
        # SentenceTransformer模型必须包含的基本文件
        required_files = ['config.json']
        # 可能的模型权重文件 (不同模型可能有不同的命名)
        model_weight_files = [
            'pytorch_model.bin',  # 标准PyTorch模型
            'model.safetensors',  # SafeTensors格式
            'tf_model.h5',        # TensorFlow模型
        ]
        # tokenizer相关文件 (至少需要一个)
        tokenizer_files = [
            'tokenizer.json',     # 标准tokenizer
            'vocab.txt',          # 词汇表
            'tokenizer_config.json',  # tokenizer配置
        ]
        
        missing_files = []
        
        # 检查必需文件
        for file_name in required_files:
            if not (model_path / file_name).exists():
                missing_files.append(file_name)
        
        # 检查是否有模型权重文件
        has_model_weights = any((model_path / f).exists() for f in model_weight_files)
        if not has_model_weights:
            missing_files.append("模型权重文件 (pytorch_model.bin 或 model.safetensors)")
        
        # 检查是否有tokenizer文件
        has_tokenizer = any((model_path / f).exists() for f in tokenizer_files)
        if not has_tokenizer:
            missing_files.append("tokenizer文件 (tokenizer.json 或 vocab.txt)")
        
        if missing_files:
            print(f"模型文件不完整，缺少: {', '.join(missing_files)}")
            return False  # 私有化环境不可以使用在线模型
        else:
            print(f"本地模型文件完整: {model_path}")
            return True
    else:
        print(f"本地模型文件不存在: {model_path}")
        return False  # 私有化环境不可以使用在线模型


async def check_nacos_connection():
    """检查Nacos连接并获取配置"""
    from app.config.settings import settings
    from app.config.nacos_client import NacosManager
    
    print("检查Nacos连接...")
    nacos_manager = NacosManager()
    
    try:
        # 尝试初始化Nacos连接
        print(f"Nacos服务器地址: {settings.nacos.server_addresses}")

        await nacos_manager.init_nacos()
        
        if nacos_manager.is_connected:
            print(f"Nacos连接成功，协议: {nacos_manager.protocol_info}")
            # 尝试获取远程配置
            print("正在获取Nacos配置...")
            try:
                remote_config = await nacos_manager.get_config()
                if remote_config:
                    print("成功获取Nacos配置，正在更新本地配置...")
                    settings.update_config(remote_config)
                    print("配置更新完成")
                else:
                    print("Nacos中暂无配置，使用本地配置")
            except Exception as e:
                print(f"获取Nacos配置失败: {e}")
                print("将使用本地配置文件")
            return True  # Nacos连接成功
        else:
            print(f"Nacos连接失败: {settings.nacos.server_addresses}")
            print("将使用本地配置文件")
            return False  # Nacos连接失败，但不是致命错误
    
    except Exception as e:
        print(f"Nacos连接异常: {e}")
        print("将使用本地配置文件")
        return False  # Nacos连接失败，但不是致命错误
    
    finally:
        try:
            await nacos_manager.close()
        except:
            pass


async def check_elasticsearch_connection():
    """检查Elasticsearch连接并创建索引"""
    from app.config.settings import settings
    from app.services.elasticsearch_service import ElasticsearchService
    
    print("检查Elasticsearch连接...")
    
    # 显示连接配置信息
    print(f"ES地址: {settings.elasticsearch.hosts}")
    if settings.elasticsearch.username:
        print(f"认证用户: {settings.elasticsearch.username}")
    if settings.elasticsearch.use_ssl:
        print("启用SSL连接")
    
    es_service = ElasticsearchService()
    
    try:
        # 初始化ES服务
        await es_service.initialize()
        
        print(f"Elasticsearch连接成功")
        
        # 检查健康状态
        health_info = await es_service.health_check()
        cluster_status = health_info.get('cluster_status', 'unknown')
        index_exists = health_info.get('index_exists', False)
        
        print(f"集群状态: {cluster_status}")
        
        if index_exists:
            print(f"索引已存在: {settings.elasticsearch.index_name}")
        else:
            print(f"索引不存在，将自动创建: {settings.elasticsearch.index_name}")
            # 索引创建已在 initialize() 方法中自动执行
            
        print("Elasticsearch检查完成")
        return True  # 连接成功
        
    except Exception as e:
        print(f"Elasticsearch连接失败: {e}")
        print("请确保Elasticsearch服务正在运行")
        print(f"配置地址: {settings.elasticsearch.hosts}")
        if settings.elasticsearch.username:
            print("请检查用户名密码是否正确")
        return False  # 连接失败，这是致命错误
    
    finally:
        try:
            await es_service.close()
        except:
            pass


async def run_startup_checks():
    """运行所有启动检查"""
    print("-" * 60)
    print("启动前检查")
    print("-" * 60)
    
    check_results = {}
    
    # 1. 检查embedding模型文件 (关键)
    check_results['embedding'] = await check_embedding_model()

    # 2. 检查Nacos连接 (非关键)
    check_results['nacos'] = await check_nacos_connection()
    
    # 3. 检查Elasticsearch连接 (关键)
    check_results['elasticsearch'] = await check_elasticsearch_connection()
    
    print("-" * 60)
    print("检查完成")
    print("-" * 60)
    
    # 检查关键服务状态
    critical_checks = ['embedding', 'elasticsearch']  # 定义关键检查项
    failed_critical = []
    
    for check_name in critical_checks:
        if not check_results.get(check_name, False):
            failed_critical.append(check_name)
    
    # 显示检查结果总结
    if failed_critical:
        print("关键服务检查失败:")
        for service in failed_critical:
            print(f"   - {service}")
        print()
        print("无法启动应用，请解决以上问题后重试")
        print("-" * 60)
        print()
        return False
    else:
        # 显示非关键服务的警告
        print("所有关键服务检查通过，可以启动应用")
        print("-" * 60)
        print()
        return True


if __name__ == "__main__":
    # 导入配置
    from app.config.settings import settings
    
    # 运行启动检查
    startup_ok = asyncio.run(run_startup_checks())
    
    # 如果关键检查失败，退出程序
    if not startup_ok:
        print("=" * 60)
        print("启动中止")
        print("=" * 60)
        sys.exit(1)
    
    # 构建服务地址
    service_url = f"http://{settings.app.host}:{settings.app.port}"
    
    print("智能体记忆模块 (Agent Memory)")
    print("=" * 60)
    print(f"应用名称: {settings.app.name}")
    print(f"应用版本: {settings.app.version}")
    print(f"主机地址: {settings.app.host}")
    print(f"主机端口: {settings.app.port}")
    print(f"调试模式: {settings.app.debug}")
    print(f"日志级别: {settings.app.log_level}")
    print(f"工作进程: {settings.app.workers} (CPU核心数: {multiprocessing.cpu_count()})")
    print("=" * 60)
    print()
    print("启动应用中...")
    print()
    print(f"API文档: {service_url}/docs")
    print(f"应用信息: {service_url}/info")
    print(f"健康检查: {service_url}/agentmemory/v1/health")
    print()  
    print("按 Ctrl+C 停止服务")
    print("=" * 60)
    
    # 启动应用
    try:
        # 在debug模式下禁用多进程，因为reload与workers不兼容
        workers = 1 if settings.app.debug else settings.app.workers
        
        uvicorn.run(
            "app.main:app",
            host=settings.app.host,
            port=settings.app.port,
            log_level=settings.app.log_level.lower(),
            reload=settings.app.debug,
            workers=workers,
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n\n智能体记忆模块已停止")
    except Exception as e:
        print(f"\n启动失败: {e}")
        sys.exit(1) 