package com.ai.application.base.log.api.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 消息接收方：10：支持租户管理员:20：任务发起人:100：自定义
 */
@Getter
public enum MessageReceiverTypeEnum {
    TENANT_ADMIN(10, "租户管理员"),
    TASK_INITIATOR(20, "任务发起人"),
    CUSTOMIZE(100, "自定义")
    ;

    private Integer code;
    private String title;

    MessageReceiverTypeEnum(Integer code, String title) {
        this.code = code;
        this.title = title;
    }

    public static String getTitle(Integer code) {
        for(MessageReceiverTypeEnum vo :values() ) {
            if (Objects.equals(vo.code, code)) {
                return vo.title;
            }
        }
        return null;
    }
}
