package com.ai.application.agent.base.controller;

import com.ai.application.agent.base.api.dto.ApiGenDTO;
import com.ai.application.agent.base.api.vo.ApiGenVO;
import com.ai.application.agent.base.service.IAgentApiService;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.kafka.helper.KafkaProducerHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 智能体
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Tag(name = "智能体API接口", description = "智能体API接口")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1/api")
public class ApiInfoController {

    @Resource
    private IAgentApiService agentApiService;

    @Autowired
    private KafkaProducerHelper kafkaProducerHelper;


    @Operation(summary = "Agent")
    @PostMapping("/info")
    public ResultVo<ApiGenVO> info(@Validated @RequestBody ApiGenDTO dto){
        return ResultVo.data(agentApiService.gen(dto));
    }

    @GetMapping(value = "/producer")
    public String sendKafka(@RequestParam("message")  String message) {
        kafkaProducerHelper.sendAsync("topic-test", message);
        return "ok";
    }
}