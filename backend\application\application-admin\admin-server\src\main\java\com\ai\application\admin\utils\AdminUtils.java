package com.ai.application.admin.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AdminUtils {
    // 邮箱正则表达式模式
    private static final String EMAIL_PATTERN =
            "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$";

    private static final Pattern pattern = Pattern.compile(EMAIL_PATTERN);

    /**
     * 验证邮箱地址是否有效
     * @param email 待验证的邮箱地址
     * @return 如果邮箱格式正确返回true，否则返回false
     */
    public static boolean isValidEmail(String email) {
        if (email == null) {
            return false;
        }
        Matcher matcher = pattern.matcher(email);
        return matcher.matches();
    }

    public static boolean isValidMobile(String str) {
        String regEx = "^1[3456789]\\d{9}$";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.find();
    }

    public static boolean isValidLoginAccount(String str) {
        //String regEx = "^[a-z0-9A-Z]+$";
        String regEx = "^[a-zA-Z][a-zA-Z0-9_-]{0,20}$";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.find();
    }

    public static void main(String[] args) {
        boolean validLoginAccount = isValidLoginAccount("Admin-123");
        System.out.println(validLoginAccount);
    }
}
