package com.ai.application.app.api.dto.query;

import com.ai.framework.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 应用用户表 查询条件
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(name = "应用用户表QueryDTO带分页")
public class AppUserQueryPageDTO extends PageParam {
    /**
     * 部门id集合
     */
    @Schema(description = "部门id集合")
    private List<Integer> deptIds;

    /**
     * 用户名称
     */
    @Schema(description = "搜索关键词(用户名/)")
    private String keyword;

    /**
     * 用户名称
     */
    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "是否使用用户状态查询",hidden = true)
    private Integer useUserStatus = 1;
}