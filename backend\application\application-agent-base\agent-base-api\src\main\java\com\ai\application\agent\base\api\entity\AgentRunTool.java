package com.ai.application.agent.base.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 智能体工具执行记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("agent_run_tool")
public class AgentRunTool implements Serializable {
        /**
    * 工具执行id
    */
    @Schema(description = "工具执行id")
    @TableId(type = IdType.AUTO)
private Integer toolRunId;

    /**
    * 调用类型:10-系统内嵌,20-API,30-代码
    */
    @Schema(description = "调用类型:10-系统内嵌,20-API,30-代码")
    private Integer toolCallType;

    /**
    * 工具名称
    */
    @Schema(description = "工具名称")
    private String toolName;

    /**
    * 工具输入参数
    */
    @Schema(description = "工具输入参数")
    private String toolInput;

    /**
    * 工具输出结果
    */
    @Schema(description = "工具输出结果")
    private String toolOutput;

    /**
    * 执行状态:1-执行中,2-成功,3-失败,4-超时
    */
    @Schema(description = "执行状态:1-执行中,2-成功,3-失败,4-超时")
    private Integer toolStatus;

    /**
    * 错误信息
    */
    @Schema(description = "错误信息")
    private String toolError;

    /**
    * 执行时长(毫秒)
    */
    @Schema(description = "执行时长(毫秒)")
    private Integer toolDuration;

    /**
    * 工具配置快照
    */
    @Schema(description = "工具配置快照")
    private String toolSnapshot;

    /**
    * 开始时间
    */
    @Schema(description = "开始时间")
    private Date toolStartTime;

    /**
    * 结束时间
    */
    @Schema(description = "结束时间")
    private Date toolEndTime;

    /**
    * 运行记录id
    */
    @Schema(description = "运行记录id")
    private Integer runId;

    /**
    * 步骤id
    */
    @Schema(description = "步骤id")
    private Integer stepId;

    /**
    * 工具id
    */
    @Schema(description = "工具id")
    private Integer toolId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}