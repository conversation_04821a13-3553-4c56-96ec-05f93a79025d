package com.ai.framework.elasticsearch.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.annotations.Mapping;

import java.util.Date;
import java.util.List;

@Document(indexName = "intelligent_table_data")
@Data
@Mapping(mappingPath = "elasticsearch/intelligent_table_data.json")
public class IntelligentTableData {
    @Field(type = FieldType.Keyword)
    @JsonIgnoreProperties(ignoreUnknown = true)
    private String _class;
    @Id
    @Field(type = FieldType.Keyword)
    private String lineSn;
    @Field(type = FieldType.Keyword)
    private String intelligentTableSn;
    @Field(type = FieldType.Text)
    private String creator;
    @Field(type = FieldType.Text)
    private String updater;
    @Field(type = FieldType.Date)
    private Date createTime;
    @Field(type = FieldType.Date)
    private Date updateTime;
    @Field(type = FieldType.Nested)
    private List<TableDataCell> cell;
    @Field(type = FieldType.Keyword)
    private String md5;

    @Data
    public static class TableDataCell {

        @Field(type = FieldType.Keyword)
        private String cellSn;
        @Field(type = FieldType.Keyword)
        private String definitionSn;
        @Field(type = FieldType.Keyword)
        private String fieldName;
        @Field(type = FieldType.Keyword)
        private String dataType;
        @Field(type = FieldType.Text)
        private String data;
        @Field(type = FieldType.Integer)
        private Integer inputType;
        @Field(type = FieldType.Keyword)
        private String filetype;
        @Field(type = FieldType.Integer)
        private Integer key;
        @Field(type = FieldType.Keyword)
        private String inventorySn;
        @Field(type = FieldType.Keyword)
        private String inventoryName;
        @Field(type = FieldType.Keyword)
        private String fileSn;
        @Field(type = FieldType.Text)
        private String fileName;
    }

}
