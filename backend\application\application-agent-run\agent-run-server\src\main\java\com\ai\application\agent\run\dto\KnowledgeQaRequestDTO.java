package com.ai.application.agent.run.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 知识问答请求 DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(name = "KnowledgeQaRequestDTO")
public class KnowledgeQaRequestDTO {

    /**
     * 问答内容
     */
    @Schema(description = "问答内容")
    private String content;

    /**
     * 问题聚焦 1聚焦 0不聚焦
     */
    @Schema(description = "问题聚焦")
    private String questionFocus;

    /**
     * 问答模型
     */
    @Schema(description = "问答模型")
    private String llmModelSn;

    /**
     * 答复模式 efficiency响应效率优先 quality答复质量优先
     */
    @Schema(description = "答复模式")
    private String responseMode;

    /**
     * 知识库编号列表
     */
    @Schema(description = "知识库编号列表")
    private List<String> knowledgeInventorySn;

    /**
     * 知识地址列表
     */
    @Schema(description = "知识地址列表")
    private List<String> knowledgeSn;

    /**
     * 总结提示词
     */
    @Schema(description = "总结提示词")
    private String summaryPrompt;

    /**
     * 问答模式 knowledge/knowledgeInventory/expand
     */
    @Schema(description = "问答模式")
    private String qaModel;

    /**
     * 知识类型
     */
    @Schema(description = "知识类型")
    private String knowledgeType;

    /**
     * 扩展内容节点
     */
    @Schema(description = "扩展内容节点")
    private Object expand;
}
