package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.bo.MasterDictBO;
import com.ai.application.agent.base.api.dto.AgentUseDictUpdateDTO;
import com.ai.application.agent.base.service.IAgentUseDictService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ai.application.agent.base.mapper.AgentUseDictMapper;
import com.ai.application.agent.base.api.entity.AgentUseDict;
import com.ai.application.agent.base.api.dto.AgentUseDictAddDTO;
import com.ai.application.agent.base.api.dto.AgentUseDictListDTO;
import com.ai.application.agent.base.api.vo.AgentUseDictListVO;
import com.ai.application.agent.base.api.mapstruct.AgentUseDictMapstruct;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Date;
import java.util.Objects;

/**
 * 智能体关联字典表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Service
public class AgentUseDictServiceImpl implements IAgentUseDictService {

    @Resource
    private AgentUseDictMapper agentUseDictMapper;

    @Resource
    private AgentUseDictMapstruct agentUseDictMapstruct;

    @Transactional(readOnly = true)
    @Override
    public List<AgentUseDictListVO> list(AgentUseDictListDTO queryDto) {
        LambdaQueryWrapper<AgentUseDict> queryWrapper = this.buildQuery(queryDto);
        return agentUseDictMapstruct.toVoList(this.agentUseDictMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(AgentUseDictAddDTO dto) {
        AgentUseDict entity = agentUseDictMapstruct.toAddEntity(dto);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());

        agentUseDictMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AgentUseDictUpdateDTO dto) {
        BusinessAssertUtil.notNull(dto.getDictId(), "AdId不能为空");

        AgentUseDict entity = agentUseDictMapper.selectById(dto.getDictId());
        BusinessAssertUtil.notNull(entity, "找不到AdId为 " + dto.getAdId() + " 的记录");

        AgentUseDict entityList = agentUseDictMapstruct.toUpdateEntity(dto);
        entityList.setUpdateTime(new Date());
        agentUseDictMapper.updateById(entityList);
    }

    @Transactional(readOnly = true)
    @Override
    public List<MasterDictBO> toDetail(Integer versionId) {
        AgentUseDictListDTO agentUseDictListDTO = new AgentUseDictListDTO();
        List<AgentUseDictListVO> list = this.list(agentUseDictListDTO);
        return list.stream().map(dictItem -> {
            MasterDictBO dict = new MasterDictBO();
            dict.setDictId(dictItem.getDictId());
            return dict;
        }).toList();
    }

    private LambdaQueryWrapper<AgentUseDict> buildQuery(AgentUseDictListDTO queryDto) {
        LambdaQueryWrapper<AgentUseDict> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(queryDto.getVersionId()), AgentUseDict::getVersionId, queryDto.getVersionId());
        return queryWrapper;
    }
}