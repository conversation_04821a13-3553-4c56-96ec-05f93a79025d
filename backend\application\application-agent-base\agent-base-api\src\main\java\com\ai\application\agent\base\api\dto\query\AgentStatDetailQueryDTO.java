package com.ai.application.agent.base.api.dto.query;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 智能体表-查询条件
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@Schema(name = "智能体表QueryDTO")
public class AgentStatDetailQueryDTO {
    @Schema(description = "模型id")
    @NotNull(message = "模型id不能为空")
    private Integer modelId;

    @Schema(description = "查询key")
    @NotBlank(message = "查询key不能为空")
    private String itemName;

    @Schema(description = "智能体名称")
    private String agentName;

    @Schema(description = "智能体类型: 10:对话流, 20:工作流, 30:master")
    private Integer agentType;
}