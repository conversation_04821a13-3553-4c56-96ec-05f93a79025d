package com.ai.application.task.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 计划任务表
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Data
@Schema(name = "分页定时任务返回VO")
public class TaskCronPageVO {
    private Integer taskId;

    @Schema(description = "任务序列号")
    private String taskSn;

    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "任务描述")
    private String taskDesc;

    @Schema(description = "任务类型:11-单次,13-单次批量,21-周期性")
    private Integer taskType;

    @Schema(description = "任务状态:1-启用,0-禁用,2-暂停,-1-删除")
    private Integer taskStatus;

    @Schema(description = "生效状态:0：生效中，1：已完成")
    private Integer effectiveStatus = 0;

    @Schema(description = "cron表达式")
    private String cronExpression;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @Schema(description = "下次执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date nextRunTime;

    @Schema(description = "最后执行时间/最后一次运行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastRunTime;

    @Schema(description = "已执行次数")
    private Integer runCount;

    @Schema(description = "最大执行次数")
    private Integer maxRunCount;

    @Schema(description = "超时时间(秒)")
    private Integer timeoutSeconds;

    @Schema(description = "失败重试次数")
    private Integer retryCount;

    @Schema(description = "重试间隔(秒)")
    private Integer retryInterval;

    @Schema(description = "任务输入参数")
    private String taskInput;

    @Schema(description = "任务配置参数")
    private String taskConfig;

    @Schema(description = "通知配置")
    private String notificationConfig;

    @Schema(description = "创建人id")
    private String createUserId;

    @Schema(description = "创建人")
    private String createUserName;

    @Schema(description = "运行结果")
    private String runResult;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}