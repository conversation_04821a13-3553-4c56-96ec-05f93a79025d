<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.app.mapper.AppRoleMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.app.api.entity.AppRole">
                    <id column="role_id" property="roleId" />
                    <result column="role_code" property="roleCode" />
                    <result column="role_name" property="roleName" />
                    <result column="role_desc" property="roleDesc" />
                    <result column="role_status" property="roleStatus" />
                    <result column="app_id" property="appId" />
                    <result column="tenant_id" property="tenantId" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        role_id, role_code, role_name, role_desc, role_status, app_id, tenant_id, create_time, update_time
    </sql>

    <select id="selectAppRoleList" resultType="com.ai.application.app.api.vo.AppRoleVO">
        select
        <include refid="com.ai.application.app.mapper.AppRoleMapper.Base_Column_List"></include>
        from app_role
        order by create_time desc limit 10;
    </select>
</mapper>