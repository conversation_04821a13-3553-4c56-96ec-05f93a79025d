package com.ai.application.admin.api.enums;

import java.util.Objects;

/**
 * 租户状态：包括1:“正常”、2:“即将到期”、3:“已过期”、0:“已停用”
 */
public enum TenantStatusEnum {
    NORMAL(1, "正常"),
    DUE(2, "即将到期"),
    EXPIRED(3, "已过期"),
    DEACTIVATED(0, "已停用"),
    DELETE(-1, "删除"),
    ;

    private int code;
    private String name;

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    TenantStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getTitle(Integer code) {
        for(TenantStatusEnum vo :values() ) {
            if (Objects.equals(vo.code, code)) {
                return vo.name;
            }
        }
        return null;
    }
}
