package com.ai.application.agent.base.api.enums;

import lombok.Getter;

@Getter
public enum SkillTypeEnum {
    WORK_FLOW(30, "workFlow"),
    MCP(20, "mcp"),
    TOOL(10, "tool")
    ;

    private final Integer code;
    private final String desc;

    SkillTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SkillTypeEnum ofCode(Integer value) {
        for (SkillTypeEnum sessionStatusEnum : SkillTypeEnum.values()) {
            if (sessionStatusEnum.code.equals(value)) {
                return sessionStatusEnum;
            }
        }
        return null;
    }
}
