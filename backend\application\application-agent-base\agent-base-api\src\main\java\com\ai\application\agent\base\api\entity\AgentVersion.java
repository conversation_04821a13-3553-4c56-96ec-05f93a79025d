package com.ai.application.agent.base.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 智能体版本表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("agent_version")
public class AgentVersion implements Serializable {
    /**
    * 智能体版本id
    */
    @Schema(description = "智能体版本id")
    @TableId(type = IdType.AUTO)
    private Integer versionId;

    /**
    * 智能体版本sn
    */
    @Schema(description = "智能体版本sn")
    private String versionSn;

    /**
    * 智能体版本号
    */
    @Schema(description = "智能体版本号")
    private String versionNumber;

    /**
    * 版本元信息
    */
    @Schema(description = "版本元信息")
    private String versionMetadata;

    /**
    * 版本快照
    */
    @Schema(description = "版本快照")
    private String versionSnapshot;

    /**
    * 版本状态: 1:草稿,3:审批, 5:启用, 0:停用
    */
    @Schema(description = "版本状态: 1:草稿,3:审批, 5:启用, 0:停用")
    private Integer versionStatus;

    /**
    * 是否市场上架: 0否 1是
    */
    @Schema(description = "是否市场上架: 0否 1是")
    private Integer versionOnsale;

    /**
    * 智能体id
    */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
     * 创建用户id
     */
    @Schema(description = "创建用户")
    private Integer createUserId;

    /**
     * 更新用户id
     */
    @Schema(description = "更新用户")
    private Integer updateUserId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}