package com.ai.application.tenant.api.mapstruct;

import com.ai.application.tenant.api.dto.TenantExtendDTO;
import com.ai.application.tenant.api.entity.TenantExtend;
import com.ai.application.tenant.api.vo.TenantExtendVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-13T10:32:20+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class TenantExtendMapstructImpl implements TenantExtendMapstruct {

    @Override
    public TenantExtend toEntity(TenantExtendDTO dto) {
        if ( dto == null ) {
            return null;
        }

        TenantExtend tenantExtend = new TenantExtend();

        tenantExtend.setItemId( dto.getItemId() );
        tenantExtend.setItemName( dto.getItemName() );
        tenantExtend.setItemValue( dto.getItemValue() );
        tenantExtend.setItemStatus( dto.getItemStatus() );
        tenantExtend.setTenantId( dto.getTenantId() );
        tenantExtend.setCreateTime( dto.getCreateTime() );
        tenantExtend.setUpdateTime( dto.getUpdateTime() );

        return tenantExtend;
    }

    @Override
    public List<TenantExtend> toEntityList(List<TenantExtendDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<TenantExtend> list = new ArrayList<TenantExtend>( dtolist.size() );
        for ( TenantExtendDTO tenantExtendDTO : dtolist ) {
            list.add( toEntity( tenantExtendDTO ) );
        }

        return list;
    }

    @Override
    public TenantExtendVO toVo(TenantExtend entity) {
        if ( entity == null ) {
            return null;
        }

        TenantExtendVO tenantExtendVO = new TenantExtendVO();

        tenantExtendVO.setItemId( entity.getItemId() );
        tenantExtendVO.setItemName( entity.getItemName() );
        tenantExtendVO.setItemValue( entity.getItemValue() );
        tenantExtendVO.setItemStatus( entity.getItemStatus() );
        tenantExtendVO.setTenantId( entity.getTenantId() );
        tenantExtendVO.setCreateTime( entity.getCreateTime() );
        tenantExtendVO.setUpdateTime( entity.getUpdateTime() );

        return tenantExtendVO;
    }

    @Override
    public TenantExtendDTO toVo(TenantExtendVO vo) {
        if ( vo == null ) {
            return null;
        }

        TenantExtendDTO tenantExtendDTO = new TenantExtendDTO();

        tenantExtendDTO.setItemId( vo.getItemId() );
        tenantExtendDTO.setItemName( vo.getItemName() );
        tenantExtendDTO.setItemValue( vo.getItemValue() );
        tenantExtendDTO.setItemStatus( vo.getItemStatus() );
        tenantExtendDTO.setTenantId( vo.getTenantId() );
        tenantExtendDTO.setCreateTime( vo.getCreateTime() );
        tenantExtendDTO.setUpdateTime( vo.getUpdateTime() );
        tenantExtendDTO.setBaseDTO( vo.getBaseDTO() );

        return tenantExtendDTO;
    }

    @Override
    public List<TenantExtendVO> toVoList(List<TenantExtend> entities) {
        if ( entities == null ) {
            return null;
        }

        List<TenantExtendVO> list = new ArrayList<TenantExtendVO>( entities.size() );
        for ( TenantExtend tenantExtend : entities ) {
            list.add( toVo( tenantExtend ) );
        }

        return list;
    }
}
