package com.ai.application.admin.api.mapstruct;

import com.ai.application.admin.api.dto.TenantAddDTO;
import com.ai.application.admin.api.dto.TenantDTO;
import com.ai.application.admin.api.dto.TenantUpdateDTO;
import com.ai.application.admin.api.entity.Tenant;
import com.ai.application.admin.api.vo.TenantDetailVO;
import com.ai.application.admin.api.vo.TenantSimpleVO;
import com.ai.application.admin.api.vo.TenantVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <p>
 * 租户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */

@Mapper(componentModel = "spring")
public interface TenantMapstruct {
    TenantMapstruct INSTANCE = Mappers.getMapper(TenantMapstruct.class);

    Tenant toEntity(TenantDTO dto);
    Tenant toEntity(TenantAddDTO dto);
    Tenant toEntity(TenantUpdateDTO dto);
    List<Tenant> toEntityList(List<TenantDTO> dtolist);
    TenantVO toVo(Tenant entity);
    TenantDetailVO toDetailVo(Tenant entity);
    List<TenantVO> toVoList(List<Tenant> entities);
    List<TenantSimpleVO> toVoSimpleList(List<Tenant> entities);
}
