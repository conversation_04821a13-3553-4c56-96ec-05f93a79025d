package com.ai.application.admin.api.mapstruct;

import com.ai.application.admin.api.dto.AppUserLogDTO;
import com.ai.application.admin.api.entity.AppUserLog;
import com.ai.application.admin.api.vo.AppUserLogVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-14T18:16:38+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AppUserLogMapstructImpl implements AppUserLogMapstruct {

    @Override
    public AppUserLog toEntity(AppUserLogDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AppUserLog appUserLog = new AppUserLog();

        appUserLog.setLogId( dto.getLogId() );
        appUserLog.setAppId( dto.getAppId() );
        appUserLog.setTenantId( dto.getTenantId() );
        appUserLog.setUserId( dto.getUserId() );
        appUserLog.setLogModule( dto.getLogModule() );
        appUserLog.setLogAction( dto.getLogAction() );
        appUserLog.setLogValue( dto.getLogValue() );
        appUserLog.setLogRemark( dto.getLogRemark() );
        appUserLog.setLogTime( dto.getLogTime() );

        return appUserLog;
    }

    @Override
    public List<AppUserLog> toEntityList(List<AppUserLogDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<AppUserLog> list = new ArrayList<AppUserLog>( dtolist.size() );
        for ( AppUserLogDTO appUserLogDTO : dtolist ) {
            list.add( toEntity( appUserLogDTO ) );
        }

        return list;
    }

    @Override
    public AppUserLogVO toVo(AppUserLog entity) {
        if ( entity == null ) {
            return null;
        }

        AppUserLogVO appUserLogVO = new AppUserLogVO();

        appUserLogVO.setLogId( entity.getLogId() );
        appUserLogVO.setTenantId( entity.getTenantId() );
        appUserLogVO.setUserId( entity.getUserId() );
        appUserLogVO.setLogModule( entity.getLogModule() );
        appUserLogVO.setLogAction( entity.getLogAction() );
        appUserLogVO.setLogValue( entity.getLogValue() );
        appUserLogVO.setLogRemark( entity.getLogRemark() );
        appUserLogVO.setLogTime( entity.getLogTime() );

        return appUserLogVO;
    }

    @Override
    public List<AppUserLogVO> toVoList(List<AppUserLog> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AppUserLogVO> list = new ArrayList<AppUserLogVO>( entities.size() );
        for ( AppUserLog appUserLog : entities ) {
            list.add( toVo( appUserLog ) );
        }

        return list;
    }
}
