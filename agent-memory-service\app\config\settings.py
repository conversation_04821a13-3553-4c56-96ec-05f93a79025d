# -*- coding: utf-8 -*-
"""
配置管理模块

应用程序配置管理系统，提供：
- 多环境配置支持（开发、测试、生产）
- YAML配置文件解析和验证
- 环境变量覆盖机制
- 动态配置更新（Nacos集成）
- 配置模型定义和验证
- 默认配置和配置文件降级

Project: Agent Memory Service
Author: 张小龙 <<EMAIL>>
Created: 2025-06-01
Version: 1.0.0
License: private
"""

import os
import yaml
import multiprocessing
from typing import List, Optional, Any, Dict
from pathlib import Path

from pydantic_settings import BaseSettings
from pydantic import Field, BaseModel
from ..utils import get_local_ip


class AppSettings(BaseSettings):
    """应用配置"""
    name: str = "AgentMemory"
    version: str = "1.0.0"
    host: str = Field(default_factory=get_local_ip)
    port: int = 6023
    debug: bool = False
    log_level: str = "INFO"
    workers: int = Field(default_factory=lambda: min(multiprocessing.cpu_count() * 2, 8))  # 默认为CPU核心数的2倍，最大8个


class ElasticsearchSettings(BaseSettings):
    """Elasticsearch配置"""
    hosts: List[str] = ["http://localhost:9200"]
    index_name: str = "agent_memory"
    timeout: int = 30
    max_retries: int = 3
    retry_on_timeout: bool = True
    # 认证配置
    username: str = ""
    password: str = ""
    use_ssl: bool = False
    verify_certs: bool = False
    ca_certs: str = ""
    client_cert: str = ""
    client_key: str = ""


class EmbeddingSettings(BaseSettings):
    """向量编码配置"""
    model_name: str = "thenlper/gte-base-zh"
    model_path: str = "./models/gte-base-zh"
    device: str = "cpu"
    dimensions: int = 768
    max_length: int = 512


class GrpcConfig(BaseModel):
    """gRPC配置"""
    enabled: bool = True
    timeout: int = 5000
    max_receive_message_length: int = 104857600
    max_keep_alive_ms: int = 60000
    initial_window_size: int = 10485760
    initial_conn_window_size: int = 10485760


class HealthCheckConfig(BaseModel):
    """健康检查配置"""
    enabled: bool = True
    interval: int = 5
    timeout: int = 3


class FailoverConfig(BaseModel):
    """容错配置"""
    enabled: bool = True
    retry_times: int = 3
    retry_interval: int = 1


class NacosSettings(BaseSettings):
    """Nacos配置"""
    server_addresses: str = "localhost:8848"
    namespace: str = "public"
    username: str = ""
    password: str = ""
    group: str = "DEFAULT_GROUP"
    data_id: str = "agentmemory-config"
    cluster_name: str = "DEFAULT"
    service_name: str = "agentmemory"
    ip: str = Field(default_factory=get_local_ip)
    port: int = 6023
    weight: float = 1.0
    grpc: GrpcConfig = Field(default_factory=GrpcConfig)
    health_check: HealthCheckConfig = Field(default_factory=HealthCheckConfig)
    failover: FailoverConfig = Field(default_factory=FailoverConfig)


class MemorySettings(BaseSettings):
    """记忆检索配置"""
    default_similarity_threshold: float = 0.7
    max_search_results: int = 50
    default_search_results: int = 10
    memory_expire_days: int = 0
    vector_dimension: int = 768


class LoggingSettings(BaseSettings):
    """日志配置"""
    level: str = "INFO"
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
    rotation: str = "1 day"
    retention: str = "30 days"


class PerformanceSettings(BaseSettings):
    """性能配置"""
    max_workers: int = 10
    connection_pool_size: int = 20
    request_timeout: int = 30


class SecuritySettings(BaseSettings):
    """安全配置"""
    api_key_enabled: bool = False
    api_key: str = ""
    cors_enabled: bool = True
    allowed_origins: List[str] = ["*"]
    allowed_methods: List[str] = ["GET", "POST", "PUT", "DELETE"]
    allowed_headers: List[str] = ["*"]


class Settings:
    """全局配置管理类"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or self._get_default_config_path()
        self._load_config()
    
    def _get_default_config_path(self) -> str:
        """获取默认配置文件路径"""
        # 从当前文件位置向上查找config目录
        current_dir = Path(__file__).parent
        config_path = current_dir.parent.parent / "config" / "config.yaml"
        return str(config_path)
    
    def _load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            # 初始化各个配置模块
            self.app = AppSettings(**config_data.get('app', {}))
            self.elasticsearch = ElasticsearchSettings(**config_data.get('elasticsearch', {}))
            self.embedding = EmbeddingSettings(**config_data.get('embedding', {}))
            self.nacos = NacosSettings(**config_data.get('nacos', {}))
            self.memory = MemorySettings(**config_data.get('memory', {}))
            self.logging = LoggingSettings(**config_data.get('logging', {}))
            self.performance = PerformanceSettings(**config_data.get('performance', {}))
            self.security = SecuritySettings(**config_data.get('security', {}))
            
        except FileNotFoundError:
            print(f"配置文件未找到: {self.config_file}，使用默认配置")
            self._load_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}，使用默认配置")
            self._load_default_config()
    
    def _load_default_config(self):
        """加载默认配置"""
        self.app = AppSettings()
        self.elasticsearch = ElasticsearchSettings()
        self.embedding = EmbeddingSettings()
        self.nacos = NacosSettings()
        self.memory = MemorySettings()
        self.logging = LoggingSettings()
        self.performance = PerformanceSettings()
        self.security = SecuritySettings()
    
    def update_config(self, config_data: Dict[str, Any]):
        """动态更新配置（用于Nacos配置更新）"""
        try:
            if 'app' in config_data:
                self.app = AppSettings(**config_data['app'])
            if 'elasticsearch' in config_data:
                self.elasticsearch = ElasticsearchSettings(**config_data['elasticsearch'])
            if 'embedding' in config_data:
                self.embedding = EmbeddingSettings(**config_data['embedding'])
            if 'nacos' in config_data:
                self.nacos = NacosSettings(**config_data['nacos'])
            if 'memory' in config_data:
                self.memory = MemorySettings(**config_data['memory'])
            if 'logging' in config_data:
                self.logging = LoggingSettings(**config_data['logging'])
            if 'performance' in config_data:
                self.performance = PerformanceSettings(**config_data['performance'])
            if 'security' in config_data:
                self.security = SecuritySettings(**config_data['security'])
            
            print("配置更新成功")
        except Exception as e:
            print(f"配置更新失败: {e}")
    
    def to_dict(self) -> Dict[str, Any]:
        """将配置转换为字典"""
        return {
            'app': self.app.model_dump(),
            'elasticsearch': self.elasticsearch.model_dump(),
            'embedding': self.embedding.model_dump(),
            'nacos': self.nacos.model_dump(),
            'memory': self.memory.model_dump(),
            'logging': self.logging.model_dump(),
            'performance': self.performance.model_dump(),
            'security': self.security.model_dump()
        }


# 全局配置实例
settings = Settings() 