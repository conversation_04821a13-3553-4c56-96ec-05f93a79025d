package com.ai.application.tenant.authorize.api.feign.fallback;

import com.ai.application.tenant.authorize.api.dto.ResourceAddReqDTO;
import com.ai.application.tenant.authorize.api.dto.ResourceGrantReqDTO;
import com.ai.application.tenant.authorize.api.feign.ITenantAuthorizeClient;
import com.ai.application.tenant.authorize.api.vo.ResourceVO;
import com.ai.framework.core.vo.ResultVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public class ITenantAuthorizeClientFallback implements ITenantAuthorizeClient {
    @Override
    public ResultVo<List<ResourceVO>> queryGrantResourceList(@RequestBody @Validated ResourceGrantReqDTO dto){
        return ResultVo.fail("查询失败");
    }

    @Override
    public ResultVo<Void> addResource(ResourceAddReqDTO dto) {
        return ResultVo.fail("添加失败");
    }
}