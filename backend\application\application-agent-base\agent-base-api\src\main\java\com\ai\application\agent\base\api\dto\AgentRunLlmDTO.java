package com.ai.application.agent.base.api.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

/**
 * 智能体LLM调用记录表
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@Schema(name = "智能体LLM调用记录表DTO")
public class AgentRunLlmDTO {
    /**
     * LLM调用id
     */
    @Schema(description = "LLM调用id")
    private Integer llmRunId;

    /**
     * 模型名称
     */
    @Schema(description = "模型名称")
    private String llmModelName;

    /**
     * 提示词
     */
    @Schema(description = "提示词")
    private String llmPrompt;

    /**
     * 响应内容
     */
    @Schema(description = "响应内容")
    private String llmResponse;

    /**
     * 对话消息
     */
    @Schema(description = "对话消息")
    private String llmMessages;

    /**
     * 调用状态:1-调用中,2-成功,3-失败,4-超时
     */
    @Schema(description = "调用状态:1-调用中,2-成功,3-失败,4-超时")
    private Integer llmStatus;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String llmError;

    /**
     * 调用时长(毫秒)
     */
    @Schema(description = "调用时长(毫秒)")
    private Integer llmDuration;

    /**
     * 提示词tokens
     */
    @Schema(description = "提示词tokens")
    private Integer llmPromptTokens;

    /**
     * 生成tokens
     */
    @Schema(description = "生成tokens")
    private Integer llmCompletionTokens;

    /**
     * 总tokens
     */
    @Schema(description = "总tokens")
    private Integer llmTotalTokens;

    /**
     * 模型配置快照
     */
    @Schema(description = "模型配置快照")
    private String llmConfig;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private Date llmStartTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private Date llmEndTime;

    /**
     * 运行记录id
     */
    @Schema(description = "运行记录id")
    private Integer runId;

    /**
     * 步骤id
     */
    @Schema(description = "步骤id")
    private Integer stepId;

    /**
     * 模型id
     */
    @Schema(description = "模型id")
    private Integer modelId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}