#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SentenceTransformer功能测试脚本

测试向量编码服务的功能和性能，提供：
- 单文本和批量文本向量编码测试
- 向量相似度计算验证
- 模型配置和维度匹配检查
- 系统资源使用情况监控
- 线程池性能测试

Usage:
    python test/test_sentence_transformer.py

Project: Agent Memory Service
Author: 张小龙 <<EMAIL>>
Created: 2025-06-01
Version: 1.0.0
License: private
"""

import asyncio
import sys
import multiprocessing
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.config.settings import settings
from app.services.embedding_service import EmbeddingService


async def test_sentence_transformer():
    """测试SentenceTransformer功能"""
    print("=" * 60)
    print("[START] 测试SentenceTransformer功能")
    print("=" * 60)
    
    # 初始化embedding服务
    embedding_service = EmbeddingService()
    
    try:
        print("[INIT] 正在初始化Embedding服务...")
        await embedding_service.initialize()
        print("[SUCCESS] Embedding服务初始化成功")
        
        # 显示系统和配置信息
        print(f"\n[INFO] 系统信息:")
        print(f"  CPU核心数: {multiprocessing.cpu_count()}")
        print(f"  配置设备: {settings.embedding.device}")
        print(f"  线程池worker数量: {embedding_service.get_worker_count()}")
        
        # 测试单个文本编码
        print("\n[TEST1] 测试单个文本编码:")
        test_text = "这是一个测试句子，用于验证中文文本编码功能。"
        print(f"  输入文本: {test_text}")
        
        vector = await embedding_service.encode_text(test_text)
        print(f"[SUCCESS] 输出向量维度: {len(vector)}")
        print(f"  向量前5个值: {vector[:5]}")
        
        # 测试批量文本编码
        print("\n[TEST2] 测试批量文本编码:")
        test_texts = [
            "我喜欢吃苹果。",
            "我喜欢吃水果。",
            "今天天气很好。",
            "明天会下雨。"
        ]
        print(f"  输入文本列表: {test_texts}")
        
        vectors = await embedding_service.encode_texts(test_texts)
        print(f"[SUCCESS] 输出向量数量: {len(vectors)}")
        print(f"  每个向量维度: {[len(v) for v in vectors]}")
        
        # 测试相似度计算
        print("\n[TEST3] 测试相似度计算:")
        similarity1 = embedding_service.calculate_cosine_similarity(vectors[0], vectors[1])
        similarity2 = embedding_service.calculate_cosine_similarity(vectors[0], vectors[2])
        
        print(f"  '{test_texts[0]}' 与 '{test_texts[1]}' 的相似度: {similarity1:.4f}")
        print(f"  '{test_texts[0]}' 与 '{test_texts[2]}' 的相似度: {similarity2:.4f}")
        
        # 验证配置
        print("\n[VERIFY] 验证配置:")
        print(f"  配置的模型名称: {settings.embedding.model_name}")
        print(f"  配置的向量维度: {settings.embedding.dimensions}")
        print(f"  实际向量维度: {len(vector)}")
        match_status = "正确" if len(vector) == settings.embedding.dimensions else "错误"
        print(f"  配置匹配: {match_status}")
        
        print("\n" + "=" * 60)
        print("[SUCCESS] 所有测试通过，SentenceTransformer功能正常")
        return True
        
    except Exception as e:
        print(f"[ERROR] 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await embedding_service.close()


if __name__ == "__main__":
    success = asyncio.run(test_sentence_transformer())
    if not success:
        sys.exit(1) 