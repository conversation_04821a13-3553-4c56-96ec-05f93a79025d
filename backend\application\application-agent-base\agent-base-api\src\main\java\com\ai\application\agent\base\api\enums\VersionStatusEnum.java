package com.ai.application.agent.base.api.enums;

import lombok.Getter;

@Getter
public enum VersionStatusEnum {
    STOP(0, "停用"),
    ENABLE(5, "启用"),
    AUDIT(3, "审批"),
    DRAFT(1, "草稿"),
    DELETED(-1, "删除"),
    ;

    private final Integer code;
    private final String desc;

    VersionStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static VersionStatusEnum ofCode(Integer value) {
        for (VersionStatusEnum sessionStatusEnum : VersionStatusEnum.values()) {
            if (sessionStatusEnum.code.equals(value)) {
                return sessionStatusEnum;
            }
        }
        return null;
    }
}
