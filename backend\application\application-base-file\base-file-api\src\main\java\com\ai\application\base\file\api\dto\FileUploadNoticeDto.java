package com.ai.application.base.file.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "回调上传传参")
public class FileUploadNoticeDto {

    /**
     * 数据集ID，关联当前agent上传的多个文件
     */
//    @NotBlank(message = "dataSetId不能为空")
//    private String dataSetId;
    /**
     * 文件上传oss后生成的object_id
     */
    @NotBlank(message = "fileSn不能为空")
    @Schema(name = "fileSn")
    private String fileSn;
    /**
     * 文件名称
     */
    @NotBlank(message = "fileName不能为空")
    @Schema(name = "fileName")
    private String fileName;
    /**
     * 文件类型
     */

    @Schema(name = "fileType",description = "文件类型:10-文本,20-图片,30-音频,40-视频,50-其他")
    private Integer fileType;
    /**
     * 文件md5
     */
    @NotBlank(message = "fileMd5不能为空")
    @Schema(description = "文件fileMd5",name= "fileHash")
    private String fileHash;
    /**
     * embedding mode sn
     */
//    @NotBlank(message = "模型不能为空")
//    private String llmModelSn;

    /**
     * 文档类型 pdf/doc/docx/xls/xlsx/feishu/url/txt
     */
    @Schema(description = "文件来源:10-用户上传,11-用户输入,20-系统生成,30-url导入")
    private String fileFrom;


    @Schema(description = "文件大小byte")
    private String fileSize;


    @Schema(description = "文件宽")
    private String fileWidth;

    @Schema(description = "文件高")
    private String fileHeight;

    @Schema(description = "文件时长")
    private String fileDuration;

    @Schema(description = "文件来源链接|地址")
    private String fileSource;


    @Schema(description = "文件状态:0-待处理,1-处理中,3-正常,-1删除")
    private String fileStatus;

    @Schema(description = "应用id")
    private String appId;

    @Schema(description = "租户id")
    private String tenentId;

    @Schema(description = "上传用户id")
    private String userId;



//    private BatchEmbeddingDto.EmbeddingConfig embeddingConfig = new BatchEmbeddingDto.EmbeddingConfig();
}
