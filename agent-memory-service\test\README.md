# 测试与验证文件目录

本目录包含所有用于测试和验证项目功能的脚本文件。

## 文件列表

### 检查类脚本 (check_*)
- **check_dependencies.py** - 依赖包检查脚本
  - 验证项目依赖包的安装和导入状态
  - 用法: `python test/check_dependencies.py`

- **check_health.py** - 健康检查脚本
  - 用于Docker容器的健康检查
  - 用法: `python test/check_health.py`

- **check_vector_dimensions.py** - 向量维度匹配性检查脚本
  - 检查系统中各组件的向量维度配置一致性
  - 用法: `python test/check_vector_dimensions.py`

### 测试类脚本 (test_*)
- **test_memory_api.py** - 智能体记忆模块API测试脚本
  - 综合API测试工具，包含健康检查、记忆操作等测试
  - 用法: `python test/test_memory_api.py [api_base_url]`

- **test_nacos_v3_compatibility.py** - Nacos 3.0兼容性测试脚本
  - 全面测试Nacos 3.0的兼容性和功能
  - 用法: `python test/test_nacos_v3_compatibility.py`

- **test_sentence_transformer.py** - SentenceTransformer功能测试脚本
  - 测试向量编码服务的功能和性能
  - 用法: `python test/test_sentence_transformer.py`

- **test_nacos_imports.py** - Nacos导入配置测试脚本
  - 测试Nacos SDK的导入和配置状态
  - 用法: `python test/test_nacos_imports.py`

## 使用说明

所有测试脚本都支持从项目根目录执行，已正确配置了导入路径。

### 快速测试命令

```bash
# 检查依赖
python test/check_dependencies.py

# 检查向量维度配置
python test/check_vector_dimensions.py

# 测试API功能
python test/test_memory_api.py

# 测试向量编码功能
python test/test_sentence_transformer.py

# 测试Nacos功能
python test/test_nacos_v3_compatibility.py
python test/test_nacos_imports.py
```

## 目录结构

```
test/
├── README.md                          # 本说明文件
├── check_dependencies.py              # 依赖检查
├── check_health.py                    # 健康检查
├── check_vector_dimensions.py         # 向量维度检查
├── test_memory_api.py                 # API测试
├── test_nacos_v3_compatibility.py     # Nacos兼容性测试
├── test_sentence_transformer.py       # 向量编码测试
└── test_nacos_imports.py              # Nacos导入测试
```

## 注意事项

1. 所有测试脚本都已更新导入路径，支持从test目录执行
2. Docker健康检查已更新为使用 `test/check_health.py`
3. 所有脚本都包含详细的错误报告和使用说明 