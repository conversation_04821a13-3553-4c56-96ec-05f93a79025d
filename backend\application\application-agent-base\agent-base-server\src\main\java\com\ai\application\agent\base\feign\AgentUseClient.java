package com.ai.application.agent.base.feign;

import com.ai.application.agent.base.api.dto.query.*;
import com.ai.application.agent.base.api.feign.IAgentUseClient;
import com.ai.application.agent.base.api.vo.*;
import com.ai.application.agent.base.mapper.*;
import com.ai.framework.core.vo.ResultVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
public class AgentUseClient implements IAgentUseClient {
    private final AgentUseMcpMapper agentUseMcpMapper;;
    private final AgentUseToolMapper agentUseToolMapper;
    private final AgentUseKbMapper agentUseKbMapper;
    private final AgentUseTableMapper agentUseTableMapper;
    private final AgentUseDictMapper agentUseDictMapper;

    @Override
    public ResultVo<PageInfo<AgentUseMcpQueryVO>> selectUseMcpByPage(AgentUseMcpQueryDTO dto) {
        Page<AgentUseMcpQueryVO> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        IPage<AgentUseMcpQueryVO> agentUseMcpQueryVOIPage = agentUseMcpMapper.selectUseMcpByPage(page, dto);
        return ResultVo.data(PageInfo.of(agentUseMcpQueryVOIPage.getRecords()));
    }

    @Override
    public ResultVo<PageInfo<AgentUseToolQueryVO>> selectUseToolByPage(AgentUseToolQueryDTO dto) {
        Page<AgentUseToolQueryVO> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        IPage<AgentUseToolQueryVO> agentUseMcpQueryVOIPage = agentUseToolMapper.selectUseToolByPage(page, dto);
        return ResultVo.data(PageInfo.of(agentUseMcpQueryVOIPage.getRecords()));
    }

    @Override
    public ResultVo<PageInfo<AgentUseKnowledgeTableQueryVO>> selectUseTableByPage(AgentUseKnowledgeTableQueryDTO dto) {
        Page<AgentUseKnowledgeTableQueryVO> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        IPage<AgentUseKnowledgeTableQueryVO> agentUseMcpQueryVOIPage = agentUseTableMapper.selectUseTableByPage(page, dto);
        return ResultVo.data(PageInfo.of(agentUseMcpQueryVOIPage.getRecords()));
    }

    @Override
    public ResultVo<PageInfo<AgentUseKnowledgeDocQueryVO>> selectUseDocByPage(AgentUseKnowledgeDocQueryDTO dto) {
        Page<AgentUseKnowledgeDocQueryVO> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        IPage<AgentUseKnowledgeDocQueryVO> agentUseMcpQueryVOIPage = agentUseKbMapper.selectUseDocByPage(page, dto);
        return ResultVo.data(PageInfo.of(agentUseMcpQueryVOIPage.getRecords()));
    }

    @Override
    public ResultVo<PageInfo<AgentUseKnowledgeDictQueryVO>> selectUseDictByPage(AgentUseKnowledgeDictQueryDTO dto) {
        Page<AgentUseKnowledgeDictQueryVO> page = new Page<>(dto.getPageNo(), dto.getPageSize());
        IPage<AgentUseKnowledgeDictQueryVO> agentUseMcpQueryVOIPage = agentUseDictMapper.selectUseDictByPage(page, dto);
        return ResultVo.data(PageInfo.of(agentUseMcpQueryVOIPage.getRecords()));
    }
}
