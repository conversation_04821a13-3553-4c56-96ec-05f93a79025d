package com.ai.application.agent.base.feign;

import com.ai.application.agent.base.api.dto.FindAgentByUseDTO;
import com.ai.application.agent.base.api.feign.IAgentUseClient;
import com.ai.application.agent.base.api.vo.FindAgentByUseVO;
import com.ai.application.agent.base.service.IAgentUseDictService;
import com.ai.application.agent.base.service.IAgentVersionService;
import com.ai.framework.core.vo.ResultVo;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@RestController
@AllArgsConstructor
public class AgentUseClient implements IAgentUseClient {
    private final IAgentUseDictService agentUseDictService;
    private final IAgentVersionService agentVersionService;

    @Override
    public ResultVo<List<FindAgentByUseVO>> findAgentByUse(FindAgentByUseDTO dto) {
        return ResultVo.data(List.of());
    }
}
