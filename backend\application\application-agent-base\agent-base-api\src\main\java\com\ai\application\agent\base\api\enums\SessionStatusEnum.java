package com.ai.application.agent.base.api.enums;

import lombok.Getter;

@Getter
public enum SessionStatusEnum {
    ACTIVE(1, "活跃"),
    END(3, "结束"),
    DELETE(-1, "删除")
    ;

    private final Integer code;
    private final String desc;

    SessionStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SessionStatusEnum ofCode(Integer value) {
        for (SessionStatusEnum sessionStatusEnum : SessionStatusEnum.values()) {
            if (sessionStatusEnum.code.equals(value)) {
                return sessionStatusEnum;
            }
        }
        return null;
    }
}
