package com.ai.application.app.service.impl;

import com.ai.application.app.api.dto.*;
import com.ai.application.app.api.dto.query.AppRoleQueryDTO;
import com.ai.application.app.api.dto.query.AppUserQueryDTO;
import com.ai.application.app.api.dto.query.AppUserQueryPageDTO;
import com.ai.application.app.api.dto.query.AppUserVerifyPasswordDTO;
import com.ai.application.app.api.entity.AppUser;
import com.ai.application.app.api.mapstruct.AppUserMapstruct;
import com.ai.application.app.api.vo.AppRoleSimpleVO;
import com.ai.application.app.api.vo.AppUserBatchImportResultVO;
import com.ai.application.app.api.vo.AppUserDetailVO;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.application.app.constants.ExtendConstants;
import com.ai.application.app.mapper.AppUserMapper;
import com.ai.application.app.service.IAppRoleService;
import com.ai.application.app.service.IAppUserService;
import com.ai.application.app.utils.AppUtils;
import com.ai.application.tenant.api.dto.TenantExtendDTO;
import com.ai.application.tenant.api.dto.query.TenantExtendQueryDTO;
import com.ai.application.tenant.api.entity.Tenant;
import com.ai.application.tenant.api.entity.TenantDepartment;
import com.ai.application.tenant.api.feign.ITenantClient;
import com.ai.application.tenant.api.vo.TenantExtendVO;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.enums.AppEnum;
import com.ai.framework.core.enums.RoleEnum;
import com.ai.framework.core.enums.StatusEnum;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.sensitive.DesensitizationUtil;
import com.ai.framework.core.sensitive.EncryptUtil;
import com.ai.framework.core.sensitive.SensitiveDecrypt;
import com.ai.framework.core.sensitive.SensitiveType;
import com.ai.framework.core.util.BusinessAssertUtil;
import com.ai.framework.core.util.password.PasswordUtils;
import com.ai.framework.core.util.password.SaltedPassword;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.util.validator.AssertUtil;
import com.ai.framework.core.vo.ResultVo;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 应用用户表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
@Slf4j
public class AppUserServiceImpl implements IAppUserService {
    @Value("${config.userDefaultPassword:aiagent}")
    private String defaultPassword;
    @Value("${config.userSensitiveSaltKey:''}")
    private String saltKey;

    @Resource
    private AppUserMapper appUserMapper;
    @Resource
    private AppUserMapstruct appUserMapstruct;
    @Resource
    private IAppRoleService appRoleService;
    @Resource
    private ResourceLoader resourceLoader;
    @Resource
    private ITenantClient tenantClient;


    @Transactional(readOnly = true)
    @Override
    public PageInfo<AppUserVO> page(AppUserQueryPageDTO queryDto) {
        queryDto.setUseUserStatus(0);
        QueryWrapper<AppUser> queryWrapper = this.buildPageQuery(queryDto);
        Page<AppUser> page = new Page<>(queryDto.getPageNo(), queryDto.getPageSize());
        Page<AppUser> result = this.appUserMapper.selectPage(page, queryWrapper);
        List<AppUser> records = result.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return PageInfo.of(Lists.newArrayList());
        }
        List<AppUserVO> list = appUserMapstruct.toVoList(records);
        list.forEach(appUserVO -> {
            appUserVO.setUserMobile(decryptSensitive(appUserVO.getUserMobile(), SensitiveType.MOBILE));
        });

        List<AppRoleSimpleVO> simpleAppRoleVOS = appRoleService.simpleList(new AppRoleQueryDTO());
        list.forEach(record -> {
            record.setRoles(simpleAppRoleVOS.stream().filter(a -> a.getRoleId().equals(record.getRoleId())).collect(Collectors.toList()));
            //是否管理员标记
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(record.getRoles())) {
                record.setIsAdmin(record.getRoles().stream().anyMatch(a -> a.getRoleCode().equals(RoleEnum.ROLE_ADMIN.getCode())));
            }
        });
        return PageInfo.of(list);
    }

    @Transactional(readOnly = true)
    @Override
    public List<AppUserVO> list(AppUserQueryDTO queryDto) {
        QueryWrapper<AppUser> queryWrapper = this.buildQuery(queryDto);
        List<AppUserVO> list = appUserMapstruct.toVoList(this.appUserMapper.selectList(queryWrapper));
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list)) {
            list.forEach(appUserVO -> {
                appUserVO.setUserMobile(decryptSensitive(appUserVO.getUserMobile(), SensitiveType.MOBILE));
            });
        }
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveUserStatus(String userSn, Boolean enable) {
        AssertUtil.isNotNull(userSn, "userSn不能为空");
        String currentUserSn = UserContext.getUserSn();
        if (userSn.equals(currentUserSn)) {
            AssertUtil.isTrue(enable, "不能禁用当前用户");
        }
        AppUser appUser = appUserMapper.findByUserSn(userSn);
        AssertUtil.isNotNull(appUser, "用户不存在");

        appUser.setUserStatus(enable ? 1 : 0);
        appUser.setUpdateTime(null);
        appUserMapper.updateById(appUser);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(AppUserCreateDTO dto) {
        AppUser appUser = appUserMapper.getByUserAccount(AppEnum.APP_100.getCode(), UserContext.getTenantId(), dto.getUserAccount());
        AssertUtil.isFalse(Objects.nonNull(appUser), "登录账号已存在");
        AppUser entity = appUserMapstruct.toEntity(dto);

        List<TenantExtendVO> list = queryTenantExtendList();
        //部门为空，取根部门
        if (Objects.isNull(dto.getDeptId())) {
            ResultVo<TenantDepartment> tenantRootDepartment = tenantClient.getTenantRootDepartment(UserContext.getTenantId());
            AssertUtil.isTrue(tenantRootDepartment.isSuccess(), tenantRootDepartment.getMessage());
            TenantDepartment tenantDepartment = tenantRootDepartment.getData();
            AssertUtil.isNotNull(tenantRootDepartment, "根部门不存在");
            entity.setDeptId(tenantDepartment.getDeptId());
        }

        String userSn = UUIDUtil.genRandomSn("user");
        SaltedPassword saltedPassword;
        if (CollectionUtils.isEmpty(list)) {
            saltedPassword = PasswordUtils.createSaltedPassword(defaultPassword, userSn);
        } else {
            saltedPassword = PasswordUtils.createSaltedPassword(list.get(0).getItemValue(), userSn);
        }
        entity.setUserPassword(saltedPassword.getHashedPasswordBase64());
        entity.setTenantId(UserContext.getTenantId());
        entity.setAppId(AppEnum.APP_100.getCode());
        entity.setUserSn(userSn);
        //
        if (StringUtils.isBlank(dto.getUserName())) {
            entity.setUserName(dto.getUserAccount());
        }

        //数据校验
        validUserData(entity);
        appUserMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    //@SensitiveEncrypt
    public void update(AppUserUpdateDTO dto) {
        BusinessAssertUtil.notNull(dto.getUserSn(), "用户编号不能为空");
        AppUser appUser = appUserMapper.findByUserSn(dto.getUserSn());
        BusinessAssertUtil.notNull(appUser, "找不到用户编号为 " + dto.getUserSn() + " 的记录");

        appUser.setUserName(dto.getUserName());
        appUser.setRoleId(dto.getRoleId());
        appUser.setDeptId(dto.getDeptId());
        appUser.setUserAvatar(dto.getUserAvatar());
        appUser.setUserStaffSn(dto.getUserStaffSn());
        if(StringUtils.isNotBlank(dto.getUserMobile()) && !StringUtils.contains(dto.getUserMobile(), "*")) {
            appUser.setUserMobile(dto.getUserMobile());
        }
        appUser.setUserEmail(dto.getUserEmail());
        appUser.setUserStatus(dto.getUserStatus());

        //数据校验
        validUserData(appUser);
        appUser.setUpdateTime(null);
        appUserMapper.updateById(appUser);
    }

    private void validUserData(AppUser data) {
        //登录名校验
        AssertUtil.isNotNull(data.getUserAccount(),"登录名不能为空");
        AssertUtil.isNotNull(data.getRoleId(),"角色不能为空");
        AssertUtil.isFalse(data.getUserAccount().length() > 20,"登录名长度不能超过20");
        AssertUtil.isTrue(AppUtils.isValidLoginAccount(data.getUserAccount()),"登录名格式不正确");
        if (StringUtils.isNotBlank(data.getUserEmail())) {
            AssertUtil.isFalse(data.getUserEmail().length() > 50, "邮箱长度不能超过50");
            AssertUtil.isTrue(AppUtils.isValidEmail(data.getUserEmail()),"邮箱格式校验不通过");
        }

        //手机号校验
        if (StringUtils.isNotBlank(data.getUserMobile())) {
            String decrypted = EncryptUtil.decrypt(data.getUserMobile(),saltKey);
            AssertUtil.isFalse(decrypted.length() > 11,"手机号长度不能超过11");
            AssertUtil.isTrue(AppUtils.isValidMobile(data.getUserMobile()),"手机号码格式校验不通过");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updatePassword(String userSn, UserUpdatePasswordDTO dto) {
        AssertUtil.isNotNull(userSn, "userSn不能为空");
        AppUser appUser = appUserMapper.findByUserSn(userSn);
        AssertUtil.isNotNull(appUser, "用户不存在");
        AssertUtil.equals(dto.getUserPassword(), dto.getConfirmPassword(), "确认密码不一致");

        SaltedPassword saltedPassword = PasswordUtils.createSaltedPassword(dto.getConfirmPassword(), appUser.getUserSn());
        appUser.setUserPassword(saltedPassword.getHashedPasswordBase64());
        appUser.setUpdateTime(null);
        appUserMapper.updateById(appUser);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Set<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            appUserMapper.deleteBatchIds(ids);
        }
    }

    @Transactional(readOnly = true)
    @Override
    @SensitiveDecrypt
    public AppUserVO get(Integer id) {
        BusinessAssertUtil.notNull(id, "id不能为空");
        AppUser entity = appUserMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到id为 " + id + " 的记录");
        return appUserMapstruct.toVo(entity);
    }

    @Override
    @SensitiveDecrypt
    public AppUserDetailVO detail(String userSn) {
        AssertUtil.isNotNull(userSn, "userSn不能为空");
        AppUser appUser = appUserMapper.findByUserSn(userSn);
        if (Objects.isNull(appUser)) {
            return null;
        }
        AppUserDetailVO detailVo = appUserMapstruct.toDetailVo(appUser);

        List<AppRoleSimpleVO> simpleAppRoleVOS = appRoleService.simpleList(new AppRoleQueryDTO());
        detailVo.setRoles(simpleAppRoleVOS.stream().filter(a -> a.getRoleId().equals(detailVo.getRoleId())).collect(Collectors.toList()));
        return detailVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void initPassword(AppUserInitPasswordDto dto) {
        AssertUtil.equals(dto.getPassword(), dto.getConfirmPassword(), "确认密码不一致");
        List<TenantExtendVO> list = queryTenantExtendList();
        TenantExtendDTO saveDto = new TenantExtendDTO();
        if (CollectionUtils.isEmpty(list)) {
            saveDto.setTenantId(UserContext.getTenantId());
            saveDto.setItemName(ExtendConstants.TENANT_EXTEND_INIT_PASSWORD);
            saveDto.setItemValue(dto.getConfirmPassword());
            saveDto.setItemStatus(1);
        } else {
            //更新密码
            TenantExtendVO tenantExtendVO = list.get(0);
            BeanUtils.copyProperties(tenantExtendVO, saveDto);
            saveDto.setItemValue(dto.getConfirmPassword());
        }
        ResultVo<Void> voidResultVo = tenantClient.saveOrUpdate(saveDto);
        AssertUtil.isTrue(voidResultVo.isSuccess(), voidResultVo.getMessage());
    }

    private List<TenantExtendVO> queryTenantExtendList() {
        TenantExtendQueryDTO extendQueryDTO = new TenantExtendQueryDTO();
        extendQueryDTO.setItemName(ExtendConstants.TENANT_EXTEND_INIT_PASSWORD);
        ResultVo<List<TenantExtendVO>> listResultVo = tenantClient.queryTenantExtendList(extendQueryDTO);
        AssertUtil.isTrue(listResultVo.isSuccess(), listResultVo.getMessage());
        return listResultVo.getData();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void resetPassword(String userSn) {
        AssertUtil.isNotNull(userSn, "userSn不能为空");
        AppUser appUser = appUserMapper.findByUserSn(userSn);
        AssertUtil.isNotNull(appUser, "用户不存在");

        String password = defaultPassword;
        List<TenantExtendVO> list = queryTenantExtendList();
        if (CollectionUtils.isNotEmpty(list)) {
            //更新密码
            TenantExtendVO tenantExtendVO = list.get(0);
            password = tenantExtendVO.getItemValue();
        }

        //密码加密处理
        SaltedPassword saltedPassword = PasswordUtils.createSaltedPassword(password, appUser.getUserSn());
        appUser.setUserPassword(saltedPassword.getHashedPasswordBase64());
        appUser.setUpdateTime(null);
        appUserMapper.updateById(appUser);
    }

    private QueryWrapper<AppUser> buildPageQuery(AppUserQueryPageDTO queryDto) {
        QueryWrapper<AppUser> queryWrapper = new QueryWrapper<>();
        if (Objects.isNull(queryDto)) {
            return queryWrapper;
        }
        queryWrapper.lambda().eq(AppUser::getTenantId, UserContext.getTenantId());
        queryWrapper.lambda().eq(AppUser::getAppId, AppEnum.APP_100.getCode());
        queryWrapper.lambda().eq(queryDto.getUseUserStatus() > 0, AppUser::getUserStatus, 1);
        queryWrapper.lambda().in(org.apache.commons.collections4.CollectionUtils.isNotEmpty(queryDto.getDeptIds()), AppUser::getDeptId, queryDto.getDeptIds());
        queryWrapper.lambda().like(StringUtils.isNoneBlank(queryDto.getUserName()), AppUser::getUserName, queryDto.getUserName());
        if (StringUtils.isNoneBlank(queryDto.getKeyword())) {
            queryWrapper.lambda()
                    .like(AppUser::getUserName, queryDto.getKeyword()).or()
                    .like(AppUser::getUserStaffSn, queryDto.getKeyword()).or()
                    .like(AppUser::getUserSn, queryDto.getKeyword());
        }
        return queryWrapper;
    }


    private QueryWrapper<AppUser> buildQuery(AppUserQueryDTO queryDto) {
        QueryWrapper<AppUser> queryWrapper = new QueryWrapper<>();
        if (Objects.isNull(queryDto)) {
            return queryWrapper;
        }
        queryWrapper.lambda().eq(AppUser::getTenantId, UserContext.getTenantId());
        queryWrapper.lambda().eq(AppUser::getAppId, AppEnum.APP_100.getCode());
        queryWrapper.lambda().eq(AppUser::getUserStatus, 1);
        queryWrapper.lambda().in(org.apache.commons.collections4.CollectionUtils.isNotEmpty(queryDto.getDeptIds()), AppUser::getDeptId, queryDto.getDeptIds());
        queryWrapper.lambda().in(org.apache.commons.collections4.CollectionUtils.isNotEmpty(queryDto.getUserIds()), AppUser::getUserId, queryDto.getUserIds());
        return queryWrapper;
    }


    @SneakyThrows
    @Override
    public ResponseEntity<byte[]> downloadImportTemplage() {
        // 使用类加载器获取模板文件的输入流
        org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:template/user_import_template.xlsx");
        InputStream inputStream = resource.getInputStream();

        // 读取模板文件的内容
        byte[] fileContent = inputStream.readAllBytes();
        inputStream.close();

        // 设置响应头信息
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", URLEncoder.encode("用户批量导入模版.xlsx", StandardCharsets.UTF_8));

        // 返回响应实体
        return ResponseEntity.ok()
                .headers(headers)
                .body(fileContent);
    }

    @SneakyThrows
    @Override
    public AppUserBatchImportResultVO batchImport(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        AssertUtil.isFalse(StringUtils.isNotBlank(originalFilename) && !originalFilename.toLowerCase().endsWith(".xlsx"), "文件格式不正确");

        List<AppUserBatchImportData> list = EasyExcelFactory.read(file.getInputStream()).head(AppUserBatchImportData.class).sheet(0).headRowNumber(6).doReadSync();
        AssertUtil.isNotEmpty(list, "无导入数据");
        AssertUtil.isFalse(list.size() > 200, "导入数据件数不能超过200件");

        List<AppRoleSimpleVO> appRoleSimpleVOS = appRoleService.simpleList(new AppRoleQueryDTO());

        AppUserBatchImportResultVO res = new AppUserBatchImportResultVO();
        //数据校验
        log.info("导入数据列表,list={}", JSON.toJSONString(list));
        List<AppUserBatchImportData> listFailData = new ArrayList<>();
        List<AppUserBatchImportData> listSuccessData = new ArrayList<>();
        for (AppUserBatchImportData data : list) {
            //导入数据校验
            Boolean validResult = validImportData(data);
            if (!validResult) {
                listFailData.add(data);
                continue;
            }
            AppUserCreateDTO dto = new AppUserCreateDTO();
            dto.setUserAccount(data.getUserAccount());
            dto.setUserName(data.getUserName());
            dto.setUserEmail(data.getUserEmail());
            //手机号加密
            String encrypt = EncryptUtil.encrypt(data.getUserMobile(), saltKey);
            dto.setUserMobile(encrypt);
            dto.setUserStatus(StatusEnum.STATUS_ENABLE.getCode());
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(appRoleSimpleVOS)) {
                appRoleSimpleVOS.stream().filter(a -> a.getRoleCode().equals(data.getRoleName()))
                        .findFirst().ifPresent(appRoleSimpleVO -> {
                            dto.setRoleId(appRoleSimpleVO.getRoleId());
                        });
            } else {
                dto.setRoleId(1);
            }
            try {
                ((IAppUserService) AopContext.currentProxy()).save(dto);
                listSuccessData.add(data);
            } catch (ServiceException e) {
                data.setFailReson(new StringBuilder().append(e.getMessage()).toString());
                listFailData.add(data);
            }
        }
        log.info("导入员工成功");
        res.setListFailData(listFailData);
        res.setListSuccessData(listSuccessData);
        return res;
    }

    @SneakyThrows
    @Override
    public ResponseEntity<byte[]> exportFailData(ImportFailUserDTO dto) {
        log.info("导入失败用户导出，参数={}", JSON.toJSONString(dto));
        // 设置响应头信息
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", URLEncoder.encode("批量导入失败用户.xlsx", StandardCharsets.UTF_8));

        // 使用类加载器获取模板文件的输入流
        org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:template/user_import_fail_template.xlsx");
        EasyExcel.write(resource.getFile(), AppUserBatchImportData.class).sheet("批量导入失败用户").doWrite(dto.getListFailData());
        //ExcelWriter excelWriter = EasyExcelFactory.write(resource.getFile()).excelType(ExcelTypeEnum.XLSX).withTemplate(resource.getFile()).build();
        InputStream inputStream = resource.getInputStream();
        // 读取模板文件的内容
        byte[] fileContent = inputStream.readAllBytes();
        inputStream.close();
        // 返回响应实体
        return ResponseEntity.ok()
                .headers(headers)
                .body(fileContent);
    }

    private Boolean validImportData(AppUserBatchImportData data) {
        StringBuilder failReson = new StringBuilder();
        Boolean validResult = true;
        //登录名校验
        if (StringUtils.isBlank(data.getUserAccount())) {
            failReson.append("登录名为空").append(";");
            validResult = false;
        } else {
            if (data.getUserAccount().length() > 20) {
                failReson.append("登录名长度不能超过20").append(";");
                validResult = false;
            } else if (!AppUtils.isValidLoginAccount(data.getUserAccount())) {
                failReson.append("登录名格式不正确").append(";");
                validResult = false;
            } else {
                AppUser appUser = appUserMapper.getByUserAccount(AppEnum.APP_100.getCode(), UserContext.getTenantId(), data.getUserAccount());
                if (Objects.nonNull(appUser)) {
                    failReson.append("登录名重复").append(";");
                    validResult = false;
                }
            }
        }

        //角色校验
        if (StringUtils.isBlank(data.getRoleName())) {
            failReson.append("角色为空").append(";");
            validResult = false;
        } else {
            List<String> codeList = Arrays.stream(RoleEnum.values()).map(RoleEnum::getCode).toList();
            if (!codeList.contains(data.getRoleName())) {
                failReson.append("角色必须为SA/ADMIN/IT/USER").append(";");
                validResult = false;
            }
        }

        if (StringUtils.isNotBlank(data.getUserName()) && data.getUserName().length() > 20) {
            failReson.append("显示名长度不能超过20").append(";");
            validResult = false;
        }

        //手机号校验
        if (StringUtils.isBlank(data.getUserMobile())) {
            failReson.append("手机号为空").append(";");
            validResult = false;
        }else{
            if (data.getUserMobile().length() > 11) {
                failReson.append("手机号长度不能超过11").append(";");
                validResult = false;
            } else if (!AppUtils.isValidMobile(data.getUserMobile())) {
                failReson.append("手机号码格式校验不通过").append(";");
                validResult = false;
            }
        }

        //邮箱校验
        if (StringUtils.isNotBlank(data.getUserEmail())) {
            if (data.getUserEmail().length() > 20) {
                failReson.append("邮箱长度不能超过50").append(";");
                validResult = false;
            } else if (!AppUtils.isValidEmail(data.getUserEmail())) {
                failReson.append("邮箱格式校验不通过").append(";");
                validResult = false;
            }
        }

        if (StringUtils.isNotBlank(failReson)) {
            data.setFailReson(failReson.deleteCharAt(failReson.length() - 1).toString());
        }
        return validResult;
    }

    @Override
    public void moveUsers(Integer deptId, UserMoveDto dto) {
        List<String> userSnList = dto.getUserSnList();
        List<AppUserVO> appUserVOS = appUserMapper.queryAppUserList(UserContext.getTenantId(), userSnList);
        AssertUtil.isNotEmpty(appUserVOS, "没有查询到用户数据");

        //校验转移用户中是否包含超级管理员
        List<Integer> roleIds = appUserVOS.stream().map(AppUserVO::getRoleId).toList();
        AppRoleQueryDTO queryDTO = new AppRoleQueryDTO();
        queryDTO.setRoleIds(roleIds);
        List<AppRoleSimpleVO> appRoleSimpleVOS = appRoleService.simpleList(queryDTO);
        //AssertUtil.isNotEmpty(appRoleSimpleVOS, "没有查询到用户角色");
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(appRoleSimpleVOS)) {
            boolean blCheck = appRoleSimpleVOS.stream().anyMatch(appRoleSimpleVO -> RoleEnum.ROLE_SA.equals(appRoleSimpleVO.getRoleCode()));
            AssertUtil.isFalse(blCheck, "无法转移超管组织管理员");
        }

        appUserVOS.forEach(appUserVO -> {
            appUserVO.setDeptId(deptId);
            appUserVO.setUpdateTime(null);
            appUserMapper.updateById(appUserMapstruct.toEntity(appUserVO));
        });
    }

    @Override
    public void setAdmin(String userSn) {
        BusinessAssertUtil.notNull(userSn, "用户编号不能为空");
        AppUser appUser = appUserMapper.findByUserSn(userSn);
        BusinessAssertUtil.notNull(appUser, "找不到用户编号为 " + userSn + " 的记录");

        AppRoleQueryDTO queryDTO = new AppRoleQueryDTO();
        queryDTO.setRoleCode(RoleEnum.ROLE_ADMIN.getCode());
        List<AppRoleSimpleVO> appRoleSimpleVOS = appRoleService.simpleList(queryDTO);
        AssertUtil.isNotEmpty(appRoleSimpleVOS, "没有查询到" + RoleEnum.ROLE_ADMIN.getName() + "的角色数据");
        Integer roleId = appRoleSimpleVOS.get(0).getRoleId();

        appUser.setRoleId(roleId);
        appUser.setUpdateTime(null);
        appUserMapper.updateById(appUser);
    }

    @Override
    public void deleteAdmin(String userSn) {
        BusinessAssertUtil.notNull(userSn, "用户编号不能为空");
        AppUser appUser = appUserMapper.findByUserSn(userSn);
        BusinessAssertUtil.notNull(appUser, "找不到用户编号为 " + userSn + " 的记录");

        AppRoleQueryDTO queryDTO = new AppRoleQueryDTO();
        queryDTO.setRoleCode(RoleEnum.ROLE_USER.getCode());
        List<AppRoleSimpleVO> appRoleSimpleVOS = appRoleService.simpleList(queryDTO);
        AssertUtil.isNotEmpty(appRoleSimpleVOS, "没有查询到" + RoleEnum.ROLE_ADMIN.getName() + "的角色数据");
        Integer roleId = appRoleSimpleVOS.get(0).getRoleId();

        appUser.setRoleId(roleId);
        appUser.setUpdateTime(null);
        appUserMapper.updateById(appUser);
    }

    @Override
    public void deleteUser(String userSn) {
        BusinessAssertUtil.notNull(userSn, "用户编号不能为空");
        AppUser appUser = appUserMapper.findByUserSn(userSn);
        BusinessAssertUtil.notNull(appUser, "找不到用户编号为 " + userSn + " 的记录");

        appUser.setUserStatus(StatusEnum.STATUS_DELETE.getCode());
        appUser.setUpdateTime(null);
        appUserMapper.updateById(appUser);
    }

    @Override
    public AppUserVO verifyPassword(AppUserVerifyPasswordDTO dto) {
        BusinessAssertUtil.notNull(dto.getTenantSn(), "租户编号不能为空");
        BusinessAssertUtil.notNull(dto.getLoginName(), "账号不能为空");
        BusinessAssertUtil.notNull(dto.getPassword(), "密码不能为空");

        ResultVo<Tenant> tenantBySn = tenantClient.getTenantBySn(dto.getTenantSn());
        AssertUtil.isTrue(tenantBySn.isSuccess(), tenantBySn.getMessage());
        Tenant tenant = tenantBySn.getData();
        AppUser appUser = appUserMapper.getByUserAccount(dto.getAppId(), tenant.getTenantId(), dto.getLoginName());
        AssertUtil.isNotNull(appUser, "用户不存在");

        // 验证密码
        byte[] salt = appUser.getUserSn().getBytes();
        byte[] hashedPassword = PasswordUtils.fromBase64(appUser.getUserPassword());
        boolean check = PasswordUtils.verifyPassword(dto.getPassword(), salt, hashedPassword);
        AssertUtil.isNotNull(check, "账号或密码错误");
        return appUserMapstruct.toVo(appUser);
    }

    private String decryptSensitive(String value, SensitiveType type) {
        String decrypted = EncryptUtil.decrypt(value, saltKey);
        if (StringUtils.equals(value, decrypted)) {
            return decrypted;
        }
        return DesensitizationUtil.desensitize(
                decrypted,
                type,
                0,
                0,
                "*");
    }
}