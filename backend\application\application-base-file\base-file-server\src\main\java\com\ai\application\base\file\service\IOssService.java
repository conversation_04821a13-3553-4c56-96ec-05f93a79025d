package com.ai.application.base.file.service;

import com.ai.application.base.file.api.dto.BatchUploadFileDto;
import com.ai.application.base.file.api.vo.UploadCertificateVo;
import com.ai.application.base.file.api.vo.UploadFileVo;
import com.amazonaws.services.s3.model.S3Object;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

public interface IOssService {
    UploadCertificateVo uploadCertificate();

    String getDownloadUrl(String fileSn, String type);

    String getDownloadUrlByFileName(String fileSn, String type, String fileName);

    void batchDelete(List<String> objectNames);

//    UploadFileVo uploadFileStream(InputStream inputStream, String fileName);

//    List<UploadFileVo> batchUploadFileUrl(List<BatchUploadFileDto> batchUploadFiles);

//    void uploadFile(InputStream inputStream, long length, String fileSn, String fileType);


    void uploadFile(MultipartFile file, long length, String fileSn, String fileType);

    String uploadFileReturnUrl(MultipartFile file, long length, String fileSn, String fileType);

    void uploadFile(byte[] bytes , long length, String fileSn, String fileType);

    byte[] downloadAsBytes(String objectName);


}
