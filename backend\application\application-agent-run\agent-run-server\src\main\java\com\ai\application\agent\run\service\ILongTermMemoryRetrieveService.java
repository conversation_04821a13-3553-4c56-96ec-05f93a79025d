package com.ai.application.agent.run.service;

import com.ai.application.agent.run.dto.LongTermMemoryRetrieveRequestDTO;
import com.ai.application.agent.run.dto.LongTermMemoryRetrieveResultDTO;
import com.ai.framework.core.vo.ResultVo;

/**
 * 长期记忆检索服务接口
 */
public interface ILongTermMemoryRetrieveService {

    /**
     * 执行长期记忆检索
     *
     * @param request 检索请求
     * @param authorization 授权信息
     * @return 检索结果
     */
    ResultVo<LongTermMemoryRetrieveResultDTO> executeLongTermMemoryRetrieve(LongTermMemoryRetrieveRequestDTO request, String authorization);

    /**
     * 验证长期记忆检索请求
     *
     * @param request 检索请求
     * @return 是否有效
     */
    boolean validateLongTermMemoryRetrieveRequest(LongTermMemoryRetrieveRequestDTO request);

    /**
     * 构建记忆搜索请求
     *
     * @param request 检索请求
     * @return 搜索请求
     */
    Object buildMemorySearchRequest(LongTermMemoryRetrieveRequestDTO request);
}
