package com.ai.application.base.file.util;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.CountDownLatch;

@Slf4j
public class ProcessInputSteramThread implements Runnable{
    private InputStream is ;
    private CountDownLatch latch;

    public ProcessInputSteramThread(InputStream is,CountDownLatch latch){
        this.is = is ;
        this.latch = latch;
    }

    @Override
    public void run() {
        byte[] bytes = new byte[1024];
        int len = 0 ;
        while (true) {
            try {
                //处理流判断读写流
                if ((len = is.read(bytes)) != -1){
                    // System.out.println(new String(bytes, 0, len, "gb2312"));
                }else {
                    break ;
                }
            } catch (IOException e) {
                e.printStackTrace();
                log.error("WK生成PDF处理流判断读写流失败");
                break;
            }
        }

        latch.countDown();
    }
}

