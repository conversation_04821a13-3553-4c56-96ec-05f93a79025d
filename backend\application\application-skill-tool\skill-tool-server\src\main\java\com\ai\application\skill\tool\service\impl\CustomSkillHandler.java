package com.ai.application.skill.tool.service.impl;


import com.ai.application.skill.tool.api.dto.PythonCodeExecReq;
import com.ai.application.skill.tool.api.dto.PythonCodeParamDto;
import com.ai.application.skill.tool.api.dto.SkillExecuteDto;
import com.ai.application.skill.tool.api.dto.SkillSaveDto;
import com.ai.application.skill.tool.api.entity.SkillParamValue;
import com.ai.application.skill.tool.api.entity.Tool;
import com.ai.application.skill.tool.api.entity.ToolCode;
import com.ai.application.skill.tool.api.enums.SkillErrorCode;
import com.ai.application.skill.tool.api.enums.SkillType;
import com.ai.application.skill.tool.api.vo.*;
import com.ai.application.skill.tool.config.PythonExecConfig;
import com.ai.application.skill.tool.feign.PythonExecClient;
import com.ai.application.skill.tool.mapper.ToolCodeMapper;
import com.ai.application.skill.tool.service.ISkillHandler;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.string.StringUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import feign.Request;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class CustomSkillHandler implements ISkillHandler {

    @Resource
    private ToolCodeMapper toolCodeMapper;


    private final PythonExecClient pythonExecClient;

    private final PythonExecConfig config;

    public CustomSkillHandler( PythonExecClient pythonExecClient, PythonExecConfig config) {

        this.pythonExecClient = pythonExecClient;
        this.config = config;
    }

    @Override
    public String getType() {
        return "custom";
    }

    @Override
    public void updateSkillStatus(List<Tool> skillInfoList) {

    }

    @Override
    public ToolDetailVo getSkillDetail(Tool skillInfo, AuthorizedDataVo authorizedDataVo) {
        ToolDetailVo skillDetailVo = null;
        if (skillInfo != null) {
            Integer toolId = skillInfo.getToolId();
            ToolCode toolCode = toolCodeMapper.selectOne(new LambdaQueryWrapper<ToolCode>()
                    .eq(ToolCode::getToolId, toolId)
            );
            JSONObject extendedJson = null;
            if(Objects.nonNull(toolCode)) {
                extendedJson = (JSONObject) JSON.toJSON(toolCode);
            }


            skillDetailVo = ToolDetailVo.builder()
                    .userName("ceshi")
                    .createTime(skillInfo.getCreateTime())
                    .updateTime(skillInfo.getUpdateTime())
                    .build();
            if (authorizedDataVo != null && !authorizedDataVo.getAuthorizedPermissions().contains("skill:publish")) {
                extendedJson.put("code", "");
            }
            skillDetailVo.setToolSn(skillInfo.getToolSn());
            skillDetailVo.setToolStatus(skillInfo.getToolStatus());
            skillDetailVo.setToolName(skillInfo.getToolName());
            skillDetailVo.setToolType(skillInfo.getToolType());
            skillDetailVo.setToolDesc(skillInfo.getToolDesc());
            skillDetailVo.setExtendedData(extendedJson);
            skillDetailVo.setToolTypeName(SkillType.ofType(skillInfo.getToolType()).getName());

        }
        return skillDetailVo;
    }

//    @Override
//    public void refreshSkillInputAndOutput(int skillType, SkillBaseVo skillBaseVo) {
//
//    }

    @Override
    public SkillResultVo runSkill(Tool skillInfo, SkillExecuteDto executeDto) {

        Integer toolId = skillInfo.getToolId();
        ToolCode toolCode = toolCodeMapper.selectOne(new LambdaQueryWrapper<ToolCode>()
                .eq(ToolCode::getToolId, toolId)
        );
        PythonCodeExecReq execReq = new PythonCodeExecReq();
        execReq.setCode(toolCode.getCodeSource());
        List<SkillParamValue> input = executeDto.getInput();
        execReq.setPythonCodeInput(this.pythonCodeInput(input));

        // 修改后的代码
        String codeOutput = toolCode.getCodeOutput();
        JSONArray jsonArray=null;
        try {
            // 先解析JSON字符串为JSONArray
            jsonArray = JSON.parseArray(codeOutput);
            log.info("解析codeOutput成功: {}", JsonUtils.toJsonString(jsonArray));
            execReq.setPythonCodeOutput(jsonArray.stream()
                    .map(e -> {
                        if (e instanceof Map) {
                            return getPythonCodeParamDto((Map<String, Object>) e);
                        } else {
                            // 如果不是Map类型，记录错误或处理异常
                            log.error("codeOutput包含非Map类型的元素: {}", e);
                            throw new ServiceException(111,  "解析输出参数配置失败");
                        }
                    })
                    .toList());
        } catch (Exception e) {
            log.error("解析codeOutput失败: {}", codeOutput, e);
            throw new ServiceException(111,  "解析输出参数配置失败");
        }
        execReq.setAuthorization("Bearer your-auth-token");
//        execReq.setWhitelistImportConfig(config.getWhitelistImportConfig());
//        Integer timeout = extendedDataVo.getTimeOut();
        Integer timeout =200;
        PythonCodeExecResp execResp = null;
        try {
            log.info("解析传参数据为: {}", JsonUtils.toJsonString(execReq));
            execResp = pythonExecClient.execPythonCode(execReq, new Request.Options(timeout, TimeUnit.SECONDS, timeout, TimeUnit.SECONDS, true));
        } catch (Exception e) {
            if (e.getMessage().contains("Read timed out")) {
                throw new ServiceException(SkillErrorCode.PYTHON_CODE_EXEC_READ_TIMEOUT_ERROR);
            } else {
                throw new ServiceException(SkillErrorCode.PYTHON_CODE_EXEC_ERROR.getCode(), e.getMessage());
            }
        }

        log.info("解析返回数据为: {}", JsonUtils.toJsonString(execResp));

        Optional<PythonCodeExecResp.ExecRespData> data = Optional.ofNullable(execResp.getData());
        if (data.isPresent()) {
            PythonCodeExecResp.ExecRespData execRespData = data.get();
            SkillResultVo vo = new SkillResultVo();
            vo.setSuccess(true);
            vo.setCode(0);
            vo.setOutputStr(execRespData.getStdout());
            vo.setMessage("ok");
            vo.setOutput(execRespData.getOutputs().stream().map(e -> {
                SkillParamValue skillParamValue = new SkillParamValue();
                skillParamValue.setValue(this.convertValue(e));
                skillParamValue.setType(e.getType());
                skillParamValue.setName(e.getKey());
                skillParamValue.setDescription(e.getDescription());
                skillParamValue.setCollectionType(e.getCollectionType());
                skillParamValue.setId(e.getId());
                return skillParamValue;
            }).toList());
            return vo;
        } else {
            throw new ServiceException(execResp.getCode(), execResp.getMessage());
        }
    }

    private Object convertValue(PythonCodeParamDto e) {
        Object value = e.getValue();
        if (!Objects.equals(e.getType(), "JSON")) {
            return value;
        }
        if (Objects.equals(e.getCollectionType(), "ARRAY") && value instanceof List<?> list) {
            return list.stream().map(JSON::toJSONString).toList();
        }
        return JSON.toJSONString(value);
    }

    private List<PythonCodeParamDto> pythonCodeInput(List<SkillParamValue> input) {
        return input.stream().map(e -> {
            PythonCodeParamDto dto = new PythonCodeParamDto();
            dto.setType(e.getType());
            dto.setKey(e.getName());
            dto.setValue(e.getValue());
            return dto;
        }).toList();
    }

    @NotNull
    private PythonCodeParamDto getPythonCodeParamDto(Map<String, Object> map) {
        PythonCodeParamDto dto = new PythonCodeParamDto();
        dto.setType(String.valueOf(map.get("type")));
        dto.setKey(String.valueOf(map.get("name")));
        dto.setValue(String.valueOf(map.get("value")));
        dto.setDescription(map.get("description") == null ? "" : String.valueOf(map.get("description")));
        dto.setCollectionType(map.get("collectionType") == null ? "" : String.valueOf(map.get("collectionType")));
        dto.setId(String.valueOf(map.get("id")));
        return dto;
    }

    @Override
    public SkillSaveVo checkSkillSaveDto(SkillSaveDto skillSaveDto) {

        if (StringUtils.isBlank(skillSaveDto.getToolType())) {
            return SkillSaveVo.builder()
                    .code(SkillErrorCode.SKILL_SAVE_FAILED.getCode())
                    .success(false)
                    .message("分类不能为空")
                    .build();
        }

        return null;
    }

    @Override
    public void savePublishAfterHandler(SkillSaveDto skillSaveDto) {
//        ComponentSaveDto componentSaveDto = new ComponentSaveDto();
//        JSONObject extendedData = skillSaveDto.getExtendedData();
//        componentSaveDto.setPccSn(extendedData.getString("pccSn"));
//        componentSaveDto.setOperator(1);
//        componentSaveDto.setName(skillSaveDto.getSkillName());
//        String code = extendedData.getString("code");
//        JSONObject inputParameters = JSONObject.of("code", code);
//        JSONArray inputArray = extendedData.getJSONArray("input");
//        List<ProcessVariable> variables = new ArrayList<>();
//        List<String> input = getParameters(inputParameters, inputArray, variables, "input");
//        inputParameters.put("input", String.join(",", input));
//        JSONObject outputParameters = JSONObject.of();
//        JSONArray outputArray = extendedData.getJSONArray("output");
//        this.getParameters(outputParameters, outputArray, variables, "output");
//        JSONObject nodeData = JSONObject.of(
//                "code", code,
//                "input", List.of(),
//                "output", List.of(),
//                "name", skillSaveDto.getSkillName(),
//                "timeout", extendedData.getIntValue("timeout", 720)
//        );
//        nodeData.put("skillSn", skillSaveDto.getSkillSn());
//        JSONObject componentData = JSONObject.of(
//                "executeType", "PYTHON_CODE_EXEC",
//                "element", "serviceAction",
//                "inputParameters", inputParameters,
//                "outputParameters", extendedData.getJSONArray("output"),
//                "nodeData", nodeData
//        );
//        componentData.put("desc", skillSaveDto.getSkillDesc());
//        componentData.put("timeout", extendedData.getIntValue("timeout", 720));
//        componentSaveDto.setType(extendedData.getString("skillCategory"));
//        componentSaveDto.setComponentData(componentData);
//        componentSaveDto.setVariables(variables);
//        ResultVo<ComponentSaveVo> resultVo = processServiceClient.saveComponent(UserContext.getAuthorization(), componentSaveDto);
//        if (!resultVo.isSuccess() || !resultVo.getData().getSuccess()) {
//            throw new ServiceException(SkillErrorCode.CUSTOM_TOOL_SAVE_ERROR, resultVo.getData().getMessage());
//        }
//        extendedData.put("pccSn", resultVo.getData().getPccSn());
    }

    @Override
    public void deleteAfterHandler(Tool skillInfo) {

    }

//    @Override
//    public void deleteAfterHandler(SkillInfo skillInfo) {
//        String extendedData = skillInfo.getExtendedData();
//        JSONObject extendedDataVo = JSONObject.parseObject(extendedData);
//        if (extendedDataVo.get("pccSn") != null && !StringUtil.isBlank(extendedDataVo.getString("pccSn"))) {
//            ResultVo<Boolean> pccSn = processServiceClient.deleteComponent(String.valueOf(extendedDataVo.get("pccSn")));
//            if (!pccSn.isSuccess() || !pccSn.getData()) {
//                throw new ServiceException(SkillErrorCode.CUSTOM_TOOL_DELETE_ERROR);
//            }
//        }
//    }

//    private List<String> getParameters(JSONObject parameters, JSONArray array, List<ProcessVariable> variables, String type) {
//        return array.stream().map(e -> {
//            Map<String, Object> map = (LinkedHashMap<String, Object>) e;
//            parameters.put(String.valueOf(map.get("name")), map.get("id"));
//            variables.add(this.getVariables(map, type));
//            return String.valueOf(map.get("id"));
//        }).toList();
//    }

//    private ProcessVariable getVariables(Map<String, Object> map, String type) {
//        ProcessVariable processVariable = new ProcessVariable();
//        processVariable.setId(String.valueOf(map.get("id")));
//        processVariable.setName(String.valueOf(map.get("name")));
//        processVariable.setCollectionType(CollectionTypeEnum.fromString(String.valueOf(map.get("collectionType"))));
//        processVariable.setType(DataTypeEnum.fromString(String.valueOf(map.get("type"))));
//        String inputType = map.get("inputType") == null ? "" : String.valueOf(map.get("inputType"));
//        processVariable.setInputType(StringUtils.isBlank(inputType) ? InputTypeEnum.NONE : InputTypeEnum.fromString(inputType));
//        processVariable.setCategory(VariableCategoryEnum.USER);
//        if ("input".equals(type)) {
//            processVariable.setRequired(map.get("required") == null ? null : (Boolean) map.get("required"));
//        } else {
//            processVariable.setRequired(false);
//        }
//        processVariable.setOptions(map.get("options") == null ? null : ((List<String>) map.get("options")));
//        processVariable.setDescription(map.get("description") == null ? "" : String.valueOf(map.get("description")));
//        return processVariable;
//    }


}
