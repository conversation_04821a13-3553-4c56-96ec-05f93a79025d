package com.ai.application.app.api.dto.query;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.ai.framework.core.vo.PageParam;

import java.util.List;

/**
 * 应用用户表 查询条件
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(name = "应用用户表QueryDTO不带分页")
public class AppUserQueryDTO {
    /**
     * 部门id集合
     */
    @Schema(description = "部门id集合")
    private List<Integer> deptIds;

    /**
     * 用户名称
     */
    @Schema(description = "用户名")
    private String userName;


    /**
     * 用户id集合
     */
    @Schema(description = "用户id集合")
    private List<Integer> userIds;
}