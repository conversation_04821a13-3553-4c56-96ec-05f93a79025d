package com.ai.application.agent.base.api.mapstruct;
import com.ai.application.agent.base.api.entity.AgentVersionExtend;
import com.ai.application.agent.base.api.dto.AgentVersionExtendDTO;
import com.ai.application.agent.base.api.vo.AgentVersionExtendVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * agent版本扩展信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */

@Mapper(componentModel = "spring")
public interface AgentVersionExtendMapstruct {

    AgentVersionExtend toEntity(AgentVersionExtendDTO dto);
    List<AgentVersionExtend> toEntityList(List<AgentVersionExtendDTO> dtolist);
    AgentVersionExtendVO toVo(AgentVersionExtend entity);
    List<AgentVersionExtendVO> toVoList(List<AgentVersionExtend> entities);
}
