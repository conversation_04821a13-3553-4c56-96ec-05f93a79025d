package com.ai.application.agent.run.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 文档知识检索请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocKnowledgeSearchRequestDTO {

    /**
     * 知识库编号
     */
    private String knowledgeInventorySn;

    /**
     * 知识库编号列表
     */
    private List<String> knowledgeInventorySnList;

    /**
     * 检索方式：rule规则检索、embedding语义检索、keywords关键词检索
     */
    private String type;

    /**
     * 检索条件
     */
    private Object conditions;

    /**
     * 检索内容：documentName文档名称、documentContent文档内容、documentSummary文档摘要、documentTag标签
     */
    private String searchContent;

    /**
     * 检索文档内容
     */
    private String searchKnowledgeContent;

    /**
     * 逻辑关系：and满足全部、or满足任一
     */
    private String logic;

    /**
     * 输出文档数量：为空时全部按照最大阈值，不为空按照输出数量
     */
    private String topK;

    /**
     * 知识类型
     */
    private String knowledgeType;

    /**
     * 规则检索条件DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RuleConditionDTO {
        /**
         * 目标节点
         */
        private String target;

        /**
         * 表达式列表
         */
        private List<ExpressionDTO> expression;
    }

    /**
     * 表达式DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExpressionDTO {
        /**
         * 字段名：documentTag知识标签、documentSummary摘要、documentName文档名称、documentSuffix文档后缀
         */
        private String field;

        /**
         * 操作符：equals等于、contains包含、startsWith开始于、endsWith结束于
         */
        private String operator;

        /**
         * 值
         */
        private String value;
    }

    /**
     * 关键词检索词汇DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VocabularyWordDTO {
        /**
         * 词汇
         */
        private String word;

        /**
         * 权重
         */
        private Double weight;
    }
}
