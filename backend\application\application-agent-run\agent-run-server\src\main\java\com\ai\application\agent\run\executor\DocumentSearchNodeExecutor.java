package com.ai.application.agent.run.executor;

import com.ai.application.agent.run.dto.DocumentSearchRequestDTO;
import com.ai.application.agent.run.dto.DocumentSearchResultDTO;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.service.IDocumentSearchService;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import com.ai.framework.workflow.excutor.NodeExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 文档知识检索节点执行器
 * 用于在工作流中执行文档知识检索功能
 */
@Slf4j
@Component
public class DocumentSearchNodeExecutor implements NodeExecutor {

    @Autowired
    private IDocumentSearchService documentSearchService;

    @Override
    public void execute(WorkflowContext context) {
        String nodeKey = context.getCurrentNodeKey();
        NodeContext nodeCtx = context.getNodeContexts().get(nodeKey);
        Map<String, Object> nodeDef = nodeCtx.getNodeDefinition();

        log.info("DocumentSearchNodeExecutor execute start, nodeKey: {}, nodeDef: {}", nodeKey, JsonUtils.toJsonString(nodeDef));

        try {
            // 设置节点状态为运行中
            nodeCtx.setStatus(NodeStatus.RUNNING);

            // 初始化节点输出
            if (nodeCtx.getOutput() == null) {
                nodeCtx.setOutput(new HashMap<>());
            }

            // 从节点定义中获取输入参数
            Map<String, Object> inputParameters = (Map<String, Object>) nodeDef.get("inputParameters");
            if (inputParameters == null) {
                throw new ServiceException(ExecutorError.NODE_DEFINITION_IS_NULL);
            }

            // 构建文档检索请求
            DocumentSearchRequestDTO request = buildDocumentSearchRequest(inputParameters, context);

            // 执行文档检索
            String authorization = (String) context.getGlobalVars().get("authorization");
            ResultVo<DocumentSearchResultDTO> result = documentSearchService.executeDocumentSearch(request, authorization);

            if (result.getCode() != 0) {
                throw new ServiceException(result.getCode(), result.getMessage());
            }

            DocumentSearchResultDTO searchResult = result.getData();
            if (searchResult == null) {
                throw new ServiceException(ExecutorError.DOCUMENT_SEARCH_RESULT_IS_NULL);
            }

            // 构建输出结果
            Map<String, Object> outputResult = buildOutputResult(searchResult);

            // 将结果写入输出参数
            writeOutputParameters(nodeDef, context, outputResult);

            // 设置节点输出
            nodeCtx.getOutput().putAll(outputResult);

            // 设置节点状态为成功
            nodeCtx.setStatus(NodeStatus.SUCCESS);
            nodeCtx.setEndTime(java.time.LocalDateTime.now());

            log.info("DocumentSearchNodeExecutor execute success, result: {}", JsonUtils.toJsonString(outputResult));

        } catch (Exception e) {
            log.error("DocumentSearchNodeExecutor execute error", e);
            nodeCtx.setStatus(NodeStatus.FAILED);
            nodeCtx.setErrorMsg("文档知识检索执行失败: " + e.getMessage());
            nodeCtx.setEndTime(java.time.LocalDateTime.now());
            throw e;
        }
    }

    /**
     * 构建文档检索请求
     */
    private DocumentSearchRequestDTO buildDocumentSearchRequest(Map<String, Object> inputParameters, WorkflowContext context) {
        // 获取参数值，支持变量替换
        String type = getParameterValue(inputParameters, "type", context);
        String searchModel = getParameterValue(inputParameters, "searchModel", context);
        String knowledgeInventorySn = getParameterValue(inputParameters, "knowledgeInventorySn", context);
        String knowledgeType = getParameterValue(inputParameters, "knowledgeType", context);
        String searchContent = getParameterValue(inputParameters, "searchContent", context);
        String searchKnowledgeContent = getParameterValue(inputParameters, "searchKnowledgeContent", context);
        String topKStr = getParameterValue(inputParameters, "topK", context);
        String embeddingStr = getParameterValue(inputParameters, "embedding", context);
        String keywordStr = getParameterValue(inputParameters, "keyword", context);
        String textStr = getParameterValue(inputParameters, "text", context);

        // 获取知识库编号列表
        Object knowledgeInventorySnListObj = inputParameters.get("knowledgeInventorySnList");
        List<String> knowledgeInventorySnList = parseStringList(knowledgeInventorySnListObj, context);

        // 获取知识编号列表
        Object knowledgeSnObj = inputParameters.get("knowledgeSn");
        List<String> knowledgeSn = parseStringList(knowledgeSnObj, context);

        // 获取检索条件
        Object conditionsObj = inputParameters.get("conditions");
        List<DocumentSearchRequestDTO.SearchConditionDTO> conditions = parseConditions(conditionsObj, context);

        // 获取扩展参数
        Object expandParamsObj = inputParameters.get("expandParams");
        DocumentSearchRequestDTO.ExpandSearchParamsDTO expandParams = parseExpandParams(expandParamsObj, context);

        // 获取手动检索知识
        Object searchKnowledge = getParameterObject(inputParameters, "searchKnowledge", context);

        // 参数校验
        validateParameters(type, knowledgeInventorySn, knowledgeInventorySnList, searchContent, searchKnowledgeContent);

        return DocumentSearchRequestDTO.builder()
                .type(type)
                .searchModel(searchModel)
                .knowledgeInventorySn(knowledgeInventorySn)
                .knowledgeInventorySnList(knowledgeInventorySnList)
                .knowledgeSn(knowledgeSn)
                .knowledgeType(knowledgeType)
                .searchContent(searchContent)
                .searchKnowledgeContent(searchKnowledgeContent)
                .topK(parseInteger(topKStr))
                .embedding(parseInteger(embeddingStr))
                .keyword(parseInteger(keywordStr))
                .text(parseInteger(textStr))
                .conditions(conditions)
                .expandParams(expandParams)
                .searchKnowledge(searchKnowledge)
                .build();
    }

    /**
     * 参数校验
     */
    private void validateParameters(String type, String knowledgeInventorySn, List<String> knowledgeInventorySnList,
                                  String searchContent, String searchKnowledgeContent) {
        if (StringUtils.isBlank(searchContent) && StringUtils.isBlank(searchKnowledgeContent)) {
            throw new ServiceException(ExecutorError.DOCUMENT_SEARCH_CONTENT_IS_BLANK);
        }
        if (StringUtils.isBlank(knowledgeInventorySn) && CollectionUtils.isEmpty(knowledgeInventorySnList)) {
            throw new ServiceException(ExecutorError.KNOWLEDGE_INVENTORY_SN_IS_NULL);
        }
    }

    /**
     * 解析字符串列表
     */
    private List<String> parseStringList(Object obj, WorkflowContext context) {
        if (obj == null) {
            return new ArrayList<>();
        }

        List<String> result = new ArrayList<>();
        
        if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            for (Object item : list) {
                if (item != null) {
                    String value = item.toString();
                    // 支持变量替换
                    if (value.startsWith("$")) {
                        String varName = value.substring(1);
                        Object varValue = context.getGlobalVars().get(varName);
                        if (varValue != null) {
                            result.add(varValue.toString());
                        }
                    } else {
                        result.add(value);
                    }
                }
            }
        } else {
            String value = obj.toString();
            // 支持变量替换
            if (value.startsWith("$")) {
                String varName = value.substring(1);
                Object varValue = context.getGlobalVars().get(varName);
                if (varValue != null) {
                    result.add(varValue.toString());
                }
            } else {
                result.add(value);
            }
        }

        return result;
    }

    /**
     * 解析检索条件
     */
    private List<DocumentSearchRequestDTO.SearchConditionDTO> parseConditions(Object obj, WorkflowContext context) {
        if (obj == null) {
            return new ArrayList<>();
        }

        List<DocumentSearchRequestDTO.SearchConditionDTO> conditions = new ArrayList<>();
        
        if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            for (Object item : list) {
                if (item instanceof Map) {
                    Map<?, ?> conditionMap = (Map<?, ?>) item;
                    DocumentSearchRequestDTO.SearchConditionDTO condition = new DocumentSearchRequestDTO.SearchConditionDTO();
                    condition.setTarget(getStringValue(conditionMap.get("target")));
                    condition.setValue(getStringValue(conditionMap.get("value")));
                    condition.setOperator(getStringValue(conditionMap.get("operator")));
                    conditions.add(condition);
                }
            }
        }

        return conditions;
    }

    /**
     * 解析扩展参数
     */
    private DocumentSearchRequestDTO.ExpandSearchParamsDTO parseExpandParams(Object obj, WorkflowContext context) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof Map) {
            Map<?, ?> expandMap = (Map<?, ?>) obj;
            return DocumentSearchRequestDTO.ExpandSearchParamsDTO.builder()
                    .fragmentType(getStringValue(expandMap.get("fragmentType")))
                    .length(getIntegerValue(expandMap.get("length")))
                    .forward(getIntegerValue(expandMap.get("forward")))
                    .backward(getIntegerValue(expandMap.get("backward")))
                    .expand(expandMap.get("expand"))
                    .build();
        }

        return null;
    }

    /**
     * 获取参数值，支持变量替换
     */
    private String getParameterValue(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        String strValue = value.toString();
        
        // 如果是变量引用（以$开头），从全局变量中获取
        if (strValue.startsWith("$")) {
            String varName = strValue.substring(1);
            Object varValue = context.getGlobalVars().get(varName);
            return varValue != null ? varValue.toString() : null;
        }
        
        return strValue;
    }

    /**
     * 获取参数对象，支持变量替换
     */
    private Object getParameterObject(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        if (value instanceof String) {
            String strValue = value.toString();
            // 如果是变量引用（以$开头），从全局变量中获取
            if (strValue.startsWith("$")) {
                String varName = strValue.substring(1);
                return context.getGlobalVars().get(varName);
            }
        }
        
        return value;
    }

    /**
     * 解析整数
     */
    private Integer parseInteger(String str) {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        try {
            return Integer.parseInt(str);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 获取字符串值
     */
    private String getStringValue(Object obj) {
        return obj != null ? obj.toString() : null;
    }

    /**
     * 获取整数值
     */
    private Integer getIntegerValue(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return Integer.parseInt(obj.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 构建输出结果
     */
    private Map<String, Object> buildOutputResult(DocumentSearchResultDTO searchResult) {
        Map<String, Object> result = new HashMap<>();
        
        // 设置主要输出
        result.put("docFile", searchResult.getOutput() != null ? searchResult.getOutput() : "[]");
        result.put("output", searchResult.getOutput() != null ? searchResult.getOutput() : "[]");
        
        // 设置片段信息
        if (searchResult.getFragments() != null) {
            result.put("fragments", searchResult.getFragments());
        } else {
            result.put("fragments", Collections.emptyList());
        }
        
        // 设置执行状态
        result.put("success", searchResult.getSuccess() != null ? searchResult.getSuccess() : false);
        result.put("errorMessage", searchResult.getErrorMessage());
        result.put("totalCount", searchResult.getTotalCount() != null ? searchResult.getTotalCount() : 0);

        return result;
    }

    /**
     * 写入输出参数
     */
    private void writeOutputParameters(Map<String, Object> nodeDef, WorkflowContext context, Map<String, Object> result) {
        Map<String, Object> outputParameters = (Map<String, Object>) nodeDef.get("outputParameters");
        if (outputParameters != null) {
            for (Map.Entry<String, Object> entry : outputParameters.entrySet()) {
                String outputKey = entry.getKey();
                String variableName = entry.getValue().toString();
                
                Object resultValue = result.get(outputKey);
                if (resultValue != null) {
                    context.setVar(variableName, resultValue);
                    log.info("Set variable {} = {}", variableName, resultValue);
                }
            }
        }
    }

    @Override
    public String getType() {
        return "DOCUMENT_KNOWLEDGE_SEARCH";
    }
}
