package com.ai.application.base.file.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static org.springframework.util.StreamUtils.BUFFER_SIZE;

@Slf4j
public class ZipUtils {

    /**
     * @param datas   Map数组，key是文件名，byte[]是数据
     * @param output  输出流
     * @param charset 编码
     */
    public static void zipStringToOutputStream(Map<String, byte[]> datas, OutputStream output, String charset) {
        ZipOutputStream zipOut = new ZipOutputStream(new BufferedOutputStream(output));

        Set<Map.Entry<String, byte[]>> dataEntry = datas.entrySet();
        for (Map.Entry<String, byte[]> data : dataEntry) {
            try {

                if (StringUtils.isNotBlank(data.getKey()) && data.getValue() != null && data.getValue().length > 0) {
                    InputStream bufferIn = new BufferedInputStream(new ByteArrayInputStream(data.getValue()));
                    byte[] bs = new byte[BUFFER_SIZE];
                    Arrays.fill(bs, (byte) 0);
                    zipOut.putNextEntry(new ZipEntry(data.getKey()));//创建压缩文件内的文件
                    int len = -1;
                    while ((len = bufferIn.read(bs)) > 0) {//写入文件内容
                        zipOut.write(bs, 0, len);
                    }
                    bufferIn.close();
                }

            } catch (Exception e) {
                log.error("字符串ZIP流写入压缩文件[{}]失败，忽略该文件压缩：[{}]", data.getKey(), e.toString());
            }
        }
        IOUtils.closeQuietly(zipOut);

    }

}
