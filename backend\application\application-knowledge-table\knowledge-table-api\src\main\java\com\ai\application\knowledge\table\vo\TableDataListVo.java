package com.ai.application.knowledge.table.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.Map;

@Data
public class TableDataListVo {

    @Schema(description = "行id")
    private String rowId;

    @Schema(description = "行数据")
    private Map<String, Definition> fieldSnMap;

    @Data
    @Accessors(chain = true)
    public static class Definition {

        @Schema(description = "值")
        private String data;

        @Schema(description = "值类型  FILE - 文件 TEXT - 文本")
        private String fieldType;

        @Schema(description = "文件SN")
        private String fileSn;

        @Schema(description = "文件名称")
        private String fileName;
    }

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Schema(description = "创建人姓名")
    private String creator;

    @Schema(description = "更新人姓名")
    private String updater;

    @Schema(description = "创建用户id")
    private Integer createUserId;

    @Schema(description = "更新用户id")
    private Integer updateUserId;

}
