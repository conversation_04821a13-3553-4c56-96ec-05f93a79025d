<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.knowledge.dict.mapper.KnowledgeDictWordMapper">
    <select id="list" resultType="com.ai.application.knowledge.dict.vo.DictWordListVo"
            parameterType="com.ai.application.knowledge.dict.dto.DictWordListDto">

        select
        word_id as wordId,
        word_name as wordName,
        word_alias as wordAlias,
        word_desc as wordDesc,
        word_status as wordStatus,
        dict_id as dictId,
        create_time as createTime,
        update_time as updateTime,
        create_user_id as createUserId,
        update_user_id as updateUserId
        from knowledge_dict_word
        where dict_id = #{dictId}
        and word_status != -1
        <if test="keyword != null and keyword != ''">
            and (word_name like concat('%', #{keyword}, '%')
            or word_desc like concat('%', #{keyword}, '%')
            or create_user_id in
            <foreach item="item" index="index" collection="userIdsByName" open="(" separator="," close=")">
                #{item}
            </foreach>)
        </if>
        <if test="createTimeStart != null and createTimeEnd != null">
            AND create_time BETWEEN #{createTimeStart} AND #{createTimeEnd}
        </if>
        <if test="updateTimeStart != null and updateTimeEnd != null">
            AND update_time BETWEEN #{updateTimeStart} AND #{updateTimeEnd}
        </if>
        order by create_time desc
    </select>
</mapper>
