package com.ai.application.base.model.api.mapstruct;

import com.ai.application.base.model.api.dto.ModelTestDTO;
import com.ai.application.base.model.api.entity.ModelTest;
import com.ai.application.base.model.api.vo.ModelTestVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T09:54:02+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class ModelTestMapstructImpl implements ModelTestMapstruct {

    @Override
    public ModelTest toEntity(ModelTestDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ModelTest modelTest = new ModelTest();

        modelTest.setTestId( dto.getTestId() );
        modelTest.setTestConfig( dto.getTestConfig() );
        modelTest.setTestStatus( dto.getTestStatus() );
        modelTest.setTestResult( dto.getTestResult() );
        modelTest.setModelId( dto.getModelId() );
        modelTest.setTenantId( dto.getTenantId() );
        modelTest.setCreateTime( dto.getCreateTime() );
        modelTest.setUpdateTime( dto.getUpdateTime() );

        return modelTest;
    }

    @Override
    public List<ModelTest> toEntityList(List<ModelTestDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<ModelTest> list = new ArrayList<ModelTest>( dtolist.size() );
        for ( ModelTestDTO modelTestDTO : dtolist ) {
            list.add( toEntity( modelTestDTO ) );
        }

        return list;
    }

    @Override
    public ModelTestVO toVo(ModelTest entity) {
        if ( entity == null ) {
            return null;
        }

        ModelTestVO modelTestVO = new ModelTestVO();

        modelTestVO.setTestId( entity.getTestId() );
        modelTestVO.setTestConfig( entity.getTestConfig() );
        modelTestVO.setTestStatus( entity.getTestStatus() );
        modelTestVO.setTestResult( entity.getTestResult() );
        modelTestVO.setModelId( entity.getModelId() );
        modelTestVO.setTenantId( entity.getTenantId() );
        modelTestVO.setCreateTime( entity.getCreateTime() );
        modelTestVO.setUpdateTime( entity.getUpdateTime() );

        return modelTestVO;
    }

    @Override
    public List<ModelTestVO> toVoList(List<ModelTest> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ModelTestVO> list = new ArrayList<ModelTestVO>( entities.size() );
        for ( ModelTest modelTest : entities ) {
            list.add( toVo( modelTest ) );
        }

        return list;
    }
}
