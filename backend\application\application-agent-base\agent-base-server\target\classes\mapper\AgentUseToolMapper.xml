<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.agent.base.mapper.AgentUseToolMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.agent.base.api.entity.AgentUseTool">
                    <id column="atl_id" property="atlId" />
                    <result column="atl_status" property="atlStatus" />
                    <result column="agent_id" property="agentId" />
                    <result column="version_id" property="versionId" />
                    <result column="tool_id" property="toolId" />
                    <result column="tool_extend" property="toolExtend" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        atl_id, atl_status, agent_id, version_id, tool_id, tool_extend, create_time, update_time
    </sql>

    <select id="selectUseToolByPage" resultType="com.ai.application.agent.base.api.vo.AgentUseToolQueryVO">
        select
        <include refid="com.ai.application.agent.base.mapper.AgentUseToolMapper.Base_Column_List"></include>
        from agent_use_tool
        order by create_time desc;
    </select>
</mapper>