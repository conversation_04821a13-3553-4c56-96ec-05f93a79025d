{"element": "serviceAction", "id": "shortTermMemoryStore001", "name": "短期记忆存储", "type": "SHORT_TERM_MEMORY_STORE", "inputParameters": {"agent_id": "agent_001", "user_id": "user_001", "bucketSn": "conversation", "memoryContent": "用户询问了今天的天气情况", "memoryContents": [{"memoryContent": "用户表达了对天气的关心", "bucketSn": "conversation"}, {"memoryContent": "用户希望了解明天的天气预报", "bucketSn": "weather"}]}, "outputParameters": {"output": "storeResult", "success": "storeSuccess", "memoryCount": "storedMemoryCount", "memoryIds": "storedMemoryIds"}}