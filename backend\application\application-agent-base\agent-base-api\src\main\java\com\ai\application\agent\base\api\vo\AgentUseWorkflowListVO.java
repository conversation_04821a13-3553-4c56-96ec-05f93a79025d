package com.ai.application.agent.base.api.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 智能体工作流表
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Data
@Schema(name = "")
public class AgentUseWorkflowListVO {
    /**
     * 工作流id
     */
    @Schema(description = "工作流id")
    private Integer flowId;

    /**
     * 工作流变量定义
     */
    @Schema(description = "工作流变量定义")
    private String flowVariables;

    /**
     * 工作流定义
     */
    @Schema(description = "工作流定义")
    private String flowDefinition;

    /**
     * 额外定义参数
     */
    @Schema(description = "额外定义参数")
    private String flowExtensions;

    /**
     * 开始变量
     */
    @Schema(description = "开始变量")
    private String flowStartVariables;

    /**
     * 结束变量
     */
    @Schema(description = "结束变量")
    private String flowEndVariables;

    /**
     * 使用引导
     */
    @Schema(description = "使用引导")
    private String flowGuide;

    /**
     * 启用状态 0未发布 1发布 -1废弃
     */
    @Schema(description = "启用状态 0未发布 1发布 -1废弃")
    private Integer flowStatus;

    /**
     * 智能体id
     */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
     * 智能体版本id
     */
    @Schema(description = "智能体版本id")
    private Integer versionId;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}