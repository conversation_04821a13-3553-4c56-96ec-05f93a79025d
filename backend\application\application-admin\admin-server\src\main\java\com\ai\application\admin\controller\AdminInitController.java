package com.ai.application.admin.controller;

import com.ai.application.admin.service.IAdminUserService;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 应用用户表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Tag(name = "管理后台-数据初始化", description = "管理后台-数据初始化相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1/data")
public class AdminInitController {

    @Autowired
    private IAdminUserService adminUserService;

    @Operation(summary = "超管初始化")
    @PostMapping("/init")
    public ResultVo<Void> initAdmin() {
        adminUserService.initAdmin();
        return ResultVo.success("初始化成功");
    }

}