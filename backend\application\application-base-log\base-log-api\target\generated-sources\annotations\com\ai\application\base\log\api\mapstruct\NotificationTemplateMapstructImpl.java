package com.ai.application.base.log.api.mapstruct;

import com.ai.application.base.log.api.dto.NotificationTemplateAddDTO;
import com.ai.application.base.log.api.dto.NotificationTemplateDTO;
import com.ai.application.base.log.api.dto.NotificationTemplateUpdateDTO;
import com.ai.application.base.log.api.entity.NotificationTemplate;
import com.ai.application.base.log.api.vo.NotificationTemplatePageVO;
import com.ai.application.base.log.api.vo.NotificationTemplateVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-12T18:43:53+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class NotificationTemplateMapstructImpl implements NotificationTemplateMapstruct {

    @Override
    public NotificationTemplate toEntity(NotificationTemplateDTO dto) {
        if ( dto == null ) {
            return null;
        }

        NotificationTemplate notificationTemplate = new NotificationTemplate();

        notificationTemplate.setNtplId( dto.getNtplId() );
        notificationTemplate.setNtplType( dto.getNtplType() );
        notificationTemplate.setNtplName( dto.getNtplName() );
        notificationTemplate.setNtplContent( dto.getNtplContent() );
        notificationTemplate.setNtplStatus( dto.getNtplStatus() );
        notificationTemplate.setNtplConfig( dto.getNtplConfig() );
        notificationTemplate.setTenantId( dto.getTenantId() );
        notificationTemplate.setCreateTime( dto.getCreateTime() );
        notificationTemplate.setUpdateTime( dto.getUpdateTime() );

        return notificationTemplate;
    }

    @Override
    public NotificationTemplate toEntity(NotificationTemplateAddDTO dto) {
        if ( dto == null ) {
            return null;
        }

        NotificationTemplate notificationTemplate = new NotificationTemplate();

        notificationTemplate.setNtplName( dto.getNtplName() );
        notificationTemplate.setNtplContent( dto.getNtplContent() );

        return notificationTemplate;
    }

    @Override
    public NotificationTemplate toEntity(NotificationTemplateUpdateDTO dto) {
        if ( dto == null ) {
            return null;
        }

        NotificationTemplate notificationTemplate = new NotificationTemplate();

        notificationTemplate.setNtplId( dto.getNtplId() );
        notificationTemplate.setNtplName( dto.getNtplName() );
        notificationTemplate.setNtplContent( dto.getNtplContent() );

        return notificationTemplate;
    }

    @Override
    public List<NotificationTemplate> toEntityList(List<NotificationTemplateDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<NotificationTemplate> list = new ArrayList<NotificationTemplate>( dtolist.size() );
        for ( NotificationTemplateDTO notificationTemplateDTO : dtolist ) {
            list.add( toEntity( notificationTemplateDTO ) );
        }

        return list;
    }

    @Override
    public NotificationTemplateVO toVo(NotificationTemplate entity) {
        if ( entity == null ) {
            return null;
        }

        NotificationTemplateVO notificationTemplateVO = new NotificationTemplateVO();

        notificationTemplateVO.setNtplId( entity.getNtplId() );
        notificationTemplateVO.setNtplType( entity.getNtplType() );
        notificationTemplateVO.setNtplName( entity.getNtplName() );
        notificationTemplateVO.setNtplContent( entity.getNtplContent() );
        notificationTemplateVO.setNtplStatus( entity.getNtplStatus() );
        notificationTemplateVO.setNtplConfig( entity.getNtplConfig() );

        return notificationTemplateVO;
    }

    @Override
    public List<NotificationTemplateVO> toVoList(List<NotificationTemplate> entities) {
        if ( entities == null ) {
            return null;
        }

        List<NotificationTemplateVO> list = new ArrayList<NotificationTemplateVO>( entities.size() );
        for ( NotificationTemplate notificationTemplate : entities ) {
            list.add( toVo( notificationTemplate ) );
        }

        return list;
    }

    @Override
    public List<NotificationTemplatePageVO> toPageVoList(List<NotificationTemplate> entities) {
        if ( entities == null ) {
            return null;
        }

        List<NotificationTemplatePageVO> list = new ArrayList<NotificationTemplatePageVO>( entities.size() );
        for ( NotificationTemplate notificationTemplate : entities ) {
            list.add( notificationTemplateToNotificationTemplatePageVO( notificationTemplate ) );
        }

        return list;
    }

    protected NotificationTemplatePageVO notificationTemplateToNotificationTemplatePageVO(NotificationTemplate notificationTemplate) {
        if ( notificationTemplate == null ) {
            return null;
        }

        NotificationTemplatePageVO notificationTemplatePageVO = new NotificationTemplatePageVO();

        notificationTemplatePageVO.setNtplId( notificationTemplate.getNtplId() );
        notificationTemplatePageVO.setNtplType( notificationTemplate.getNtplType() );
        notificationTemplatePageVO.setNtplName( notificationTemplate.getNtplName() );
        notificationTemplatePageVO.setNtplContent( notificationTemplate.getNtplContent() );
        notificationTemplatePageVO.setNtplStatus( notificationTemplate.getNtplStatus() );
        notificationTemplatePageVO.setNtplConfig( notificationTemplate.getNtplConfig() );
        notificationTemplatePageVO.setCreateTime( notificationTemplate.getCreateTime() );
        notificationTemplatePageVO.setUpdateTime( notificationTemplate.getUpdateTime() );

        return notificationTemplatePageVO;
    }
}
