package com.ai.application.task.api.mapstruct;

import com.ai.application.task.api.dto.TaskAttachmentDTO;
import com.ai.application.task.api.entity.TaskAttachment;
import com.ai.application.task.api.vo.TaskAttachmentVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-12T18:41:02+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class TaskAttachmentMapstructImpl implements TaskAttachmentMapstruct {

    @Override
    public TaskAttachment toEntity(TaskAttachmentDTO dto) {
        if ( dto == null ) {
            return null;
        }

        TaskAttachment taskAttachment = new TaskAttachment();

        taskAttachment.setAttachId( dto.getAttachId() );
        taskAttachment.setAttachStatus( dto.getAttachStatus() );
        taskAttachment.setProcessStartTime( dto.getProcessStartTime() );
        taskAttachment.setProcessEndTime( dto.getProcessEndTime() );
        taskAttachment.setProcessDuration( dto.getProcessDuration() );
        taskAttachment.setProcessInput( dto.getProcessInput() );
        taskAttachment.setProcessOutput( dto.getProcessOutput() );
        taskAttachment.setProcessFlag( dto.getProcessFlag() );
        taskAttachment.setProcessError( dto.getProcessError() );
        taskAttachment.setProcessMetadata( dto.getProcessMetadata() );
        taskAttachment.setRetryCount( dto.getRetryCount() );
        taskAttachment.setTokensUsed( dto.getTokensUsed() );
        taskAttachment.setFileResultPath( dto.getFileResultPath() );
        taskAttachment.setTaskId( dto.getTaskId() );
        taskAttachment.setTaskRunId( dto.getTaskRunId() );
        taskAttachment.setFileId( dto.getFileId() );
        taskAttachment.setAgentRunId( dto.getAgentRunId() );
        taskAttachment.setCreateTime( dto.getCreateTime() );
        taskAttachment.setUpdateTime( dto.getUpdateTime() );

        return taskAttachment;
    }

    @Override
    public List<TaskAttachment> toEntityList(List<TaskAttachmentDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<TaskAttachment> list = new ArrayList<TaskAttachment>( dtolist.size() );
        for ( TaskAttachmentDTO taskAttachmentDTO : dtolist ) {
            list.add( toEntity( taskAttachmentDTO ) );
        }

        return list;
    }

    @Override
    public TaskAttachmentVO toVo(TaskAttachment entity) {
        if ( entity == null ) {
            return null;
        }

        TaskAttachmentVO taskAttachmentVO = new TaskAttachmentVO();

        taskAttachmentVO.setAttachId( entity.getAttachId() );
        taskAttachmentVO.setAttachStatus( entity.getAttachStatus() );
        taskAttachmentVO.setProcessStartTime( entity.getProcessStartTime() );
        taskAttachmentVO.setProcessEndTime( entity.getProcessEndTime() );
        taskAttachmentVO.setProcessDuration( entity.getProcessDuration() );
        taskAttachmentVO.setProcessInput( entity.getProcessInput() );
        taskAttachmentVO.setProcessOutput( entity.getProcessOutput() );
        taskAttachmentVO.setProcessFlag( entity.getProcessFlag() );
        taskAttachmentVO.setProcessError( entity.getProcessError() );
        taskAttachmentVO.setProcessMetadata( entity.getProcessMetadata() );
        taskAttachmentVO.setRetryCount( entity.getRetryCount() );
        taskAttachmentVO.setTokensUsed( entity.getTokensUsed() );
        taskAttachmentVO.setFileResultPath( entity.getFileResultPath() );
        taskAttachmentVO.setTaskId( entity.getTaskId() );
        taskAttachmentVO.setTaskRunId( entity.getTaskRunId() );
        taskAttachmentVO.setFileId( entity.getFileId() );
        taskAttachmentVO.setAgentRunId( entity.getAgentRunId() );
        taskAttachmentVO.setCreateTime( entity.getCreateTime() );
        taskAttachmentVO.setUpdateTime( entity.getUpdateTime() );

        return taskAttachmentVO;
    }

    @Override
    public List<TaskAttachmentVO> toVoList(List<TaskAttachment> entities) {
        if ( entities == null ) {
            return null;
        }

        List<TaskAttachmentVO> list = new ArrayList<TaskAttachmentVO>( entities.size() );
        for ( TaskAttachment taskAttachment : entities ) {
            list.add( toVo( taskAttachment ) );
        }

        return list;
    }
}
