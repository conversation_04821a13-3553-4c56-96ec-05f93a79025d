<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.agent.base.mapper.AgentRunWorkflowMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.agent.base.api.entity.AgentRunWorkflow">
                    <id column="workflow_run_id" property="workflowRunId" />
                    <result column="workflow_name" property="workflowName" />
                    <result column="workflow_status" property="workflowStatus" />
                    <result column="workflow_input" property="workflowInput" />
                    <result column="workflow_output" property="workflowOutput" />
                    <result column="workflow_variables" property="workflowVariables" />
                    <result column="workflow_error" property="workflowError" />
                    <result column="workflow_duration" property="workflowDuration" />
                    <result column="workflow_snapshot" property="workflowSnapshot" />
                    <result column="workflow_start_time" property="workflowStartTime" />
                    <result column="workflow_end_time" property="workflowEndTime" />
                    <result column="current_node_run_id" property="currentNodeRunId" />
                    <result column="run_id" property="runId" />
                    <result column="step_id" property="stepId" />
                    <result column="flow_id" property="flowId" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        workflow_run_id, workflow_name, workflow_status, workflow_input, workflow_output, workflow_variables, workflow_error, workflow_duration, workflow_snapshot, workflow_start_time, workflow_end_time, current_node_run_id, run_id, step_id, flow_id, create_time, update_time
    </sql>

    <select id="getWorkflowCount" resultType="java.lang.Integer">
        SELECT
        COUNT(workflow_run_id)
        FROM agent_run_workflow A
        LEFT JOIN agent_use_workflow B ON(A.flow_id=B.flow_id)
        LEFT JOIN agent C ON(B.agent_id=C.agent_id)
        WHERE C.tenant_id=#{tenantId}
        <if test="userId!=null and userId >0">
            and C.user_id=#{userId}
        </if>
        <if test="days!=null and days = 7">
            and A.create_time>=DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        </if>
        <if test="days = 30">
            and A.create_time>=DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        </if>
        <if test="startDate!=null and endDate !=null">
            and A.create_time BETWEEN #{startDate} and #{endDate}
        </if>
    </select>
    <select id="queryWorkflowCountByAgent" resultType="com.ai.application.agent.base.api.vo.AgentUseDetailVO">
        SELECT
        B.agent_sn,
        ROUND(AVG(A.workflow_run_id),0) workFlowMetrics
        FROM agent_run_workflow A
        LEFT JOIN agent_use_workflow B ON(A.flow_id=B.flow_id)
        LEFT JOIN agent C ON(B.agent_id=C.agent_id)
        WHERE C.tenant_id=#{tenantId}
        <if test="userId!=null and userId >0">
            and C.user_id=#{userId}
        </if>
        <if test="startDate!=null and endDate !=null">
            and A.create_time BETWEEN #{startDate} and #{endDate}
        </if>
        GROUP BY C.agent_sn
    </select>
</mapper>