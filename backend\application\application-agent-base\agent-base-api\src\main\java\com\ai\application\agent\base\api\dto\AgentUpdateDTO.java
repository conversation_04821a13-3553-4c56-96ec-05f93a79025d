package com.ai.application.agent.base.api.dto;

import com.ai.application.agent.base.api.bo.AgentMetadataBO;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;

/**
 * 智能体表
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@Schema(name = "智能体表DTO")
public class AgentUpdateDTO {
    /**
     * 智能体sn
     */
    @Schema(description = "智能体sn")
    private String agentSn;

    /**
     * 智能体名称
     */
    @Schema(description = "智能体名称")
    private String agentName;

    /**
     * 智能体描述
     */
    @Schema(description = "智能体描述")
    private String agentDesc;

    /**
     * 智能体类型: 10:对话流, 20:工作流, 30:master
     */
    @Schema(description = "智能体类型: 10:对话流, 20:工作流, 30:master")
    private Integer agentType;

    /**
     * 智能体元信息:logo,icon,创建人名等
     */
    @Schema(description = "智能体元信息:logo,icon,创建人名等")
    private AgentMetadataBO agentMetadata;

    /**
     * 推荐问题
     */
    @Schema(description = "推荐问题")
    private List<String> recommends;

    @Schema(description = "使用说明")
    private String explain;

    @Schema(description = "记忆开关")
    private Boolean memorySwitch;
}