package com.ai.application.base.file.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 应用文件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-29
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("app_file")
@Builder
public class AppFile implements Serializable {
        /**
    * 文件id
    */
    @Schema(description = "文件id")
    private Integer fileId;

    /**
    * 文件sn
    */
    @Schema(description = "文件sn")
    private String fileSn;

    /**
    * 文件名称
    */
    @Schema(description = "文件名称")
    private String fileName;

    /**
    * 文件扩展名
    */
    @Schema(description = "文件扩展名")
    private String fileExt;

    /**
    * 文件媒体类型
    */
    @Schema(description = "文件媒体类型")
    private String fileMime;

    /**
    * s3文件访问路径
    */
    @Schema(description = "s3文件访问路径")
    private String filePath;

    /**
    * 文件类型:10-文本,20-图片,30-音频,40-视频,50-其他
    */
    @Schema(description = "文件类型:10-文本,20-图片,30-音频,40-视频,50-其他")
    private Integer fileType;

    /**
    * 文件来源:10-用户上传,11-用户输入,20-系统生成,30-url导入
    */
    @Schema(description = "文件来源:10-用户上传,11-用户输入,20-系统生成,30-url导入")
    private Integer fileFrom;

    /**
    * 文件大小byte
    */
    @Schema(description = "文件大小byte")
    private Integer fileSize;

    /**
    * 文件宽
    */
    @Schema(description = "文件宽")
    private Integer fileWidth;

    /**
    * 文件高
    */
    @Schema(description = "文件高")
    private Integer fileHeight;

    /**
    * 文件时长s
    */
    @Schema(description = "文件时长s")
    private Integer fileDuration;

    /**
    * 文件hash|md5
    */
    @Schema(description = "文件hash|md5")
    private String fileHash;

    /**
    * 文件来源链接|地址
    */
    @Schema(description = "文件来源链接|地址")
    private String fileSource;

    /**
    * 文件状态:0-待处理,1-处理中,3-正常,-1删除
    */
    @Schema(description = "文件状态:0-待处理,1-处理中,3-正常,-1删除")
    private Integer fileStatus;

    /**
    * 应用id
    */
    @Schema(description = "应用id")
    private Integer appId;

    /**
    * 租户id
    */
    @Schema(description = "租户id")
    private Integer tenantId;

    /**
    * 上传用户id
    */
    @Schema(description = "上传用户id")
    private Integer userId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}