package com.ai.application.agent.run.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 短期记忆提取请求DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "ShortTermMemoryRetrieveRequestDTO")
public class ShortTermMemoryRetrieveRequestDTO {

    /**
     * 智能体ID
     */
    @Schema(description = "智能体ID")
    private String agentId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 记忆类别/存储桶编号
     */
    @Schema(description = "记忆类别/存储桶编号")
    private String category;

    /**
     * 检索内容（用于向量检索）
     */
    @Schema(description = "检索内容（用于向量检索）")
    private String retrieveContent;

    /**
     * 相似度阈值
     */
    @Schema(description = "相似度阈值")
    private Double similarityThreshold;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    /**
     * 排序方式
     */
    @Schema(description = "排序方式")
    private String sortBy;

    /**
     * 返回结果数量
     */
    @Schema(description = "返回结果数量")
    private Integer limit;

    /**
     * 输出格式：0-简化格式，1-详细格式
     */
    @Schema(description = "输出格式：0-简化格式，1-详细格式")
    private Integer outputFormat;
}
