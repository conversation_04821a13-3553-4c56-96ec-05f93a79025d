package com.ai.application.agent.run.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 短期记忆提取请求DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "ShortTermMemoryRetrieveRequestDTO")
public class ShortTermMemoryRetrieveRequestDTO {

    /**
     * 智能体ID
     */
    @Schema(description = "智能体ID")
    private String agentId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 提取配置列表
     */
    @Schema(description = "提取配置列表")
    private List<MemoryRetrieveConfigDTO> extractConfigs;

    /**
     * 全局开始时间（可选）
     */
    @Schema(description = "全局开始时间")
    private LocalDateTime startTime;

    /**
     * 全局结束时间（可选）
     */
    @Schema(description = "全局结束时间")
    private LocalDateTime endTime;

    /**
     * 全局排序方式（可选）
     */
    @Schema(description = "全局排序方式")
    private String sortBy;
}
