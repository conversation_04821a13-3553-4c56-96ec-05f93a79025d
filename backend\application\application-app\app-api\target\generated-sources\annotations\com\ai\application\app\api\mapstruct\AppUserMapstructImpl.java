package com.ai.application.app.api.mapstruct;

import com.ai.application.app.api.dto.AppUserBatchImportData;
import com.ai.application.app.api.dto.AppUserCreateDTO;
import com.ai.application.app.api.dto.AppUserDTO;
import com.ai.application.app.api.dto.AppUserUpdateDTO;
import com.ai.application.app.api.entity.AppUser;
import com.ai.application.app.api.vo.AppUserDetailVO;
import com.ai.application.app.api.vo.AppUserVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-12T18:39:56+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AppUserMapstructImpl implements AppUserMapstruct {

    @Override
    public AppUser toEntity(AppUserDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AppUser appUser = new AppUser();

        appUser.setUserId( dto.getUserId() );
        appUser.setUserSn( dto.getUserSn() );
        appUser.setUserAccount( dto.getUserAccount() );
        appUser.setUserName( dto.getUserName() );
        appUser.setUserPassword( dto.getUserPassword() );
        appUser.setUserMobile( dto.getUserMobile() );
        appUser.setUserEmail( dto.getUserEmail() );
        appUser.setUserAvatar( dto.getUserAvatar() );
        appUser.setUserStaffSn( dto.getUserStaffSn() );
        appUser.setUserStatus( dto.getUserStatus() );
        appUser.setAppId( dto.getAppId() );
        appUser.setRoleId( dto.getRoleId() );
        appUser.setTenantId( dto.getTenantId() );
        appUser.setDeptId( dto.getDeptId() );
        appUser.setCreateTime( dto.getCreateTime() );
        appUser.setUpdateTime( dto.getUpdateTime() );

        return appUser;
    }

    @Override
    public AppUser toEntity(AppUserCreateDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AppUser appUser = new AppUser();

        appUser.setUserAccount( dto.getUserAccount() );
        appUser.setUserName( dto.getUserName() );
        appUser.setUserMobile( dto.getUserMobile() );
        appUser.setUserEmail( dto.getUserEmail() );
        appUser.setUserAvatar( dto.getUserAvatar() );
        appUser.setUserStaffSn( dto.getUserStaffSn() );
        appUser.setUserStatus( dto.getUserStatus() );
        appUser.setRoleId( dto.getRoleId() );
        appUser.setDeptId( dto.getDeptId() );

        return appUser;
    }

    @Override
    public AppUser toEntity(AppUserUpdateDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AppUser appUser = new AppUser();

        appUser.setUserSn( dto.getUserSn() );
        appUser.setUserName( dto.getUserName() );
        appUser.setUserMobile( dto.getUserMobile() );
        appUser.setUserEmail( dto.getUserEmail() );
        appUser.setUserAvatar( dto.getUserAvatar() );
        appUser.setUserStaffSn( dto.getUserStaffSn() );
        appUser.setUserStatus( dto.getUserStatus() );
        appUser.setRoleId( dto.getRoleId() );
        appUser.setDeptId( dto.getDeptId() );

        return appUser;
    }

    @Override
    public AppUser toEntity(AppUserVO dto) {
        if ( dto == null ) {
            return null;
        }

        AppUser appUser = new AppUser();

        appUser.setUserId( dto.getUserId() );
        appUser.setUserSn( dto.getUserSn() );
        appUser.setUserAccount( dto.getUserAccount() );
        appUser.setUserName( dto.getUserName() );
        appUser.setUserPassword( dto.getUserPassword() );
        appUser.setUserMobile( dto.getUserMobile() );
        appUser.setUserEmail( dto.getUserEmail() );
        appUser.setUserAvatar( dto.getUserAvatar() );
        appUser.setUserStaffSn( dto.getUserStaffSn() );
        appUser.setUserStatus( dto.getUserStatus() );
        appUser.setAppId( dto.getAppId() );
        appUser.setRoleId( dto.getRoleId() );
        appUser.setTenantId( dto.getTenantId() );
        appUser.setDeptId( dto.getDeptId() );
        appUser.setCreateTime( dto.getCreateTime() );
        appUser.setUpdateTime( dto.getUpdateTime() );

        return appUser;
    }

    @Override
    public List<AppUser> toEntityList(List<AppUserDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<AppUser> list = new ArrayList<AppUser>( dtolist.size() );
        for ( AppUserDTO appUserDTO : dtolist ) {
            list.add( toEntity( appUserDTO ) );
        }

        return list;
    }

    @Override
    public AppUserVO toVo(AppUser entity) {
        if ( entity == null ) {
            return null;
        }

        AppUserVO appUserVO = new AppUserVO();

        appUserVO.setUserId( entity.getUserId() );
        appUserVO.setUserSn( entity.getUserSn() );
        appUserVO.setUserAccount( entity.getUserAccount() );
        appUserVO.setUserName( entity.getUserName() );
        appUserVO.setUserPassword( entity.getUserPassword() );
        appUserVO.setUserMobile( entity.getUserMobile() );
        appUserVO.setUserEmail( entity.getUserEmail() );
        appUserVO.setUserAvatar( entity.getUserAvatar() );
        appUserVO.setUserStaffSn( entity.getUserStaffSn() );
        appUserVO.setUserStatus( entity.getUserStatus() );
        appUserVO.setAppId( entity.getAppId() );
        appUserVO.setRoleId( entity.getRoleId() );
        appUserVO.setTenantId( entity.getTenantId() );
        appUserVO.setDeptId( entity.getDeptId() );
        appUserVO.setCreateTime( entity.getCreateTime() );
        appUserVO.setUpdateTime( entity.getUpdateTime() );

        return appUserVO;
    }

    @Override
    public AppUserDetailVO toDetailVo(AppUser entity) {
        if ( entity == null ) {
            return null;
        }

        AppUserDetailVO appUserDetailVO = new AppUserDetailVO();

        appUserDetailVO.setUserSn( entity.getUserSn() );
        appUserDetailVO.setUserAccount( entity.getUserAccount() );
        appUserDetailVO.setUserName( entity.getUserName() );
        appUserDetailVO.setUserPassword( entity.getUserPassword() );
        appUserDetailVO.setUserMobile( entity.getUserMobile() );
        appUserDetailVO.setUserEmail( entity.getUserEmail() );
        appUserDetailVO.setUserAvatar( entity.getUserAvatar() );
        appUserDetailVO.setUserStaffSn( entity.getUserStaffSn() );
        appUserDetailVO.setUserStatus( entity.getUserStatus() );
        appUserDetailVO.setAppId( entity.getAppId() );
        appUserDetailVO.setRoleId( entity.getRoleId() );
        appUserDetailVO.setTenantId( entity.getTenantId() );
        appUserDetailVO.setDeptId( entity.getDeptId() );
        appUserDetailVO.setCreateTime( entity.getCreateTime() );
        appUserDetailVO.setUpdateTime( entity.getUpdateTime() );

        return appUserDetailVO;
    }

    @Override
    public AppUserDTO toDto(AppUserBatchImportData data) {
        if ( data == null ) {
            return null;
        }

        AppUserDTO appUserDTO = new AppUserDTO();

        appUserDTO.setUserAccount( data.getUserAccount() );
        appUserDTO.setUserName( data.getUserName() );
        appUserDTO.setUserMobile( data.getUserMobile() );
        appUserDTO.setUserEmail( data.getUserEmail() );

        return appUserDTO;
    }

    @Override
    public List<AppUserVO> toVoList(List<AppUser> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AppUserVO> list = new ArrayList<AppUserVO>( entities.size() );
        for ( AppUser appUser : entities ) {
            list.add( toVo( appUser ) );
        }

        return list;
    }
}
