package com.ai.application.knowledge.table.dto;

import com.ai.framework.core.vo.PageParam;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class TableDataListDto extends PageParam {

    @Schema(description = "智能表格Sn")
    private String tableSn;

    @Schema(description = "创建开始时间")
    private Long createTimeStart;

    @Schema(description = "创建结束时间")
    private Long createTimeEnd;

    @Schema(description = "更新开始时间")
    private Long updateTimeStart;

    @Schema(description = "更新结束时间")
    private Long updateTimeEnd;

    private List<Filter> filter;

    private Filter creator;

    private Filter updater;

    private Integer maxRow;

    @JsonIgnore
    private Integer tenantId;

    @Data
    public static class Filter {

        @Schema(description = "字段sn")
        private String fieldSn;

        @Schema(description = "表达式 like ")
        private String expression;

        @Schema(description = "值")
        private String value;

    }

}
