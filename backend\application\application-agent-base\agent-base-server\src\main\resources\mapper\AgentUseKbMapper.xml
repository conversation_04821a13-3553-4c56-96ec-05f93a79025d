<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.agent.base.mapper.AgentUseKbMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.agent.base.api.entity.AgentUseKb">
                    <id column="akb_id" property="akbId" />
                    <result column="akb_status" property="akbStatus" />
                    <result column="agent_id" property="agentId" />
                    <result column="version_id" property="versionId" />
                    <result column="kb_id" property="kbId" />
                    <result column="kb_extend" property="kbExtend" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        akb_id, akb_status, agent_id, version_id, kb_id, kb_extend, create_time, update_time
    </sql>

    <select id="selectUseDocByPage" resultType="com.ai.application.agent.base.api.vo.AgentUseKnowledgeTableQueryVO">
        select
        <include refid="com.ai.application.agent.base.mapper.AgentUseKbMapper.Base_Column_List"></include>
        from agent_use_kb
        order by create_time desc;
    </select>
</mapper>