package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.dto.AgentRunWorkflowDTO;
import com.ai.application.agent.base.api.entity.AgentRunWorkflow;
import com.ai.application.agent.base.api.vo.AgentRunWorkflowVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-12T18:39:57+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentRunWorkflowMapstructImpl implements AgentRunWorkflowMapstruct {

    @Override
    public AgentRunWorkflow toEntity(AgentRunWorkflowDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentRunWorkflow agentRunWorkflow = new AgentRunWorkflow();

        agentRunWorkflow.setWorkflowRunId( dto.getWorkflowRunId() );
        agentRunWorkflow.setWorkflowName( dto.getWorkflowName() );
        agentRunWorkflow.setWorkflowStatus( dto.getWorkflowStatus() );
        agentRunWorkflow.setWorkflowInput( dto.getWorkflowInput() );
        agentRunWorkflow.setWorkflowOutput( dto.getWorkflowOutput() );
        agentRunWorkflow.setWorkflowVariables( dto.getWorkflowVariables() );
        agentRunWorkflow.setWorkflowError( dto.getWorkflowError() );
        agentRunWorkflow.setWorkflowDuration( dto.getWorkflowDuration() );
        agentRunWorkflow.setWorkflowSnapshot( dto.getWorkflowSnapshot() );
        agentRunWorkflow.setWorkflowStartTime( dto.getWorkflowStartTime() );
        agentRunWorkflow.setWorkflowEndTime( dto.getWorkflowEndTime() );
        agentRunWorkflow.setCurrentNodeRunId( dto.getCurrentNodeRunId() );
        agentRunWorkflow.setRunId( dto.getRunId() );
        agentRunWorkflow.setStepId( dto.getStepId() );
        agentRunWorkflow.setFlowId( dto.getFlowId() );
        agentRunWorkflow.setCreateTime( dto.getCreateTime() );
        agentRunWorkflow.setUpdateTime( dto.getUpdateTime() );

        return agentRunWorkflow;
    }

    @Override
    public List<AgentRunWorkflow> toEntityList(List<AgentRunWorkflowDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<AgentRunWorkflow> list = new ArrayList<AgentRunWorkflow>( dtolist.size() );
        for ( AgentRunWorkflowDTO agentRunWorkflowDTO : dtolist ) {
            list.add( toEntity( agentRunWorkflowDTO ) );
        }

        return list;
    }

    @Override
    public AgentRunWorkflowVO toVo(AgentRunWorkflow entity) {
        if ( entity == null ) {
            return null;
        }

        AgentRunWorkflowVO agentRunWorkflowVO = new AgentRunWorkflowVO();

        agentRunWorkflowVO.setWorkflowRunId( entity.getWorkflowRunId() );
        agentRunWorkflowVO.setWorkflowName( entity.getWorkflowName() );
        agentRunWorkflowVO.setWorkflowStatus( entity.getWorkflowStatus() );
        agentRunWorkflowVO.setWorkflowInput( entity.getWorkflowInput() );
        agentRunWorkflowVO.setWorkflowOutput( entity.getWorkflowOutput() );
        agentRunWorkflowVO.setWorkflowVariables( entity.getWorkflowVariables() );
        agentRunWorkflowVO.setWorkflowError( entity.getWorkflowError() );
        agentRunWorkflowVO.setWorkflowDuration( entity.getWorkflowDuration() );
        agentRunWorkflowVO.setWorkflowSnapshot( entity.getWorkflowSnapshot() );
        agentRunWorkflowVO.setWorkflowStartTime( entity.getWorkflowStartTime() );
        agentRunWorkflowVO.setWorkflowEndTime( entity.getWorkflowEndTime() );
        agentRunWorkflowVO.setCurrentNodeRunId( entity.getCurrentNodeRunId() );
        agentRunWorkflowVO.setRunId( entity.getRunId() );
        agentRunWorkflowVO.setStepId( entity.getStepId() );
        agentRunWorkflowVO.setFlowId( entity.getFlowId() );
        agentRunWorkflowVO.setCreateTime( entity.getCreateTime() );
        agentRunWorkflowVO.setUpdateTime( entity.getUpdateTime() );

        return agentRunWorkflowVO;
    }

    @Override
    public List<AgentRunWorkflowVO> toVoList(List<AgentRunWorkflow> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AgentRunWorkflowVO> list = new ArrayList<AgentRunWorkflowVO>( entities.size() );
        for ( AgentRunWorkflow agentRunWorkflow : entities ) {
            list.add( toVo( agentRunWorkflow ) );
        }

        return list;
    }
}
