package com.ai.application.agent.run.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 智能表格写入请求DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "SmartTableWriteRequestDTO")
public class SmartTableWriteRequestDTO {

    /**
     * 智能表格编号
     */
    @Schema(description = "智能表格编号")
    private String tableSn;

    /**
     * 写入类型：0-覆盖，1-追加
     */
    @Schema(description = "写入类型：0-覆盖，1-追加")
    private Integer type;

    /**
     * 是否覆盖：0-不覆盖，1-覆盖
     */
    @Schema(description = "是否覆盖：0-不覆盖，1-覆盖")
    private Integer overwrite;

    /**
     * 写入数据
     */
    @Schema(description = "写入数据")
    private Object data;

    /**
     * 输入类型
     */
    @Schema(description = "输入类型")
    private Integer inputType;
}
