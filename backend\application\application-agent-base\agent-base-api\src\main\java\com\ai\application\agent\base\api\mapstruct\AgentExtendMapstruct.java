package com.ai.application.agent.base.api.mapstruct;
import com.ai.application.agent.base.api.dto.AgentExtendAddDTO;
import com.ai.application.agent.base.api.entity.AgentExtend;
import com.ai.application.agent.base.api.dto.AgentExtendUpdateDTO;
import org.mapstruct.Mapper;

/**
 * <p>
 * agent扩展信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Mapper(componentModel = "spring")
public interface AgentExtendMapstruct {
    AgentExtend toEntityAdd(AgentExtendAddDTO dto);
    AgentExtend toEntityUpdate(AgentExtendUpdateDTO dto);
}
