package dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(name = "会话基类")
@Data
public class RunBaseDTO {
    /**
     * agent类型
     */
    @Schema(description = "agent类型 10:对话流, 20:工作流, 30:master")
    private Integer agentType;

    /**
     * 文本消息
     */
    @Schema(description = "文本消息")
    private String text;

    /**
     * 会话编码
     */
    @Schema(description = "会话编号")
    private String sessionSn;

    /**
     * 流式状态
     */
    @Schema(description = "流式状态 true开启 false不开启")
    private Boolean stream;

    /**
     * 请求来源
     */
    @Schema(description = "请求来源，非必填")
    private Integer requestSource;

    /**
     * 请求ID
     */
    @Schema(description = "请求ID")
    private String requestId;

    /**
     * 语言
     */
    @Schema(description = "语言 zh，en，follow")
    private String lang;
}
