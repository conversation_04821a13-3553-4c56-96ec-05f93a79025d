package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.dto.AgentRunToolListDTO;
import com.ai.application.agent.base.api.dto.AgentRunToolDTO;
import com.ai.application.agent.base.api.vo.AgentRunToolVO;
import java.util.List;

/**
 * 智能体工具执行记录表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public interface IAgentRunToolService {

    /**
     * 列表
     *
     * @param queryDto
     * @return
     */
    List<AgentRunToolVO> list(AgentRunToolListDTO queryDto);

    /**
     * 保存
     *
     * @param dto
     */
    void add(AgentRunToolDTO dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(AgentRunToolDTO dto);

    /**
     * 查看
     *
     * @param id
     * @return
     */
    AgentRunToolVO get(Integer id);
}