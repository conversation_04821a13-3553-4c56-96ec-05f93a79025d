package com.ai.application.app.service.impl;

import com.ai.application.app.api.entity.AppRole;
import com.ai.application.app.api.entity.AppRoleFunction;
import com.ai.application.app.api.vo.AppRoleFunctionTreeVO;
import com.ai.application.app.mapper.AppRoleFunctionMapper;
import com.ai.application.app.mapper.AppRoleMapper;
import com.ai.application.tenant.api.entity.TenantDepartment;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.util.validator.AssertUtil;
import com.github.pagehelper.PageInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ai.application.app.mapper.AppFunctionMapper;
import com.ai.application.app.api.entity.AppFunction;
import com.ai.application.app.service.IAppFunctionService;
import com.ai.application.app.api.dto.AppFunctionDTO;
import com.ai.application.app.api.dto.query.AppFunctionQueryDTO;
import com.ai.application.app.api.vo.AppFunctionVO;
import com.ai.application.app.api.mapstruct.AppFunctionMapstruct;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 应用功能表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Service
public class AppFunctionServiceImpl implements IAppFunctionService {

    @Resource
    private AppFunctionMapper appFunctionMapper;
    @Resource
    private AppFunctionMapstruct appFunctionMapstruct;

    @Transactional(readOnly = true)
    @Override
    public List<AppRoleFunctionTreeVO> queryRoleFunctionTree() {
        /*AssertUtil.isNotNull(roleCode, "角色code不能为空");
        AppRole appRole = appRoleMapper.selectRoleByCode(UserContext.getTenantId(), roleCode);
        AssertUtil.isNotNull(appRole, "角色信息不存在");

        //根据角色查询功能
        List<AppRoleFunction> appRoleFunctions = appRoleFunctionMapper.queryFunctionListByRoleId(appRole.getRoleId());
        AssertUtil.isNotEmpty(appRoleFunctions, "没有查询到功能信息");*/

        QueryWrapper<AppFunction> queryWrapper = this.buildQuery(new AppFunctionQueryDTO());
        List<AppRoleFunctionTreeVO> treeVoList = appFunctionMapstruct.toTreeVoList(this.appFunctionMapper.selectList(queryWrapper));
        if (CollectionUtils.isEmpty(treeVoList)) {
            return null;
        }
        //树形结构
        return buildTree(treeVoList, 0);
    }

    private List<AppRoleFunctionTreeVO> buildTree(List<AppRoleFunctionTreeVO> nodes, Integer rootParentId) {
        // 使用Stream分组，按parentId分组
        Map<Integer, List<AppRoleFunctionTreeVO>> childrenMap = nodes.stream()
                .collect(Collectors.groupingBy(node -> Objects.requireNonNullElse(node.getParentId(), 0)));

        // 获取根节点列表
        List<AppRoleFunctionTreeVO> rootNodes = childrenMap.getOrDefault(rootParentId, new ArrayList<>());

        // 为每个节点设置其子节点
        nodes.forEach(node -> {
            List<AppRoleFunctionTreeVO> children = childrenMap.getOrDefault(node.getFunId(), new ArrayList<>());
            if (CollectionUtils.isNotEmpty(children)) {
                children.sort((p1, p2) -> Integer.compare(p1.getFunSort(), p2.getFunSort()));
            }
            node.setChildren(children);
        });

        return rootNodes;
    }

    @Transactional(readOnly = true)
    @Override
    public PageInfo<AppFunctionVO> page(AppFunctionQueryDTO queryDto) {
        QueryWrapper<AppFunction> queryWrapper = this.buildQuery(queryDto);
        Page<AppFunction> page = new Page<>(queryDto.getPageNo(), queryDto.getPageSize());
        Page<AppFunction> result = this.appFunctionMapper.selectPage(page, queryWrapper);
        return PageInfo.of(appFunctionMapstruct.toVoList(result.getRecords()));
    }

    @Transactional(readOnly = true)
    @Override
    public List<AppFunctionVO> list(AppFunctionQueryDTO queryDto) {
        QueryWrapper<AppFunction> queryWrapper = this.buildQuery(queryDto);
        return appFunctionMapstruct.toVoList(this.appFunctionMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(AppFunctionDTO dto) {
        // TODO 唯一性字段校验
        dto.setFunId(null);
        AppFunction entity = appFunctionMapstruct.toEntity(dto);
        entity.setCreateTime(new Date());
        appFunctionMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AppFunctionDTO dto) {
        BusinessAssertUtil.notNull(dto.getFunId(), "id不能为空");
        // TODO 唯一性字段校验
        AppFunction entity = appFunctionMapper.selectById(dto.getFunId());
        BusinessAssertUtil.notNull(entity, "找不到id为 " + dto.getFunId() + " 的记录");
        AppFunction entityList = appFunctionMapstruct.toEntity(dto);
        entityList.setUpdateTime(new Date());
        appFunctionMapper.updateById(entityList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Set<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            appFunctionMapper.deleteBatchIds(ids);
        }
    }

    @Transactional(readOnly = true)
    @Override
    public AppFunctionVO get(Long id) {
        BusinessAssertUtil.notNull(id, "id不能为空");
        AppFunction entity = appFunctionMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到id为 " + id + " 的记录");
        return appFunctionMapstruct.toVo(entity);
    }

    private QueryWrapper<AppFunction> buildQuery(AppFunctionQueryDTO queryDto) {
        QueryWrapper<AppFunction> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AppFunction::getFunStatus, 1);
        return queryWrapper;
    }
}