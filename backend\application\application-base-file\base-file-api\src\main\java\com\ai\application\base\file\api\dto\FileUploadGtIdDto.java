package com.ai.application.base.file.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FileUploadGtIdDto {
    @Schema(description = "文件基础信息")
    private FileUploadNoticeDto fileUploadNoticeDto;
    @Schema(description = "文件字节")
    private byte[] content;
    private String fileSn;
    @Schema(description = "文件内容类型  MIME 类型")
    private String contentType;
    @Schema(description = "type传public表示公共文件，不传表示私有文件")
    private String type;
}
