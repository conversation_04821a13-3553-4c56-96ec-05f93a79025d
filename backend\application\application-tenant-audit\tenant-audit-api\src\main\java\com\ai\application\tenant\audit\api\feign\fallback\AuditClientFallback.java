package com.ai.application.tenant.audit.api.feign.fallback;


import com.ai.application.tenant.audit.api.dto.TenantAuditDTO;
import com.ai.application.tenant.audit.api.dto.query.TenantAddDTO;
import com.ai.application.tenant.audit.api.feign.IAuditFeignClient;
import com.ai.framework.core.vo.ResultVo;


public class AuditClientFallback implements IAuditFeignClient {


    @Override
    public ResultVo<Void> update(TenantAuditDTO dto) {
      return  ResultVo.fail("更新审批错误");

    }

    @Override
    public ResultVo<Void> add(TenantAddDTO dto) {
       return  ResultVo.fail("添加审批错误");
    }
}
