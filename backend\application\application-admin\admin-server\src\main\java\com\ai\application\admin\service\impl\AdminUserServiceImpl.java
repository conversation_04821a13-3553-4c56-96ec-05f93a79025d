package com.ai.application.admin.service.impl;

import com.ai.application.admin.api.dto.AdminUserCreateDTO;
import com.ai.application.admin.api.dto.AdminUserUpdateDTO;
import com.ai.application.admin.api.dto.query.AdminUserQueryDTO;
import com.ai.application.admin.api.entity.AdminRole;
import com.ai.application.admin.api.entity.AdminUser;
import com.ai.application.admin.api.mapstruct.AdminUserMapstruct;
import com.ai.application.admin.api.vo.AdminUserDetailVO;
import com.ai.application.admin.api.vo.AdminUserVO;
import com.ai.application.admin.mapper.AdminRoleMapper;
import com.ai.application.admin.mapper.AdminUserMapper;
import com.ai.application.admin.service.IAdminUserService;
import com.ai.application.admin.utils.AdminUtils;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.enums.AppEnum;
import com.ai.framework.core.util.BusinessAssertUtil;
import com.ai.framework.core.util.password.PasswordUtils;
import com.ai.framework.core.util.password.SaltedPassword;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.util.validator.AssertUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 应用用户表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
@Slf4j
public class AdminUserServiceImpl implements IAdminUserService {
    @Value("${config.userDefaultPassword:aiagent}")
    private String defaultPassword;

    @Resource
    private AdminUserMapper adminUserMapper;
    @Resource
    private AdminUserMapstruct adminUserMapstruct;
    @Resource
    private AdminRoleMapper adminRoleMapper;

    @Transactional(readOnly = true)
    @Override
    public PageInfo<AdminUserVO> page(AdminUserQueryDTO queryDto) {
        LambdaQueryWrapper<AdminUser> queryWrapper = this.buildQuery(queryDto);
        Page<AdminUser> page = new Page<>(queryDto.getPageNo(), queryDto.getPageSize());
        Page<AdminUser> result = this.adminUserMapper.selectPage(page, queryWrapper);
        List<AdminUser> records = result.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return PageInfo.of(Lists.newArrayList());
        }

        List<AdminUserVO> list = adminUserMapstruct.toVoList(records);
        //设置角色名称
        AdminRole saAdminRole = adminRoleMapper.getSaAdminRole();
        if (Objects.nonNull(saAdminRole)) {
            Optional<AdminUserVO> first = list.stream().filter(a -> a.getRoleId().equals(saAdminRole.getRoleId())).findFirst();
            first.ifPresent(adminUserVO -> adminUserVO.setRoleName(saAdminRole.getRoleName()));

        }
        return PageInfo.of(list);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void create(AdminUserCreateDTO dto) {
        dto.setUserAccount(dto.getUserAccount().trim());
        //校验默认密码是否配置(nacos中配置)
        AssertUtil.isNotNull(defaultPassword, "默认密码未配置");
        //校验是否超级管理员
        AdminRole saAdminRole = adminRoleMapper.getSaAdminRole();
        AssertUtil.isNotNull(saAdminRole, "超级管理员角色不存在");
        AdminUser adminUser = adminUserMapper.selectById(UserContext.getUserId());
        AssertUtil.isNotNull(adminUser, "当前登录用户不存在");
        AssertUtil.isTrue(saAdminRole.getRoleId().equals(adminUser.getRoleId()), "非超级管理员，不能创建用户");

        AdminUser appUser = adminUserMapper.getByUserAccount(AppEnum.APP_500.getCode(), 0, dto.getUserAccount());
        AssertUtil.isFalse(Objects.nonNull(appUser), "登录账号已存在");
        AdminUser entity = adminUserMapstruct.toEntity(dto);

        String userSn = UUIDUtil.genRandomSn("admin");
        SaltedPassword saltedPassword = PasswordUtils.createSaltedPassword(defaultPassword, userSn);
        entity.setUserPassword(saltedPassword.getHashedPasswordBase64());
        entity.setAppId(AppEnum.APP_500.getCode());
        entity.setUserSn(userSn);
        entity.setTenantId(0);
        entity.setRoleId(0);
        entity.setDeptId(0);
        if (StringUtils.isBlank(dto.getUserName())) {
            entity.setUserName(dto.getUserAccount());
        }

        //数据校验
        validUserData(entity);
        adminUserMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(String userSn,AdminUserUpdateDTO dto) {
        BusinessAssertUtil.notNull(userSn, "用户编号不能为空");
        AdminUser appUser = adminUserMapper.findByUserSn(userSn);
        BusinessAssertUtil.notNull(appUser, "找不到用户编号为 " + userSn + " 的记录");

        appUser.setUserName(dto.getUserName());
        appUser.setUpdateTime(null);
        adminUserMapper.updateById(appUser);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void resetPassword(String userSn) {
        AssertUtil.isNotNull(userSn, "userSn不能为空");
        AdminUser appUser = adminUserMapper.findByUserSn(userSn);
        AssertUtil.isNotNull(appUser, "用户不存在");
        AssertUtil.isNotNull(defaultPassword, "默认密码为配置");

        //密码加密处理
        SaltedPassword saltedPassword = PasswordUtils.createSaltedPassword(defaultPassword, appUser.getUserSn());
        appUser.setUserPassword(saltedPassword.getHashedPasswordBase64());
        appUser.setUpdateTime(null);
        adminUserMapper.updateById(appUser);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveUserStatus(String userSn, Boolean enable) {
        AssertUtil.isNotNull(userSn, "userSn不能为空");
        String currentUserSn = UserContext.getUserSn();
        if (userSn.equals(currentUserSn)) {
            AssertUtil.isTrue(enable, "不能禁用当前用户");
        }
        AdminUser appUser = adminUserMapper.findByUserSn(userSn);
        AssertUtil.isNotNull(appUser, "用户不存在");

        appUser.setUserStatus(enable ? 1 : 0);
        appUser.setUpdateTime(null);
        adminUserMapper.updateById(appUser);
    }

    @Override
    public AdminUserDetailVO detail(String userSn) {
        AssertUtil.isNotNull(userSn, "userSn不能为空");
        AdminUser appUser = adminUserMapper.findByUserSn(userSn);
        if (Objects.isNull(appUser)) {
            return null;
        }
        return adminUserMapstruct.toDetailVo(appUser);
    }

    private void validUserData(AdminUser data) {
        //登录名校验
        AssertUtil.isNotNull(data.getUserAccount(),"登录名不能为空");
        AssertUtil.isFalse(data.getUserAccount().length() > 20,"登录名长度不能超过20");
        AssertUtil.isTrue(AdminUtils.isValidLoginAccount(data.getUserAccount()),"登录名格式不正确");
    }

    private LambdaQueryWrapper<AdminUser> buildQuery(AdminUserQueryDTO queryDto) {
        LambdaQueryWrapper<AdminUser> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.isNull(queryDto)) {
            return queryWrapper;
        }
        queryWrapper.eq(AdminUser::getAppId, AppEnum.APP_500.getCode());
        if (queryDto.getUserStatus() >= 0) {
            queryWrapper.eq( AdminUser::getUserStatus, queryDto.getUserStatus());
        }else{
            queryWrapper.ge(AdminUser::getUserStatus, 0);
        }

        if (StringUtils.isNoneBlank(queryDto.getKeyword())) {
            queryWrapper.and(wrapper ->
                    wrapper.like(AdminUser::getUserName, queryDto.getKeyword())
                            .or()
                            .like(AdminUser::getUserAccount, queryDto.getKeyword())
            );
        }
        return queryWrapper;
    }

    @Override
    public void initAdmin(){
        //校验默认密码是否配置(nacos中配置)
        AssertUtil.isNotNull(defaultPassword, "默认密码未配置");
        AdminRole adminRole = adminRoleMapper.getSaAdminRole();
        Integer roleId;
        if(Objects.isNull(adminRole)) {
            adminRole = new AdminRole();
            adminRole.setRoleName("超级管理员");
            adminRole.setRoleDesc("管理后台超级管理员");
            adminRole.setRoleStatus(1);
            adminRole.setRoleCode("SA");
            adminRole.setAppId(AppEnum.APP_500.getCode());
            adminRole.setTenantId(0);
            adminRoleMapper.insert(adminRole);
        }
        roleId = adminRole.getRoleId();

        AdminUser adminUser = adminUserMapper.getByUserAccount(AppEnum.APP_500.getCode(), 0, "admin");
        if(Objects.isNull(adminUser)) {
            adminUser = new AdminUser();
            String userSn = UUIDUtil.genRandomSn("admin");
            SaltedPassword saltedPassword = PasswordUtils.createSaltedPassword(defaultPassword, userSn);
            adminUser.setUserPassword(saltedPassword.getHashedPasswordBase64());
            adminUser.setAppId(AppEnum.APP_500.getCode());
            adminUser.setUserSn(userSn);
            adminUser.setTenantId(0);
            adminUser.setRoleId(roleId);
            adminUser.setDeptId(0);
            adminUser.setUserAccount("admin");
            adminUser.setUserName("超级管理员");
            adminUserMapper.insert(adminUser);
        }
    }
}