#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖检查脚本

验证项目依赖包的安装和导入状态，提供：
- requirements.txt中依赖包的导入测试
- 依赖包状态检查和报告
- 缺失依赖的安装指导
- 项目环境完整性验证

Usage:
    python test/check_dependencies.py

Project: Agent Memory Service
Author: 张小龙 <<EMAIL>>
Created: 2025-06-01
Version: 1.0.0
License: private
"""

import sys

def check_dependencies():
    """检查依赖包导入"""
    dependencies = [
        ('fastapi', 'Web框架'),
        ('uvicorn', 'ASGI服务器'),
        ('pydantic', '数据验证'),
        ('elasticsearch', 'Elasticsearch客户端'),
        ('sentence_transformers', '句子向量化'),
        ('numpy', '数值计算'),
        ('yaml', 'YAML配置解析'),
        ('loguru', '日志管理'),
        ('requests', 'HTTP客户端')
    ]
    
    print("[START] 检查项目依赖包:")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for package, description in dependencies:
        try:
            __import__(package)
            print(f"[SUCCESS] {package:<20} - {description}")
            passed += 1
        except ImportError as e:
            print(f"[ERROR] {package:<20} - {description} (导入失败: {e})")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"[REPORT] 检查结果: {passed} 个通过, {failed} 个失败")
    
    if failed == 0:
        print("\n[SUCCESS] 所有依赖检查通过！")
        return True
    else:
        print(f"\n[WARNING] 有 {failed} 个依赖缺失，请运行:")
        print("  pip install -r requirements.txt")
        return False

if __name__ == "__main__":
    success = check_dependencies()
    sys.exit(0 if success else 1) 