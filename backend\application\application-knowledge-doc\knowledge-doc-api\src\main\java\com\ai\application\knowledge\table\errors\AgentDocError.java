package com.ai.application.knowledge.table.errors;

public enum AgentDocError {

    VER_SN_NOT_EXIST(31300, "verSn not exist error"),

    KNOWLEDGE_BASE_NAME_HAS_EXISTS(31301, "知识库名称已存在"),

    OPERATION_WITHOUT_PERMISSION(31302, "权限不足"),

    KNOWLEDGE_TAG_HAS_EXISTS(31303, "含有重复标签"),

    CAN_NOT_EDIT_MODEL(31304, "关联已启用agent无法修改学习模型"),

    CAN_NOT_DELETE_INVENTORY(31305, "已绑定agent，知识库无法删除"),

    KNOWLEDGE_BASE_NOT_FOUND(31306, "知识库不存在"),

    KNOWLEDGE_FILE_NOT_FOUND(31311, "知识不存在"),

    FAQ_QUESTION_IS_NOT_EXISTS(31307, "问题不存在"),

    FAQ_QUESTION_HAS_EXISTS(31308, "问题已存在"),

    KN<PERSON>LEDGE_NODES_IS_BLANK(31309, "知识片段为空"),

    KNOWLEDGE_JSON_PARSE_ERROR(31310, "知识片段json解析异常"),

    FILE_EXIST(31311, "文件已经绑定知识库"),

    EMBEDDING_INTERFACE_ERROR(31312, "调用算法embedding接口失败"),

    MODEL_NOT_EXISTS(31313, "模型不可用"),

    KNOWLEDGE_BASE_RELATIVE_AGENT(31314,"知识库已关联启用的智能体，请先解除关联");;

    private Integer code;
    private String message;

    AgentDocError(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getMessage() {
        return this.message;
    }

    public Integer getCode() {
        return this.code;
    }
}
