package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.dto.AgentRunNodeListDTO;
import com.ai.application.agent.base.api.dto.AgentRunNodeDTO;
import com.ai.application.agent.base.api.vo.AgentRunNodeVO;
import java.util.List;

/**
 * 工作流节点运行记录表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public interface IAgentRunNodeService {

    /**
     * 列表
     *
     * @param queryDto
     * @return
     */
    List<AgentRunNodeVO> list(AgentRunNodeListDTO queryDto);

    /**
     * 保存
     *
     * @param dto
     */
    void add(AgentRunNodeDTO dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(AgentRunNodeDTO dto);

    /**
     * 查看
     *
     * @param id
     * @return
     */
    AgentRunNodeVO get(Integer id);
}