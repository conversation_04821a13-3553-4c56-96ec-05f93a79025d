package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.bo.MasterSkillBO;
import com.ai.application.agent.base.api.dto.AgentUseMcpUpdateDTO;
import com.ai.application.agent.base.api.enums.SkillTypeEnum;
import com.ai.application.agent.base.api.enums.ToolExtendNameEnum;
import com.ai.application.agent.base.service.IAgentUseMcpService;
import com.ai.framework.core.util.json.JsonUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ai.application.agent.base.mapper.AgentUseMcpMapper;
import com.ai.application.agent.base.api.entity.AgentUseMcp;
import com.ai.application.agent.base.api.dto.AgentUseMcpAddDTO;
import com.ai.application.agent.base.api.dto.AgentUseMcpListDTO;
import com.ai.application.agent.base.api.vo.AgentUseMcpListVO;
import com.ai.application.agent.base.api.mapstruct.AgentUseMcpMapstruct;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 智能体MCP工具关联表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Service
public class AgentUseMcpServiceImpl implements IAgentUseMcpService {

    @Resource
    private AgentUseMcpMapper agentUseMcpMapper;

    @Resource
    private AgentUseMcpMapstruct agentUseMcpMapstruct;

    @Transactional(readOnly = true)
    @Override
    public List<AgentUseMcpListVO> list(AgentUseMcpListDTO queryDto) {
        LambdaQueryWrapper<AgentUseMcp> queryWrapper = this.buildQuery(queryDto);
        return agentUseMcpMapstruct.toVoList(this.agentUseMcpMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(AgentUseMcpAddDTO dto) {
        AgentUseMcp entity = agentUseMcpMapstruct.toAddEntity(dto);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());

        agentUseMcpMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AgentUseMcpUpdateDTO dto) {
        BusinessAssertUtil.notNull(dto.getMcpToolId(), "mcpToolId不能为空");

        AgentUseMcp entity = agentUseMcpMapper.selectById(dto.getMcpToolId());
        BusinessAssertUtil.notNull(entity, "找不到AdId为 " + dto.getMcpToolId() + " 的记录");

        AgentUseMcp entityList = agentUseMcpMapstruct.toUpdateEntity(dto);
        entityList.setUpdateTime(new Date());
        agentUseMcpMapper.updateById(entityList);
    }

    @Transactional(readOnly = true)
    @Override
    public List<MasterSkillBO> toDetail(Integer versionId) {
        AgentUseMcpListDTO agentUseMcpListDTO = new AgentUseMcpListDTO();
        List<AgentUseMcpListVO> list = this.list(agentUseMcpListDTO);
        return list.stream().map(mcp->{
            MasterSkillBO skill = new MasterSkillBO();
            skill.setSkillId(mcp.getMcpToolId());
            skill.setType(SkillTypeEnum.MCP.getCode());
            String mcpExtend = mcp.getMcpExtend();
            Map<String, String> mapStr = JsonUtils.parseMapStr(mcpExtend);
            skill.setReplayType(Integer.valueOf(mapStr.get(ToolExtendNameEnum.REPLY_TYPE.getCode())));
            return skill;
        }).toList();
    }

    private LambdaQueryWrapper<AgentUseMcp> buildQuery(AgentUseMcpListDTO queryDto) {
        LambdaQueryWrapper<AgentUseMcp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(queryDto.getVersionId()), AgentUseMcp::getVersionId, queryDto.getVersionId());
        return queryWrapper;
    }
}