package com.ai.application.agent.run.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 文档聊天请求 DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(name = "DocChatRequestDTO")
public class DocChatRequestDTO {

    /**
     * 知识库编号列表
     */
    @Schema(description = "知识库编号列表")
    private List<String> inventorySns;

    /**
     * 知识列表
     */
    @Schema(description = "知识列表")
    private List<String> knowledge;

    /**
     * 使用模式: 知识问答0, 文档检索1, 网络搜索2
     */
    @Schema(description = "使用模式")
    private Integer requestType;

    /**
     * 问题聚焦 false = 聚焦(关闭扩写), true = 不聚焦(打开改写扩写)
     */
    @Schema(description = "问题聚焦")
    private Boolean questionFocus;

    /**
     * 答复模式, false为答复效率优先, true为质量优先
     */
    @Schema(description = "答复模式")
    private Boolean responseMode;

    /**
     * 提示词
     */
    @Schema(description = "提示词")
    private String prompt;

    /**
     * 总结提示词
     */
    @Schema(description = "总结提示词")
    private String summaryPrompt;

    /**
     * 模型
     */
    @Schema(description = "模型")
    private String model;

    /**
     * 请求内容
     */
    @Schema(description = "请求内容")
    private String msgContent;

    /**
     * 延迟时间
     */
    @Schema(description = "延迟时间")
    private Long delayInMs;

    /**
     * 文件内容的片段
     */
    @Schema(description = "文件内容的片段")
    private List<PageContentDTO> pageList;

    /**
     * 是否流式输出
     */
    @Schema(description = "是否流式输出")
    private Boolean stream;

    /**
     * 内容节点
     */
    @Schema(description = "内容节点")
    private List<KnowledgeFragmentDTO> contentNodes;

    /**
     * 页面内容 DTO
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @SuperBuilder
    @Schema(name = "PageContentDTO")
    public static class PageContentDTO {

        /**
         * 页面ID
         */
        @Schema(description = "页面ID")
        private String pageId;

        /**
         * 内容
         */
        @Schema(description = "内容")
        private String content;

        /**
         * 分数
         */
        @Schema(description = "分数")
        private Double score;

        /**
         * 文件名
         */
        @Schema(description = "文件名")
        private String fileName;

        /**
         * 页码
         */
        @Schema(description = "页码")
        private Integer pageNumber;
    }

    /**
     * 知识片段 DTO
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @SuperBuilder
    @Schema(name = "KnowledgeFragmentDTO")
    public static class KnowledgeFragmentDTO {

        /**
         * 片段ID
         */
        @Schema(description = "片段ID")
        private String fragmentId;

        /**
         * 内容
         */
        @Schema(description = "内容")
        private String content;

        /**
         * 分数
         */
        @Schema(description = "分数")
        private Double score;

        /**
         * 排序
         */
        @Schema(description = "排序")
        private Integer sort;
    }
}
