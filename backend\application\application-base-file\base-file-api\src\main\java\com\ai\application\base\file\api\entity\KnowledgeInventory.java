package com.ai.application.base.file.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("knowledge_inventory")
public class KnowledgeInventory {
    @TableId(type = IdType.AUTO)
    private Long inventoryId;

    private Long tenantId;

    private String inventorySn;

    private String name;
    private String description;
    private String modelSn;
    private Integer splitRule;
    private Integer splitter;
    private Integer wordCountLimit;
    private Integer wordCountOverlap;
    private Integer nlp;
    private Integer llm;
    private Integer deleted;
    private Long creator;
    private Long updator;

    private Timestamp createTime;
    private Timestamp updateTime;


}
