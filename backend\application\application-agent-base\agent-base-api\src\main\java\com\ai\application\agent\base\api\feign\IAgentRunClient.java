package com.ai.application.agent.base.api.feign;

import com.ai.application.agent.base.api.feign.fallback.IAgentClientFallback;
import com.ai.application.agent.base.api.vo.AgentRunMcpVO;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import java.util.List;

@Tag(name = "智能体执行接口", description = "智能体执行接口基本操作")
@FeignClient(
        value = ServiceConstant.AGENT_BASE,
        fallback = IAgentClientFallback.class,
        contextId = "IAgentRunClient"
)
public interface IAgentRunClient {
    String API_PREFIX = "/v1/feign/agent/run";

    @Operation(summary = "根据mcpId查看执行日志")
    @GetMapping(API_PREFIX + "/mcp/logs/{mcpId}")
    ResultVo<List<AgentRunMcpVO>> mcpLogs(@PathVariable("mcpId") Integer mcpId);
}