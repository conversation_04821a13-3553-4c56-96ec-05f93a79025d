package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.dto.AgentRunWorkflowListDTO;
import com.ai.application.agent.base.api.dto.AgentRunWorkflowDTO;
import com.ai.application.agent.base.api.vo.AgentRunWorkflowVO;
import java.util.List;

/**
 * 智能体工作流执行记录表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public interface IAgentRunWorkflowService {

    /**
     * 列表
     *
     * @param queryDto
     * @return
     */
    List<AgentRunWorkflowVO> list(AgentRunWorkflowListDTO queryDto);

    /**
     * 保存
     *
     * @param dto
     */
    void add(AgentRunWorkflowDTO dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(AgentRunWorkflowDTO dto);

    /**
     * 查看
     *
     * @param id
     * @return
     */
    AgentRunWorkflowVO get(Integer id);
}