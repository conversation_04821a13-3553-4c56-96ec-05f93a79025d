package com.ai.application.app.auth.api.constants;

/**
 * token的Redis键
 */
public class TokenConstant {
    // 用户key
    public static final String USER_KEY_CODE_ACCESS_TOKEN = "auth:jwt:accessCode:accessToken:";
    // 用户key
    public static final String USER_KEY_CODE = "auth:jwt:accessCode:";
    // 用户key
    public static final String USER_KEY = "auth:jwt:user:";
    // 刷新key
    public static final String USER_REFRESH_TOKEN = "auth:jwt:refreshToken:";
    // token的Key
    public static final String USER_ACCESS_TOKEN = "auth:jwt:accessToken:";

    public static final String ENCRYPTION_SECRET = "763be504c440ea4a";

    public static final String THIRD_LOGIN_TEMP_CODE = "auth:third:login:code:";
}
