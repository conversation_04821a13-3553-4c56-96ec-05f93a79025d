package com.ai.application.agent.run.service;

import com.ai.application.agent.run.dto.KnowledgeFileEmbeddingDTO;
import com.ai.application.base.file.api.dto.FileInfoDto;
import com.ai.framework.core.vo.ResultVo;

/**
 * 知识库服务接口
 */
public interface IKnowledgeService {

    /**
     * 检查知识库是否存在
     *
     * @param knowledgeInventory 知识库编号
     * @return 知识库信息
     */
    KnowledgeInventoryInfo checkKnowledgeInventory(String knowledgeInventory);

    /**
     * 文件嵌入处理
     *
     * @param embeddingDto 嵌入请求
     * @param authorization 授权头
     * @return 嵌入结果
     */
    ResultVo<FileInfoDto> fileEmbedding(KnowledgeFileEmbeddingDTO embeddingDto, String authorization);

    /**
     * 查询文件嵌入状态
     *
     * @param fileId 文件ID
     * @return 文件状态信息
     */
    ResultVo<FileStatusInfo> getFileStatus(Long fileId);

    /**
     * 根据知识库ID和MD5查询文件
     *
     * @param knowledgeInventory 知识库编号
     * @param fileMd5 文件MD5
     * @return 文件列表
     */
    ResultVo<java.util.List<FileInfoDto>> findFilesByDatasetIdAndMd5(String knowledgeInventory, String fileMd5);

    /**
     * 删除文件
     *
     * @param fileSn 文件编号
     * @param datasetId 知识库ID
     * @return 删除结果
     */
    ResultVo<String> deleteFile(String fileSn, String datasetId);

    /**
     * 知识库信息
     */
    class KnowledgeInventoryInfo {
        private String modelSn;
        private Integer splitRule;
        private Integer splitter;
        private Integer wordCountLimit;
        private Integer wordCountOverlap;
        private String separatorContent;

        // Getters and Setters
        public String getModelSn() { return modelSn; }
        public void setModelSn(String modelSn) { this.modelSn = modelSn; }
        public Integer getSplitRule() { return splitRule; }
        public void setSplitRule(Integer splitRule) { this.splitRule = splitRule; }
        public Integer getSplitter() { return splitter; }
        public void setSplitter(Integer splitter) { this.splitter = splitter; }
        public Integer getWordCountLimit() { return wordCountLimit; }
        public void setWordCountLimit(Integer wordCountLimit) { this.wordCountLimit = wordCountLimit; }
        public Integer getWordCountOverlap() { return wordCountOverlap; }
        public void setWordCountOverlap(Integer wordCountOverlap) { this.wordCountOverlap = wordCountOverlap; }
        public String getSeparatorContent() { return separatorContent; }
        public void setSeparatorContent(String separatorContent) { this.separatorContent = separatorContent; }
    }

    /**
     * 文件状态信息
     */
    class FileStatusInfo {
        private Integer status; // 1-处理中, 2-成功, 3-失败
        private String errorReason;

        // Getters and Setters
        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }
        public String getErrorReason() { return errorReason; }
        public void setErrorReason(String errorReason) { this.errorReason = errorReason; }
    }
}
