<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.knowledge.table.mapper.KnowledgeTableMapper">

    <select id="list" resultType="com.ai.application.knowledge.table.vo.TableListVo"
            parameterType="com.ai.application.knowledge.table.dto.TableListDto">
        select
        t.table_id as tableId,
        t.table_sn as tableSn,
        t.table_name as tableName,
        t.table_desc as tableDesc,
        t.table_type as tableType,
        t.table_status as tableStatus,
        t.table_agents as tableAgents,
        t.tenant_id as tenantId,
        t.create_user_id as createUserId,
        t.update_user_id as updateUserId,
        t.create_time as createTime,
        t.update_time as updateTime
        from
        knowledge_table t
        where t.tenant_id = #{tenantId} and t.table_status = 1
        <if test="keyword != null and keyword != ''">
            and (t.table_name like concat('%', #{keyword}, '%')
            or t.table_desc like concat('%', #{keyword}, '%'))
            or t.create_user_id in
            <foreach item="item" index="index" collection="userIdsByName" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="permissionIds != null and permissionIds.size() > 0">
            and t.create_user_id in
            <foreach item="item" index="index" collection="permissionIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by t.create_time desc
    </select>
</mapper>
