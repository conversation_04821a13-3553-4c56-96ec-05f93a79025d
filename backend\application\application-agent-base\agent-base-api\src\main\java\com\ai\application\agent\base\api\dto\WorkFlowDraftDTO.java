package com.ai.application.agent.base.api.dto;

import com.ai.application.agent.base.api.bo.*;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Schema(name = "WorkFlowDraftDTO")
@Data
public class WorkFlowDraftDTO {
    @Schema(description = "Agent编码")
    private String agentSn;

    @Schema(description = "元素")
    private JSONObject graph;

    @Schema(description = "定量")
    private List<FlowVariableBO> variables;

    @Schema(description = "流程")
    private List<FlowElementBO> steps;

    @Schema(description = "短期记忆，对话流有")
    private List<ShortMemoryBO> shortMemory;

    @Schema(description = "长期记忆， 对话流有")
    private LongMemoryBO longMemory;

    @Schema(description = "敏感词")
    public List<MasterDictBO> sensitives;
}