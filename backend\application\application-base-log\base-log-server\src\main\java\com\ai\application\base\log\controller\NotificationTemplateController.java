package com.ai.application.base.log.controller;

import com.ai.application.base.log.api.dto.LogUpdateStatusDTO;
import com.ai.application.base.log.api.dto.NotificationTemplateAddDTO;
import com.ai.application.base.log.api.dto.NotificationTemplateUpdateDTO;
import com.ai.application.base.log.api.dto.query.NotificationTemplateQueryDTO;
import com.ai.application.base.log.api.enums.MessageReceiverTypeEnum;
import com.ai.application.base.log.api.vo.NotificationTemplatePageVO;
import com.ai.application.base.log.api.vo.NotificationTemplateVO;
import com.ai.application.base.log.service.INotificationTemplateService;
import com.ai.framework.core.vo.ResultVo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 通知模板配置表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Tag(name = "通知模板配置表", description = "通知模板配置表-相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1/template")
public class NotificationTemplateController {

    @Resource
    private INotificationTemplateService  notificationTemplateService;

    /**
     * 分页查询
     *
     * @param queryDto
     * @return
     */
    @Operation(summary = "通知模板配置表-分页查询", description = "查询所有通知模板配置表 信息")
    @PostMapping("/page")
    public ResultVo<PageInfo<NotificationTemplatePageVO>> page(@Validated @RequestBody NotificationTemplateQueryDTO queryDto){
        return ResultVo.data(notificationTemplateService.page(queryDto));
    }

    /**
     * 保存
     *
     * @param dto
     * @return
     */
    @Operation(summary = "通知模板配置表-新增")
    @PostMapping("/add")
    public ResultVo<Void> add(@Validated @RequestBody NotificationTemplateAddDTO dto){
        notificationTemplateService.add(dto);
        return ResultVo.success("保存成功");
    }
    
    /**
     * 修改
     *
     * @param dto
     * @return
     */
    @Operation(summary = "通知模板配置表-修改")
    @PostMapping(value = "/update")
    public ResultVo<Void> update(@Validated @RequestBody NotificationTemplateUpdateDTO dto){
        notificationTemplateService.update(dto);
        return ResultVo.success("修改成功");
    }

    @Operation(summary = "通知模板配置表-详情", description = "根据id通知模板配置表信息")
    @ApiResponse(responseCode = "0", description = "成功",
            content = @Content(schema = @Schema(implementation = NotificationTemplateVO.class)))
    @GetMapping("/{id}/detail")
    public ResultVo<NotificationTemplateVO> detail(@PathVariable("id") Integer id) {
        return ResultVo.data(notificationTemplateService.detail(id));
    }

    @Operation(summary = "通知模板配置表-删除", description = "根据id删除通知模板配置表信息")
    @DeleteMapping("/{id}/delete")
    public ResultVo<Void> delete(@PathVariable("id") Integer id) {
        notificationTemplateService.delete(id);
        return ResultVo.success("删除成功");
    }

    @Operation(summary = "通知模板配置表-启用/禁用", description = "根据id设置启用/禁用")
    @PostMapping("/{id}/enable")
    public ResultVo<Boolean> updateStatus(@PathVariable("id") String id, @RequestBody @Validated LogUpdateStatusDTO dto) {
        notificationTemplateService.updateStatus(id, dto);
        return ResultVo.success("类型配置状态设置成功");
    }

    @Operation(summary = "通知模板配置表-获取接收消息类型", description = "获取接收消息类型")
    @GetMapping("/getReceiverTypes")
    public ResultVo<MessageReceiverTypeEnum[]> getMessageReceiverTypes() {
        return ResultVo.data(MessageReceiverTypeEnum.values());
    }

}