package com.ai.application.agent.base.api.enums;

/**
 * 智能体页面来源:10-管理,20-市场,30-工作区
 */
public enum AgentPageSourceEnum {
    MANAGE(10, "管理"),
    MARKET(20, "市场"),
    WORKSPACE(30, "工作区"),
    USER(50, "用户"),
    ;

    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    AgentPageSourceEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
