#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能体记忆模块API测试脚本

综合API测试工具，提供：
- 健康检查测试
- 记忆添加功能测试
- 记忆搜索和查询测试
- 相似记忆检索测试
- 统计信息获取测试
- 记忆清除功能测试
- 完整的端到端测试流程

Usage:
    python test/test_memory_api.py

Project: Agent Memory Service
Author: 张小龙 <<EMAIL>>
Created: 2025-06-01
Version: 1.0.0
License: private
"""

import json
import requests
import sys
import time
from datetime import datetime
from typing import Dict, Any

# API基础URL
BASE_URL = "http://localhost:6023"
API_V1_URL = f"{BASE_URL}/agentmemory/v1"


class AgentMemoryTester:
    """智能体记忆模块测试器"""
    
    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.api_url = f"{base_url}/agentmemory/v1"
        self.session = requests.Session()
    
    def test_health_check(self) -> bool:
        """测试健康检查"""
        print("[HEALTH] 测试健康检查...")
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                print(f"[SUCCESS] 健康检查通过: {health_data.get('status')}")
                return True
            else:
                print(f"[ERROR] 健康检查失败: HTTP {response.status_code}")
                return False
        except Exception as e:
            print(f"[ERROR] 健康检查异常: {e}")
            return False
    
    def test_add_memory(self) -> str:
        """测试添加记忆"""
        print("\n[ADD] 测试添加记忆...")
        
        memory_data = {
            "agent_id": "test_agent_001",
            "user_id": "test_user_001",
            "category": "conversation",
            "user_question": "今天天气怎么样？",
            "question_reply": "今天天气晴朗，温度适宜，是个不错的天气。"
        }
        
        try:
            response = self.session.post(
                f"{self.api_url}/add",
                json=memory_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    memory_id = result.get("data", {}).get("memory_id")
                    print(f"[SUCCESS] 记忆添加成功: {memory_id}")
                    return memory_id
                else:
                    print(f"[ERROR] 记忆添加失败: {result.get('message')}")
                    return None
            else:
                print(f"[ERROR] 记忆添加失败: HTTP {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
                
        except Exception as e:
            print(f"[ERROR] 记忆添加异常: {e}")
            return None
    
    def test_search_memory(self) -> bool:
        """测试搜索记忆"""
        print("\n[SEARCH] 测试搜索记忆...")
        
        search_data = {
            "agent_id": "test_agent_001",
            "user_id": "test_user_001",
            "user_question": "天气情况",
            "similarity_threshold": 0.5,
            "limit": 10
        }
        
        try:
            response = self.session.post(
                f"{self.api_url}/search",
                json=search_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    data = result.get("data", {})
                    memories = data.get("memories", [])
                    total = data.get("total", 0)
                    print(f"[SUCCESS] 搜索完成: 找到{len(memories)}条记忆，总计{total}条")
                    
                    # 显示搜索结果
                    for memory in memories[:2]:  # 只显示前2条
                        similarity = memory.get("similarity_score", 0)
                        print(f"  - 相似度: {similarity:.3f}, 问题: {memory['user_question'][:50]}...")
                    
                    return True
                else:
                    print(f"[ERROR] 搜索失败: {result.get('message')}")
                    return False
            else:
                print(f"[ERROR] 搜索失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"[ERROR] 搜索异常: {e}")
            return False
    
    def test_get_similar_memories(self) -> bool:
        """测试获取相似记忆"""
        print("\n[SIMILAR] 测试获取相似记忆...")
        
        try:
            params = {
                "question": "天气如何",
                "limit": 5,
                "similarity_threshold": 0.5
            }
            
            response = self.session.get(
                f"{self.api_url}/similar/test_agent_001/test_user_001",
                params=params,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    data = result.get("data", {})
                    memories = data.get("memories", [])
                    total = data.get("total", 0)
                    print(f"[SUCCESS] 相似记忆获取成功: 找到{len(memories)}条相似记忆，总计{total}条")
                    return True
                else:
                    print(f"[ERROR] 相似记忆获取失败: {result.get('message')}")
                    return False
            else:
                print(f"[ERROR] 相似记忆获取失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"[ERROR] 相似记忆获取异常: {e}")
            return False
    
    def test_get_recent_memories(self) -> bool:
        """测试获取最近记忆"""
        print("\n[RECENT] 测试获取最近记忆...")
        
        try:
            params = {
                "days": 7,
                "limit": 10
            }
            
            response = self.session.get(
                f"{self.api_url}/recent/test_agent_001/test_user_001",
                params=params,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    data = result.get("data", {})
                    memories = data.get("memories", [])
                    total = data.get("total", 0)
                    print(f"[SUCCESS] 最近记忆获取成功: 找到{len(memories)}条最近记忆，总计{total}条")
                    return True
                else:
                    print(f"[ERROR] 最近记忆获取失败: {result.get('message')}")
                    return False
            else:
                print(f"[ERROR] 最近记忆获取失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"[ERROR] 最近记忆获取异常: {e}")
            return False
    
    def test_get_statistics(self) -> bool:
        """测试获取统计信息"""
        print("\n[STATS] 测试获取统计信息...")
        
        try:
            response = self.session.get(
                f"{self.api_url}/statistics/test_agent_001/test_user_001",
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    data = result.get("data", {})
                    total_memories = data.get("total_memories", 0)
                    categories = data.get("category_stats", {})
                    print(f"[SUCCESS] 统计信息获取成功: 总记忆数{total_memories}, 分类统计{categories}")
                    return True
                else:
                    print(f"[ERROR] 统计信息获取失败: {result.get('message')}")
                    return False
            else:
                print(f"[ERROR] 统计信息获取失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"[ERROR] 统计信息获取异常: {e}")
            return False
    
    def test_clear_memories(self) -> bool:
        """测试清除记忆"""
        print("\n[CLEAR] 测试清除记忆...")
        
        clear_data = {
            "agent_id": "test_agent_001",
            "user_id": "test_user_001",
            "category": "conversation"  # 只清除特定分类的记忆
        }
        
        try:
            response = self.session.delete(
                f"{self.api_url}/clear",
                json=clear_data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    data = result.get("data", {})
                    deleted_count = data.get("deleted_count", 0)
                    print(f"[SUCCESS] 记忆清除成功: 删除了{deleted_count}条记忆")
                    return True
                else:
                    print(f"[ERROR] 记忆清除失败: {result.get('message')}")
                    return False
            else:
                print(f"[ERROR] 记忆清除失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"[ERROR] 记忆清除异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("[START] 智能体记忆模块API测试")
        print("=" * 60)
        
        tests = [
            ("健康检查", self.test_health_check),
            ("添加记忆", self.test_add_memory),
            ("搜索记忆", self.test_search_memory),
            ("获取相似记忆", self.test_get_similar_memories),
            ("获取最近记忆", self.test_get_recent_memories),
            ("获取统计信息", self.test_get_statistics),
            ("清除记忆", self.test_clear_memories)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                if result:
                    passed += 1
                time.sleep(1)  # 避免请求过于频繁
            except Exception as e:
                print(f"[ERROR] {test_name}测试异常: {e}")
        
        print("\n" + "=" * 60)
        print(f"[REPORT] 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("[SUCCESS] 所有API测试通过！")
            return True
        else:
            print(f"[WARNING] {total - passed} 个测试失败")
            return False


def main():
    """主函数"""
    try:
        # 检查是否提供了自定义URL
        base_url = sys.argv[1] if len(sys.argv) > 1 else BASE_URL
        
        print(f"[INFO] 使用API地址: {base_url}")
        
        # 创建测试器并运行测试
        tester = AgentMemoryTester(base_url)
        success = tester.run_all_tests()
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n[INFO] 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"[ERROR] 测试运行异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 