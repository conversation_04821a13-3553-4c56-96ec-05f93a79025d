package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.dto.AgentRunListDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ai.application.agent.base.mapper.AgentRunMapper;
import com.ai.application.agent.base.api.entity.AgentRun;
import com.ai.application.agent.base.service.IAgentRunService;
import com.ai.application.agent.base.api.dto.AgentRunDTO;
import com.ai.application.agent.base.api.vo.AgentRunVO;
import com.ai.application.agent.base.api.mapstruct.AgentRunMapstruct;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Date;

/**
 * 智能体运行记录表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
public class AgentRunServiceImpl implements IAgentRunService{

    @Resource
    private AgentRunMapper agentRunMapper;

    @Resource
    private AgentRunMapstruct agentRunMapstruct;

    @Transactional(readOnly = true)
    @Override
    public List<AgentRunVO> list(AgentRunListDTO queryDto) {
        LambdaQueryWrapper<AgentRun> queryWrapper = this.buildQuery(queryDto);
        return agentRunMapstruct.toVoList(this.agentRunMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(AgentRunDTO dto) {
        dto.setRunId(null);
        AgentRun entity = agentRunMapstruct.toEntity(dto);
        entity.setCreateTime(new Date());

        agentRunMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AgentRunDTO dto) {
        BusinessAssertUtil.notNull(dto.getRunId(), "RunId不能为空");

        AgentRun entity = agentRunMapper.selectById(dto.getRunId());
        BusinessAssertUtil.notNull(entity, "找不到RunId为 " + dto.getRunId() + " 的记录");

        AgentRun entityList = agentRunMapstruct.toEntity(dto);
        entityList.setUpdateTime(new Date());
        agentRunMapper.updateById(entityList);
    }

    @Transactional(readOnly = true)
    @Override
    public AgentRunVO get(Integer id) {
        BusinessAssertUtil.notNull(id, "RunId不能为空");

        AgentRun entity = agentRunMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到RunId为 " + id + " 的记录");

        return agentRunMapstruct.toVo(entity);
    }

    private LambdaQueryWrapper<AgentRun> buildQuery(AgentRunListDTO queryDto) {
        LambdaQueryWrapper<AgentRun> queryWrapper = new LambdaQueryWrapper<>();
        return queryWrapper;
    }
}