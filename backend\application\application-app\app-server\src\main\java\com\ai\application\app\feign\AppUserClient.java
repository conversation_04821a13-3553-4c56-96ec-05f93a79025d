package com.ai.application.app.feign;

import com.ai.application.app.api.dto.query.AppUserQueryDTO;
import com.ai.application.app.api.dto.query.AppUserVerifyPasswordDTO;
import com.ai.application.app.api.feign.IAppUserClient;
import com.ai.application.app.api.vo.AppUserDetailVO;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.application.app.service.IAppUserService;
import com.ai.framework.core.vo.ResultVo;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@AllArgsConstructor
public class AppUserClient implements IAppUserClient {
    private final IAppUserService appUserService;

    @Override
    public ResultVo<AppUserVO> verifyPassword(@RequestBody @Validated AppUserVerifyPasswordDTO dto){
        return ResultVo.data(appUserService.verifyPassword(dto));
    }

    @Override
    public ResultVo<AppUserDetailVO> getUserBySn(@PathVariable("userSn") String userSn){
        return ResultVo.data(appUserService.detail(userSn));
    }

  @Override
    public ResultVo<AppUserVO> getUserById(@PathVariable("userId") Integer userId){
        return ResultVo.data(appUserService.get(userId));
    }

    @Override
    public ResultVo<List<AppUserVO>> list(AppUserQueryDTO queryDto) {
        return ResultVo.data(appUserService.list(queryDto));
    }

    @Override
    public ResultVo<List<AppUserVO>> ceshi(AppUserQueryDTO queryDto) {
        return null;
    }
}
