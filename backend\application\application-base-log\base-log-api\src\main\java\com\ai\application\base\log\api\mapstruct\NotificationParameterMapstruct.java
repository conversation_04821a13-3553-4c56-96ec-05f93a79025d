package com.ai.application.base.log.api.mapstruct;
import com.ai.application.base.log.api.entity.NotificationParameter;
import com.ai.application.base.log.api.dto.NotificationParameterDTO;
import com.ai.application.base.log.api.vo.NotificationParameterVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 通知参数配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */

@Mapper(componentModel = "spring")
public interface NotificationParameterMapstruct {

    NotificationParameter toEntity(NotificationParameterDTO dto);
    List<NotificationParameter> toEntityList(List<NotificationParameterDTO> dtolist);
    NotificationParameterVO toVo(NotificationParameter entity);
    List<NotificationParameterVO> toVoList(List<NotificationParameter> entities);
}
