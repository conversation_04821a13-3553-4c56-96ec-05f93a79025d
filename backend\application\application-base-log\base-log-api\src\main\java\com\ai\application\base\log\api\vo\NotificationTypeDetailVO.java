package com.ai.application.base.log.api.vo;

import com.ai.application.base.log.api.bo.MailConfigBO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 通知类型配置表
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@Schema(name = "")
public class NotificationTypeDetailVO {
    /**
     * 通知类型id
     */
    @Schema(description = "通知类型id")
    private Integer ntypeId;

    /**
     * 通知类型:10-邮件,20-短信,30-钉钉,40-企微,50-飞书,60-Slack,70-Webhook,80-站内消息
     */
    @Schema(description = "通知类型:10-邮件,20-短信,30-钉钉,40-企微,50-飞书,60-Slack,70-Webhook,80-站内消息")
    private Integer ntypeType;

    /**
     * 类型配置名称
     */
    @Schema(description = "类型配置名称")
    private String ntypeName;

    /**
     * 类型配置描述
     */
    @Schema(description = "类型配置描述")
    private String ntypeDesc;

    /**
     * 类型配置状态:1-启用,0-停用,-1-弃用
     */
    @Schema(description = "类型配置状态:1-启用,0-停用,-1-弃用")
    private Integer ntypeStatus;

    /**
     * 类型配置参数:渠道配置,重试配置等
     */
    @Schema(description = "类型配置参数:渠道配置,重试配置等")
    private String ntypeConfig;

    /**
     * 类型配置参数:渠道配置,重试配置等
     */
    @Schema(description = "类型配置参数:渠道配置,重试配置等-对象")
    private MailConfigBO mailConfig;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}