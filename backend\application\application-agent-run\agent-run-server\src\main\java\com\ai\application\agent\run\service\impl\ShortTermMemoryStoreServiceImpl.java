package com.ai.application.agent.run.service.impl;

import com.ai.application.agent.run.dto.*;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.feign.IMemoryFeignClient;
import com.ai.application.agent.run.service.IShortTermMemoryStoreService;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 短期记忆存储服务实现
 */
@Slf4j
@Service
public class ShortTermMemoryStoreServiceImpl implements IShortTermMemoryStoreService {

    private final IMemoryFeignClient memoryFeignClient;

    public ShortTermMemoryStoreServiceImpl(IMemoryFeignClient memoryFeignClient) {
        this.memoryFeignClient = memoryFeignClient;
    }

    @Override
    public ResultVo<ShortTermMemoryStoreResultDTO> executeShortTermMemoryStore(ShortTermMemoryStoreRequestDTO request, String authorization) {
        log.info("ShortTermMemoryStoreService executeShortTermMemoryStore start, request: {}", JsonUtils.toJsonString(request));

        try {
            // 参数校验
            validateShortTermMemoryStoreRequest(request);

            List<String> memoryIds = new ArrayList<>();

            // 处理单个记忆内容
            if (StringUtils.isNotBlank(request.getMemoryContent())) {
                String memoryId = storeMemory(request.getAgentId(), request.getUserId(), 
                        request.getBucketSn(), request.getMemoryContent());
                if (StringUtils.isNotBlank(memoryId)) {
                    memoryIds.add(memoryId);
                }
            }

            // 处理批量记忆内容
            if (CollectionUtils.isNotEmpty(request.getMemoryContents())) {
                for (ShortTermMemoryStoreRequestDTO.MemoryContentItem item : request.getMemoryContents()) {
                    if (StringUtils.isNotBlank(item.getMemoryContent())) {
                        String memoryId = storeMemory(request.getAgentId(), request.getUserId(), 
                                item.getBucketSn(), item.getMemoryContent());
                        if (StringUtils.isNotBlank(memoryId)) {
                            memoryIds.add(memoryId);
                        }
                    }
                }
            }

            // 构建返回结果
            ShortTermMemoryStoreResultDTO storeResult = ShortTermMemoryStoreResultDTO.builder()
                    .success(true)
                    .output("SUCCESS")
                    .memoryIds(memoryIds)
                    .memoryCount(memoryIds.size())
                    .build();

            log.info("ShortTermMemoryStoreService executeShortTermMemoryStore success, stored {} memories", memoryIds.size());
            return ResultVo.data(storeResult);

        } catch (Exception e) {
            log.error("ShortTermMemoryStoreService executeShortTermMemoryStore error", e);
            ShortTermMemoryStoreResultDTO errorResult = ShortTermMemoryStoreResultDTO.builder()
                    .success(false)
                    .output("FAILED")
                    .errorMessage(e.getMessage())
                    .memoryIds(Collections.emptyList())
                    .memoryCount(0)
                    .build();
            return ResultVo.data(errorResult);
        }
    }

    @Override
    public boolean validateMemoryStoreRequest(ShortTermMemoryStoreRequestDTO request) {
        if (request == null) {
            return false;
        }

        // 必须有智能体ID和用户ID
        if (StringUtils.isBlank(request.getAgentId()) || StringUtils.isBlank(request.getUserId())) {
            return false;
        }

        // 必须有记忆内容（单个或批量）
        boolean hasSingleContent = StringUtils.isNotBlank(request.getMemoryContent());
        boolean hasBatchContent = CollectionUtils.isNotEmpty(request.getMemoryContents());

        return hasSingleContent || hasBatchContent;
    }

    @Override
    public String storeMemory(String agentId, String userId, String bucketSn, String memoryContent) {
        try {
            // 构建记忆添加请求
            String category = StringUtils.isNotBlank(bucketSn) ? "short_term_memory_"+bucketSn : "short_term_memory_default";
            MemoryAddRequestDTO memoryRequest = MemoryAddRequestDTO.builder()
                    .agentId(agentId)
                    .userId(userId)
                    .category(category)
                    .userQuestion(memoryContent)
                    .questionReply(null)
                    .questionTime(LocalDateTime.now())
                    .build();

            log.info("Calling memory service to add memory: agentId={}, userId={}, category={}", 
                    agentId, userId, memoryRequest.getCategory());

            // 调用记忆服务
            MemoryAddResponseDTO response = memoryFeignClient.addMemory(memoryRequest);

            if (response != null && response.getSuccess()) {
                Map<String, Object> data = response.getData();
                if (data != null && data.containsKey("memory_id")) {
                    String memoryId = data.get("memory_id").toString();
                    log.info("Memory stored successfully: memoryId={}", memoryId);
                    return memoryId;
                }
            } else {
                String errorMsg = response != null ? response.getMessage() : "Unknown error";
                log.error("Failed to store memory: {}", errorMsg);
                throw new ServiceException(ExecutorError.SHORT_TERM_MEMORY_STORE_FAILED.getCode(), errorMsg);
            }

        } catch (Exception e) {
            log.error("Error storing memory: agentId={}, userId={}, content={}", agentId, userId, memoryContent, e);
            throw new ServiceException(ExecutorError.SHORT_TERM_MEMORY_STORE_FAILED.getCode(), 
                    "记忆存储失败: " + e.getMessage());
        }

        return null;
    }

    /**
     * 校验短期记忆存储请求
     */
    private void validateShortTermMemoryStoreRequest(ShortTermMemoryStoreRequestDTO request) {
        if (StringUtils.isBlank(request.getAgentId())) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "智能体ID不能为空");
        }

        if (StringUtils.isBlank(request.getUserId())) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "用户ID不能为空");
        }

        if (!validateMemoryStoreRequest(request)) {
            throw new ServiceException(ExecutorError.SHORT_TERM_MEMORY_CONTENT_IS_BLANK);
        }
    }
}
