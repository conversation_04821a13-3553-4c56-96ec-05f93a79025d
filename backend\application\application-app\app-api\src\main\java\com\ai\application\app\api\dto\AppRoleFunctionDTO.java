package com.ai.application.app.api.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

/**
 * 应用角色功能表
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Data
@Schema(name = "应用角色功能表DTO")
public class AppRoleFunctionDTO {
    /**
     * 主键id
     */
    @Schema(description = "主键id")
    private Integer rfId;
    /**
     * 角色id
     */
    @Schema(description = "角色id")
    private Integer roleId;
    /**
     * 功能id
     */
    @Schema(description = "功能id")
    private Integer funId;
    /**
     * 状态 1有效 0无效
     */
    @Schema(description = "状态 1有效 0无效")
    private Integer rfStatus;
    @Schema(description = "")
    private Date createTime;
    @Schema(description = "")
    private Date updateTime;
}