package com.ai.application.skill.mcp.service.impl;

import com.ai.application.skill.mcp.api.dto.McpToolDTO;
import com.ai.application.skill.mcp.api.dto.query.McpToolQueryDTO;
import com.ai.application.skill.mcp.api.entity.McpServer;
import com.ai.application.skill.mcp.api.entity.McpTool;
import com.ai.application.skill.mcp.api.mapstruct.McpToolMapstruct;
import com.ai.application.skill.mcp.api.vo.McpToolVO;
import com.ai.application.skill.mcp.mapper.McpServerMapper;
import com.ai.application.skill.mcp.mapper.McpToolMapper;
import com.ai.application.skill.mcp.service.IMcpToolService;
import com.ai.framework.core.util.list.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;

import java.sql.Timestamp;
import java.util.List;
import java.util.Date;

/**
 * MCP工具表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Service
public class McpToolServiceImpl implements IMcpToolService {

    @Resource
    private McpToolMapper mcpToolMapper;
    @Resource
    private McpServerMapper mcpServerMapper;
    @Resource
    private McpToolMapstruct mcpToolMapstruct;

    @Transactional(readOnly = true)
    @Override
    public List<McpToolVO> tools(McpToolQueryDTO queryDto) {

         McpServer mcpServer = mcpServerMapper.selectOne(
                new LambdaQueryWrapper<McpServer>()
                        .eq(McpServer::getServerSn, queryDto.getServerSn())

        );
         List<McpTool> mcpTools = mcpToolMapper.selectList(
                new LambdaQueryWrapper<McpTool>()
                        .eq(McpTool::getServerId, mcpServer.getServerId())
        );
         if(CollectionUtils.isEmpty(mcpTools)){
             return List.of(new McpToolVO());
         }
        return mcpToolMapstruct.toVoList(mcpTools);
    }

    @Transactional(readOnly = true)
    @Override
    public List<McpToolVO> list(McpToolQueryDTO queryDto) {
        QueryWrapper<McpTool> queryWrapper = this.buildQuery(queryDto);
        return mcpToolMapstruct.toVoList(this.mcpToolMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(McpToolDTO dto) {
        dto.setMcpToolId(null);
        Timestamp now = new Timestamp(System.currentTimeMillis());
        McpTool entity = mcpToolMapstruct.toEntity(dto);
        entity.setCreateTime(now);

        mcpToolMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(McpToolDTO dto) {
        BusinessAssertUtil.notNull(dto.getMcpToolId(), "McpToolId不能为空");
        Timestamp now = new Timestamp(System.currentTimeMillis());
        // TODO 唯一性字段校验
        McpTool entity = mcpToolMapper.selectById(dto.getMcpToolId());
        BusinessAssertUtil.notNull(entity, "找不到McpToolId为 " + dto.getMcpToolId() + " 的记录");

        McpTool entityList = mcpToolMapstruct.toEntity(dto);
        entityList.setUpdateTime(now);
        mcpToolMapper.updateById(entityList);
    }

    @Transactional(readOnly = true)
    @Override
    public McpToolVO get(Integer id) {
        BusinessAssertUtil.notNull(id, "McpToolId不能为空");

        McpTool entity = mcpToolMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到McpToolId为 " + id + " 的记录");

        return mcpToolMapstruct.toVo(entity);
    }

    private QueryWrapper<McpTool> buildQuery(McpToolQueryDTO queryDto) {
        QueryWrapper<McpTool> queryWrapper = new QueryWrapper<>();

        return queryWrapper;
    }
}