package com.ai.application.admin.api.vo;

import com.ai.application.admin.api.serializer.TenantStatusSerializer;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 租户表
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(name = "")
public class TenantDetailVO {
    /**
     * 租户sn
     */
    @Schema(description = "租户sn")
    private String tenantSn;
    /**
     * 租户名称
     */
    @Schema(description = "租户名称")
    private String tenantName;
    /**
     * 租户域名
     */
    @Schema(description = "租户域名")
    private String tenantDomain;
    /**
     * 租户描述
     */
    @Schema(description = "租户描述")
    private String tenantDesc;
    /**
     * 有效截止时间
     */
    @Schema(description = "有效截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tenantExpireTime;
    /**
     * 租户状态 0:禁用,1:启用,-1:删除
     */
    @Schema(description = "租户状态 0:禁用,1:启用,-1:删除")
    @JsonSerialize(using = TenantStatusSerializer.class)
    private Integer tenantStatus;

    @Schema(description = "授权的模型列表")
    private List<AdminResourceVO> grantModelList;

    @Schema(description = "授权的智能体表")
    private List<AdminResourceVO> grantAgentlList;
}