package com.ai.application.app.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
@Schema(name = "应用用户表重置密码DTO")
public class AppUserResetPasswordDto {
    /**
     * 旧密码
     */
    @NotEmpty(message="旧密码不能为空")
    @Length(min = 8,message = "密码长度不能小于8个字符")
    String oldPassword;

    /**
     * 新密码
     */
    @NotEmpty(message="新密码不能为空")
    @Length(min = 8,message = "密码长度不能小于8个字符")
    String newPassword;

    /**
     * 确认密码
     */
    @NotEmpty(message="确认密码不能为空")
    @Length(min = 8,message = "密码长度不能小于8个字符")
    String confirmPassword;

}