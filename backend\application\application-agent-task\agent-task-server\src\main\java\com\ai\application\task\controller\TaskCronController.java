package com.ai.application.task.controller;

import com.ai.application.task.api.dto.TaskCronAddDTO;
import com.ai.application.task.api.dto.query.TaskQueryDTO;
import com.ai.application.task.api.vo.TaskCronDetailVO;
import com.ai.application.task.api.vo.TaskCronPageVO;
import com.ai.application.task.service.ITaskCronService;
import com.ai.framework.core.vo.ResultVo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 定时任务表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Tag(name = "定时任务表", description = "定时任务-相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1/cron")
public class TaskCronController {

    @Resource
    private ITaskCronService taskCronService;

    @Operation(summary = "定时任务-分页查询", description = "查询所有任务表 信息")
    @PostMapping("/page")
    public ResultVo<PageInfo<TaskCronPageVO>> page(@Validated @RequestBody TaskQueryDTO queryDto){
        return ResultVo.data(taskCronService.page(queryDto));
    }

    @Operation(summary = "定时任务-新增")
    @PostMapping("/add")
    public ResultVo<Void> add(@Validated @RequestBody TaskCronAddDTO dto){
        taskCronService.add(dto);
        return ResultVo.success("保存成功");
    }

    @Operation(summary = "定时任务-删除")
    @PostMapping(value = "/delete/{taskSn}")
    public ResultVo<Void> delete(@PathVariable("taskSn") String taskSn){
        taskCronService.delete(taskSn);
        return ResultVo.success("删除成功");
    }

    @Operation(summary = "定时任务-启用")
    @PostMapping(value = "/enable/{taskSn}")
    public ResultVo<Void> enable(@PathVariable("taskSn") String taskSn){
        taskCronService.enable(taskSn);
        return ResultVo.success("启用成功");
    }

    @Operation(summary = "定时任务-终止")
    @PostMapping(value = "/stop/{taskSn}")
    public ResultVo<Void> stop(@PathVariable("taskSn") String taskSn){
        taskCronService.stop(taskSn);
        return ResultVo.success("停用成功");
    }

    @Operation(summary = "定时任务-详情")
    @PostMapping(value = "/dtail/{taskSn}")
    public ResultVo<TaskCronDetailVO> dtail(@PathVariable("taskSn") String taskSn){
        return ResultVo.data(taskCronService.detail(taskSn));
    }

}