package com.ai.application.base.log.controller;

import com.ai.application.base.log.api.dto.LogUpdateStatusDTO;
import com.ai.application.base.log.api.dto.NotificationTypeAddDTO;
import com.ai.application.base.log.api.dto.NotificationTypeUpdateDTO;
import com.ai.application.base.log.api.dto.query.NotificationTypeQueryDTO;
import com.ai.application.base.log.api.vo.NotificationTypeDetailVO;
import com.ai.application.base.log.api.vo.NotificationTypePageVO;
import com.ai.application.base.log.api.vo.NotificationTypeVO;
import com.ai.application.base.log.service.INotificationTypeService;
import com.ai.framework.core.vo.ResultVo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 通知类型配置表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Tag(name = "通知类型配置表", description = "通知类型配置表-相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1/type")
public class NotificationTypeController {

    @Resource
    private INotificationTypeService notificationTypeService;

    /**
     * 分页查询
     *
     * @param queryDto
     * @return
     */
    @Operation(summary = "通知类型配置表-分页查询", description = "查询所有通知类型配置表 信息")
    @PostMapping("/page")
    public ResultVo<PageInfo<NotificationTypePageVO>> page(@Validated @RequestBody NotificationTypeQueryDTO queryDto){
        return ResultVo.data(notificationTypeService.page(queryDto));
    }

    /**
     * 不带分页查询
     *
     * @param queryDto
     * @return
     */
    @Operation(summary = "通知类型配置表-不带分页查询", description = "查询所有通知类型配置表 信息")
    @PostMapping("/list")
    public ResultVo<List<NotificationTypeVO>> list(@Validated @RequestBody NotificationTypeQueryDTO queryDto){
        return ResultVo.data(notificationTypeService.list(queryDto));
    }

    /**
     * 保存
     *
     * @param dto
     * @return
     */
    @Operation(summary = "通知类型配置表-新增")
    @PostMapping("/add")
    public ResultVo<Void> add(@Validated @RequestBody NotificationTypeAddDTO dto){
        notificationTypeService.add(dto);
        return ResultVo.success("保存成功");
    }
    
    /**
     * 修改
     *
     * @param dto
     * @return
     */
    @Operation(summary = "通知类型配置表-修改")
    @PostMapping(value = "/update")
    public ResultVo<Void> update(@Validated @RequestBody NotificationTypeUpdateDTO dto){
        notificationTypeService.update(dto);
        return ResultVo.success("修改成功");
    }

    @Operation(summary = "通知类型配置表-详情", description = "根据id通知类型配置表信息")
    @ApiResponse(responseCode = "0", description = "成功",
            content = @Content(schema = @Schema(implementation = NotificationTypeDetailVO.class)))
    @GetMapping("/{id}/detail")
    public ResultVo<NotificationTypeDetailVO> detail(@PathVariable("id") Integer id) {
        return ResultVo.data(notificationTypeService.detail(id));
    }

    @Operation(summary = "通知类型配置表-删除", description = "根据id删除通知类型配置表信息")
    @DeleteMapping("/{id}/delete")
    public ResultVo<Void> delete(@PathVariable("id") Integer id) {
        notificationTypeService.delete(id);
        return ResultVo.success("删除成功");
    }

    @Operation(summary = "通知类型配置表-启用/禁用", description = "根据id设置启用/禁用")
    @PostMapping("/{id}/enable")
    public ResultVo<Boolean> updateStatus(@PathVariable("id") String id, @RequestBody @Validated LogUpdateStatusDTO dto) {
        notificationTypeService.updateStatus(id, dto);
        return ResultVo.success("类型配置状态设置成功");
    }
}