package com.ai.application.knowledge.dict.service.impl;

import com.ai.application.agent.base.api.dto.query.AgentUseKnowledgeDictQueryDTO;
import com.ai.application.agent.base.api.feign.IAgentUseClient;
import com.ai.application.agent.base.api.vo.AgentUseKnowledgeDictQueryVO;
import com.ai.application.app.api.dto.query.AppUserQueryDTO;
import com.ai.application.app.api.feign.IAppUserClient;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.application.knowledge.dict.dto.AgentByBbDto;
import com.ai.application.knowledge.dict.dto.DictCreateDto;
import com.ai.application.knowledge.dict.dto.DictListDto;
import com.ai.application.knowledge.dict.entity.KnowledgeDict;
import com.ai.application.knowledge.dict.entity.KnowledgeDictWord;
import com.ai.application.knowledge.dict.enums.DictStatusEnum;
import com.ai.application.knowledge.dict.enums.DictWordStatusEnum;
import com.ai.application.knowledge.dict.error.AgentDocError;
import com.ai.application.knowledge.dict.mapper.KnowledgeDictMapper;
import com.ai.application.knowledge.dict.mapper.KnowledgeDictWordMapper;
import com.ai.application.knowledge.dict.service.IKnowledgeDictService;
import com.ai.application.knowledge.dict.vo.AgentByBbVo;
import com.ai.application.knowledge.dict.vo.DictDetailVo;
import com.ai.application.knowledge.dict.vo.DictListVo;
import com.ai.application.tenant.authorize.api.dto.ResourceAddReqDTO;
import com.ai.application.tenant.authorize.api.dto.ResourceGrantReqDTO;
import com.ai.application.tenant.authorize.api.enums.GrantObjectTypeEnum;
import com.ai.application.tenant.authorize.api.enums.GrantTypeEnum;
import com.ai.application.tenant.authorize.api.enums.ResourceTypeEnum;
import com.ai.application.tenant.authorize.api.feign.ITenantAuthorizeClient;
import com.ai.application.tenant.authorize.api.vo.ResourceVO;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.util.validator.AssertUtil;
import com.ai.framework.core.vo.ResultVo;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 词库表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
@Slf4j
public class KnowledgeDictServiceImpl extends ServiceImpl<KnowledgeDictMapper, KnowledgeDict> implements IKnowledgeDictService {

    @Autowired
    private KnowledgeDictWordMapper dictWordMapper;

    @Autowired
    private IAppUserClient userClient;

    @Resource
    private IAgentUseClient agentUseClient;

    @Resource
    private ITenantAuthorizeClient tenantAuthorizeClient;

    @Override
    public ResultVo<String> create(DictCreateDto dto) {
        // 租户赋值
        dto.setTenantId(UserContext.getTenantId());

        // 校验名称是否重复
        KnowledgeDict existingDict = this.baseMapper.selectOne(
                Wrappers.lambdaQuery(KnowledgeDict.class)
                        .eq(KnowledgeDict::getDictType, dto.getDictType())
                        .eq(KnowledgeDict::getDictName, dto.getDictName())
                        .eq(KnowledgeDict::getTenantId, dto.getTenantId())
                        .last("limit 1"));

        if (ObjectUtils.isNotEmpty(existingDict)) {
            return ResultVo.fail(AgentDocError.DICT_NAME_EXISTS);
        }

        KnowledgeDict knowledgeDict = new KnowledgeDict();
        // 将dto属性复制到knowledgeDict实体中
        BeanUtils.copyProperties(dto, knowledgeDict);
        // 其他值赋值处理
        knowledgeDict.setDictSn(UUIDUtil.genRandomSn("dict"));
        knowledgeDict.setDictStatus(DictStatusEnum.VALID.getCode());
        knowledgeDict.setCreateUserId(UserContext.getUserId());
        knowledgeDict.setUpdateUserId(UserContext.getUserId());
        this.baseMapper.insert(knowledgeDict);
        //授权
        ResourceAddReqDTO resourceAddReqDTO = new ResourceAddReqDTO();
        resourceAddReqDTO.setResourceObjectId(knowledgeDict.getDictId());
        resourceAddReqDTO.setResourceTenantId(UserContext.getTenantId());
        resourceAddReqDTO.setResourceType(40);
        tenantAuthorizeClient.addResource(resourceAddReqDTO);
        return ResultVo.data(knowledgeDict.getDictSn());
    }

    @Override
    public ResultVo<String> update(DictCreateDto dto) {
        // 租户赋值
        dto.setTenantId(UserContext.getTenantId());

        // 校验词库是否存在
        KnowledgeDict existingDict = checkDictExist(dto.getDictSn());

        // 校验名称是否重复
        KnowledgeDict dict = this.baseMapper.selectOne(
                Wrappers.lambdaQuery(KnowledgeDict.class)
                        .eq(KnowledgeDict::getDictName, dto.getDictName())
                        .eq(KnowledgeDict::getDictType, dto.getDictType())
                        .eq(KnowledgeDict::getTenantId, dto.getTenantId())
                        .ne(KnowledgeDict::getDictId, existingDict.getDictId())
                        .last("limit 1"));

        if (ObjectUtils.isNotEmpty(dict)) {
            return ResultVo.fail(AgentDocError.DICT_NAME_EXISTS);
        }

        BeanUtils.copyProperties(dto, existingDict);
        existingDict.setUpdateUserId(UserContext.getUserId());
        existingDict.setUpdateTime(new Date());
        this.baseMapper.updateById(existingDict);
        return ResultVo.data("更新成功");
    }

    @Override
    public ResultVo<String> delete(String dictSn) {
        // 校验词库是否存在
        KnowledgeDict existingDict = checkDictExist(dictSn);
        //TODO 已关联启用agent无法删除
//        AgentUseKnowledgeDictQueryDTO dto = new AgentUseKnowledgeDictQueryDTO();
//        dto.setDictId(String.valueOf(existingDict.getDictId()));
//        PageInfo<AgentUseKnowledgeDictQueryVO> voPageInfo = agentUseClient.selectUseDictByPage(dto).getData();
//        if (voPageInfo.getTotal() > 0) {
//            throw new ServiceException(AgentDocError.DICT_WORD_RELATIVE_AGENT.getCode(), AgentDocError.DICT_WORD_RELATIVE_AGENT.getMessage());
//        }
        // 删除词库
        existingDict.setDictStatus(DictStatusEnum.DELETED.getCode());
        this.baseMapper.updateById(existingDict);
        //  查看词库下所有的词 进行删除
        dictWordMapper.update(Wrappers.lambdaUpdate(KnowledgeDictWord.class)
                .set(KnowledgeDictWord::getWordStatus, DictWordStatusEnum.DELETED.getCode())
                .eq(KnowledgeDictWord::getDictId, existingDict.getDictId()));
        return ResultVo.data("操作成功");
    }

    @Override
    public ResultVo<DictDetailVo> detail(String dictSn) {
        // 校验词库是否存在
        KnowledgeDict existingDict = checkDictExist(dictSn);
        // 查询详情
        DictDetailVo dictDetailVo = new DictDetailVo();
        BeanUtils.copyProperties(existingDict, dictDetailVo);
        //创建人 更新人
        AppUserVO createUser = userClient.getUserById(dictDetailVo.getCreateUserId()).getData();
        dictDetailVo.setCreator(createUser.getUserName());
        AppUserVO updateUser = userClient.getUserById(dictDetailVo.getUpdateUserId()).getData();
        dictDetailVo.setUpdater(updateUser.getUserName());
        return ResultVo.data(dictDetailVo);
    }


    @Override
    public ResultVo<DictDetailVo> detailById(Integer dictId) {
        KnowledgeDict knowledgeDict = this.baseMapper.selectOne(Wrappers.lambdaQuery(KnowledgeDict.class)
                .eq(KnowledgeDict::getDictId, dictId).last(" limit 1 "));
        if (knowledgeDict == null) {
            throw new ServiceException(AgentDocError.DICT_IS_NOT_EXISTS.getCode(), AgentDocError.DICT_IS_NOT_EXISTS.getMessage());
        }
        // 查询详情
        DictDetailVo dictDetailVo = new DictDetailVo();
        BeanUtils.copyProperties(knowledgeDict, dictDetailVo);
        return ResultVo.data(dictDetailVo);
    }

    @Override
    public ResultVo<PageInfo<DictListVo>> list(DictListDto dto) {
        // 租户赋值
        dto.setTenantId(UserContext.getTenantId());

        //询满足姓名模糊查询的用户
        likeNameQuery(dto);

        //权限处理
        permissionHandler(dto);

        // 设置分页参数
        PageHelper.startPage(dto.getPageNo(), dto.getPageSize());

        // 查询数据源列表
        List<DictListVo> result = this.getBaseMapper().list(dto);

        // 获取所有的创建人姓名和更新人姓名
        List<Integer> creators = result.stream().map(DictListVo::getCreateUserId).distinct().toList();

        List<Integer> updaters = result.stream().map(DictListVo::getUpdateUserId).distinct().toList();

        List<Integer> updaterAndCreator = new ArrayList<>();
        updaterAndCreator.addAll(creators);
        updaterAndCreator.addAll(updaters);

        // 获取创建人名字
        AppUserQueryDTO appUserQuery = new AppUserQueryDTO();
        appUserQuery.setUserIds(updaterAndCreator);
        log.info("查询姓名模糊查询的用户 入参：{}", JSON.toJSONString(appUserQuery));
        List<AppUserVO> list = userClient.list(appUserQuery).getData();
        log.info("查询姓名模糊查询的用户 出参：{}", JSON.toJSONString((list)));

        Map<Integer, AppUserVO> wordMap = list.stream().collect(Collectors.toMap(AppUserVO::getUserId, vo -> vo, (o1, o2) -> o1));

        //相关字段处理
        result.forEach(f -> {
            //姓名处理
            f.setCreator(wordMap.get(f.getCreateUserId()).getUserName());
            f.setUpdater(wordMap.get(f.getUpdateUserId()).getUserName());
        });

        // 返回分页结果
        return ResultVo.data(new PageInfo<>(result));
    }

    //检查词库是否存在
    public KnowledgeDict checkDictExist(String dictSn) {
        KnowledgeDict knowledgeDict = this.baseMapper.selectOne(Wrappers.lambdaQuery(KnowledgeDict.class)
                .eq(KnowledgeDict::getDictSn, dictSn).last(" limit 1 "));
        if (knowledgeDict == null) {
            throw new ServiceException(AgentDocError.DICT_IS_NOT_EXISTS.getCode(), AgentDocError.DICT_IS_NOT_EXISTS.getMessage());
        }
        return knowledgeDict;
    }

    @Override
    public ResultVo<List<AgentByBbVo>> agentByBb(AgentByBbDto dto) {
        return null;
    }

    public void permissionHandler(DictListDto dto) {
        // 构建资源授权请求参数
        ResourceGrantReqDTO resourceGrantReqDTO = new ResourceGrantReqDTO();
        resourceGrantReqDTO.setResourceType(ResourceTypeEnum.DICTIONARY.getCode());
        resourceGrantReqDTO.setGrantObjectType(GrantObjectTypeEnum.USER.getCode());
        Integer grantObjectId = UserContext.getUserId();
        List<Integer> grantTypes = Lists.newArrayList();
        grantTypes.add(GrantTypeEnum.OWNER.getCode());
        grantTypes.add(GrantTypeEnum.MANAGE.getCode());
        grantTypes.add(GrantTypeEnum.COORDINATION.getCode());
        resourceGrantReqDTO.setGrantObjectId(grantObjectId);
        resourceGrantReqDTO.setGrantTypes(grantTypes);
        // 查询授权资源列表
        ResultVo<List<ResourceVO>> listResultVo = tenantAuthorizeClient.queryGrantResourceList(resourceGrantReqDTO);
        AssertUtil.isTrue(listResultVo.isSuccess(), "获取知识库授权资源失败");
        List<ResourceVO> listResource = listResultVo.getData();
        if (CollectionUtils.isEmpty(listResource)) {
            return;
        }
        dto.setPermissionIds(listResource.stream().map(ResourceVO::getResourceObjectId).toList());
    }

    public void likeNameQuery(DictListDto dto) {
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(dto.getKeyword())) {
            AppUserQueryDTO appUserQuery = new AppUserQueryDTO();
            appUserQuery.setUserName(dto.getKeyword());
            log.info("查询满足姓名模糊查询的用户 入参：{}", JSON.toJSONString((appUserQuery)));
            List<AppUserVO> list = userClient.list(appUserQuery).getData();
            log.info("查询满足姓名模糊查询的用户 出参：{}", JSON.toJSONString(list));
            dto.setUserIdsByName(list.stream().map(AppUserVO::getUserId).toList());
        }
    }
}
