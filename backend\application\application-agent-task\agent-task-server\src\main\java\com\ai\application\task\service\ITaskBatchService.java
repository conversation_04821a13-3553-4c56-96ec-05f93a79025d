package com.ai.application.task.service;

import com.ai.application.task.api.dto.TaskAddDTO;
import com.ai.application.task.api.dto.TaskBatchAddDTO;
import com.ai.application.task.api.dto.TaskRunResultDTO;
import com.ai.application.task.api.vo.TaskBatchDetailVO;
import com.ai.application.task.api.vo.TaskDetailVO;
import com.ai.application.task.api.vo.TaskBatchPageVO;
import com.github.pagehelper.PageInfo;
import com.ai.application.task.api.dto.TaskDTO;
import com.ai.application.task.api.dto.query.TaskQueryDTO;
import com.ai.application.task.api.vo.TaskVO;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * 计划任务表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
public interface ITaskBatchService {

        PageInfo<TaskBatchPageVO> page(TaskQueryDTO queryDto);

        void add(TaskBatchAddDTO dto);

        void update(TaskDTO dto);

        void delete(String taskSn);

        void stop(String taskSn);

        TaskVO get(Integer id);

        TaskBatchDetailVO detail(String taskSn);

        void getDataSetTemplate(String agentSn, String versionSn, HttpServletResponse response);

        void saveTaskRunResult(TaskRunResultDTO dto);
}