package com.ai.application.agent.run.executor;

import com.ai.application.agent.run.dto.DocumentSearchResultDTO;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.service.IDocumentSearchService;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 文档知识检索节点执行器单元测试
 */
@ExtendWith(MockitoExtension.class)
class DocumentSearchNodeExecutorTest {

    @Mock
    private IDocumentSearchService documentSearchService;

    @InjectMocks
    private DocumentSearchNodeExecutor documentSearchNodeExecutor;

    private WorkflowContext workflowContext;
    private NodeContext nodeContext;
    private Map<String, Object> nodeDefinition;
    private Map<String, Object> inputParameters;
    private Map<String, Object> outputParameters;

    @BeforeEach
    void setUp() {
        // 初始化工作流上下文
        workflowContext = new WorkflowContext();
        workflowContext.setWorkflowInstanceId(12345L);
        workflowContext.setCurrentNodeKey("document_search_node_001");
        workflowContext.setGlobalVars(new HashMap<>());
        workflowContext.getGlobalVars().put("authorization", "Bearer test-token");
        workflowContext.getGlobalVars().put("tenantName", "TEST_TENANT");

        // 初始化节点上下文
        nodeContext = new NodeContext();
        nodeContext.setNodeKey("document_search_node_001");
        nodeContext.setStatus(NodeStatus.INIT);
        nodeContext.setOutput(new HashMap<>());

        // 初始化输入参数
        inputParameters = new HashMap<>();
        inputParameters.put("type", "embedding");
        inputParameters.put("knowledgeInventorySn", "7c1d4ec1-fba9-458c-aa3a-a72bbe77f99f");
        inputParameters.put("knowledgeType", "sn");
        inputParameters.put("searchContent", "documentName");
        inputParameters.put("searchKnowledgeContent", "adf");
        inputParameters.put("topK", "10");
        inputParameters.put("conditions", Arrays.asList(
                Map.of("target", null),
                Map.of("target", null)
        ));

        // 初始化输出参数
        outputParameters = new HashMap<>();
        outputParameters.put("docFile", "2309baf83c");

        // 初始化节点定义
        nodeDefinition = new HashMap<>();
        nodeDefinition.put("inputParameters", inputParameters);
        nodeDefinition.put("outputParameters", outputParameters);
        nodeContext.setNodeDefinition(nodeDefinition);

        // 设置节点上下文
        workflowContext.getNodeContexts().put("document_search_node_001", nodeContext);
    }

    @Test
    void testExecuteSuccessWithEmbeddingSearch() {
        // 准备测试数据
        DocumentSearchResultDTO mockResult = DocumentSearchResultDTO.builder()
                .output("[{\"fragmentId\":\"frag1\",\"content\":\"测试文档片段\"}]")
                .success(true)
                .totalCount(1)
                .fragments(Arrays.asList(
                        DocumentSearchResultDTO.DocumentFragmentDTO.builder()
                                .fragmentId("frag1")
                                .documentId("doc1")
                                .fileName("test_document.pdf")
                                .content("测试文档片段内容")
                                .score(0.95)
                                .pageNumber(1)
                                .method("embedding")
                                .build()
                ))
                .build();

        // 模拟服务调用
        when(documentSearchService.executeDocumentSearch(any(), anyString()))
                .thenReturn(ResultVo.data(mockResult));

        // 执行测试
        assertDoesNotThrow(() -> documentSearchNodeExecutor.execute(workflowContext));

        // 验证结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        assertNotNull(nodeContext.getOutput().get("docFile"));
        assertNotNull(nodeContext.getOutput().get("fragments"));
        assertEquals(1, nodeContext.getOutput().get("totalCount"));
        assertNotNull(nodeContext.getEndTime());

        // 验证服务调用
        verify(documentSearchService, times(1)).executeDocumentSearch(any(), eq("Bearer test-token"));
    }

    @Test
    void testExecuteSuccessWithMultipleKnowledgeInventories() {
        // 修改为多知识库检索
        inputParameters.put("knowledgeInventorySnList", Arrays.asList("kb1", "kb2"));
        inputParameters.remove("knowledgeInventorySn");

        DocumentSearchResultDTO mockResult = DocumentSearchResultDTO.builder()
                .output("[]")
                .success(true)
                .totalCount(0)
                .fragments(Collections.emptyList())
                .build();

        when(documentSearchService.executeDocumentSearch(any(), anyString()))
                .thenReturn(ResultVo.data(mockResult));

        // 执行测试
        assertDoesNotThrow(() -> documentSearchNodeExecutor.execute(workflowContext));

        // 验证结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        assertEquals("[]", nodeContext.getOutput().get("docFile"));
    }

    @Test
    void testExecuteSuccessWithExpandSearch() {
        // 修改为扩展检索
        inputParameters.put("searchModel", "expand");
        inputParameters.put("expandParams", Map.of(
                "fragmentType", "paragraph",
                "length", 500,
                "forward", 2,
                "backward", 2
        ));

        DocumentSearchResultDTO mockResult = DocumentSearchResultDTO.builder()
                .output("[{\"fragmentId\":\"expand_frag1\"}]")
                .success(true)
                .totalCount(1)
                .fragments(Arrays.asList(
                        DocumentSearchResultDTO.DocumentFragmentDTO.builder()
                                .fragmentId("expand_frag1")
                                .content("扩展检索片段内容")
                                .method("expand")
                                .build()
                ))
                .build();

        when(documentSearchService.executeDocumentSearch(any(), anyString()))
                .thenReturn(ResultVo.data(mockResult));

        // 执行测试
        assertDoesNotThrow(() -> documentSearchNodeExecutor.execute(workflowContext));

        // 验证结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        assertTrue(nodeContext.getOutput().get("docFile").toString().contains("expand_frag1"));
    }

    @Test
    void testExecuteSuccessWithManualRetriever() {
        // 修改为手动检索
        inputParameters.put("searchModel", "all");
        inputParameters.put("searchKnowledge", "[{\"id\":\"manual1\",\"content\":\"手动知识\"}]");

        DocumentSearchResultDTO mockResult = DocumentSearchResultDTO.builder()
                .output("[{\"fragmentId\":\"manual_frag1\"}]")
                .success(true)
                .totalCount(1)
                .fragments(Arrays.asList(
                        DocumentSearchResultDTO.DocumentFragmentDTO.builder()
                                .fragmentId("manual_frag1")
                                .content("手动检索片段内容")
                                .method("manual")
                                .build()
                ))
                .build();

        when(documentSearchService.executeDocumentSearch(any(), anyString()))
                .thenReturn(ResultVo.data(mockResult));

        // 执行测试
        assertDoesNotThrow(() -> documentSearchNodeExecutor.execute(workflowContext));

        // 验证结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        assertTrue(nodeContext.getOutput().get("docFile").toString().contains("manual_frag1"));
    }

    @Test
    void testExecuteMissingInputParameters() {
        // 准备测试数据 - 缺少输入参数
        nodeDefinition.remove("inputParameters");

        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, 
            () -> documentSearchNodeExecutor.execute(workflowContext));

        assertEquals(ExecutorError.NODE_DEFINITION_IS_NULL.getCode(), exception.getCode());
        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
        assertNotNull(nodeContext.getErrorMsg());
        assertNotNull(nodeContext.getEndTime());
    }

    @Test
    void testExecuteBlankSearchContent() {
        // 准备测试数据 - 空的检索内容
        inputParameters.put("searchContent", "");
        inputParameters.put("searchKnowledgeContent", "");

        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, 
            () -> documentSearchNodeExecutor.execute(workflowContext));

        assertEquals(ExecutorError.DOCUMENT_SEARCH_CONTENT_IS_BLANK.getCode(), exception.getCode());
        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
    }

    @Test
    void testExecuteBlankKnowledgeInventory() {
        // 准备测试数据 - 空的知识库编号
        inputParameters.put("knowledgeInventorySn", "");
        inputParameters.remove("knowledgeInventorySnList");

        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, 
            () -> documentSearchNodeExecutor.execute(workflowContext));

        assertEquals(ExecutorError.KNOWLEDGE_INVENTORY_SN_IS_NULL.getCode(), exception.getCode());
        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
    }

    @Test
    void testExecuteWithVariableReplacement() {
        // 准备测试数据 - 使用变量替换
        workflowContext.getGlobalVars().put("searchQuery", "人工智能相关文档");
        workflowContext.getGlobalVars().put("targetKnowledgeBase", "ai_knowledge_base");
        
        inputParameters.put("searchContent", "$searchQuery");
        inputParameters.put("knowledgeInventorySn", "$targetKnowledgeBase");

        DocumentSearchResultDTO mockResult = DocumentSearchResultDTO.builder()
                .output("[]")
                .success(true)
                .totalCount(0)
                .fragments(Collections.emptyList())
                .build();

        when(documentSearchService.executeDocumentSearch(any(), anyString()))
                .thenReturn(ResultVo.data(mockResult));

        // 执行测试
        assertDoesNotThrow(() -> documentSearchNodeExecutor.execute(workflowContext));

        // 验证结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
    }

    @Test
    void testExecuteWithTopKAndSearchMethods() {
        // 准备测试数据 - 设置TopK和检索方法参数
        inputParameters.put("topK", "5");
        inputParameters.put("embedding", "3");
        inputParameters.put("keyword", "2");
        inputParameters.put("text", "1");

        DocumentSearchResultDTO mockResult = DocumentSearchResultDTO.builder()
                .output("[]")
                .success(true)
                .totalCount(0)
                .fragments(Collections.emptyList())
                .build();

        when(documentSearchService.executeDocumentSearch(any(), anyString()))
                .thenReturn(ResultVo.data(mockResult));

        // 执行测试
        assertDoesNotThrow(() -> documentSearchNodeExecutor.execute(workflowContext));

        // 验证结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
    }

    @Test
    void testExecuteServiceError() {
        // 模拟服务调用失败
        when(documentSearchService.executeDocumentSearch(any(), anyString()))
                .thenReturn(ResultVo.error("文档检索服务异常"));

        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, 
            () -> documentSearchNodeExecutor.execute(workflowContext));

        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
        assertNotNull(nodeContext.getErrorMsg());
        assertTrue(nodeContext.getErrorMsg().contains("文档知识检索执行失败"));
    }

    @Test
    void testExecuteNullResult() {
        // 模拟服务返回空结果
        when(documentSearchService.executeDocumentSearch(any(), anyString()))
                .thenReturn(ResultVo.data(null));

        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, 
            () -> documentSearchNodeExecutor.execute(workflowContext));

        assertEquals(ExecutorError.DOCUMENT_SEARCH_RESULT_IS_NULL.getCode(), exception.getCode());
        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
    }

    @Test
    void testGetType() {
        assertEquals("DOCUMENT_KNOWLEDGE_SEARCH", documentSearchNodeExecutor.getType());
    }
}
