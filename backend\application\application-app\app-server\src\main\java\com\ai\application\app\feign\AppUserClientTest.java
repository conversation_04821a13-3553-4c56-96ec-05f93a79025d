package com.ai.application.app.feign;

import com.ai.application.app.api.dto.query.AppUserQueryDTO;
import com.ai.application.app.api.dto.query.AppUserVerifyPasswordDTO;
import com.ai.application.app.api.feign.IAppUserClient;
import com.ai.application.app.api.feign.IAppUserTestClient;
import com.ai.application.app.api.vo.AppUserDetailVO;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.application.app.service.IAppUserService;
import com.ai.framework.core.vo.ResultVo;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@AllArgsConstructor
public class AppUserClientTest implements IAppUserTestClient {
    private final IAppUserService appUserService;


    @Override
    public ResultVo<AppUserVO> getUserByIdTest(Integer userId) {
        return null;
    }

    @Override
    public ResultVo<List<AppUserVO>> listTest(AppUserQueryDTO queryDto) {
        return null;
    }

    @Override
    public ResultVo<List<AppUserVO>> test(AppUserQueryDTO queryDto) {
        return null;
    }
}
