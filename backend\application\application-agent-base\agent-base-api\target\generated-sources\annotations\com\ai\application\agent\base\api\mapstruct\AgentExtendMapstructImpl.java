package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.dto.AgentExtendAddDTO;
import com.ai.application.agent.base.api.dto.AgentExtendUpdateDTO;
import com.ai.application.agent.base.api.entity.AgentExtend;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-13T10:32:23+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentExtendMapstructImpl implements AgentExtendMapstruct {

    @Override
    public AgentExtend toEntityAdd(AgentExtendAddDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentExtend agentExtend = new AgentExtend();

        agentExtend.setItemName( dto.getItemName() );
        agentExtend.setItemValue( dto.getItemValue() );
        agentExtend.setAgentId( dto.getAgentId() );

        return agentExtend;
    }

    @Override
    public AgentExtend toEntityUpdate(AgentExtendUpdateDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentExtend agentExtend = new AgentExtend();

        agentExtend.setItemId( dto.getItemId() );
        agentExtend.setItemName( dto.getItemName() );
        agentExtend.setItemValue( dto.getItemValue() );
        agentExtend.setItemStatus( dto.getItemStatus() );
        agentExtend.setAgentId( dto.getAgentId() );

        return agentExtend;
    }
}
