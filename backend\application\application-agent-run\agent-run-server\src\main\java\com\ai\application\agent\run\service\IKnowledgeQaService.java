package com.ai.application.agent.run.service;

import com.ai.application.agent.run.dto.DocChatRequestDTO;
import com.ai.application.agent.run.dto.KnowledgeQaRequestDTO;
import com.ai.application.agent.run.dto.KnowledgeQaResultDTO;
import com.ai.framework.core.vo.ResultVo;

import java.util.List;

/**
 * 知识问答服务接口
 */
public interface IKnowledgeQaService {

    /**
     * 执行知识问答
     *
     * @param request 知识问答请求
     * @param authorization 授权信息
     * @return 知识问答结果
     */
    ResultVo<KnowledgeQaResultDTO> executeKnowledgeQa(KnowledgeQaRequestDTO request, String authorization);

    /**
     * 通过文档聊天获取知识片段
     *
     * @param request 文档聊天请求
     * @param authorization 授权信息
     * @return 知识片段字符串
     */
    ResultVo<String> getKnowledgeFragmentByDocChat(DocChatRequestDTO request, String authorization);

    /**
     * 通过知识片段执行问答
     *
     * @param request 知识问答请求
     * @param knowledgeFragment 知识片段
     * @param authorization 授权信息
     * @return 知识问答结果
     */
    ResultVo<KnowledgeQaResultDTO> executeQaByFragment(KnowledgeQaRequestDTO request, String knowledgeFragment, String authorization);

    /**
     * 获取知识树并添加到知识列表
     *
     * @param knowledgeSn 知识编号
     * @param knowledgeList 知识列表
     * @return 知识库编号列表
     */
    List<String> getKnowledgeTreeAndAddKnowList(Object knowledgeSn, List<String> knowledgeList);


    /**
     * 知识库信息
     */
    class KnowledgeInventoryInfo {
        private String inventorySn;
        private String modelSn;
        private String name;
        private Integer status;

        // Getters and Setters
        public String getInventorySn() {
            return inventorySn;
        }

        public void setInventorySn(String inventorySn) {
            this.inventorySn = inventorySn;
        }

        public String getModelSn() {
            return modelSn;
        }

        public void setModelSn(String modelSn) {
            this.modelSn = modelSn;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }
    }
}
