package com.ai.application.tenant.authorize.service.impl;

import com.ai.application.agent.base.api.feign.IAgentClient;
import com.ai.application.agent.base.api.vo.AgentVO;
import com.ai.application.app.api.dto.query.AppUserQueryDTO;
import com.ai.application.app.api.feign.IAppUserClient;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.application.base.model.api.feign.IModelClient;
import com.ai.application.base.model.api.vo.ModelInfoVO;
import com.ai.application.tenant.api.feign.ITenantClient;
import com.ai.application.tenant.api.vo.TenantDepartmentTreeVO;
import com.ai.application.tenant.authorize.api.dto.ResourceAddReqDTO;
import com.ai.application.tenant.authorize.api.dto.ResourceGrantReqDTO;
import com.ai.application.tenant.authorize.api.dto.UserAuthorizationReqDTO;
import com.ai.application.tenant.authorize.api.dto.query.ResourceGrantQueryDTO;
import com.ai.application.tenant.authorize.api.dto.query.ResourceQueryDTO;
import com.ai.application.tenant.authorize.api.dto.query.UserAuthorizationQueryDTO;
import com.ai.application.tenant.authorize.api.entity.Resource;
import com.ai.application.tenant.authorize.api.entity.ResourceGrant;
import com.ai.application.tenant.authorize.api.enums.GrantObjectTypeEnum;
import com.ai.application.tenant.authorize.api.enums.GrantTypeEnum;
import com.ai.application.tenant.authorize.api.enums.ResourceTypeEnum;
import com.ai.application.tenant.authorize.api.vo.*;
import com.ai.application.tenant.authorize.mapper.ResourceMapper;
import com.ai.application.tenant.authorize.service.IAuthorizationService;
import com.ai.application.tenant.authorize.service.IResourceGrantService;
import com.ai.application.tenant.authorize.service.IResourceService;
import com.ai.application.tenant.authorize.vo.UserAuthorizationResultVO;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.validator.AssertUtil;
import com.ai.framework.core.vo.ResultVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@AllArgsConstructor
@Slf4j
public class AuthorizationServiceImpl implements IAuthorizationService {
    private final IResourceGrantService resourceGrantService;
    private final IResourceService resourceService;
    private final ITenantClient tenantClient;
    private final IAppUserClient appUserClient;
    private final IAgentClient agentClient;
    private final IModelClient modelClient;
    private final ResourceMapper resourceMapper;

    @Transactional(readOnly = true)
    @Override
    public List<ResourceVO> queryGrantResourceList(ResourceGrantReqDTO dto) {
        log.info("授权资源列表查询，参数={}", JsonUtils.toJsonString(dto));
        ResourceGrantQueryDTO grantQueryDTO = new ResourceGrantQueryDTO();
        BeanUtils.copyProperties(dto, grantQueryDTO);
        grantQueryDTO.setGrantTypes(dto.getGrantTypes());
        grantQueryDTO.setGrantObjectIds(Collections.singletonList(dto.getGrantObjectId()));
        if(Objects.nonNull(dto.getGrantObjectId())){
            grantQueryDTO.setGrantObjectIds(Collections.singletonList(dto.getGrantObjectId()));
        }
        if(GrantObjectTypeEnum.USER.getCode().equals(dto.getGrantObjectType())){
            ResultVo<AppUserVO> appUserVO = appUserClient.getUserById(dto.getGrantObjectId());
            Integer deptId = appUserVO.getData().getDeptId();
            ResultVo<List<Integer>> parentDepartmentIds = tenantClient.findParentDepartmentIds(deptId);
            if(CollectionUtils.isNotEmpty(parentDepartmentIds.getData())) {
                grantQueryDTO.getGrantObjectIds().addAll(parentDepartmentIds.getData());
            }
        }
        List<ResourceGrantVO> resourceGrants = resourceGrantService.list(grantQueryDTO);
        if(CollectionUtils.isEmpty(resourceGrants)){
            return Lists.newArrayList();
        }
        List<Integer> resourceIds = resourceGrants.stream().map(ResourceGrantVO::getResourceId).toList();

        ResourceQueryDTO queryDto = new ResourceQueryDTO();
        queryDto.setTenantId(UserContext.getTenantId());
        queryDto.setResourceIds(resourceIds);
        return resourceService.list(queryDto);
    }

    @Override
    public List<UserAuthorizationResultVO> list(UserAuthorizationQueryDTO dto){
        log.info("用户授权列表查询，参数={}", JsonUtils.toJsonString(dto));
        List<UserAuthorizationResultVO> retList = new ArrayList<>();

        //部门列表查询
        ResultVo<TenantDepartmentTreeVO> treeDepartment =  tenantClient.getTreeDepartment();
        TenantDepartmentTreeVO treeDept = treeDepartment.getData();

        //用户列表查询
        ResultVo<List<AppUserVO>> listResultVo = appUserClient.list(new AppUserQueryDTO());
        List<AppUserVO> listAppUser = listResultVo.getData();

        //查询授权资源
        ResourceGrantQueryDTO grantQueryDTO = new ResourceGrantQueryDTO();
        grantQueryDTO.setResourceType(dto.getResourceType());
        grantQueryDTO.setResourceObjectId(this.convertResourceObjectSnToId(dto.getResourceType(),dto.getResourceSn()));
        List<ResourceGrantVO> resourceGrants = resourceGrantService.list(grantQueryDTO);
        Map<Integer, List<ResourceGrantVO>> mapGrantData = null;
        if(CollectionUtils.isNotEmpty(resourceGrants)) {
            mapGrantData = resourceGrants.stream().collect(Collectors.groupingBy(ResourceGrantVO::getGrantType));
        }

        Map<Integer, List<ResourceGrantVO>> finalMapGrantData = mapGrantData;
        //按 授权类型:10-使用,20-协作,30-管理,50-所有者 分组
        Stream.of(GrantTypeEnum.values()).forEach(grantTypeEnum -> {
            UserAuthorizationResultVO resultVO = new UserAuthorizationResultVO();
            List<Integer> grantDimensions = new ArrayList<>();
            resultVO.setGrantType(grantTypeEnum.getCode());
            resultVO.setGrantName(grantTypeEnum.getName());

            //部门列表复制
            resultVO.setListDept(treeDept);

            //用户列表复制
            List<AuthorizeUserVO> listUser = new ArrayList<>();
            listAppUser.forEach(user->{
                AuthorizeUserVO authorizeUserVO = new AuthorizeUserVO();
                BeanUtils.copyProperties(user, authorizeUserVO);
                listUser.add(authorizeUserVO);
            });
            resultVO.setListUser(listUser);

            //授权数据
            if(Objects.nonNull(finalMapGrantData)){
                List<ResourceGrantVO> resourceGrantVOS = finalMapGrantData.get(grantTypeEnum.getCode());
                if(CollectionUtils.isNotEmpty(resourceGrantVOS)){
                    //查部门
                    List<ResourceGrantVO> listGrantDept = resourceGrantVOS.stream().filter(a -> a.getGrantObjectType().equals(GrantObjectTypeEnum.DEPARTMENT.getCode())).toList();
                    if(CollectionUtils.isNotEmpty(listGrantDept)){
                        grantDimensions.add(2);
                        resultVO.setListGrantDeptId(listGrantDept.stream().map(ResourceGrantVO::getGrantObjectId).toList());
                    }
                    //查用户
                    List<ResourceGrantVO> listGrantUser = resourceGrantVOS.stream().filter(a -> a.getGrantObjectType().equals(GrantObjectTypeEnum.USER.getCode())).toList();
                    if(CollectionUtils.isNotEmpty(listGrantUser)){
                        grantDimensions.add(1);
                        //userId转sn
                        List<Integer> userIds = listGrantUser.stream().map(ResourceGrantVO::getGrantObjectId).toList();
                        List<String> userSns = listAppUser.stream().filter(a -> userIds.contains(a.getUserId())).map(AppUserVO::getUserSn).toList();
                        resultVO.setListGrantUserSn(userSns);
                    }
                }
            }
            resultVO.setGrantDimensions(grantDimensions);
            retList.add(resultVO);
        });

        return retList;
    }

    private Integer convertResourceObjectSnToId(Integer resourceType,String resourceSn){
        //智能体
        if (ResourceTypeEnum.AGENT.getCode().equals(resourceType)) {
            ResultVo<AgentVO> agentBySn = agentClient.getAgentBySn(resourceSn);
            return agentBySn.getData().getAgentId();
        }

        //模型
        if (ResourceTypeEnum.MODEL.getCode().equals(resourceType)) {
            ResultVo<ModelInfoVO> agentBySn = modelClient.getModelBySn(resourceSn);
            return agentBySn.getData().getModelId();
        }

        return 0;
    }

    @Override
    public void addResource(ResourceAddReqDTO dto){
        log.info("资源新增,参数={}", JsonUtils.toJsonString(dto));
        Resource resource = resourceMapper.getResourceByResourceType(dto.getResourceObjectId(), dto.getResourceType());
        if(Objects.isNull(resource)){
            Resource entity = new Resource();
            entity.setResourceType(dto.getResourceType());
            entity.setResourceTenantId(dto.getResourceTenantId());
            entity.setResourceObjectId(dto.getResourceObjectId());
            entity.setResourceStatus(1);
            resourceMapper.insert(entity);
            return;
        }
        resource.setResourceType(dto.getResourceType());
        resource.setResourceTenantId(dto.getResourceTenantId());
        resource.setResourceObjectId(dto.getResourceObjectId());
        resource.setResourceStatus(1);
        resourceMapper.updateById(resource);
    }

    /**
     * 资源授权处理
     * @param dto
     */
    @Override
    public void authorization(UserAuthorizationReqDTO dto){
        log.info("资源授权列，参数={}", JsonUtils.toJsonString(dto));

        //查询资源
        ResourceQueryDTO resourceQueryDTO = new ResourceQueryDTO();
        resourceQueryDTO.setResourceType(dto.getResourceType());
        List<ResourceVO> listResource = resourceService.list(resourceQueryDTO);
        AssertUtil.isNotEmpty(listResource,"授权失败：资源不存在");
        ResourceVO resourceVO = listResource.get(0);

        //查询当前表中存储的授权资源
        ResourceGrantQueryDTO grantQueryDTO = new ResourceGrantQueryDTO();
        grantQueryDTO.setResourceId(resourceVO.getResourceId());
        grantQueryDTO.setResourceType(dto.getResourceType());
        grantQueryDTO.setResourceObjectId(resourceVO.getResourceObjectId());
        grantQueryDTO.setGrantTypes(Collections.singletonList(dto.getGrantType()));
        List<ResourceGrantVO> resourceGrants = resourceGrantService.list(grantQueryDTO);

        //授权基础数据
        ResourceGrant resourceGrant = new ResourceGrant();
        resourceGrant.setResourceId(resourceVO.getResourceId());
        resourceGrant.setResourceType(resourceVO.getResourceType());
        resourceGrant.setResourceObjectId(resourceVO.getResourceObjectId());
        resourceGrant.setGrantType(dto.getGrantType());
        resourceGrant.setGrantStatus(1);

        List<ResourceGrant> addGrantData = new ArrayList<>();
        if(CollectionUtils.isEmpty(resourceGrants)){
            //部门授权数据
            if(CollectionUtils.isNotEmpty(dto.getGrantDeptIds())) {
                dto.getGrantDeptIds().forEach(grantDeptId -> {
                    ResourceGrant resourceGrantAdd = new ResourceGrant();
                    BeanUtils.copyProperties(resourceGrant, resourceGrantAdd);
                    resourceGrantAdd.setGrantObjectType(GrantObjectTypeEnum.DEPARTMENT.getCode());
                    resourceGrantAdd.setGrantObjectId(grantDeptId);
                    addGrantData.add(resourceGrantAdd);
                });
            }

            //用户授权数据
            if(CollectionUtils.isNotEmpty(dto.getGrantUserSns())){
                //用户列表查询
                ResultVo<List<AppUserVO>> listResultVo = appUserClient.list(new AppUserQueryDTO());
                List<AppUserVO> listAppUser = listResultVo.getData();

                dto.getGrantUserSns().forEach(grantUserSn->{
                    listAppUser.stream().filter(a->a.getUserSn().equals(grantUserSn)).findFirst().ifPresent(b->{
                        ResourceGrant resourceGrantAdd = new ResourceGrant();
                        BeanUtils.copyProperties(resourceGrant, resourceGrantAdd);
                        resourceGrantAdd.setGrantObjectType(GrantObjectTypeEnum.USER.getCode());
                        resourceGrantAdd.setGrantObjectId(b.getUserId());
                        addGrantData.add(resourceGrantAdd);
                    });
                });
            }

            //数据保存处理
            resourceGrantService.addBatch(addGrantData);
            return;
        }

        //删除处理
        List<ResourceGrant> delGrantData = new ArrayList<>();
        //部门授权保存
        if(CollectionUtils.isNotEmpty(dto.getGrantDeptIds())){
            List<ResourceGrantVO> listDbDepts = resourceGrants.stream().filter(a -> a.getGrantObjectType().equals(GrantObjectTypeEnum.DEPARTMENT.getCode())).toList();
            //删除授权资源
            List<ResourceGrantVO> listDeptDel = listDbDepts.stream().filter(a -> !dto.getGrantDeptIds().contains(a.getGrantObjectId())).toList();
            if(CollectionUtils.isNotEmpty(listDeptDel)){
                listDeptDel.forEach(grantDept->{
                    ResourceGrant resourceGrantDel = new ResourceGrant();
                    resourceGrantDel.setGrantId(grantDept.getGrantId());
                    delGrantData.add(resourceGrantDel);
                });
            }

            //添加授权资源
            List<Integer> listDBDeptIds = listDbDepts.stream().map(ResourceGrantVO::getGrantObjectId).toList();
            List<Integer> listAddDeptIds = dto.getGrantDeptIds().stream().filter(a -> !listDBDeptIds.contains(a)).toList();
            listAddDeptIds.forEach(grantDeptId->{
                ResourceGrant resourceGrantAdd = new ResourceGrant();
                BeanUtils.copyProperties(resourceGrant, resourceGrantAdd);
                resourceGrantAdd.setGrantObjectType(GrantObjectTypeEnum.DEPARTMENT.getCode());
                resourceGrantAdd.setGrantObjectId(grantDeptId);
                addGrantData.add(resourceGrantAdd);
            });
        }

        //用户授权保存
        if(CollectionUtils.isNotEmpty(dto.getGrantUserSns())){
            List<ResourceGrantVO> listDbUsers = resourceGrants.stream().filter(a -> a.getGrantObjectType().equals(GrantObjectTypeEnum.USER.getCode())).toList();
            //用户列表查询
            ResultVo<List<AppUserVO>> listResultVo = appUserClient.list(new AppUserQueryDTO());
            List<AppUserVO> listAppUser = listResultVo.getData();

            List<Integer> listDbAppUserIds = listAppUser.stream().filter(a -> dto.getGrantUserSns().contains(a.getUserSn())).map(AppUserVO::getUserId).toList();
            //删除授权资源
            List<ResourceGrantVO> listUserDel = listDbUsers.stream().filter(a -> !listDbAppUserIds.contains(a.getGrantObjectId())).toList();
            if(CollectionUtils.isNotEmpty(listUserDel)){
                listUserDel.forEach(grantUser->{
                    ResourceGrant resourceGrantDel = new ResourceGrant();
                    resourceGrantDel.setGrantId(grantUser.getGrantId());
                    delGrantData.add(resourceGrantDel);
                });
            }

            //添加授权资源
            List<Integer> listDBResourceGrantUserIds = listDbUsers.stream().map(ResourceGrantVO::getGrantObjectId).toList();
            List<Integer> listAddUserIds = listDbAppUserIds.stream().filter(a -> !listDBResourceGrantUserIds.contains(a)).toList();
            listAddUserIds.forEach(grantUserId->{
                ResourceGrant resourceGrantAdd = new ResourceGrant();
                BeanUtils.copyProperties(resourceGrant, resourceGrantAdd);
                resourceGrantAdd.setGrantObjectType(GrantObjectTypeEnum.USER.getCode());
                resourceGrantAdd.setGrantObjectId(grantUserId);
                addGrantData.add(resourceGrantAdd);
            });
        }

        //数据保存处理
        resourceGrantService.addBatch(addGrantData);
        //删除授权数据
        resourceGrantService.delBatch(delGrantData);
    }
}
