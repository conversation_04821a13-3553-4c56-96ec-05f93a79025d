package com.ai.application.base.log.api.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 通知方式:10-邮件,20-短信,30-钉钉,40-企微,50-飞书,60-<PERSON>lack,70-Webhook,80-站内消息
 */
@Getter
public enum NTypeTypeEnum {
    MAIL(10, "邮件"),
    SMS(20, "短信"),
    DINGTALK(30, "钉钉"),
    WECHAT(40, "企微"),
    FEISHU(50, "飞书"),
    SLACK(60, "Slack"),
    WEBHOOK(70, "Webhook"),
    INTERNAL_MESSAGE(80, "站内消息"),
    ;

    private Integer code;
    private String title;

    NTypeTypeEnum(Integer code, String title) {
        this.code = code;
        this.title = title;
    }

    public static String getTitle(Integer code) {
        for(NTypeTypeEnum vo :values() ) {
            if (Objects.equals(vo.code, code)) {
                return vo.title;
            }
        }
        return null;
    }
}
