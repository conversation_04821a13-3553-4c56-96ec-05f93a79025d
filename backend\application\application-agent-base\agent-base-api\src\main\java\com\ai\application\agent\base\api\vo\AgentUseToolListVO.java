package com.ai.application.agent.base.api.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 智能体使用工具表
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Data
@Schema(name = "")
public class AgentUseToolListVO {
    @Schema(description = "")
    private Integer atlId;

    /**
     * 状态 0失效 1有效
     */
    @Schema(description = "状态 0失效 1有效")
    private Integer atlStatus;

    /**
     * 智能体id
     */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
     * 智能体版本id
     */
    @Schema(description = "智能体版本id")
    private Integer versionId;

    /**
     * 工具id
     */
    @Schema(description = "工具id")
    private Integer toolId;

    /**
     * 额外属性
     */
    @Schema(description = "额外属性")
    private String toolExtend;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}