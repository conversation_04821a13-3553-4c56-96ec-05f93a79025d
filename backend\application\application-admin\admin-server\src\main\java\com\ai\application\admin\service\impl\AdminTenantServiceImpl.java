package com.ai.application.admin.service.impl;

import com.ai.application.admin.api.dto.GrantResourceDTO;
import com.ai.application.admin.api.dto.TenantAddDTO;
import com.ai.application.admin.api.dto.TenantUpdateDTO;
import com.ai.application.admin.api.dto.TenantUpdatePasswordDTO;
import com.ai.application.admin.api.dto.query.TenantQueryDTO;
import com.ai.application.admin.api.dto.query.TenantQueryPageDTO;
import com.ai.application.admin.api.entity.*;
import com.ai.application.admin.api.enums.*;
import com.ai.application.admin.api.mapstruct.TenantMapstruct;
import com.ai.application.admin.api.vo.AdminResourceVO;
import com.ai.application.admin.api.vo.TenantDetailVO;
import com.ai.application.admin.api.vo.TenantSimpleVO;
import com.ai.application.admin.api.vo.TenantVO;
import com.ai.application.admin.mapper.*;
import com.ai.application.admin.service.IAdminTenantService;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.enums.AppEnum;
import com.ai.framework.core.enums.RoleEnum;
import com.ai.framework.core.enums.StatusEnum;
import com.ai.framework.core.util.BusinessAssertUtil;
import com.ai.framework.core.util.password.PasswordUtils;
import com.ai.framework.core.util.password.SaltedPassword;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.util.validator.AssertUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 租户表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
@AllArgsConstructor
public class AdminTenantServiceImpl implements IAdminTenantService {
    private final TenantMapper tenantMapper;
    private final TenantExtendMapper tenantExtendMapper;
    private final TenantMapstruct tenantMapstruct;
    private final TenantDepartmentMapper tenantDepartmentMapper;
    private final AdminUserMapper appUserMapper;
    private final AdminRoleMapper appRoleMapper;
    private final MarketMapper marketMapper;
    private final ResourceMapper resourceMapper;
    private final ResourceGrantMapper resourceGrantMapper;

    @Transactional(readOnly = true)
    @Override
    public PageInfo<TenantVO> page(TenantQueryPageDTO queryDto) {
        QueryWrapper<Tenant> queryWrapper = this.buildPageQuery(queryDto);
        Page<Tenant> page = new Page<>(queryDto.getPageNo(), queryDto.getPageSize());
        Page<Tenant> result = this.tenantMapper.selectPage(page, queryWrapper);
        List<Tenant> records = result.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return PageInfo.of(Lists.newArrayList());
        }
        List<TenantVO> voList = tenantMapstruct.toVoList(result.getRecords());
        voList.forEach(vo -> {
            if (!vo.getTenantStatus().equals(TenantStatusEnum.DEACTIVATED.getCode())) {
                LocalDate localDate = LocalDate.now().plusDays(30);
                Instant instant = localDate.atTime(LocalTime.MIDNIGHT).atZone(ZoneId.systemDefault()).toInstant();
                Date date = Date.from(instant);

                if (vo.getTenantExpireTime().compareTo(date) > 0) {
                    vo.setTenantStatus(TenantStatusEnum.NORMAL.getCode());
                } else if (vo.getTenantExpireTime().compareTo(new Date()) < 0) {
                    vo.setTenantStatus(TenantStatusEnum.EXPIRED.getCode());
                } else {
                    vo.setTenantStatus(TenantStatusEnum.DUE.getCode());
                }
            }
        });

        return PageInfo.of(voList);
    }

    @Transactional(readOnly = true)
    @Override
    public List<TenantVO> list(TenantQueryDTO queryDto) {
        QueryWrapper<Tenant> queryWrapper = this.buildQuery(queryDto);
        return tenantMapstruct.toVoList(this.tenantMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(TenantAddDTO dto) {
        AssertUtil.isTrue(StringUtils.equals(dto.getPassword(), dto.getConfirmPassword()), "密码和确认密码不一致");
        //校验租户编码唯一性
        Tenant tenantDomain = tenantMapper.findByTenantDomain(dto.getTenantDomain());
        AssertUtil.isFalse(Objects.nonNull(tenantDomain), "租户编码已经存在");

        Tenant entity = tenantMapstruct.toEntity(dto);
        entity.setTenantSn(UUIDUtil.genRandomSn("tenant"));
        tenantMapper.insert(entity);

        //创建租户扩展表
        //智能体限制数量
        TenantExtend tenantExtend = new TenantExtend();
        tenantExtend.setTenantId(entity.getTenantId());
        tenantExtend.setItemStatus(1);
        tenantExtend.setItemName("agentLimitCount");
        tenantExtend.setItemValue(String.valueOf(dto.getAgentLimitCount()));
        tenantExtendMapper.insert(tenantExtend);
        //用户限制数量
        tenantExtend = new TenantExtend();
        tenantExtend.setTenantId(entity.getTenantId());
        tenantExtend.setItemStatus(1);
        tenantExtend.setItemName("userLimitCount");
        tenantExtend.setItemValue(String.valueOf(dto.getUserLimitCount()));
        tenantExtendMapper.insert(tenantExtend);

        //授权智能体
        List<ResourceGrant> addGrantData = new ArrayList<>();
        List<ResourceGrant> delGrantData = new ArrayList<>();
        this.setResourceGrantList(addGrantData,delGrantData,dto.getGrantAgentList(),entity,ResourceTypeEnum.AGENT.getCode());
        //授权模型
        this.setResourceGrantList(addGrantData,delGrantData,dto.getGrantAgentList(),entity,ResourceTypeEnum.AGENT.getCode());
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(addGrantData)) {
            addGrantData.forEach(resourceGrantMapper::insert);
        }
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(delGrantData)) {
            delGrantData.forEach(resourceGrantMapper::updateById);
        }

        //创建默认部门
        TenantDepartment department = new TenantDepartment();
        department.setTenantId(entity.getTenantId());
        department.setDeptStatus(StatusEnum.STATUS_ENABLE.getCode());
        department.setDeptSort(1);
        department.setDeptName(dto.getTenantName());
        department.setParentId(0);
        tenantDepartmentMapper.insert(department);

        int saRoleId = 0;
        //初始化角色
        for (RoleEnum roleEnum : RoleEnum.values()) {
            AdminRole appRole = new AdminRole();
            appRole.setTenantId(entity.getTenantId());
            appRole.setRoleCode(roleEnum.getCode());
            appRole.setRoleName(roleEnum.getName());
            appRole.setRoleDesc(roleEnum.getDesc());
            appRole.setAppId(AppEnum.APP_100.getCode());
            appRole.setRoleStatus(StatusEnum.STATUS_ENABLE.getCode());
            appRoleMapper.insert(appRole);
            if (appRole.getRoleCode().equals(RoleEnum.ROLE_SA.getCode())) {
                saRoleId = appRole.getRoleId();
            }
        }

        //创建管理员账号
        AdminUser appUser = new AdminUser();
        appUser.setTenantId(entity.getTenantId());
        appUser.setAppId(AppEnum.APP_100.getCode());
        appUser.setUserAccount("admin");
        appUser.setUserName("admin");
        appUser.setRoleId(saRoleId);
        appUser.setDeptId(department.getDeptId());
        appUser.setUserSn(entity.getTenantSn());
        appUser.setUserStatus(StatusEnum.STATUS_ENABLE.getCode());
        SaltedPassword saltedPassword = PasswordUtils.createSaltedPassword(dto.getPassword(), appUser.getUserSn());
        appUser.setUserPassword(saltedPassword.getHashedPasswordBase64());
        appUserMapper.insert(appUser);

        //初始化市场数据(公共市场/团队市场)
        Market market = new Market();
        market.setTenantId(entity.getTenantId());
        market.setMarketSn(UUIDUtil.genRandomSn("market"));
        market.setMarketType(MarketTypeEnum.PUBLIC_MARKET.getCode());
        marketMapper.insert(market);
        market = new Market();
        market.setTenantId(entity.getTenantId());
        market.setMarketSn(UUIDUtil.genRandomSn("market"));
        market.setMarketType(MarketTypeEnum.TEAM_MARKET.getCode());
        marketMapper.insert(market);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(TenantUpdateDTO dto) {
        BusinessAssertUtil.notNull(dto.getTenantSn(), "tenantSn不能为空");
        Tenant entity = tenantMapper.findByTenantSn(dto.getTenantSn());
        BusinessAssertUtil.notNull(entity, "找不到tenantSn为 " + dto.getTenantSn() + " 的记录");
        String tenantName = entity.getTenantName();

        entity.setTenantName(dto.getTenantName());
        entity.setTenantDesc(dto.getTenantDesc());
        entity.setTenantExpireTime(dto.getTenantExpireTime());
        entity.setTenantStatus(dto.getTenantStatus());
        entity.setUpdateTime(null);
        tenantMapper.updateById(entity);

        //更新根部门名称
        if (!dto.getTenantName().equals(tenantName)) {
            List<TenantDepartment> tenantDepartments = tenantDepartmentMapper.queryTenantSubDepartment(UserContext.getTenantId(), 0);
            TenantDepartment department;
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(tenantDepartments)) {
                department = new TenantDepartment();
                department.setTenantId(entity.getTenantId());
                department.setDeptStatus(StatusEnum.STATUS_ENABLE.getCode());
                department.setDeptSort(1);
                department.setDeptName(dto.getTenantName());
                department.setParentId(0);
                tenantDepartmentMapper.insert(department);
            } else {
                department = tenantDepartments.get(0);
                department.setDeptName(dto.getTenantName());
                tenantDepartmentMapper.updateById(department);
            }
        }

        //智能体限制数量
        List<TenantExtend> listTenantExtend = tenantExtendMapper.findByTenantId(entity.getTenantId());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(listTenantExtend)) {
            TenantExtend tenantExtend = new TenantExtend();
            tenantExtend.setTenantId(entity.getTenantId());
            tenantExtend.setItemStatus(1);
            tenantExtend.setItemName("agentLimitCount");
            tenantExtend.setItemValue(String.valueOf(dto.getAgentLimitCount()));
            tenantExtendMapper.insert(tenantExtend);
            //用户限制数量
            tenantExtend = new TenantExtend();
            tenantExtend.setTenantId(entity.getTenantId());
            tenantExtend.setItemStatus(1);
            tenantExtend.setItemName("userLimitCount");
            tenantExtend.setItemValue(String.valueOf(dto.getUserLimitCount()));
            tenantExtendMapper.insert(tenantExtend);
        } else {
            List<TenantExtend> agentLimitCount = listTenantExtend.stream().filter(a -> a.getItemName().equals("agentLimitCount")).toList();
            List<TenantExtend> userLimitCount = listTenantExtend.stream().filter(a -> a.getItemName().equals("userLimitCount")).toList();
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(agentLimitCount)) {
                TenantExtend tenantExtend = new TenantExtend();
                tenantExtend.setTenantId(entity.getTenantId());
                tenantExtend.setItemStatus(1);
                tenantExtend.setItemName("agentLimitCount");
                tenantExtend.setItemValue(String.valueOf(dto.getAgentLimitCount()));
                tenantExtendMapper.insert(tenantExtend);
            } else {
                TenantExtend tenantExtend = agentLimitCount.get(0);
                tenantExtend.setItemValue(String.valueOf(dto.getAgentLimitCount()));
                tenantExtend.setUpdateTime(null);
                tenantExtendMapper.updateById(tenantExtend);
            }

            if (org.apache.commons.collections4.CollectionUtils.isEmpty(userLimitCount)) {
                TenantExtend tenantExtend = new TenantExtend();
                tenantExtend.setTenantId(entity.getTenantId());
                tenantExtend.setItemStatus(1);
                tenantExtend.setItemName("userLimitCount");
                tenantExtend.setItemValue(String.valueOf(dto.getUserLimitCount()));
                tenantExtendMapper.insert(tenantExtend);
            } else {
                TenantExtend tenantExtend = agentLimitCount.get(0);
                tenantExtend.setItemValue(String.valueOf(dto.getUserLimitCount()));
                tenantExtend.setUpdateTime(null);
                tenantExtendMapper.updateById(tenantExtend);
            }

        }

        //授权智能体
        //授权智能体
        List<ResourceGrant> addGrantData = new ArrayList<>();
        List<ResourceGrant> delGrantData = new ArrayList<>();
        this.setResourceGrantList(addGrantData,delGrantData,dto.getGrantAgentList(),entity,ResourceTypeEnum.AGENT.getCode());
        //授权模型
        this.setResourceGrantList(addGrantData,delGrantData,dto.getGrantAgentList(),entity,ResourceTypeEnum.AGENT.getCode());
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(addGrantData)) {
            addGrantData.forEach(resourceGrantMapper::insert);
        }
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(delGrantData)) {
            delGrantData.forEach(resourceGrantMapper::updateById);
        }

    }

    /**
     * 设置资源授权列表
     * @param addGrantData
     * @param delGrantData
     * @param grantResourcesList
     * @param entity
     * @param resourceType
     */
    private void setResourceGrantList(
            List<ResourceGrant> addGrantData,
            List<ResourceGrant> delGrantData,
            List<GrantResourceDTO> grantResourcesList,
            Tenant entity,
            Integer resourceType){

        List<ResourceGrant> dbGrantResources = resourceGrantMapper.queryResourceGrantByTenantId(entity.getTenantId(), resourceType);
        //新增
        if(CollectionUtils.isEmpty(dbGrantResources) && CollectionUtils.isNotEmpty(grantResourcesList)){
            grantResourcesList.forEach(agent -> {
                        ResourceGrant resourceGrant = new ResourceGrant();
                        resourceGrant.setResourceId(agent.getResourceId());
                        resourceGrant.setResourceType(resourceType);
                        resourceGrant.setResourceObjectId(agent.getObjectId());
                        resourceGrant.setGrantType(GrantTypeEnum.OWNER.getCode());
                        resourceGrant.setGrantStatus(1);
                        resourceGrant.setGrantObjectType(GrantObjectTypeEnum.TENANT.getCode());
                        resourceGrant.setGrantObjectId(entity.getTenantId());
                        addGrantData.add(resourceGrant);
                    }
            );
            return;
        }

        //删除
        if(CollectionUtils.isNotEmpty(dbGrantResources) && CollectionUtils.isEmpty(grantResourcesList)){
            dbGrantResources.forEach(agent->{
                ResourceGrant resourceGrant = new ResourceGrant();
                resourceGrant.setGrantId(agent.getGrantId());
                resourceGrant.setGrantStatus(-1);
                resourceGrant.setUpdateTime(null);
                delGrantData.add(resourceGrant);
            });

        }

        //新增/删除
        if(CollectionUtils.isNotEmpty(dbGrantResources) && CollectionUtils.isNotEmpty(grantResourcesList)){
            List<Integer> pageIds = grantResourcesList.stream().map(GrantResourceDTO::getObjectId).toList();
            List<ResourceGrant> delResourceGrant = dbGrantResources.stream().filter(a -> !pageIds.contains(a.getResourceObjectId())).toList();
            //删除授权资源
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(delResourceGrant)) {
                delResourceGrant.forEach(agent -> {
                    ResourceGrant resourceGrant = new ResourceGrant();
                    resourceGrant.setGrantId(agent.getGrantId());
                    resourceGrant.setGrantStatus(-1);
                    resourceGrant.setUpdateTime(null);
                    delGrantData.add(resourceGrant);
                });
            }
            //新增授权资源
            List<Integer> dbIds = dbGrantResources.stream().map(ResourceGrant::getResourceObjectId).toList();
            List<GrantResourceDTO> addResourceGrant = grantResourcesList.stream().filter(a -> !dbIds.contains(a.getObjectId())).toList();
            //删除授权资源
            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(addResourceGrant)) {
                addResourceGrant.forEach(agent -> {
                    ResourceGrant resourceGrant = new ResourceGrant();
                    resourceGrant.setResourceId(agent.getResourceId());
                    resourceGrant.setResourceType(resourceType);
                    resourceGrant.setResourceObjectId(agent.getObjectId());
                    resourceGrant.setGrantType(GrantTypeEnum.OWNER.getCode());
                    resourceGrant.setGrantStatus(1);
                    resourceGrant.setGrantObjectType(GrantObjectTypeEnum.TENANT.getCode());
                    resourceGrant.setGrantObjectId(entity.getTenantId());
                    addGrantData.add(resourceGrant);
                });
            }

        }
    }

    @Transactional(readOnly = true)
    @Override
    public List<TenantSimpleVO> valids() {
        TenantQueryDTO tenantQueryDTO = new TenantQueryDTO();
        tenantQueryDTO.setTenantStatus(1);
        QueryWrapper<Tenant> queryWrapper = this.buildQuery(tenantQueryDTO);
        List<Tenant> result = this.tenantMapper.selectList(queryWrapper);
        return tenantMapstruct.toVoSimpleList(result);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updatePassword(String tenantSn, TenantUpdatePasswordDTO dto) {
        AssertUtil.isNotNull(tenantSn, "tenantSn不能为空");
        Tenant tenant = tenantMapper.findByTenantSn(tenantSn);
        AssertUtil.isNotNull(tenant, "租户不存在");
        AssertUtil.equals(dto.getPassword(), dto.getConfirmPassword(), "确认密码不一致");

        AdminUser appUser = appUserMapper.findByUserSn(tenantSn);
        AssertUtil.isNotNull(appUser, "租户不存在");

        SaltedPassword saltedPassword = PasswordUtils.createSaltedPassword(dto.getConfirmPassword(), appUser.getUserSn());
        appUser.setUserPassword(saltedPassword.getHashedPasswordBase64());
        appUser.setUpdateTime(null);
        appUserMapper.updateById(appUser);
    }

    @Override
    public TenantDetailVO detail(String tenantSn) {
        AssertUtil.isNotNull(tenantSn, "tenantSn不能为空");
        Tenant tenant = tenantMapper.findByTenantSn(tenantSn);
        AssertUtil.isNotNull(tenant, "租户不存在");
        TenantDetailVO detailVo = tenantMapstruct.toDetailVo(tenant);

        if (!detailVo.getTenantStatus().equals(TenantStatusEnum.DEACTIVATED.getCode())) {
            LocalDate localDate = LocalDate.now().plusDays(30);
            Instant instant = localDate.atTime(LocalTime.MIDNIGHT).atZone(ZoneId.systemDefault()).toInstant();
            Date date = Date.from(instant);

            if (detailVo.getTenantExpireTime().compareTo(date) > 0) {
                detailVo.setTenantStatus(TenantStatusEnum.NORMAL.getCode());
            } else if (detailVo.getTenantExpireTime().compareTo(new Date()) < 0) {
                detailVo.setTenantStatus(TenantStatusEnum.EXPIRED.getCode());
            } else {
                detailVo.setTenantStatus(TenantStatusEnum.DUE.getCode());
            }
        }

        //智能体查询
        detailVo.setGrantModelList(resourceMapper.queryGrantModelList(tenant.getTenantId()));
        detailVo.setGrantAgentlList(resourceMapper.queryGrantAgentList(tenant.getTenantId()));

        return detailVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveTenantStatus(String tenantSn, Boolean enable) {
        AssertUtil.isNotNull(tenantSn, "tenantSn不能为空");
        Tenant tenant = tenantMapper.findByTenantSn(tenantSn);
        AssertUtil.isNotNull(tenant, "租户不存在");

        tenant.setTenantStatus(enable ? 1 : 0);
        tenant.setUpdateTime(null);
        tenantMapper.updateById(tenant);
    }

    @Transactional(readOnly = true)
    @Override
    public TenantVO get(Long id) {
        BusinessAssertUtil.notNull(id, "id不能为空");
        Tenant entity = tenantMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到id为 " + id + " 的记录");
        return tenantMapstruct.toVo(entity);
    }

    @Override
    public Tenant getTenantBySn(String tenantSn) {
        return tenantMapper.findByTenantSn(tenantSn);
    }

    @Override
    public Tenant getTenantById(Integer tenantId) {
        return tenantMapper.selectById(tenantId);
    }

    @Override
    public List<AdminResourceVO> queryAgentList() {
        return resourceMapper.queryAgentList();
    }

    @Override
    public List<AdminResourceVO> queryModelList() {
        return resourceMapper.queryModelList();
    }

    private QueryWrapper<Tenant> buildQuery(TenantQueryDTO queryDto) {
        QueryWrapper<Tenant> queryWrapper = new QueryWrapper<>();
        if (Objects.isNull(queryDto)) {
            return queryWrapper;
        }
        return queryWrapper;
    }

    private QueryWrapper<Tenant> buildPageQuery(TenantQueryPageDTO queryDto) {
        QueryWrapper<Tenant> queryWrapper = new QueryWrapper<>();
        if (Objects.isNull(queryDto)) {
            return queryWrapper;
        }
        if (Objects.nonNull(queryDto.getTenantStatus())) {
            if (queryDto.getTenantStatus() == 0) {
                queryWrapper.lambda().eq(Tenant::getTenantStatus, 0);
            } else {
                queryWrapper.lambda().eq(Tenant::getTenantStatus, 1);
                if (TenantStatusEnum.NORMAL.getCode() == queryDto.getTenantStatus()) {
                    //正常：启用状态下的租户，距离到期时间一个月以上，则状态为正常
                    LocalDate localDate = LocalDate.now().plusDays(30);
                    Instant instant = localDate.atTime(LocalTime.MIDNIGHT).atZone(ZoneId.systemDefault()).toInstant();
                    Date date = Date.from(instant);
                    queryWrapper.lambda().gt(Tenant::getTenantExpireTime, date);
                } else if (TenantStatusEnum.DUE.getCode() == queryDto.getTenantStatus()) {
                    //即将到期：启用状态下的租户，距离到期时间一个月以内，则状态为“即将到期”
                    LocalDate localDate = LocalDate.now().plusDays(31);
                    Instant instant = localDate.atTime(LocalTime.MIDNIGHT).atZone(ZoneId.systemDefault()).toInstant();
                    Date date = Date.from(instant);
                    queryWrapper.lambda().gt(Tenant::getTenantExpireTime, new Date());
                    queryWrapper.lambda().lt(Tenant::getTenantExpireTime, date);
                } else if (TenantStatusEnum.EXPIRED.getCode() == queryDto.getTenantStatus()) {
                    //即将到期：启用状态下的租户，距离到期时间一个月以内，则状态为“即将到期”
                    queryWrapper.lambda().lt(Tenant::getTenantExpireTime, new Date());
                }
            }
        }
        if (StringUtils.isNoneBlank(queryDto.getKeyword())) {
            queryWrapper.lambda()
                    .like(Tenant::getTenantName, queryDto.getKeyword()).or()
                    .like(Tenant::getTenantDomain, queryDto.getKeyword());
        }
        if (Objects.nonNull(queryDto.getCreateDateStart()) && Objects.nonNull(queryDto.getCreateDateEnd())) {
            queryWrapper.lambda().gt(Tenant::getCreateTime, queryDto.getCreateDateStart());
            queryWrapper.lambda().lt(Tenant::getCreateTime, queryDto.getCreateDateEnd());
        }
        if (Objects.nonNull(queryDto.getExpireDateStart()) && Objects.nonNull(queryDto.getExpireDateEnd())) {
            queryWrapper.lambda().gt(Tenant::getTenantExpireTime, queryDto.getExpireDateStart());
            queryWrapper.lambda().lt(Tenant::getTenantExpireTime, queryDto.getExpireDateEnd());
        }
        return queryWrapper;
    }

}