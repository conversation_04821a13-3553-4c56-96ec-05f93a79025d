package com.ai.application.agent.base.api.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

/**
 * 智能体工具执行记录表
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@Schema(name = "智能体工具执行记录表DTO")
public class AgentRunToolDTO {
    /**
     * 工具执行id
     */
    @Schema(description = "工具执行id")
    private Integer toolRunId;

    /**
     * 调用类型:10-系统内嵌,20-API,30-代码
     */
    @Schema(description = "调用类型:10-系统内嵌,20-API,30-代码")
    private Integer toolCallType;

    /**
     * 工具名称
     */
    @Schema(description = "工具名称")
    private String toolName;

    /**
     * 工具输入参数
     */
    @Schema(description = "工具输入参数")
    private String toolInput;

    /**
     * 工具输出结果
     */
    @Schema(description = "工具输出结果")
    private String toolOutput;

    /**
     * 执行状态:1-执行中,2-成功,3-失败,4-超时
     */
    @Schema(description = "执行状态:1-执行中,2-成功,3-失败,4-超时")
    private Integer toolStatus;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String toolError;

    /**
     * 执行时长(毫秒)
     */
    @Schema(description = "执行时长(毫秒)")
    private Integer toolDuration;

    /**
     * 工具配置快照
     */
    @Schema(description = "工具配置快照")
    private String toolSnapshot;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private Date toolStartTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private Date toolEndTime;

    /**
     * 运行记录id
     */
    @Schema(description = "运行记录id")
    private Integer runId;

    /**
     * 步骤id
     */
    @Schema(description = "步骤id")
    private Integer stepId;

    /**
     * 工具id
     */
    @Schema(description = "工具id")
    private Integer toolId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}