package com.ai.application.agent.run.service;

import com.ai.application.agent.run.dto.ShortTermMemoryRetrieveRequestDTO;
import com.ai.application.agent.run.dto.ShortTermMemoryRetrieveResultDTO;
import com.ai.framework.core.vo.ResultVo;

/**
 * 短期记忆提取服务接口
 */
public interface IShortTermMemoryRetrieveService {

    /**
     * 执行短期记忆提取
     *
     * @param request 提取请求
     * @param authorization 授权信息
     * @return 提取结果
     */
    ResultVo<ShortTermMemoryRetrieveResultDTO> executeShortTermMemoryRetrieve(ShortTermMemoryRetrieveRequestDTO request, String authorization);

    /**
     * 验证记忆提取请求
     *
     * @param request 提取请求
     * @return 是否有效
     */
    boolean validateMemoryRetrieveRequest(ShortTermMemoryRetrieveRequestDTO request);

    /**
     * 构建记忆搜索请求
     *
     * @param request 提取请求
     * @return 搜索请求
     */
    Object buildMemorySearchRequest(ShortTermMemoryRetrieveRequestDTO request);
}
