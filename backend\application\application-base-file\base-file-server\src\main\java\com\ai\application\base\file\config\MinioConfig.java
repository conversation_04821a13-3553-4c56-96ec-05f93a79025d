package com.ai.application.base.file.config;

import io.minio.MinioClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/29 11:18
 */
@Configuration
public class MinioConfig {

    @Value("${oss.endpoint}")
    String ossEndPoint;

    @Value("${oss.accessKeyId}")
    String ossAccessKeyId;

    @Value("${oss.accessKeySecret}")
    String ossAccessKeySecret;

    @Value("${oss.regionId}")
    String ossRegionId;

    @Value("${oss.roleArn}")
    String ossRoleArn;

    @Value("${oss.bucketName}")
    String ossBucketName;

    @Value("${oss.private.endpoint}")
    String privateEndpoint;

    @Bean
    public MinioClient minioClient() {
        return MinioClient.builder()
                .endpoint(ossEndPoint)
                .credentials(ossAccessKeyId, ossAccessKeySecret)
                .build();
    }

    // 获取默认存储桶名称
    public String getDefaultBucket() {
        return ossBucketName;
    }
}