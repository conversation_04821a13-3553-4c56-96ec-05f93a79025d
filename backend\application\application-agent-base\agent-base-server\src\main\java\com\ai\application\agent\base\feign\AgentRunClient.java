package com.ai.application.agent.base.feign;

import com.ai.application.agent.base.api.feign.IAgentRunClient;
import com.ai.application.agent.base.api.vo.AgentRunMcpVO;
import com.ai.framework.core.vo.ResultVo;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@RestController
@AllArgsConstructor
public class AgentRunClient implements IAgentRunClient {
    @Override
    public ResultVo<List<AgentRunMcpVO>> mcpLogs(Integer mcpId) {
        return null;
    }
}
