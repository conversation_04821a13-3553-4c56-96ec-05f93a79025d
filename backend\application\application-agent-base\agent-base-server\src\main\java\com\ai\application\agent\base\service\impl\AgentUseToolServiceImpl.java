package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.bo.MasterSkillBO;
import com.ai.application.agent.base.api.dto.*;
import com.ai.application.agent.base.api.enums.SkillTypeEnum;
import com.ai.application.agent.base.api.enums.ToolExtendNameEnum;
import com.ai.application.agent.base.service.IAgentUseToolService;
import com.ai.framework.core.util.BusinessAssertUtil;
import com.ai.framework.core.util.json.JsonUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ai.application.agent.base.mapper.AgentUseToolMapper;
import com.ai.application.agent.base.api.entity.AgentUseTool;
import com.ai.application.agent.base.api.vo.AgentUseToolListVO;
import com.ai.application.agent.base.api.mapstruct.AgentUseToolMapstruct;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 智能体使用工具表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Service
public class AgentUseToolServiceImpl implements IAgentUseToolService {

    @Resource
    private AgentUseToolMapper agentUseToolMapper;

    @Resource
    private AgentUseToolMapstruct agentUseToolMapstruct;

    @Transactional(readOnly = true)
    @Override
    public List<AgentUseToolListVO> list(AgentUseToolListDTO queryDto) {
        LambdaQueryWrapper<AgentUseTool> queryWrapper = this.buildQuery(queryDto);
        return agentUseToolMapstruct.toVoList(this.agentUseToolMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(AgentUseToolAddDTO dto) {
        AgentUseTool entity = agentUseToolMapstruct.toAddEntity(dto);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());

        agentUseToolMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AgentUseToolUpdateDTO dto) {
        BusinessAssertUtil.notNull(dto.getToolId(), "AdId不能为空");

        AgentUseTool entity = agentUseToolMapper.selectById(dto.getToolId());
        BusinessAssertUtil.notNull(entity, "找不到为 " + dto.getToolId() + " 的记录");

        AgentUseTool entityList = agentUseToolMapstruct.toUpdateEntity(dto);
        entityList.setUpdateTime(new Date());
        agentUseToolMapper.updateById(entityList);
    }

    @Transactional(readOnly = true)
    @Override
    public List<MasterSkillBO> toDetail(Integer versionId) {
        AgentUseToolListDTO agentUseToolListDTO = new AgentUseToolListDTO();
        List<AgentUseToolListVO> list = this.list(agentUseToolListDTO);
        return list.stream().map(mcp->{
            MasterSkillBO skill = new MasterSkillBO();
            skill.setSkillId(mcp.getToolId());
            skill.setType(SkillTypeEnum.TOOL.getCode());
            String mcpExtend = mcp.getToolExtend();
            Map<String, String> mapStr = JsonUtils.parseMapStr(mcpExtend);
            skill.setReplayType(Integer.valueOf(mapStr.get(ToolExtendNameEnum.REPLY_TYPE.getCode())));
            return skill;
        }).toList();
    }

    private LambdaQueryWrapper<AgentUseTool> buildQuery(AgentUseToolListDTO queryDto) {
        LambdaQueryWrapper<AgentUseTool> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(queryDto.getVersionId()), AgentUseTool::getVersionId, queryDto.getVersionId());
        return queryWrapper;
    }
}