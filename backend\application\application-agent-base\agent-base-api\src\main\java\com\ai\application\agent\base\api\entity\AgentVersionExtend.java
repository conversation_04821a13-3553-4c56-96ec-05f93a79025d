package com.ai.application.agent.base.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * agent版本扩展信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("agent_version_extend")
public class AgentVersionExtend implements Serializable {
    @Schema(description = "")
    @TableId(type = IdType.AUTO)
    private Integer itemId;

    /**
    * 参数名称
    */
    @Schema(description = "参数名称")
    private String itemName;

    /**
    * 参数值
    */
    @Schema(description = "参数值")
    private String itemValue;

    /**
    * 状态 0失效 1有效
    */
    @Schema(description = "状态 0失效 1有效")
    private Integer itemStatus;

    /**
    * 智能体id
    */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
    * 智能体版本id
    */
    @Schema(description = "智能体版本id")
    private Integer versionId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}