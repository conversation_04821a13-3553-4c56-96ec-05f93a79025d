package com.ai.application.agent.run.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 表达式DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ExpressionDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 5576622135346889648L;

    private String knowledgeInventorySn;

    private List<String> knowledgeInventorySnList;

    private List<Expression> expressions;

    private String logic;

    private Integer topK;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Expression implements Serializable {

        @Serial
        private static final long serialVersionUID = 5571645095287244652L;
        
        /**
         * 字段
         */
        private String field;

        /**
         * 操作符 == != contains !contains
         */
        private String operator;
        
        /**
         * 值
         */
        private String value;
    }
}
