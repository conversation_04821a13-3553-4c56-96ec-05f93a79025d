package com.ai.application.base.log.api.mapstruct;

import com.ai.application.base.log.api.dto.NotificationParameterDTO;
import com.ai.application.base.log.api.entity.NotificationParameter;
import com.ai.application.base.log.api.vo.NotificationParameterVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-13T10:32:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class NotificationParameterMapstructImpl implements NotificationParameterMapstruct {

    @Override
    public NotificationParameter toEntity(NotificationParameterDTO dto) {
        if ( dto == null ) {
            return null;
        }

        NotificationParameter notificationParameter = new NotificationParameter();

        notificationParameter.setNtprId( dto.getNtprId() );
        notificationParameter.setNtprType( dto.getNtprType() );
        notificationParameter.setNtprCode( dto.getNtprCode() );
        notificationParameter.setNtprName( dto.getNtprName() );
        notificationParameter.setNtprDescription( dto.getNtprDescription() );
        notificationParameter.setNtprStatus( dto.getNtprStatus() );
        notificationParameter.setCreateTime( dto.getCreateTime() );
        notificationParameter.setUpdateTime( dto.getUpdateTime() );

        return notificationParameter;
    }

    @Override
    public List<NotificationParameter> toEntityList(List<NotificationParameterDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<NotificationParameter> list = new ArrayList<NotificationParameter>( dtolist.size() );
        for ( NotificationParameterDTO notificationParameterDTO : dtolist ) {
            list.add( toEntity( notificationParameterDTO ) );
        }

        return list;
    }

    @Override
    public NotificationParameterVO toVo(NotificationParameter entity) {
        if ( entity == null ) {
            return null;
        }

        NotificationParameterVO notificationParameterVO = new NotificationParameterVO();

        notificationParameterVO.setNtprId( entity.getNtprId() );
        notificationParameterVO.setNtprType( entity.getNtprType() );
        notificationParameterVO.setNtprCode( entity.getNtprCode() );
        notificationParameterVO.setNtprName( entity.getNtprName() );
        notificationParameterVO.setNtprDescription( entity.getNtprDescription() );

        return notificationParameterVO;
    }

    @Override
    public List<NotificationParameterVO> toVoList(List<NotificationParameter> entities) {
        if ( entities == null ) {
            return null;
        }

        List<NotificationParameterVO> list = new ArrayList<NotificationParameterVO>( entities.size() );
        for ( NotificationParameter notificationParameter : entities ) {
            list.add( toVo( notificationParameter ) );
        }

        return list;
    }
}
