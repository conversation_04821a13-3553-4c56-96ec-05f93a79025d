package com.ai.application.app.api.dto;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

/**
 * 应用表
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Data
@Schema(name = "应用表DTO")
public class AppDTO {
    /**
     * 应用id:100智能体平台,500管理系统
     */
    @Schema(description  = "应用id:100智能体平台,500管理系统")
    private Integer appId;
    /**
     * 应用名称
     */
    @Schema(description  = "应用名称")
    private String appName;
    /**
     * 入口url
     */
    @Schema(description  = "入口url")
    private String appUrl;
    /**
     * 应用描述
     */
    @Schema(description  = "应用描述")
    private String appDesc;
    /**
     * 应用状态 0:禁用,1:启用,-1:删除
     */
    @Schema(description  = "应用状态 0:禁用,1:启用,-1:删除")
    private Integer appStatus;
    @Schema(name = "")
    private Date createTime;
    @Schema(name = "")
    private Date updateTime;
}