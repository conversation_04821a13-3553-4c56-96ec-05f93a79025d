package com.ai.application.base.file.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class EmbeddingResultVo {
    private List<EmbeddingResult> embeddingResults;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EmbeddingResult{
        private String fileSn;
        private Integer status;
        private String fileMd5;
        private String errorReason;
    }
}
