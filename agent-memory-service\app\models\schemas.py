# -*- coding: utf-8 -*-
"""
API请求和响应模型定义

使用Pydantic定义的数据模型，包含：
- API请求模型（AddMemoryRequest、SearchMemoryRequest、ClearMemoryRequest）
- API响应模型（AddMemoryResponse、SearchMemoryResponse、ClearMemoryResponse）
- 数据传输对象（MemoryItem、HealthResponse）
- 内部数据模型（MemoryDocument）
- 枚举类型定义（SortOrder）
- 数据验证和序列化规则

Project: Agent Memory Service
Author: 张小龙 <<EMAIL>>
Created: 2025-06-01
Version: 1.0.0
License: private
"""

from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, field_validator, ConfigDict
from enum import Enum


class SortOrder(str, Enum):
    """排序方式枚举"""
    SIMILARITY_DESC = "similarity_desc"    # 相似度降序
    SIMILARITY_ASC = "similarity_asc"      # 相似度升序
    TIME_DESC = "time_desc"                # 时间降序
    TIME_ASC = "time_asc"                  # 时间升序


# ==================== 请求模型 ====================

class AddMemoryRequest(BaseModel):
    """新增记忆请求模型"""
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )
    
    agent_id: str = Field(..., description="智能体ID", min_length=1, max_length=100)
    user_id: str = Field(..., description="用户ID", min_length=1, max_length=100)
    category: str = Field(..., description="记忆类别", min_length=1, max_length=50)
    user_question: str = Field(..., description="用户问题", min_length=1, max_length=2000)
    question_reply: Optional[str] = Field(None, description="问题回复", max_length=5000)
    question_time: Optional[datetime] = Field(None, description="问题时间")
    
    @field_validator('question_time', mode='before')
    @classmethod
    def set_question_time(cls, v):
        """如果未提供问题时间，则设置为当前时间"""
        return v or datetime.now()


class SearchMemoryRequest(BaseModel):
    """检索记忆请求模型"""
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )
    
    agent_id: Optional[str] = Field(None, description="智能体ID", max_length=100)
    user_id: Optional[str] = Field(None, description="用户ID", max_length=100)
    category: Optional[str] = Field(None, description="记忆类别", max_length=50)
    user_question: Optional[str] = Field(None, description="用户问题（用于向量检索）", max_length=2000)
    similarity_threshold: Optional[float] = Field(0.7, description="相似度阈值", ge=0.0, le=1.0)
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    sort_by: SortOrder = Field(SortOrder.SIMILARITY_DESC, description="排序方式")
    limit: int = Field(10, description="返回结果数量", ge=1, le=100)
    
    @field_validator('end_time')
    @classmethod
    def validate_time_range(cls, v, info):
        """验证时间范围"""
        if v and info.data.get('start_time') and v <= info.data['start_time']:
            raise ValueError('结束时间必须大于开始时间')
        return v


class ClearMemoryRequest(BaseModel):
    """清除记忆请求模型"""
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )
    
    agent_id: Optional[str] = Field(None, description="智能体ID", max_length=100)
    user_id: Optional[str] = Field(None, description="用户ID", max_length=100)
    category: Optional[str] = Field(None, description="记忆类别", max_length=50)
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    
    @field_validator('end_time')
    @classmethod
    def validate_time_range(cls, v, info):
        """验证时间范围"""
        if v and info.data.get('start_time') and v <= info.data['start_time']:
            raise ValueError('结束时间必须大于开始时间')
        return v
    
    def has_conditions(self) -> bool:
        """检查是否有任何过滤条件"""
        return any([self.agent_id, self.user_id, self.category, self.start_time, self.end_time])


# ==================== 响应模型 ====================

class MemoryItem(BaseModel):
    """记忆项模型"""
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )
    
    id: str = Field(..., description="记忆ID")
    agent_id: str = Field(..., description="智能体ID")
    user_id: str = Field(..., description="用户ID")
    category: str = Field(..., description="记忆类别")
    user_question: str = Field(..., description="用户问题")
    question_reply: Optional[str] = Field(None, description="问题回复")
    question_time: datetime = Field(..., description="问题时间")
    similarity_score: Optional[float] = Field(None, description="相似度分数", ge=0.0, le=1.0)
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class BaseResponse(BaseModel):
    """基础响应模型"""
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )
    
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")


class AddMemoryResponse(BaseResponse):
    """新增记忆响应模型"""
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")


class SearchMemoryResponse(BaseResponse):
    """检索记忆响应模型"""
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据，包含 total 和 memories")


class ClearMemoryResponse(BaseResponse):
    """清除记忆响应模型"""
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据，包含 deleted_count")


class MemoryListResponse(BaseResponse):
    """记忆列表响应模型"""
    data: Dict[str, Any] = Field(..., description="响应数据，包含 total 和 memories")


class MemoryDetailResponse(BaseResponse):
    """记忆详情响应模型"""
    data: Dict[str, Any] = Field(..., description="响应数据，包含 memory_id 和 memory")


class MemoryStatisticsResponse(BaseResponse):
    """记忆统计响应模型"""
    data: Dict[str, Any] = Field(..., description="统计数据")


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )
    
    status: str = Field(..., description="服务状态")
    version: str = Field(..., description="服务版本")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")
    services: Dict[str, str] = Field(default_factory=dict, description="依赖服务状态")


# ==================== 内部数据模型 ====================

class MemoryDocument(BaseModel):
    """Elasticsearch文档模型"""
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )
    
    agent_id: str
    user_id: str
    category: str
    user_question: str
    question_reply: Optional[str] = None
    question_time: datetime
    question_vector: List[float]
    created_at: datetime
    updated_at: datetime 