package com.ai.application.agent.run.dto;

import com.ai.application.base.file.api.dto.FileInfoDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

/**
 * 知识嵌入结果 DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(name = "KnowledgeEmbeddingResultDTO")
public class KnowledgeEmbeddingResultDTO {

    /**
     * 成功嵌入的文件列表
     */
    @Schema(description = "成功嵌入的文件列表")
    private List<FileInfoDto> files;

    /**
     * 知识库ID
     */
    @Schema(description = "知识库ID")
    private String datasetId;

    /**
     * 错误消息映射 (文件名 -> 错误信息)
     */
    @Schema(description = "错误消息映射")
    private Map<String, String> errorMsgs;
}
