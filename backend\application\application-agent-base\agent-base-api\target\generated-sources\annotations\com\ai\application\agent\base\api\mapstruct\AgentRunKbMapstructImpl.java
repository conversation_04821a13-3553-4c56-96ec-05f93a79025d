package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.dto.AgentRunKbDTO;
import com.ai.application.agent.base.api.entity.AgentRunKb;
import com.ai.application.agent.base.api.vo.AgentRunKbVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-12T18:39:57+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentRunKbMapstructImpl implements AgentRunKbMapstruct {

    @Override
    public AgentRunKb toEntity(AgentRunKbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentRunKb agentRunKb = new AgentRunKb();

        agentRunKb.setKbRunId( dto.getKbRunId() );
        agentRunKb.setKbName( dto.getKbName() );
        agentRunKb.setKbQuery( dto.getKbQuery() );
        agentRunKb.setKbQueryVector( dto.getKbQueryVector() );
        agentRunKb.setKbResults( dto.getKbResults() );
        agentRunKb.setKbResultsCount( dto.getKbResultsCount() );
        agentRunKb.setKbSimilarityThreshold( dto.getKbSimilarityThreshold() );
        agentRunKb.setKbStatus( dto.getKbStatus() );
        agentRunKb.setKbError( dto.getKbError() );
        agentRunKb.setKbDuration( dto.getKbDuration() );
        agentRunKb.setKbMetadata( dto.getKbMetadata() );
        agentRunKb.setKbStartTime( dto.getKbStartTime() );
        agentRunKb.setKbEndTime( dto.getKbEndTime() );
        agentRunKb.setRunId( dto.getRunId() );
        agentRunKb.setStepId( dto.getStepId() );
        agentRunKb.setKbId( dto.getKbId() );
        agentRunKb.setCreateTime( dto.getCreateTime() );
        agentRunKb.setUpdateTime( dto.getUpdateTime() );

        return agentRunKb;
    }

    @Override
    public List<AgentRunKb> toEntityList(List<AgentRunKbDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<AgentRunKb> list = new ArrayList<AgentRunKb>( dtolist.size() );
        for ( AgentRunKbDTO agentRunKbDTO : dtolist ) {
            list.add( toEntity( agentRunKbDTO ) );
        }

        return list;
    }

    @Override
    public AgentRunKbVO toVo(AgentRunKb entity) {
        if ( entity == null ) {
            return null;
        }

        AgentRunKbVO agentRunKbVO = new AgentRunKbVO();

        agentRunKbVO.setKbRunId( entity.getKbRunId() );
        agentRunKbVO.setKbName( entity.getKbName() );
        agentRunKbVO.setKbQuery( entity.getKbQuery() );
        agentRunKbVO.setKbQueryVector( entity.getKbQueryVector() );
        agentRunKbVO.setKbResults( entity.getKbResults() );
        agentRunKbVO.setKbResultsCount( entity.getKbResultsCount() );
        agentRunKbVO.setKbSimilarityThreshold( entity.getKbSimilarityThreshold() );
        agentRunKbVO.setKbStatus( entity.getKbStatus() );
        agentRunKbVO.setKbError( entity.getKbError() );
        agentRunKbVO.setKbDuration( entity.getKbDuration() );
        agentRunKbVO.setKbMetadata( entity.getKbMetadata() );
        agentRunKbVO.setKbStartTime( entity.getKbStartTime() );
        agentRunKbVO.setKbEndTime( entity.getKbEndTime() );
        agentRunKbVO.setRunId( entity.getRunId() );
        agentRunKbVO.setStepId( entity.getStepId() );
        agentRunKbVO.setKbId( entity.getKbId() );
        agentRunKbVO.setCreateTime( entity.getCreateTime() );
        agentRunKbVO.setUpdateTime( entity.getUpdateTime() );

        return agentRunKbVO;
    }

    @Override
    public List<AgentRunKbVO> toVoList(List<AgentRunKb> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AgentRunKbVO> list = new ArrayList<AgentRunKbVO>( entities.size() );
        for ( AgentRunKb agentRunKb : entities ) {
            list.add( toVo( agentRunKb ) );
        }

        return list;
    }
}
