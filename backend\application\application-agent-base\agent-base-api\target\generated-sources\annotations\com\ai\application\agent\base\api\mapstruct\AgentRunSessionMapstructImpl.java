package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.entity.AgentRunSession;
import com.ai.application.agent.base.api.vo.AgentRunSessionVO;
import com.ai.application.agent.base.api.vo.SessionHistoryVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-13T10:32:23+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentRunSessionMapstructImpl implements AgentRunSessionMapstruct {

    @Override
    public List<SessionHistoryVO> toSessionHistoryList(List<AgentRunSession> entities) {
        if ( entities == null ) {
            return null;
        }

        List<SessionHistoryVO> list = new ArrayList<SessionHistoryVO>( entities.size() );
        for ( AgentRunSession agentRunSession : entities ) {
            list.add( agentRunSessionToSessionHistoryVO( agentRunSession ) );
        }

        return list;
    }

    @Override
    public List<AgentRunSessionVO> toVoList(List<AgentRunSession> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AgentRunSessionVO> list = new ArrayList<AgentRunSessionVO>( entities.size() );
        for ( AgentRunSession agentRunSession : entities ) {
            list.add( agentRunSessionToAgentRunSessionVO( agentRunSession ) );
        }

        return list;
    }

    protected SessionHistoryVO agentRunSessionToSessionHistoryVO(AgentRunSession agentRunSession) {
        if ( agentRunSession == null ) {
            return null;
        }

        SessionHistoryVO sessionHistoryVO = new SessionHistoryVO();

        sessionHistoryVO.setSessionSn( agentRunSession.getSessionSn() );
        sessionHistoryVO.setSessionTitle( agentRunSession.getSessionTitle() );
        sessionHistoryVO.setLastRunTime( agentRunSession.getLastRunTime() );

        return sessionHistoryVO;
    }

    protected AgentRunSessionVO agentRunSessionToAgentRunSessionVO(AgentRunSession agentRunSession) {
        if ( agentRunSession == null ) {
            return null;
        }

        AgentRunSessionVO agentRunSessionVO = new AgentRunSessionVO();

        agentRunSessionVO.setSessionId( agentRunSession.getSessionId() );
        agentRunSessionVO.setSessionSn( agentRunSession.getSessionSn() );
        agentRunSessionVO.setSessionTitle( agentRunSession.getSessionTitle() );
        agentRunSessionVO.setSessionType( agentRunSession.getSessionType() );
        agentRunSessionVO.setSessionStatus( agentRunSession.getSessionStatus() );
        agentRunSessionVO.setSessionMetadata( agentRunSession.getSessionMetadata() );
        agentRunSessionVO.setAgentId( agentRunSession.getAgentId() );
        agentRunSessionVO.setVersionId( agentRunSession.getVersionId() );
        agentRunSessionVO.setUserId( agentRunSession.getUserId() );
        agentRunSessionVO.setTenantId( agentRunSession.getTenantId() );
        agentRunSessionVO.setRunCount( agentRunSession.getRunCount() );
        agentRunSessionVO.setFirstRunId( agentRunSession.getFirstRunId() );
        agentRunSessionVO.setLastRunId( agentRunSession.getLastRunId() );
        agentRunSessionVO.setLastRunTime( agentRunSession.getLastRunTime() );
        agentRunSessionVO.setCreateTime( agentRunSession.getCreateTime() );
        agentRunSessionVO.setUpdateTime( agentRunSession.getUpdateTime() );

        return agentRunSessionVO;
    }
}
