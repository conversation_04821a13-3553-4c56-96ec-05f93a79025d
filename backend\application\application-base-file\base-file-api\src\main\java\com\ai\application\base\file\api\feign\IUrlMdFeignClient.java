package com.ai.application.base.file.api.feign;


import cn.hutool.json.JSONObject;
import com.ai.application.base.file.api.dto.UrlMdDto;
import com.ai.application.base.file.api.dto.UrlMdFeignDto;
import com.ai.application.base.file.api.vo.MdFileDto;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;




@Tag(name = "mcp请求", description = "mcp工具请求")
@FeignClient( url="${firecrawl.service.url}",
        name = "IUrlMdFeignClient",
        contextId = "iUrlMdFeignClient")
public interface IUrlMdFeignClient {


    @PostMapping("/scrape")
    public JSONObject getUrlMd(@Validated @RequestBody UrlMdFeignDto dto);
}