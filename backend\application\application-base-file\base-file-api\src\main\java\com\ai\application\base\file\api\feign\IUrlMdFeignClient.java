package com.ai.application.base.file.api.feign;


import cn.hutool.json.JSONObject;
import com.ai.application.base.file.api.dto.UrlMdFeignDto;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;




@Tag(name = "mcp请求", description = "mcp工具请求")
@FeignClient( url="${firecrawl.service.url}",
        name = "IUrlMdFeignClient",
        contextId = "iUrlMdFeignClient")
public interface IUrlMdFeignClient {


    @PostMapping("/scrape")
    public JSONObject getUrlMd(@Validated @RequestBody UrlMdFeignDto dto);
}