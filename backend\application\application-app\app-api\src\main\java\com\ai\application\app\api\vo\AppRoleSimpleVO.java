package com.ai.application.app.api.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 应用角色表
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(name = "")
public class AppRoleSimpleVO {
    @Schema(description = "")
    @TableId(type = IdType.AUTO)
    private Integer roleId;

    /**
     * 角色code:SA,ADMIN,IT,USER
     */
    @Schema(description = "角色code:SA,ADMIN,IT,USER")
    private String roleCode;
    /**
     * 角色名称
     */
    @Schema(description = "角色名称")
    private String roleName;

}