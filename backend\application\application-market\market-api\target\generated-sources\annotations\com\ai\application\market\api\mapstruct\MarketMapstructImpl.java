package com.ai.application.market.api.mapstruct;

import com.ai.application.market.api.dto.MarketDTO;
import com.ai.application.market.api.entity.Market;
import com.ai.application.market.api.vo.MarketVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T09:54:00+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class MarketMapstructImpl implements MarketMapstruct {

    @Override
    public Market toEntity(MarketDTO dto) {
        if ( dto == null ) {
            return null;
        }

        Market market = new Market();

        market.setMarketId( dto.getMarketId() );
        market.setMarketSn( dto.getMarketSn() );
        market.setMarketType( dto.getMarketType() );
        market.setTenantId( dto.getTenantId() );
        market.setCreateTime( dto.getCreateTime() );
        market.setUpdateTime( dto.getUpdateTime() );

        return market;
    }

    @Override
    public List<Market> toEntityList(List<MarketDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<Market> list = new ArrayList<Market>( dtolist.size() );
        for ( MarketDTO marketDTO : dtolist ) {
            list.add( toEntity( marketDTO ) );
        }

        return list;
    }

    @Override
    public MarketVO toVo(Market entity) {
        if ( entity == null ) {
            return null;
        }

        MarketVO marketVO = new MarketVO();

        marketVO.setMarketId( entity.getMarketId() );
        marketVO.setMarketSn( entity.getMarketSn() );
        marketVO.setMarketType( entity.getMarketType() );
        marketVO.setTenantId( entity.getTenantId() );
        marketVO.setCreateTime( entity.getCreateTime() );
        marketVO.setUpdateTime( entity.getUpdateTime() );

        return marketVO;
    }

    @Override
    public List<MarketVO> toVoList(List<Market> entities) {
        if ( entities == null ) {
            return null;
        }

        List<MarketVO> list = new ArrayList<MarketVO>( entities.size() );
        for ( Market market : entities ) {
            list.add( toVo( market ) );
        }

        return list;
    }
}
