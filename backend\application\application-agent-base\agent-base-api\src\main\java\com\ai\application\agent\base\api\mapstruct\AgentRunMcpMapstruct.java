package com.ai.application.agent.base.api.mapstruct;
import com.ai.application.agent.base.api.entity.AgentRunMcp;
import com.ai.application.agent.base.api.dto.AgentRunMcpDTO;
import com.ai.application.agent.base.api.vo.AgentRunMcpVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 智能体工具执行mcp工具记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */

@Mapper(componentModel = "spring")
public interface AgentRunMcpMapstruct {

    AgentRunMcp toEntity(AgentRunMcpDTO dto);
    List<AgentRunMcp> toEntityList(List<AgentRunMcpDTO> dtolist);
    AgentRunMcpVO toVo(AgentRunMcp entity);
    List<AgentRunMcpVO> toVoList(List<AgentRunMcp> entities);
}
