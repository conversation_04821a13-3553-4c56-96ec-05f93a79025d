package com.ai.application.agent.base.api.mapstruct;
import com.ai.application.agent.base.api.entity.AgentRunSession;
import com.ai.application.agent.base.api.dto.AgentRunSessionDTO;
import com.ai.application.agent.base.api.vo.AgentRunSessionVO;
import com.ai.application.agent.base.api.vo.SessionHistoryVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 智能体运行会话表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */

@Mapper(componentModel = "spring")
public interface AgentRunSessionMapstruct {
    List<SessionHistoryVO> toSessionHistoryList(List<AgentRunSession> entities);
    List<AgentRunSessionVO> toVoList(List<AgentRunSession> entities);
}
