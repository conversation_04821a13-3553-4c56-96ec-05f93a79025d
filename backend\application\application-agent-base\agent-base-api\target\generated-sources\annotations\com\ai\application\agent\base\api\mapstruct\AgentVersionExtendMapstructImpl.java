package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.dto.AgentVersionExtendDTO;
import com.ai.application.agent.base.api.entity.AgentVersionExtend;
import com.ai.application.agent.base.api.vo.AgentVersionExtendVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T09:54:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentVersionExtendMapstructImpl implements AgentVersionExtendMapstruct {

    @Override
    public AgentVersionExtend toEntity(AgentVersionExtendDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentVersionExtend agentVersionExtend = new AgentVersionExtend();

        agentVersionExtend.setItemId( dto.getItemId() );
        agentVersionExtend.setItemName( dto.getItemName() );
        agentVersionExtend.setItemValue( dto.getItemValue() );
        agentVersionExtend.setItemStatus( dto.getItemStatus() );
        agentVersionExtend.setAgentId( dto.getAgentId() );
        agentVersionExtend.setVersionId( dto.getVersionId() );
        agentVersionExtend.setCreateTime( dto.getCreateTime() );
        agentVersionExtend.setUpdateTime( dto.getUpdateTime() );

        return agentVersionExtend;
    }

    @Override
    public List<AgentVersionExtend> toEntityList(List<AgentVersionExtendDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<AgentVersionExtend> list = new ArrayList<AgentVersionExtend>( dtolist.size() );
        for ( AgentVersionExtendDTO agentVersionExtendDTO : dtolist ) {
            list.add( toEntity( agentVersionExtendDTO ) );
        }

        return list;
    }

    @Override
    public AgentVersionExtendVO toVo(AgentVersionExtend entity) {
        if ( entity == null ) {
            return null;
        }

        AgentVersionExtendVO agentVersionExtendVO = new AgentVersionExtendVO();

        agentVersionExtendVO.setItemId( entity.getItemId() );
        agentVersionExtendVO.setItemName( entity.getItemName() );
        agentVersionExtendVO.setItemValue( entity.getItemValue() );
        agentVersionExtendVO.setItemStatus( entity.getItemStatus() );
        agentVersionExtendVO.setAgentId( entity.getAgentId() );
        agentVersionExtendVO.setVersionId( entity.getVersionId() );
        agentVersionExtendVO.setCreateTime( entity.getCreateTime() );
        agentVersionExtendVO.setUpdateTime( entity.getUpdateTime() );

        return agentVersionExtendVO;
    }

    @Override
    public List<AgentVersionExtendVO> toVoList(List<AgentVersionExtend> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AgentVersionExtendVO> list = new ArrayList<AgentVersionExtendVO>( entities.size() );
        for ( AgentVersionExtend agentVersionExtend : entities ) {
            list.add( toVo( agentVersionExtend ) );
        }

        return list;
    }
}
