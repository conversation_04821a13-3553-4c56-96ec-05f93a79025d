package com.ai.application.app.api.mapstruct;

import com.ai.application.app.api.dto.AppRoleDTO;
import com.ai.application.app.api.entity.AppRole;
import com.ai.application.app.api.vo.AppRoleListVO;
import com.ai.application.app.api.vo.AppRoleVO;
import com.ai.application.app.api.vo.AppRoleSimpleVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <p>
 * 应用角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */

@Mapper(componentModel = "spring")
public interface AppRoleMapstruct {
    AppRoleMapstruct INSTANCE = Mappers.getMapper(AppRoleMapstruct.class);

    AppRole toEntity(AppRoleDTO dto);
    List<AppRole> toEntityList(List<AppRoleDTO> dtolist);
    AppRoleVO toVo(AppRole entity);
    List<AppRoleVO> toVoList(List<AppRole> entities);
    List<AppRoleSimpleVO> toSimpleVoList(List<AppRole> entities);
    List<AppRoleListVO> toRoleVoList(List<AppRole> entities);
}
