<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.task.mapper.TaskMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.task.api.entity.Task">
                    <id column="task_id" property="taskId" />
                    <result column="task_sn" property="taskSn" />
                    <result column="task_name" property="taskName" />
                    <result column="task_desc" property="taskDesc" />
                    <result column="task_type" property="taskType" />
                    <result column="task_status" property="taskStatus" />
                    <result column="cron_expression" property="cronExpression" />
                    <result column="start_time" property="startTime" />
                    <result column="end_time" property="endTime" />
                    <result column="next_run_time" property="nextRunTime" />
                    <result column="last_run_time" property="lastRunTime" />
                    <result column="run_count" property="runCount" />
                    <result column="max_run_count" property="maxRunCount" />
                    <result column="timeout_seconds" property="timeoutSeconds" />
                    <result column="retry_count" property="retryCount" />
                    <result column="retry_interval" property="retryInterval" />
                    <result column="task_input" property="taskInput" />
                    <result column="task_config" property="taskConfig" />
                    <result column="notification_config" property="notificationConfig" />
                    <result column="agent_id" property="agentId" />
                    <result column="version_id" property="versionId" />
                    <result column="tenant_id" property="tenantId" />
                    <result column="create_user_id" property="createUserId" />
                    <result column="update_user_id" property="updateUserId" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        task_id, task_sn, task_name, task_desc, task_type, task_status, cron_expression, start_time, end_time, next_run_time, last_run_time, run_count, max_run_count, timeout_seconds, retry_count, retry_interval, task_input, task_config, notification_config, agent_id, version_id, tenant_id, create_user_id, update_user_id, create_time, update_time
    </sql>

    <select id="selectTaskList" resultType="com.ai.application.task.api.vo.TaskVO">
        select
        <include refid="com.ai.application.task.mapper.TaskMapper.Base_Column_List"></include>
        from task
        order by create_time desc limit 10;
    </select>
</mapper>