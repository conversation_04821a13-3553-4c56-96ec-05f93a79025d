package com.ai.application.app.auth.api.vo;

import com.ai.application.tenant.api.entity.Tenant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Data
@Schema(name = "用户信息返回")
public class UserInfoVO {
    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "用户头像")
    private String avatar;

    @Schema(description = "用户编码")
    private String userSn;

    @Schema(description = "用户角色")
    private List<Role> roles;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "用户功能")
    private List<String> resourceCodes;

    @Schema
    private Tenant tenant;

    @Schema(description = "角色")
    @Data
    public static class Role {
        @Schema(description = "角色名称")
        private String roleName;

        @Schema(description = "角色编码")
        private String code;
    }
}
