package com.ai.application.agent.base.api.mapstruct;
import com.ai.application.agent.base.api.dto.AgentUseKbUpdateDTO;
import com.ai.application.agent.base.api.entity.AgentUseKb;
import com.ai.application.agent.base.api.dto.AgentUseKbAddDTO;
import com.ai.application.agent.base.api.vo.AgentUseKbListVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 智能体关联知识库表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-07
 */

@Mapper(componentModel = "spring")
public interface AgentUseKbMapstruct {

    AgentUseKb toAddEntity(AgentUseKbAddDTO dto);
    AgentUseKb toUpdateEntity(AgentUseKbUpdateDTO dto);
    AgentUseKbListVO toVo(AgentUseKb entity);
    List<AgentUseKbListVO> toVoList(List<AgentUseKb> entities);
}
