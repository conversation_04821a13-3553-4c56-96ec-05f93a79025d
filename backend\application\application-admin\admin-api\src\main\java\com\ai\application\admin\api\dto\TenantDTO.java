package com.ai.application.admin.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 租户表
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(name = "租户表DTO")
public class TenantDTO {
    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Integer tenantId;
    /**
     * 租户sn
     */
    @Schema(description = "租户sn")
    private String tenantSn;
    /**
     * 租户名称
     */
    @Schema(description = "租户名称")
    private String tenantName;
    /**
     * 租户域名
     */
    @Schema(description = "租户域名")
    private String tenantDomain;
    /**
     * 租户描述
     */
    @Schema(description = "租户描述")
    private String tenantDesc;
    /**
     * 有效截止时间
     */
    @Schema(description = "有效截止时间")
    private Date tenantExpireTime;
    /**
     * 租户状态 0:禁用,1:启用,-1:删除
     */
    @Schema(description = "租户状态 0:禁用,1:启用,-1:删除")
    private Integer tenantStatus;
    @Schema(description = "")
    private Date createTime;
    @Schema(description = "")
    private Date updateTime;
}