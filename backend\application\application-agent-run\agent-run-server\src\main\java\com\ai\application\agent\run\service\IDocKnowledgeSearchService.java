package com.ai.application.agent.run.service;

import com.ai.application.agent.run.dto.DocKnowledgeSearchRequestDTO;
import com.ai.application.agent.run.dto.DocKnowledgeSearchResultDTO;
import com.ai.framework.core.vo.ResultVo;

import java.util.List;
import java.util.Map;

/**
 * 文档知识检索服务接口
 */
public interface IDocKnowledgeSearchService {

    /**
     * 执行文档知识检索
     *
     * @param request 检索请求
     * @param authorization 授权信息
     * @return 检索结果
     */
    ResultVo<DocKnowledgeSearchResultDTO> executeDocKnowledgeSearch(DocKnowledgeSearchRequestDTO request, String authorization);

    /**
     * 规则检索
     *
     * @param request 检索请求
     * @param variables 工作流变量
     * @param authorization 授权信息
     * @return 检索结果
     */
    ResultVo<List<DocKnowledgeSearchResultDTO.DocFileInfoDTO>> ruleSearch(DocKnowledgeSearchRequestDTO request, 
                                                                          Map<String, Object> variables, 
                                                                          String authorization);

    /**
     * 语义检索
     *
     * @param request 检索请求
     * @param authorization 授权信息
     * @return 检索结果
     */
    ResultVo<List<DocKnowledgeSearchResultDTO.DocFileInfoDTO>> embeddingSearch(DocKnowledgeSearchRequestDTO request, 
                                                                               String authorization);

    /**
     * 关键词检索
     *
     * @param request 检索请求
     * @param authorization 授权信息
     * @return 检索结果
     */
    ResultVo<List<DocKnowledgeSearchResultDTO.DocFileInfoDTO>> keywordSearch(DocKnowledgeSearchRequestDTO request, 
                                                                             String authorization);

    /**
     * 获取知识库模型编号
     *
     * @param knowledgeInventorySn 知识库编号
     * @return 模型编号
     */
    String getModelSnByInventorySn(String knowledgeInventorySn);

    /**
     * 解析变量值
     *
     * @param value 变量值（可能包含{{variable}}格式）
     * @param variables 变量映射
     * @return 解析后的值
     */
    String parseVariableValue(String value, Map<String, Object> variables);
}
