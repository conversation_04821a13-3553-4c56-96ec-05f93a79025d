package com.ai.application.agent.run.service.impl;

import cn.hutool.json.JSONUtil;
import com.ai.application.agent.run.dto.DocChatRequestDTO;
import com.ai.application.agent.run.dto.KnowledgeQaRequestDTO;
import com.ai.application.agent.run.dto.KnowledgeQaResultDTO;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.service.IKnowledgeQaService;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 知识问答服务实现
 */
@Slf4j
@Service
public class KnowledgeQaServiceImpl implements IKnowledgeQaService {

    @Override
    public ResultVo<KnowledgeQaResultDTO> executeKnowledgeQa(KnowledgeQaRequestDTO request, String authorization) {
        log.info("KnowledgeQaService executeKnowledgeQa start, request: {}", JsonUtils.toJsonString(request));

        try {
            // 参数校验
            validateKnowledgeQaRequest(request);

            // 根据不同的问答模式执行不同的逻辑
            String qaModel = request.getQaModel();
            KnowledgeQaResultDTO result;

            if ("knowledge".equals(qaModel) || qaModel == null) {
                // 知识模式
                result = executeKnowledgeMode(request, authorization);
            } else if ("knowledgeInventory".equals(qaModel)) {
                // 知识库模式
                result = executeKnowledgeInventoryMode(request, authorization);
            } else if ("expand".equals(qaModel)) {
                // 扩展模式
                result = executeExpandMode(request, authorization);
            } else {
                throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "不支持的问答模式: " + qaModel);
            }

            log.info("KnowledgeQaService executeKnowledgeQa success, result: {}", JsonUtils.toJsonString(result));
            return ResultVo.data(result);

        } catch (Exception e) {
            log.error("KnowledgeQaService executeKnowledgeQa error", e);
            return ResultVo.fail(e.getMessage());
        }
    }

    @Override
    public ResultVo<String> getKnowledgeFragmentByDocChat(DocChatRequestDTO request, String authorization) {
        log.info("KnowledgeQaService getKnowledgeFragmentByDocChat start, request: {}", JsonUtils.toJsonString(request));

        try {
            // TODO: 调用实际的文档聊天服务获取知识片段
            // 这里需要根据实际的服务接口进行实现
            String mockFragment = "[{\"pageId\":\"page1\",\"content\":\"这是一个模拟的知识片段\",\"score\":0.95}]";
            
            log.info("KnowledgeQaService getKnowledgeFragmentByDocChat success");
            return ResultVo.data(mockFragment);

        } catch (Exception e) {
            log.error("KnowledgeQaService getKnowledgeFragmentByDocChat error", e);
            return ResultVo.fail(e.getMessage());
        }
    }

    @Override
    public ResultVo<KnowledgeQaResultDTO> executeQaByFragment(KnowledgeQaRequestDTO request, String knowledgeFragment, String authorization) {
        log.info("KnowledgeQaService executeQaByFragment start, fragment: {}", knowledgeFragment);

        try {
            if (StringUtils.isBlank(knowledgeFragment)) {
                log.info("Knowledge fragment is empty, return empty result");
                return ResultVo.data(KnowledgeQaResultDTO.builder()
                        .answer("")
                        .references(Collections.emptyList())
                        .success(false)
                        .errorMessage("知识片段为空")
                        .build());
            }

            // 解析知识片段
            List<DocChatRequestDTO.PageContentDTO> pageContents = parseKnowledgeFragment(knowledgeFragment);
            
            if (CollectionUtils.isEmpty(pageContents)) {
                log.info("Parsed page contents is empty");
                return ResultVo.data(KnowledgeQaResultDTO.builder()
                        .answer("")
                        .references(Collections.emptyList())
                        .success(false)
                        .errorMessage("解析的知识片段为空")
                        .build());
            }

            // 构建文档聊天请求
            DocChatRequestDTO docChatRequest = buildDocChatRequest(request, pageContents);

            // TODO: 调用实际的问答服务
            // 这里需要根据实际的服务接口进行实现
            KnowledgeQaResultDTO result = mockExecuteQa(request, pageContents);

            log.info("KnowledgeQaService executeQaByFragment success");
            return ResultVo.data(result);

        } catch (Exception e) {
            log.error("KnowledgeQaService executeQaByFragment error", e);
            return ResultVo.fail(e.getMessage());
        }
    }

    @Override
    public List<String> getKnowledgeTreeAndAddKnowList(Object knowledgeSn, List<String> knowledgeList) {
        log.info("KnowledgeQaService getKnowledgeTreeAndAddKnowList start, knowledgeSn: {}", knowledgeSn);

        try {
            // TODO: 根据知识列表得到知识库信息
            // 这里需要根据实际的业务逻辑进行实现
            List<String> inventorySnList = new ArrayList<>();
            
            if (knowledgeSn instanceof List) {
                List<?> snList = (List<?>) knowledgeSn;
                for (Object sn : snList) {
                    if (sn != null) {
                        inventorySnList.add(sn.toString());
                    }
                }
            } else if (knowledgeSn != null) {
                inventorySnList.add(knowledgeSn.toString());
            }

            log.info("KnowledgeQaService getKnowledgeTreeAndAddKnowList success, result: {}", inventorySnList);
            return inventorySnList;

        } catch (Exception e) {
            log.error("KnowledgeQaService getKnowledgeTreeAndAddKnowList error", e);
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "获取知识树失败: " + e.getMessage());
        }
    }

    /**
     * 校验知识问答请求
     */
    private void validateKnowledgeQaRequest(KnowledgeQaRequestDTO request) {
        if (StringUtils.isBlank(request.getContent())) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "问答内容不能为空");
        }
        if (StringUtils.isBlank(request.getLlmModelSn())) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "LLM模型不能为空");
        }
        if (StringUtils.isBlank(request.getSummaryPrompt())) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "总结提示词不能为空");
        }
    }

    /**
     * 执行知识模式
     */
    private KnowledgeQaResultDTO executeKnowledgeMode(KnowledgeQaRequestDTO request, String authorization) {
        log.info("Execute knowledge mode");

        // 获取知识片段
        // TODO 这个knowledgeList 是否需要看前端传递的是啥，xbot是 filesn:fileName, 所以需要做切割出fileSn
        List<String> knowledgeList = new ArrayList<>();
        List<String> inventorySnList = getKnowledgeTreeAndAddKnowList(request.getKnowledgeSn(), knowledgeList);
        
        // 构建文档聊天请求获取知识片段
        DocChatRequestDTO docChatRequest = DocChatRequestDTO.builder()
                .inventorySns(inventorySnList)
                .knowledge(knowledgeList)
                .requestType(1) // 文档检索
                .questionFocus(!"0".equals(request.getQuestionFocus()))
                .responseMode(!"efficiency".equals(request.getResponseMode()))
                .msgContent(request.getContent())
                .model(request.getLlmModelSn())
                .delayInMs(20L)
                .build();

        ResultVo<String> fragmentResult = getKnowledgeFragmentByDocChat(docChatRequest, authorization);
        if (fragmentResult.getCode() != 0) {
            throw new ServiceException(fragmentResult.getCode(), fragmentResult.getMessage());
        }

        // 通过知识片段执行问答
        ResultVo<KnowledgeQaResultDTO> qaResult = executeQaByFragment(request, fragmentResult.getData(), authorization);
        if (qaResult.getCode() != 0) {
            throw new ServiceException(qaResult.getCode(), qaResult.getMessage());
        }

        return qaResult.getData();
    }

    /**
     * 执行知识库模式
     */
    private KnowledgeQaResultDTO executeKnowledgeInventoryMode(KnowledgeQaRequestDTO request, String authorization) {
        log.info("Execute knowledge inventory mode");

        List<String> knowledgeInventoryList = request.getKnowledgeInventorySn();
        if (CollectionUtils.isEmpty(knowledgeInventoryList)) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "知识库编号列表不能为空");
        }


        // 构建文档聊天请求
        DocChatRequestDTO docChatRequest = DocChatRequestDTO.builder()
                .inventorySns(knowledgeInventoryList)
                .knowledge(new ArrayList<>())
                .requestType(1) // 文档检索
                .questionFocus(!"0".equals(request.getQuestionFocus()))
                .responseMode(!"efficiency".equals(request.getResponseMode()))
                .msgContent(request.getContent())
                .model(request.getLlmModelSn())
                .delayInMs(20L)
                .build();

        // 获取知识片段
        ResultVo<String> fragmentResult = getKnowledgeFragmentByDocChat(docChatRequest, authorization);
        if (fragmentResult.getCode() != 0) {
            throw new ServiceException(fragmentResult.getCode(), fragmentResult.getMessage());
        }

        // 通过知识片段执行问答
        ResultVo<KnowledgeQaResultDTO> qaResult = executeQaByFragment(request, fragmentResult.getData(), authorization);
        if (qaResult.getCode() != 0) {
            throw new ServiceException(qaResult.getCode(), qaResult.getMessage());
        }

        return qaResult.getData();
    }

    /**
     * 执行扩展模式
     */
    private KnowledgeQaResultDTO executeExpandMode(KnowledgeQaRequestDTO request, String authorization) {
        log.info("Execute expand mode");

        Object expand = request.getExpand();
        if (expand == null) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "扩展内容不能为空");
        }

        String contentNodes = expand.toString();
        if (StringUtils.isBlank(contentNodes)) {
            return KnowledgeQaResultDTO.builder()
                    .answer("未找到相关内容")
                    .references(Collections.emptyList())
                    .success(false)
                    .errorMessage("扩展内容为空")
                    .build();
        }

        try {
            // 解析扩展内容
            List<DocChatRequestDTO.KnowledgeFragmentDTO> contentList = 
                    JSONUtil.toList(contentNodes, DocChatRequestDTO.KnowledgeFragmentDTO.class);

            if (CollectionUtils.isEmpty(contentList)) {
                return KnowledgeQaResultDTO.builder()
                        .answer("未找到相关内容")
                        .references(Collections.emptyList())
                        .success(false)
                        .errorMessage("解析的扩展内容为空")
                        .build();
            }

            // TODO: 调用实际的扩展问答服务
            // 这里需要根据实际的服务接口进行实现
            return mockExecuteExpandQa(request, contentList);

        } catch (Exception e) {
            log.error("Parse expand content error", e);
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "解析扩展内容失败: " + e.getMessage());
        }
    }

    /**
     * 解析知识片段
     */
    private List<DocChatRequestDTO.PageContentDTO> parseKnowledgeFragment(String knowledgeFragment) {
        try {
            return JSONUtil.toList(knowledgeFragment, DocChatRequestDTO.PageContentDTO.class);
        } catch (Exception e) {
            log.error("Parse knowledge fragment error", e);
            return Collections.emptyList();
        }
    }

    /**
     * 构建文档聊天请求
     */
    private DocChatRequestDTO buildDocChatRequest(KnowledgeQaRequestDTO request, List<DocChatRequestDTO.PageContentDTO> pageContents) {
        return DocChatRequestDTO.builder()
                .inventorySns(request.getKnowledgeInventorySn())
                .model(request.getLlmModelSn())
                .requestType(0) // 知识问答
                .responseMode(!"efficiency".equals(request.getResponseMode()))
                .summaryPrompt(request.getSummaryPrompt())
                .msgContent(request.getContent())
                .stream(true)
                .pageList(pageContents)
                .delayInMs(20L)
                .build();
    }

    /**
     * 模拟执行问答
     */
    private KnowledgeQaResultDTO mockExecuteQa(KnowledgeQaRequestDTO request, List<DocChatRequestDTO.PageContentDTO> pageContents) {
        // TODO: 这里需要调用实际的问答服务
        List<KnowledgeQaResultDTO.PageReferenceDTO> references = new ArrayList<>();
        for (DocChatRequestDTO.PageContentDTO page : pageContents) {
            references.add(KnowledgeQaResultDTO.PageReferenceDTO.builder()
                    .pageId(page.getPageId())
                    .content(page.getContent())
                    .score(page.getScore())
                    .fileName(page.getFileName())
                    .pageNumber(page.getPageNumber())
                    .build());
        }

        return KnowledgeQaResultDTO.builder()
                .answer("基于提供的知识片段，这是一个模拟的回答。")
                .references(references)
                .success(true)
                .build();
    }

    /**
     * 模拟执行扩展问答
     */
    private KnowledgeQaResultDTO mockExecuteExpandQa(KnowledgeQaRequestDTO request, List<DocChatRequestDTO.KnowledgeFragmentDTO> contentList) {
        // TODO: 这里需要调用实际的扩展问答服务
        List<KnowledgeQaResultDTO.PageReferenceDTO> references = new ArrayList<>();
        for (DocChatRequestDTO.KnowledgeFragmentDTO fragment : contentList) {
            references.add(KnowledgeQaResultDTO.PageReferenceDTO.builder()
                    .pageId(fragment.getFragmentId())
                    .content(fragment.getContent())
                    .score(fragment.getScore())
                    .build());
        }

        return KnowledgeQaResultDTO.builder()
                .answer("基于扩展内容，这是一个模拟的回答。")
                .references(references)
                .success(true)
                .build();
    }
}
