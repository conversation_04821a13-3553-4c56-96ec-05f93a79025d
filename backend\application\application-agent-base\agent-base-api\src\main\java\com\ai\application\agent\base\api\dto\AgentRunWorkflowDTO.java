package com.ai.application.agent.base.api.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

/**
 * 智能体工作流执行记录表
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@Schema(name = "智能体工作流执行记录表DTO")
public class AgentRunWorkflowDTO {
    /**
     * 工作流执行id
     */
    @Schema(description = "工作流执行id")
    private Integer workflowRunId;

    /**
     * 工作流名称
     */
    @Schema(description = "工作流名称")
    private String workflowName;

    /**
     * 执行状态:1-执行中,2-成功,3-失败,4-中断
     */
    @Schema(description = "执行状态:1-执行中,2-成功,3-失败,4-中断")
    private Integer workflowStatus;

    /**
     * 输入变量
     */
    @Schema(description = "输入变量")
    private String workflowInput;

    /**
     * 输出变量
     */
    @Schema(description = "输出变量")
    private String workflowOutput;

    /**
     * 中间变量
     */
    @Schema(description = "中间变量")
    private String workflowVariables;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String workflowError;

    /**
     * 执行时长(毫秒)
     */
    @Schema(description = "执行时长(毫秒)")
    private Integer workflowDuration;

    /**
     * 工作流定义快照
     */
    @Schema(description = "工作流定义快照")
    private String workflowSnapshot;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private Date workflowStartTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private Date workflowEndTime;

    /**
     * 当前执行节点运行记录ID
     */
    @Schema(description = "当前执行节点运行记录ID")
    private Integer currentNodeRunId;

    /**
     * 运行记录id
     */
    @Schema(description = "运行记录id")
    private Integer runId;

    /**
     * 步骤id
     */
    @Schema(description = "步骤id")
    private Integer stepId;

    /**
     * 工作流id
     */
    @Schema(description = "工作流id")
    private Integer flowId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}