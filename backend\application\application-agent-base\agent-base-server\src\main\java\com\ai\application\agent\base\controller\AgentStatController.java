package com.ai.application.agent.base.controller;

import com.ai.application.agent.base.api.dto.AgentStatDTO;
import com.ai.application.agent.base.api.vo.*;
import com.ai.application.agent.base.service.IAgentStatService;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "智能体表-统计首页相关接口", description = "智能体表-统计相关接口")
@Validated
@CrossOrigin
@RestController
@AllArgsConstructor
@RequestMapping("/v1/stat")
public class AgentStatController {
    private final IAgentStatService agentStatService;

    /**
     * 最近会话智能体表
     * @return
     */
    @Operation(summary = "最近会话智能体表")
    @GetMapping(value = "/session/list")
    public ResultVo<List<LastSessionAgentVO>> lastSessionAgent(){
        return ResultVo.data(agentStatService.queryLastSessionAgent());
    }

    /**
     * 最近会话智能体表
     * @return
     */
    @Operation(summary = "最近会话智能体表")
    @GetMapping(value = "/session/remove/{agentSn}")
    public ResultVo<Void> removeSessionByAgentSn(@PathVariable("agentSn") String agentSn){
        agentStatService.removeSessionByAgentSn(agentSn);
        return ResultVo.success("移除成功");
    }

    @Operation(summary = "中台看板-数量统计")
    @PostMapping(value = "/statCountMetrics")
    public ResultVo<AgentStatResultVO> statCountMetrics(@Validated @RequestBody AgentStatDTO dto){
        return ResultVo.data(agentStatService.statCountMetrics(dto));
    }

    @Operation(summary = "高频智能体tokens消耗")
    @PostMapping(value = "/queryTokensFrequentAgent")
    public ResultVo<List<AgentTokensStatisticsDetailVO>> queryTokensFrequentAgent(@Validated @RequestBody AgentStatDTO dto){
        return ResultVo.data(agentStatService.queryTokensFrequentAgent(dto));
    }

    @Operation(summary = "智能体最近消耗tokens消耗趋势")
    @PostMapping(value = "/queryLastTokensAgent")
    public ResultVo<List<AgentTokensStatisticsDetailVO>> queryLastTokensAgent(@Validated @RequestBody AgentStatDTO dto){
        return ResultVo.data(agentStatService.queryLastTokensAgent(dto));
    }

    @Operation(summary = "最新智能体")
    @GetMapping(value = "/queryLastCreateAgent")
    public ResultVo<List<AgentStatVO>> queryLastCreateAgent(){
        return ResultVo.data(agentStatService.queryLastCreateAgent());
    }

    @Operation(summary = "常用智能体")
    @GetMapping(value = "/queryFrequentAgent")
    public ResultVo<List<AgentStatVO>> queryFrequentAgent(){
        return ResultVo.data(agentStatService.queryFrequentAgent());
    }

    @Operation(summary = "智能体使用统计")
    @GetMapping("/agent/useTotal/{day}")
    public ResultVo<AgentUseTotalVO> queryAgentUse(@Schema(description = "日期 0全部 10本周 20本月 30本季度 40本年") @PathVariable("day") Integer day){
        return ResultVo.data(agentStatService.agentUserTotal(day));
    }
}
