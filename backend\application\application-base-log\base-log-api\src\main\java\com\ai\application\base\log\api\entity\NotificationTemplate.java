package com.ai.application.base.log.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 通知模板配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("notification_template")
public class NotificationTemplate implements Serializable {
        /**
    * 通知模板id
    */
    @Schema(description = "通知模板id")
    @TableId(type = IdType.AUTO)
private Integer ntplId;

    /**
    * 分类:10-异常,20-一般通知,30-成功
    */
    @Schema(description = "分类:10-异常,20-一般通知,30-成功")
    private Integer ntplType;

    /**
    * 模板名称
    */
    @Schema(description = "模板名称")
    private String ntplName;

    /**
    * 模板内容
    */
    @Schema(description = "模板内容")
    private String ntplContent;

    /**
    * 模板状态:1-启用,0-禁用,-1-删除
    */
    @Schema(description = "模板状态:1-启用,0-禁用,-1-删除")
    private Integer ntplStatus;

    /**
    * 模板配置参数
    */
    @Schema(description = "模板配置参数")
    private String ntplConfig;

    /**
    * 租户id
    */
    @Schema(description = "租户id")
    private Integer tenantId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}