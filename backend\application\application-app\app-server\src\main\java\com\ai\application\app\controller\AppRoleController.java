package com.ai.application.app.controller;

import com.ai.application.app.api.dto.AppRoleDTO;
import com.ai.application.app.api.dto.query.AppRoleQueryDTO;
import com.ai.application.app.api.vo.AppRoleListVO;
import com.ai.application.app.api.vo.AppRoleSimpleVO;
import com.ai.application.app.api.vo.AppRoleVO;
import com.ai.application.app.service.IAppRoleService;
import com.ai.framework.core.vo.PageResultVo;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Set;

/**
 * 应用角色表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Tag(name = "应用角色表", description = "应用角色表 相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1/role")
public class AppRoleController {

    @Autowired
    private IAppRoleService appRoleService;

    /**
     * 分页查询
     *
     * @param queryDto
     * @return
     */
    @Operation(summary = "应用角色表-分页查询", description = "查询所有应用角色表 信息")
    @ApiResponse(responseCode = "0", description = "成功",
            content = @Content(schema = @Schema(implementation = AppRoleVO.class)))
    @RequestMapping(value = "/page", method = {RequestMethod.POST})
    public ResultVo<PageInfo<AppRoleVO>> page(@Validated @RequestBody AppRoleQueryDTO queryDto){
        return ResultVo.data(appRoleService.page(queryDto));
    }

    @Operation(summary = "应用角色表查询(基础查询)")
    @GetMapping("/list")
    public ResultVo<List<AppRoleSimpleVO>> simpleList() {
        return ResultVo.data(appRoleService.simpleList(new AppRoleQueryDTO()));
    }

    @Operation(summary = "应用角色表查询(带功能列表)")
    @GetMapping("/query")
    public ResultVo<List<AppRoleListVO>> queryRoles(){
        return ResultVo.data(appRoleService.queryRoles());
    }

    @Operation(summary = "应用角色表查询")
    @GetMapping("/function/{roleCode}")
    public ResultVo<List<AppRoleSimpleVO>> functionList() {
        return ResultVo.data(appRoleService.simpleList(null));
    }

    /**
     * 保存
     *
     * @param dto
     * @return
     */
    @Operation(summary = "应用角色表-新增")
    @PostMapping("/save")
    public ResultVo<Void> save(@Validated @RequestBody AppRoleDTO dto){
        appRoleService.save(dto);
        return ResultVo.success("保存成功");
    }

    /**
     * 修改
     *
     * @param dto
     * @return
     */
    @Operation(summary = "应用角色表-修改")
    @PostMapping(value = "/update")
    public ResultVo<Void> update(@Validated @RequestBody AppRoleDTO dto){
        appRoleService.update(dto);
        return ResultVo.success("修改成功");
    }

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @Operation(summary = "应用角色表-查询详情", description = "根据ID应用角色表 信息")
    @ApiResponse(responseCode = "0", description = "成功",
            content = @Content(schema = @Schema(implementation = AppRoleVO.class)))
    @GetMapping("/get")
    public ResultVo<AppRoleVO> get(@RequestParam Long id){
        return ResultVo.data(appRoleService.get(id));
    }
}