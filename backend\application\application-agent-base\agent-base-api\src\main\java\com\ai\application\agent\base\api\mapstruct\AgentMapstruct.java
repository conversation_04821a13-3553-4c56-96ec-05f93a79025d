package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.bo.AgentMetadataBO;
import com.ai.application.agent.base.api.entity.Agent;
import com.ai.application.agent.base.api.dto.AgentUpdateDTO;
import com.ai.application.agent.base.api.vo.AgentDetailVO;
import com.ai.application.agent.base.api.vo.AgentStatDetailVO;
import com.ai.application.agent.base.api.vo.AgentStatVO;
import com.ai.application.agent.base.api.vo.AgentVO;
import com.ai.framework.core.util.json.JsonUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * <p>
 * 智能体表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */

@Mapper(componentModel = "spring")
public interface AgentMapstruct {
    Agent toAgentUpdateEntity(AgentUpdateDTO dto);
    List<AgentVO> toVoList(List<Agent> entities);
    AgentVO toVo(Agent entity);
    AgentDetailVO toDetail(Agent entity);
    List<AgentStatDetailVO> toAgentVoList(List<AgentVO> voList);
    List<AgentStatVO> toStatVoList(List<AgentVO> voList);
    default AgentMetadataBO stringToAgentMetaData(String agentMetadata) {
        return JsonUtils.parseObject(agentMetadata, AgentMetadataBO.class);
    }
    default String agentMetadataToString(AgentMetadataBO metadata) {
        return metadata != null ? JsonUtils.toJsonString(metadata) : null;
    }
}