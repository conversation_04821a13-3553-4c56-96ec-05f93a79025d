package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.dto.*;
import com.ai.application.agent.base.api.dto.query.AgentPageDTO;
import com.ai.application.agent.base.api.dto.query.AgentQueryDTO;
import com.ai.application.agent.base.api.dto.query.AgentStatDetailQueryDTO;
import com.ai.application.agent.base.api.entity.Agent;
import com.ai.application.agent.base.api.vo.*;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 智能体表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface IAgentService {
    /**
     * 分页
     *
     * @param queryDto
     * @return
     */
    PageInfo<AgentPageVO> page(AgentPageDTO queryDto);

    List<AgentSimpleVO> simpleList();

    List<AgentVO> list(AgentQueryDTO queryDto);

    List<LastSessionAgentVO> queryLastSessionAgent();

    List<AgentStatDetailVO> queryAgentStatDetail(AgentStatDetailQueryDTO dto);

    /**
     * 根据编号查详情
     *
     * @param sn
     * @return
     */
    AgentVO getAgentBySn(String sn);

    /**
     * 保存
     *
     * @param dto
     */
    String create(AgentCreateDTO dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(AgentUpdateDTO dto);

    /**
     * 查看
     *
     * @param id
     * @return
     */
    Agent get(Integer id);

    /**
     * 获取SN查询详情
     *
     * @param agentSn
     * @return
     */
    AgentDetailVO detail(String agentSn);

    /**
     * 根据ID查询详情
     *
     * @param agentId
     * @return
     */
    AgentDetailVO detailById(Integer agentId);

    /**
     * 删除
     *
     * @param agentSn
     */
    void delete(String agentSn);

    /**
     * Agent停用与启用
     *
     * @param dto
     */
    void enable(AgentEnableDTO dto);
}