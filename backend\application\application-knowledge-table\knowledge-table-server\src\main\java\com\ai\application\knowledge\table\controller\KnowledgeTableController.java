package com.ai.application.knowledge.table.controller;

import com.ai.application.knowledge.table.dto.AgentByTableDto;
import com.ai.application.knowledge.table.dto.TableCreateDto;
import com.ai.application.knowledge.table.dto.TableListDto;
import com.ai.application.knowledge.table.service.IKnowledgeTableService;
import com.ai.application.knowledge.table.vo.AgentByTableVo;
import com.ai.application.knowledge.table.vo.TableDetailVo;
import com.ai.application.knowledge.table.vo.TableListVo;
import com.ai.framework.core.vo.ResultVo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@Validated
@RestController
@RequestMapping("/v1/")
@Tag(name = "智能表格", description = "智能表格控制器")
public class KnowledgeTableController {

    @Autowired
    private IKnowledgeTableService knowledgeTableService;

    @Operation(summary = "列表")
    @PostMapping("/list")
    public ResultVo<PageInfo<TableListVo>> list(@Valid @RequestBody TableListDto dto) {
        return knowledgeTableService.list(dto);
    }

    @Operation(summary = "详情")
    @GetMapping("/detail/{tableSn}")
    public ResultVo<TableDetailVo> detail(@PathVariable("tableSn") String tableSn) {
        return knowledgeTableService.detail(tableSn);
    }

    @Operation(summary = "删除")
    @DeleteMapping("/delete/{tableSn}")
    public ResultVo<String> delete(@PathVariable("tableSn") String tableSn) {
        return knowledgeTableService.delete(tableSn);
    }

    @Operation(summary = "编辑")
    @PostMapping("/update")
    public ResultVo<String> update(@Valid @RequestBody TableCreateDto dto) {
        return knowledgeTableService.update(dto);
    }

    @Operation(summary = "新增")
    @PostMapping("/create")
    public ResultVo<String> create(@Valid @RequestBody TableCreateDto dto) {
        return knowledgeTableService.create(dto);
    }

    @Operation(summary = "获取关联的应用")
    @PostMapping("/agentByTable")
    public ResultVo<List<AgentByTableVo>> agentByBb(@Valid @RequestBody AgentByTableDto dto) {
        return knowledgeTableService.agentByBb(dto);
    }
}
