package com.ai.application.agent.base.api.mapstruct;
import com.ai.application.agent.base.api.entity.AgentRunTool;
import com.ai.application.agent.base.api.dto.AgentRunToolDTO;
import com.ai.application.agent.base.api.vo.AgentRunToolVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 智能体工具执行记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */

@Mapper(componentModel = "spring")
public interface AgentRunToolMapstruct {

    AgentRunTool toEntity(AgentRunToolDTO dto);
    List<AgentRunTool> toEntityList(List<AgentRunToolDTO> dtolist);
    AgentRunToolVO toVo(AgentRunTool entity);
    List<AgentRunToolVO> toVoList(List<AgentRunTool> entities);
}
