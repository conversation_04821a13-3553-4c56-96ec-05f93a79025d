package com.ai.application.agent.run.executor;

import cn.hutool.json.JSONUtil;
import com.ai.application.agent.run.dto.MemoryRetrieveConfigDTO;
import com.ai.application.agent.run.dto.ShortTermMemoryRetrieveRequestDTO;
import com.ai.application.agent.run.dto.ShortTermMemoryRetrieveResultDTO;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.service.IShortTermMemoryRetrieveService;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import com.ai.framework.workflow.excutor.NodeExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 短期记忆提取节点执行器
 * 用于在工作流中从记忆服务提取短期记忆
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ShortTermMemoryRetrieveNodeExecutor implements NodeExecutor {

    private final IShortTermMemoryRetrieveService shortTermMemoryRetrieveService;

    @Override
    public void execute(WorkflowContext context) {
        String nodeKey = context.getCurrentNodeKey();
        NodeContext nodeCtx = context.getNodeContexts().get(nodeKey);
        Map<String, Object> nodeDef = nodeCtx.getNodeDefinition();

        log.info("ShortTermMemoryRetrieveNodeExecutor execute start, nodeKey: {}, nodeDef: {}", nodeKey, JsonUtils.toJsonString(nodeDef));

        try {
            // 设置节点状态为运行中
            nodeCtx.setStatus(NodeStatus.RUNNING);

            // 初始化节点输出
            if (nodeCtx.getOutput() == null) {
                nodeCtx.setOutput(new HashMap<>());
            }

            // 从节点定义中获取输入参数
            Map<String, Object> inputParameters = (Map<String, Object>) nodeDef.get("inputParameters");
            if (inputParameters == null) {
                throw new ServiceException(ExecutorError.NODE_DEFINITION_IS_NULL);
            }

            // 构建短期记忆提取请求
            ShortTermMemoryRetrieveRequestDTO request = buildShortTermMemoryRetrieveRequest(inputParameters, context);

            // 获取授权信息
            String authorization = (String) context.getGlobalVars().get("authorization");

            // 执行短期记忆提取
            ResultVo<ShortTermMemoryRetrieveResultDTO> result = shortTermMemoryRetrieveService.executeShortTermMemoryRetrieve(request, authorization);

            if (result.getCode() != 0 || result.getData() == null || !result.getData().getSuccess()) {
                String errorMsg = result.getData() != null ? result.getData().getErrorMessage() : result.getMessage();
                throw new ServiceException(result.getCode(), errorMsg);
            }

            ShortTermMemoryRetrieveResultDTO retrieveResult = result.getData();

            // 构建输出结果
            Map<String, Object> outputResult = buildOutputResult(retrieveResult);

            // 将结果写入输出参数
            writeOutputParameters(nodeDef, context, outputResult);

            // 设置节点输出
            nodeCtx.getOutput().putAll(outputResult);

            // 设置节点状态为成功
            nodeCtx.setStatus(NodeStatus.SUCCESS);
            nodeCtx.setEndTime(java.time.LocalDateTime.now());

            log.info("ShortTermMemoryRetrieveNodeExecutor execute success, processed {} configs, success {} configs",
                    retrieveResult.getProcessedConfigCount(), retrieveResult.getSuccessConfigCount());

        } catch (Exception e) {
            log.error("ShortTermMemoryRetrieveNodeExecutor execute error", e);
            nodeCtx.setStatus(NodeStatus.FAILED);
            nodeCtx.setErrorMsg("短期记忆提取执行失败: " + e.getMessage());
            nodeCtx.setEndTime(java.time.LocalDateTime.now());
            throw e;
        }
    }

    /**
     * 构建短期记忆提取请求
     */
    private ShortTermMemoryRetrieveRequestDTO buildShortTermMemoryRetrieveRequest(Map<String, Object> inputParameters, WorkflowContext context) {
        // 获取参数值，支持变量替换
        String agentId = getParameterValue(inputParameters, "agent_id", context);
        String userId = getParameterValue(inputParameters, "user_id", context);
        String extractConfigsStr = getParameterValue(inputParameters, "extractConfigs", context);
        String startTimeStr = getParameterValue(inputParameters, "startTime", context);
        String endTimeStr = getParameterValue(inputParameters, "endTime", context);
        String sortBy = getParameterValue(inputParameters, "sortBy", context);

        // 参数校验
        if (StringUtils.isBlank(agentId)) {
            throw new ServiceException(ExecutorError.AGENT_ID_IS_BLANK);
        }
        if (StringUtils.isBlank(userId)) {
            throw new ServiceException(ExecutorError.USER_ID_IS_BLANK);
        }
        if (StringUtils.isBlank(extractConfigsStr)) {
            throw new ServiceException(ExecutorError.SHORT_TERM_MEMORY_RETRIEVE_CONDITIONS_INVALID.getCode(), "提取配置不能为空");
        }

        // 解析提取配置列表
        List<MemoryRetrieveConfigDTO> extractConfigs;
        try {
            extractConfigs = JsonUtils.parseArray(extractConfigsStr, MemoryRetrieveConfigDTO.class);
        } catch (Exception e) {
            log.error("Failed to parse extractConfigs: {}", extractConfigsStr, e);
            throw new ServiceException(ExecutorError.SHORT_TERM_MEMORY_RETRIEVE_CONDITIONS_INVALID.getCode(), "提取配置格式错误");
        }

        // 构建请求
        ShortTermMemoryRetrieveRequestDTO.ShortTermMemoryRetrieveRequestDTOBuilder builder = ShortTermMemoryRetrieveRequestDTO.builder()
                .agentId(agentId)
                .userId(userId)
                .extractConfigs(extractConfigs)
                .sortBy(StringUtils.isNotBlank(sortBy) ? sortBy : "time_desc");

        // 处理时间范围
        if (StringUtils.isNotBlank(startTimeStr)) {
            try {
                builder.startTime(LocalDateTime.parse(startTimeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            } catch (Exception e) {
                log.warn("Invalid start time format: {}", startTimeStr);
            }
        }

        if (StringUtils.isNotBlank(endTimeStr)) {
            try {
                builder.endTime(LocalDateTime.parse(endTimeStr, DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            } catch (Exception e) {
                log.warn("Invalid end time format: {}", endTimeStr);
            }
        }

        return builder.build();
    }

    /**
     * 构建输出结果
     */
    private Map<String, Object> buildOutputResult(ShortTermMemoryRetrieveResultDTO retrieveResult) {
        Map<String, Object> result = new HashMap<>();

        // 将memoryMap的内容直接作为结果返回
        if (retrieveResult.getMemoryMap() != null) {
            result.putAll(retrieveResult.getMemoryMap());
        }

        // 添加元数据
        result.put("success", retrieveResult.getSuccess());
        result.put("processedConfigCount", retrieveResult.getProcessedConfigCount());
        result.put("successConfigCount", retrieveResult.getSuccessConfigCount());

        if (StringUtils.isNotBlank(retrieveResult.getErrorMessage())) {
            result.put("errorMessage", retrieveResult.getErrorMessage());
        }

        return result;
    }

    /**
     * 获取参数值，支持变量替换
     */
    private String getParameterValue(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        String strValue = value.toString();
        
        // 如果是变量引用（以$开头），从全局变量中获取
        if (strValue.startsWith("$")) {
            String varName = strValue.substring(1);
            Object varValue = context.getGlobalVars().get(varName);
            return varValue != null ? varValue.toString() : null;
        }
        
        return strValue;
    }

    /**
     * 写入输出参数
     */
    private void writeOutputParameters(Map<String, Object> nodeDef, WorkflowContext context, Map<String, Object> result) {
        Map<String, Object> outputParameters = (Map<String, Object>) nodeDef.get("outputParameters");
        if (outputParameters != null) {
            for (Map.Entry<String, Object> entry : outputParameters.entrySet()) {
                String outputKey = entry.getKey();
                String variableName = entry.getValue().toString();

                Object resultValue = result.get(outputKey);
                if (resultValue != null) {
                    context.setVar(variableName, resultValue);
                    log.info("Set variable {} = {}", variableName, resultValue);
                }
            }
        }
    }

    @Override
    public String getType() {
        return "SHORT_TERM_MEMORY_RETRIEVE";
    }
}
