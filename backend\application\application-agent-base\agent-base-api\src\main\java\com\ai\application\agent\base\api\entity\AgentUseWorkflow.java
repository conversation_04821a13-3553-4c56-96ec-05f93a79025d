package com.ai.application.agent.base.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 智能体工作流表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("agent_use_workflow")
public class AgentUseWorkflow implements Serializable {
    /**
    * 工作流id
    */
    @Schema(description = "工作流id")
    @TableId(type = IdType.AUTO)
    private Integer flowId;

    /**
    * 工作流变量定义
    */
    @Schema(description = "工作流变量定义")
    private String flowVariables;

    /**
    * 工作流定义
    */
    @Schema(description = "工作流定义")
    private String flowDefinition;

    /**
    * 额外定义参数
    */
    @Schema(description = "额外定义参数")
    private String flowExtensions;

    /**
    * 开始变量
    */
    @Schema(description = "开始变量")
    private String flowStartVariables;

    /**
    * 结束变量
    */
    @Schema(description = "结束变量")
    private String flowEndVariables;

    /**
    * 使用引导
    */
    @Schema(description = "使用引导")
    private String flowGuide;

    /**
    * 启用状态 0未发布 1发布 -1废弃
    */
    @Schema(description = "启用状态 0未发布 1发布 -1废弃")
    private Integer flowStatus;

    /**
    * 智能体id
    */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
    * 智能体版本id
    */
    @Schema(description = "智能体版本id")
    private Integer versionId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}