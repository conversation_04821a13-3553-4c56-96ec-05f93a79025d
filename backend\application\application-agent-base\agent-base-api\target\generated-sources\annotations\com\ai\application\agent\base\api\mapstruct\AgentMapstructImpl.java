package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.dto.AgentUpdateDTO;
import com.ai.application.agent.base.api.entity.Agent;
import com.ai.application.agent.base.api.vo.AgentDetailVO;
import com.ai.application.agent.base.api.vo.AgentStatDetailVO;
import com.ai.application.agent.base.api.vo.AgentStatVO;
import com.ai.application.agent.base.api.vo.AgentVO;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-14T11:00:34+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentMapstructImpl implements AgentMapstruct {

    @Override
    public Agent toAgentUpdateEntity(AgentUpdateDTO dto) {
        if ( dto == null ) {
            return null;
        }

        Agent agent = new Agent();

        agent.setAgentSn( dto.getAgentSn() );
        agent.setAgentName( dto.getAgentName() );
        agent.setAgentDesc( dto.getAgentDesc() );
        agent.setAgentType( dto.getAgentType() );
        agent.setAgentMetadata( agentMetadataToString( dto.getAgentMetadata() ) );

        return agent;
    }

    @Override
    public List<AgentVO> toVoList(List<Agent> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AgentVO> list = new ArrayList<AgentVO>( entities.size() );
        for ( Agent agent : entities ) {
            list.add( toVo( agent ) );
        }

        return list;
    }

    @Override
    public AgentVO toVo(Agent entity) {
        if ( entity == null ) {
            return null;
        }

        AgentVO agentVO = new AgentVO();

        agentVO.setAgentId( entity.getAgentId() );
        agentVO.setAgentSn( entity.getAgentSn() );
        agentVO.setAgentName( entity.getAgentName() );
        agentVO.setAgentDesc( entity.getAgentDesc() );
        agentVO.setAgentType( entity.getAgentType() );
        agentVO.setAgentMetadata( entity.getAgentMetadata() );
        agentVO.setAgentStatus( entity.getAgentStatus() );
        agentVO.setVersionId( entity.getVersionId() );
        agentVO.setTenantId( entity.getTenantId() );
        agentVO.setUserId( entity.getUserId() );
        agentVO.setCreateTime( entity.getCreateTime() );
        agentVO.setUpdateTime( entity.getUpdateTime() );

        return agentVO;
    }

    @Override
    public AgentDetailVO toDetail(Agent entity) {
        if ( entity == null ) {
            return null;
        }

        AgentDetailVO agentDetailVO = new AgentDetailVO();

        agentDetailVO.setAgentSn( entity.getAgentSn() );
        agentDetailVO.setAgentName( entity.getAgentName() );
        agentDetailVO.setAgentDesc( entity.getAgentDesc() );
        agentDetailVO.setAgentType( entity.getAgentType() );
        agentDetailVO.setAgentMetadata( stringToAgentMetaData( entity.getAgentMetadata() ) );
        agentDetailVO.setAgentStatus( entity.getAgentStatus() );
        agentDetailVO.setCreateTime( entity.getCreateTime() );
        agentDetailVO.setUpdateTime( entity.getUpdateTime() );

        return agentDetailVO;
    }

    @Override
    public List<AgentStatDetailVO> toAgentVoList(List<AgentVO> voList) {
        if ( voList == null ) {
            return null;
        }

        List<AgentStatDetailVO> list = new ArrayList<AgentStatDetailVO>( voList.size() );
        for ( AgentVO agentVO : voList ) {
            list.add( agentVOToAgentStatDetailVO( agentVO ) );
        }

        return list;
    }

    @Override
    public List<AgentStatVO> toStatVoList(List<AgentVO> voList) {
        if ( voList == null ) {
            return null;
        }

        List<AgentStatVO> list = new ArrayList<AgentStatVO>( voList.size() );
        for ( AgentVO agentVO : voList ) {
            list.add( agentVOToAgentStatVO( agentVO ) );
        }

        return list;
    }

    protected AgentStatDetailVO agentVOToAgentStatDetailVO(AgentVO agentVO) {
        if ( agentVO == null ) {
            return null;
        }

        AgentStatDetailVO agentStatDetailVO = new AgentStatDetailVO();

        agentStatDetailVO.setAgentSn( agentVO.getAgentSn() );
        agentStatDetailVO.setAgentName( agentVO.getAgentName() );
        agentStatDetailVO.setAgentDesc( agentVO.getAgentDesc() );
        agentStatDetailVO.setAgentType( agentVO.getAgentType() );
        agentStatDetailVO.setAgentStatus( agentVO.getAgentStatus() );
        agentStatDetailVO.setVersionId( agentVO.getVersionId() );

        return agentStatDetailVO;
    }

    protected AgentStatVO agentVOToAgentStatVO(AgentVO agentVO) {
        if ( agentVO == null ) {
            return null;
        }

        AgentStatVO agentStatVO = new AgentStatVO();

        agentStatVO.setAgentSn( agentVO.getAgentSn() );
        agentStatVO.setAgentName( agentVO.getAgentName() );
        agentStatVO.setAgentDesc( agentVO.getAgentDesc() );
        agentStatVO.setAgentType( agentVO.getAgentType() );
        if ( agentVO.getUpdateTime() != null ) {
            agentStatVO.setUpdateTime( new SimpleDateFormat().format( agentVO.getUpdateTime() ) );
        }

        return agentStatVO;
    }
}
