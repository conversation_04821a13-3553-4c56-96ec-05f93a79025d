package com.ai.application.skill.mcp.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

/**
 * <p>
 * MCP工具表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("mcp_tool")
@Builder
public class McpTool implements Serializable {
    /**
     * MCP工具id
     */
    @Schema(description = "MCP工具id")
    @TableId(type = IdType.AUTO)
    private Integer mcpToolId;

    /**
     * 工具名称
     */
    @Schema(description = "工具名称")
    private String toolName;

    /**
     * 工具描述
     */
    @Schema(description = "工具描述")
    private String toolDesc;

    /**
     * 工具分类
     */
    @Schema(description = "工具分类")
    private String toolCategory;

    /**
     * 输入参数结构
     */
    @Schema(description = "输入参数结构")
    private String inputSchema;

    /**
     * 输出结果结构
     */
    @Schema(description = "输出结果结构")
    private String outputSchema;

    /**
     * 工具配置
     */
    @Schema(description = "工具配置")
    private String toolConfig;

    /**
     * 工具元数据
     */
    @Schema(description = "工具元数据")
    private String toolMetadata;

    /**
     * 工具状态:1-可用,0-不可用,-1-删除
     */
    @Schema(description = "工具状态:1-可用,0-不可用,-1-删除")
    private Integer toolStatus;

    /**
     * 调用频率限制(次/分钟)
     */
    @Schema(description = "调用频率限制(次/分钟)")
    private Integer rateLimit;

    /**
     * 超时时间(秒)
     */
    @Schema(description = "超时时间(秒)")
    private Integer timeoutSeconds;

    /**
     * 最后调用时间
     */
    @Schema(description = "最后调用时间")
    private Date lastCallTime;

    /**
     * 调用次数
     */
    @Schema(description = "调用次数")
    private Integer callCount;

    /**
     * MCP服务器id
     */
    @Schema(description = "MCP服务器id")
    private Integer serverId;

    @Schema(description = "")
    private Timestamp createTime;

    @Schema(description = "")
    private Timestamp updateTime;

}