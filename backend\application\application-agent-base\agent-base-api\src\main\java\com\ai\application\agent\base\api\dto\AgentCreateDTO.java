package com.ai.application.agent.base.api.dto;

import com.ai.application.agent.base.api.bo.AgentMetadataBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 智能体表
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@Schema(name = "创建智能体DTO")
public class AgentCreateDTO {

    /**
     * 智能体名称
     */
    @Schema(description = "智能体名称")
    private String agentName;

    /**
     * 智能体描述
     */
    @Schema(description = "智能体描述")
    private String agentDesc;

    @Schema(description = "智能体类型")
    private Integer agentType;

    /**
     * 智能体元信息:logo,icon,创建人名等
     */
    @Schema(description = "智能体元信息:logo,icon,创建人名等")
    private AgentMetadataBO agentMetadata;
}