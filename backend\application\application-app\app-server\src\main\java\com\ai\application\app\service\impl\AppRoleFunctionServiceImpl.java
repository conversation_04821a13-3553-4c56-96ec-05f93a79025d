package com.ai.application.app.service.impl;

import com.ai.application.app.api.dto.AppRoleFunctionDTO;
import com.ai.application.app.api.dto.query.AppRoleFunctionQueryDTO;
import com.ai.application.app.api.entity.AppFunction;
import com.ai.application.app.api.entity.AppRoleFunction;
import com.ai.application.app.api.mapstruct.AppRoleFunctionMapstruct;
import com.ai.application.app.api.vo.AppRoleFunctionVO;
import com.ai.application.app.mapper.AppRoleFunctionMapper;
import com.ai.application.app.service.IAppRoleFunctionService;
import com.ai.framework.core.util.BusinessAssertUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 应用角色功能表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Service
public class AppRoleFunctionServiceImpl implements IAppRoleFunctionService{

    @Resource
    private AppRoleFunctionMapper appRoleFunctionMapper;
    @Resource
    private AppRoleFunctionMapstruct appRoleFunctionMapstruct;

    @Transactional(readOnly = true)
    @Override
    public PageInfo<AppRoleFunctionVO> page(AppRoleFunctionQueryDTO queryDto) {
        QueryWrapper<AppRoleFunction> queryWrapper = this.buildQuery(queryDto);
        Page<AppRoleFunction> page = new Page<>(queryDto.getPageNo(), queryDto.getPageSize());
        Page<AppRoleFunction> result = this.appRoleFunctionMapper.selectPage(page, queryWrapper);
        return PageInfo.of(appRoleFunctionMapstruct.toVoList(result.getRecords()));
    }

    @Transactional(readOnly = true)
    @Override
    public List<AppRoleFunctionVO> list(AppRoleFunctionQueryDTO queryDto) {
        QueryWrapper<AppRoleFunction> queryWrapper = this.buildQuery(queryDto);
        return appRoleFunctionMapstruct.toVoList(this.appRoleFunctionMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(AppRoleFunctionDTO dto) {
        // TODO 唯一性字段校验
        dto.setFunId(null);
        AppRoleFunction entity = appRoleFunctionMapstruct.toEntity(dto);
        entity.setCreateTime(new Date());
        appRoleFunctionMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AppRoleFunctionDTO dto) {
        BusinessAssertUtil.notNull(dto.getFunId(), "id不能为空");
        // TODO 唯一性字段校验
        AppRoleFunction entity = appRoleFunctionMapper.selectById(dto.getFunId());
        BusinessAssertUtil.notNull(entity, "找不到id为 " + dto.getFunId() + " 的记录");
        AppRoleFunction entityList = appRoleFunctionMapstruct.toEntity(dto);
        entityList.setUpdateTime(new Date());
        appRoleFunctionMapper.updateById(entityList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Set<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            appRoleFunctionMapper.deleteBatchIds(ids);
        }
    }

    @Transactional(readOnly = true)
    @Override
    public AppRoleFunctionVO get(Long id) {
        BusinessAssertUtil.notNull(id, "id不能为空");
        AppRoleFunction entity = appRoleFunctionMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到id为 " + id + " 的记录");
        return appRoleFunctionMapstruct.toVo(entity);
    }

    private QueryWrapper<AppRoleFunction> buildQuery(AppRoleFunctionQueryDTO queryDto) {
        QueryWrapper<AppRoleFunction> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AppRoleFunction::getRfStatus, 1);
        return queryWrapper;
    }
}