package com.ai.application.agent.base.mapper;

import com.ai.application.agent.base.api.dto.query.AgentUseToolQueryDTO;
import com.ai.application.agent.base.api.entity.AgentUseTool;
import com.ai.application.agent.base.api.vo.AgentUseToolQueryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 智能体使用工具表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface AgentUseToolMapper extends BaseMapper<AgentUseTool> {
    IPage<AgentUseToolQueryVO> selectUseToolByPage(IPage<AgentUseToolQueryVO> page, @Param("params") AgentUseToolQueryDTO dto);
}
