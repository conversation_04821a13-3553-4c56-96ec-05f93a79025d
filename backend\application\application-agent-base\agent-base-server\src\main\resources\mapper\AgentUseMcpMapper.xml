<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.agent.base.mapper.AgentUseMcpMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.agent.base.api.entity.AgentUseMcp">
                    <id column="amc_id" property="amcId" />
                    <result column="amc_status" property="amcStatus" />
                    <result column="agent_id" property="agentId" />
                    <result column="version_id" property="versionId" />
                    <result column="mcp_tool_id" property="mcpToolId" />
                    <result column="mcp_extend" property="mcpExtend" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        amc_id, amc_status, agent_id, version_id, mcp_tool_id, mcp_extend, create_time, update_time
    </sql>

    <select id="selectUseMcpByPage" resultType="com.ai.application.agent.base.api.vo.AgentUseMcpQueryVO">
        select
        <include refid="com.ai.application.agent.base.mapper.AgentUseMcpMapper.Base_Column_List"></include>
        from agent_use_mcp
        order by create_time desc;
    </select>
</mapper>