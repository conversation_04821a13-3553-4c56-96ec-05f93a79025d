package com.ai.application.agent.run.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ai.application.agent.run.dto.DocKnowledgeSearchRequestDTO;
import com.ai.application.agent.run.dto.DocKnowledgeSearchResultDTO;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.service.IDocKnowledgeSearchService;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 文档知识检索服务实现
 */
@Slf4j
@Service
public class DocKnowledgeSearchServiceImpl implements IDocKnowledgeSearchService {

    @Override
    public ResultVo<DocKnowledgeSearchResultDTO> executeDocKnowledgeSearch(DocKnowledgeSearchRequestDTO request, String authorization) {
        log.info("DocKnowledgeSearchService executeDocKnowledgeSearch start, request: {}", JsonUtils.toJsonString(request));

        try {
            // 参数校验
            validateDocKnowledgeSearchRequest(request);

            DocKnowledgeSearchResultDTO result;
            String type = request.getType();

            // 根据backend2的DocKnowledgeSearchExecutor逻辑
            if ("rule".equals(type)) {
                // 规则检索
                result = executeRuleSearch(request, null, authorization);
            } else if ("embedding".equals(type)) {
                // 语义检索
                result = executeEmbeddingSearch(request, authorization);
            } else if ("keywords".equals(type)) {
                // 关键词检索
                result = executeKeywordSearch(request, authorization);
            } else {
                throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "不支持的检索类型: " + type);
            }

            log.info("DocKnowledgeSearchService executeDocKnowledgeSearch success, result: {}", JsonUtils.toJsonString(result));
            return ResultVo.data(result);

        } catch (Exception e) {
            log.error("DocKnowledgeSearchService executeDocKnowledgeSearch error", e);
            return ResultVo.fail(e.getMessage());
        }
    }

    @Override
    public ResultVo<List<DocKnowledgeSearchResultDTO.DocFileInfoDTO>> ruleSearch(DocKnowledgeSearchRequestDTO request,
                                                                                 Map<String, Object> variables,
                                                                                 String authorization) {
        log.info("DocKnowledgeSearchService ruleSearch start");

        try {
            // TODO: 调用实际的规则检索服务
            // 这里需要根据实际的服务接口进行实现
            List<DocKnowledgeSearchResultDTO.DocFileInfoDTO> mockResult = mockRuleSearch(request, variables);

            log.info("DocKnowledgeSearchService ruleSearch success");
            return ResultVo.data(mockResult);

        } catch (Exception e) {
            log.error("DocKnowledgeSearchService ruleSearch error", e);
            return ResultVo.fail(e.getMessage());
        }
    }

    @Override
    public ResultVo<List<DocKnowledgeSearchResultDTO.DocFileInfoDTO>> embeddingSearch(DocKnowledgeSearchRequestDTO request,
                                                                                      String authorization) {
        log.info("DocKnowledgeSearchService embeddingSearch start");

        try {
            // TODO: 调用实际的语义检索服务
            // 这里需要根据实际的服务接口进行实现
            List<DocKnowledgeSearchResultDTO.DocFileInfoDTO> mockResult = mockEmbeddingSearch(request);

            log.info("DocKnowledgeSearchService embeddingSearch success");
            return ResultVo.data(mockResult);

        } catch (Exception e) {
            log.error("DocKnowledgeSearchService embeddingSearch error", e);
            return ResultVo.fail(e.getMessage());
        }
    }

    @Override
    public ResultVo<List<DocKnowledgeSearchResultDTO.DocFileInfoDTO>> keywordSearch(DocKnowledgeSearchRequestDTO request,
                                                                                    String authorization) {
        log.info("DocKnowledgeSearchService keywordSearch start");

        try {
            // TODO: 调用实际的关键词检索服务
            // 这里需要根据实际的服务接口进行实现
            List<DocKnowledgeSearchResultDTO.DocFileInfoDTO> mockResult = mockKeywordSearch(request);

            log.info("DocKnowledgeSearchService keywordSearch success");
            return ResultVo.data(mockResult);

        } catch (Exception e) {
            log.error("DocKnowledgeSearchService keywordSearch error", e);
            return ResultVo.fail(e.getMessage());
        }
    }

    @Override
    public String getModelSnByInventorySn(String knowledgeInventorySn) {
        log.info("DocKnowledgeSearchService getModelSnByInventorySn start, knowledgeInventorySn: {}", knowledgeInventorySn);

        try {
            // TODO: 实现获取知识库模型编号的逻辑
            // 这里需要根据实际的服务接口进行实现
            String modelSn = "localModel"; // 模拟返回
            
            log.info("DocKnowledgeSearchService getModelSnByInventorySn success, modelSn: {}", modelSn);
            return modelSn;

        } catch (Exception e) {
            log.error("DocKnowledgeSearchService getModelSnByInventorySn error", e);
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "获取模型编号失败: " + e.getMessage());
        }
    }

    @Override
    public String parseVariableValue(String value, Map<String, Object> variables) {
        if (StringUtils.isBlank(value) || variables == null) {
            return value;
        }

        String regex = "\\{\\{([^}]+)}}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(value);
        
        while (matcher.find()) {
            String variableName = matcher.group(1);
            Object variableValue = variables.get(variableName);
            if (variableValue != null) {
                return variableValue.toString();
            }
        }
        
        return value;
    }

    /**
     * 校验文档知识检索请求
     */
    private void validateDocKnowledgeSearchRequest(DocKnowledgeSearchRequestDTO request) {
        if (StringUtils.isBlank(request.getKnowledgeInventorySn()) && CollectionUtils.isEmpty(request.getKnowledgeInventorySnList())) {
            throw new ServiceException(ExecutorError.KNOWLEDGE_INVENTORY_SN_IS_NULL);
        }
        
        String type = request.getType();
        if (StringUtils.isBlank(type)) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "检索类型不能为空");
        }

        // 根据检索类型校验相应参数
        if ("embedding".equals(type) || "keywords".equals(type)) {
            if (StringUtils.isBlank(request.getSearchKnowledgeContent())) {
                throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "检索内容不能为空");
            }
        } else if ("rule".equals(type)) {
            if (request.getConditions() == null) {
                throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "规则检索条件不能为空");
            }
        }
    }

    /**
     * 执行规则检索
     */
    private DocKnowledgeSearchResultDTO executeRuleSearch(DocKnowledgeSearchRequestDTO request, 
                                                          Map<String, Object> variables, 
                                                          String authorization) {
        log.info("Execute rule search");

        ResultVo<List<DocKnowledgeSearchResultDTO.DocFileInfoDTO>> result = ruleSearch(request, variables, authorization);
        if (result.getCode() != 0) {
            throw new ServiceException(result.getCode(), result.getMessage());
        }

        return buildResultFromConditions(request.getConditions(), result.getData());
    }

    /**
     * 执行语义检索
     */
    private DocKnowledgeSearchResultDTO executeEmbeddingSearch(DocKnowledgeSearchRequestDTO request, String authorization) {
        log.info("Execute embedding search");

        ResultVo<List<DocKnowledgeSearchResultDTO.DocFileInfoDTO>> result = embeddingSearch(request, authorization);
        if (result.getCode() != 0) {
            throw new ServiceException(result.getCode(), result.getMessage());
        }

        return buildResultFromConditions(request.getConditions(), result.getData());
    }

    /**
     * 执行关键词检索
     */
    private DocKnowledgeSearchResultDTO executeKeywordSearch(DocKnowledgeSearchRequestDTO request, String authorization) {
        log.info("Execute keyword search");

        ResultVo<List<DocKnowledgeSearchResultDTO.DocFileInfoDTO>> result = keywordSearch(request, authorization);
        if (result.getCode() != 0) {
            throw new ServiceException(result.getCode(), result.getMessage());
        }

        return buildResultFromConditions(request.getConditions(), result.getData());
    }

    /**
     * 根据条件构建结果 - 基于backend2逻辑
     */
    private DocKnowledgeSearchResultDTO buildResultFromConditions(Object conditions, List<DocKnowledgeSearchResultDTO.DocFileInfoDTO> docFiles) {
        JSONArray conditionArray = JSONUtil.parseArray(conditions);
        
        boolean hasResults = CollectionUtils.isNotEmpty(docFiles);
        String target = null;
        String matchResult = hasResults ? "True" : "False";
        
        // 根据backend2逻辑：获取对应的target
        if (hasResults && conditionArray.size() > 0) {
            // 有结果时取第一个条件的target
            JSONObject condition = conditionArray.getJSONObject(0);
            target = condition.getStr("target");
        } else if (!hasResults && conditionArray.size() > 1) {
            // 无结果时取第二个条件的target
            JSONObject condition = conditionArray.getJSONObject(1);
            target = condition.getStr("target");
        }

        // 转换文档格式
        List<String> docFileList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(docFiles)) {
            for (DocKnowledgeSearchResultDTO.DocFileInfoDTO docFile : docFiles) {
                String docFileStr = String.format("%s::%s::%s", 
                    docFile.getFileId(), docFile.getFileName(), "KNOWLEDGE");
                docFileList.add(docFileStr);
            }
        }

        return DocKnowledgeSearchResultDTO.builder()
                .docFile(docFileList)
                .target(target)
                .matchResult(matchResult)
                .success(true)
                .totalCount(docFileList.size())
                .build();
    }

    /**
     * 模拟规则检索
     */
    private List<DocKnowledgeSearchResultDTO.DocFileInfoDTO> mockRuleSearch(DocKnowledgeSearchRequestDTO request, 
                                                                            Map<String, Object> variables) {
        // TODO: 这里需要调用实际的规则检索服务
        List<DocKnowledgeSearchResultDTO.DocFileInfoDTO> result = new ArrayList<>();
        
        // 模拟返回一些文档
        result.add(DocKnowledgeSearchResultDTO.DocFileInfoDTO.builder()
                .fileId(1001L)
                .fileName("规则检索文档1.pdf")
                .fileSn("rule_doc_001")
                .dataType("KNOWLEDGE")
                .documentTag("规则检索")
                .documentSummary("这是一个规则检索的测试文档")
                .createTime(System.currentTimeMillis())
                .build());
        
        return result;
    }

    /**
     * 模拟语义检索
     */
    private List<DocKnowledgeSearchResultDTO.DocFileInfoDTO> mockEmbeddingSearch(DocKnowledgeSearchRequestDTO request) {
        // TODO: 这里需要调用实际的语义检索服务
        List<DocKnowledgeSearchResultDTO.DocFileInfoDTO> result = new ArrayList<>();
        
        // 模拟返回一些文档
        result.add(DocKnowledgeSearchResultDTO.DocFileInfoDTO.builder()
                .fileId(2001L)
                .fileName("语义检索文档1.pdf")
                .fileSn("embedding_doc_001")
                .dataType("KNOWLEDGE")
                .documentTag("语义检索")
                .documentSummary("这是一个语义检索的测试文档")
                .score(0.95)
                .createTime(System.currentTimeMillis())
                .build());
        
        return result;
    }

    /**
     * 模拟关键词检索
     */
    private List<DocKnowledgeSearchResultDTO.DocFileInfoDTO> mockKeywordSearch(DocKnowledgeSearchRequestDTO request) {
        // TODO: 这里需要调用实际的关键词检索服务
        List<DocKnowledgeSearchResultDTO.DocFileInfoDTO> result = new ArrayList<>();

        // 模拟返回一些文档
        result.add(DocKnowledgeSearchResultDTO.DocFileInfoDTO.builder()
                .fileId(3001L)
                .fileName("关键词检索文档1.pdf")
                .fileSn("keyword_doc_001")
                .dataType("KNOWLEDGE")
                .documentTag("关键词检索")
                .documentSummary("这是一个关键词检索的测试文档")
                .createTime(System.currentTimeMillis())
                .build());

        return result;
    }
}
