package com.ai.application.agent.run.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ai.application.agent.run.dto.*;
import com.ai.application.agent.run.enums.DocKnowledgeSearchEnum;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.service.IDocKnowledgeSearchService;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 文档知识检索服务实现
 */
@Slf4j
public class DocKnowledgeSearchServiceImpl implements IDocKnowledgeSearchService {

    @Override
    public ResultVo<DocKnowledgeSearchResultDTO> executeDocKnowledgeSearch(DocKnowledgeSearchRequestDTO request, String authorization) {
        log.info("DocKnowledgeSearchService executeDocKnowledgeSearch start, request: {}", JsonUtils.toJsonString(request));

        try {
            // 参数校验
            validateDocKnowledgeSearchRequest(request);

            Map<String, Object> result;
            String type = request.getType();

            // 设置默认topK值
            String topK = StringUtils.isBlank(request.getTopK()) ? "999" : request.getTopK();

            // 根据backend2的DocKnowledgeSearchExecutor逻辑
            if (DocKnowledgeSearchEnum.DocKnowledgeSearchTypeEnum.RULE.getType().equals(type)) {
                result = docKnowledgeSearchByRule(request.getConditions(), request.getKnowledgeInventorySn(),
                        request.getKnowledgeInventorySnList(), request.getLogic(), null, topK);
            } else if (DocKnowledgeSearchEnum.DocKnowledgeSearchTypeEnum.EMBEDDING.getType().equals(type)) {
                result = docKnowledgeSearchByEmbedding(request.getConditions(), request.getKnowledgeInventorySn(),
                        request.getKnowledgeInventorySnList(), request.getSearchContent(), request.getSearchKnowledgeContent(), topK);
            } else if (DocKnowledgeSearchEnum.DocKnowledgeSearchTypeEnum.KEYWORD.getType().equals(type)) {
                result = docKnowledgeSearchByKeyword(request.getConditions(), request.getKnowledgeInventorySn(),
                        request.getSearchContent(), request.getSearchKnowledgeContent(), topK);
            } else {
                throw new ServiceException(ExecutorError.DOC_KNOWLEDGE_SEARCH_TYPE_ERROR.getCode(), type);
            }

            // 转换结果格式
            DocKnowledgeSearchResultDTO searchResult = convertToResultDTO(result);

            log.info("DocKnowledgeSearchService executeDocKnowledgeSearch success, result: {}", JsonUtils.toJsonString(searchResult));
            return ResultVo.data(searchResult);

        } catch (Exception e) {
            log.error("DocKnowledgeSearchService executeDocKnowledgeSearch error", e);
            return ResultVo.fail(e.getMessage());
        }
    }

    @Override
    public ResultVo<List<DocKnowledgeSearchResultDTO.DocFileInfoDTO>> ruleSearch(DocKnowledgeSearchRequestDTO request,
                                                                                 Map<String, Object> variables,
                                                                                 String authorization) {
        // 这个方法保留接口兼容性，实际逻辑在docKnowledgeSearchByRule中
        log.info("DocKnowledgeSearchService ruleSearch start");
        // TODO: 如果需要可以调用docKnowledgeSearchByRule并转换结果
        return ResultVo.data(Collections.emptyList());
    }

    @Override
    public ResultVo<List<DocKnowledgeSearchResultDTO.DocFileInfoDTO>> embeddingSearch(DocKnowledgeSearchRequestDTO request,
                                                                                      String authorization) {
        // 这个方法保留接口兼容性，实际逻辑在docKnowledgeSearchByEmbedding中
        log.info("DocKnowledgeSearchService embeddingSearch start");
        // TODO: 如果需要可以调用docKnowledgeSearchByEmbedding并转换结果
        return ResultVo.data(Collections.emptyList());
    }

    @Override
    public ResultVo<List<DocKnowledgeSearchResultDTO.DocFileInfoDTO>> keywordSearch(DocKnowledgeSearchRequestDTO request,
                                                                                    String authorization) {
        // 这个方法保留接口兼容性，实际逻辑在docKnowledgeSearchByKeyword中
        log.info("DocKnowledgeSearchService keywordSearch start");
        // TODO: 如果需要可以调用docKnowledgeSearchByKeyword并转换结果
        return ResultVo.data(Collections.emptyList());
    }

    @Override
    public String getModelSnByInventorySn(String knowledgeInventorySn) {
        log.info("DocKnowledgeSearchService getModelSnByInventorySn start, knowledgeInventorySn: {}", knowledgeInventorySn);

        try {
            // TODO: 实现获取知识库模型编号的逻辑
            // 这里需要根据实际的服务接口进行实现
            String modelSn = "localModel"; // 模拟返回
            
            log.info("DocKnowledgeSearchService getModelSnByInventorySn success, modelSn: {}", modelSn);
            return modelSn;

        } catch (Exception e) {
            log.error("DocKnowledgeSearchService getModelSnByInventorySn error", e);
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "获取模型编号失败: " + e.getMessage());
        }
    }

    @Override
    public String parseVariableValue(String value, Map<String, Object> variables) {
        if (StringUtils.isBlank(value) || variables == null) {
            return value;
        }

        String regex = "\\{\\{([^}]+)}}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(value);
        
        while (matcher.find()) {
            String variableName = matcher.group(1);
            Object variableValue = variables.get(variableName);
            if (variableValue != null) {
                return variableValue.toString();
            }
        }
        
        return value;
    }

    /**
     * 校验文档知识检索请求
     */
    private void validateDocKnowledgeSearchRequest(DocKnowledgeSearchRequestDTO request) {
        if (StringUtils.isBlank(request.getKnowledgeInventorySn()) && CollectionUtils.isEmpty(request.getKnowledgeInventorySnList())) {
            throw new ServiceException(ExecutorError.KNOWLEDGE_INVENTORY_SN_IS_NULL);
        }
        
        String type = request.getType();
        if (StringUtils.isBlank(type)) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "检索类型不能为空");
        }

        // 根据检索类型校验相应参数
        if ("embedding".equals(type) || "keywords".equals(type)) {
            if (StringUtils.isBlank(request.getSearchKnowledgeContent())) {
                throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "检索内容不能为空");
            }
        } else if ("rule".equals(type)) {
            if (request.getConditions() == null) {
                throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "规则检索条件不能为空");
            }
        }
    }



    /**
     * 关键词检索 - 基于backend2逻辑
     */
    private Map<String, Object> docKnowledgeSearchByKeyword(Object conditions, String knowledgeInventorySn,
                                                           String searchContent, String searchKnowledgeContent, String topK) {
        // 校验检索内容不能为空
        if (StringUtils.isBlank(searchKnowledgeContent)) {
            throw new ServiceException(ExecutorError.DOC_KNOWLEDGE_SEARCH_CONTENT_IS_BLANK);
        }

        List<DocSearchByKeywordDTO.VocabularyWordDTO> list;
        try {
            list = JSONUtil.toList(searchKnowledgeContent, DocSearchByKeywordDTO.VocabularyWordDTO.class);
        } catch (Exception e) {
            throw new ServiceException(ExecutorError.DOC_KNOWLEDGE_SEARCH_CONTENT_IS_NOT_JSON);
        }

        DocSearchByKeywordDTO dto = new DocSearchByKeywordDTO(list, List.of(knowledgeInventorySn), Integer.parseInt(topK),
                DocKnowledgeSearchEnum.getKeywordMappingByField(searchContent).getMapping());

        // TODO: 调用实际的关键词检索服务
        // ResultVo<List<DocFileDTO>> resultVo = agentDocClient.searchByKeyword(dto);
        ResultVo<List<DocFileDTO>> resultVo = mockSearchByKeyword(dto);

        return resultMap(conditions, resultVo);
    }

    /**
     * 语义检索 - 基于backend2逻辑
     */
    private Map<String, Object> docKnowledgeSearchByEmbedding(Object conditions, String knowledgeInventorySn,
                                                             List<String> knowledgeInventoryList, String searchContent,
                                                             String searchKnowledgeContent, String topK) {
        // 校验检索内容不能为空
        if (StringUtils.isBlank(searchKnowledgeContent)) {
            throw new ServiceException(ExecutorError.DOC_KNOWLEDGE_SEARCH_CONTENT_IS_BLANK);
        }

        String modelSn;
        DocumentRetrieverDTO dto = new DocumentRetrieverDTO();
        dto.setTopK(Integer.parseInt(topK));

        if (knowledgeInventorySn == null) {
            modelSn = getModelSnByInventorySn(knowledgeInventoryList.get(0));
            dto.setDatasetIds(knowledgeInventoryList);
        } else {
            modelSn = getModelSnByInventorySn(knowledgeInventorySn);
            dto.setDatasetIds(List.of(knowledgeInventorySn));
        }

        dto.setRetrieveMode(DocKnowledgeSearchEnum.getEmbeddingMappingByField(searchContent).getMapping());
        dto.setOriginQuery(searchKnowledgeContent);

        if ("localModel".equals(modelSn) || "embedding::localModel".equals(modelSn)) {
            dto.setModelSn("embedding::localModel");
        } else {
            dto.setModelSn("embedding::defaultModel");
        }

        // TODO: 调用实际的语义检索服务
        // ResultVo<List<DocFileDTO>> resultVo = agentDocClient.documentRetrieverDto(dto);
        ResultVo<List<DocFileDTO>> resultVo = mockDocumentRetriever(dto);

        if (resultVo.getCode() != 0) {
            throw new ServiceException(resultVo.getCode(), resultVo.getMessage());
        }

        return resultMap(conditions, resultVo);
    }

    /**
     * 规则检索 - 基于backend2逻辑
     */
    private Map<String, Object> docKnowledgeSearchByRule(Object conditions, String knowledgeInventorySn,
                                                        List<String> knowledgeInventoryList, String logic,
                                                        Map<String, Object> variables, String topK) {
        JSONArray object = JSONUtil.parseArray(conditions);
        // 获取第一个目标 true
        Object condition = object.get(0);
        Boolean isTrue = false;
        String target = "";
        List<String> docFiles = List.of();

        if (condition instanceof JSONObject jsonObject) {
            Map<String, String> collect = variables != null ?
                    variables.entrySet().stream().collect(Collectors.toMap(
                            Map.Entry::getKey,
                            e -> String.valueOf(e.getValue()),
                            (e1, e2) -> e1)) : new HashMap<>();

            target = jsonObject.getStr(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.TARGET.getField());

            // 表达式是否为空
            boolean isEmptyExpression = jsonObject.containsKey(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.EXPRESSION.getField())
                    && jsonObject.getStr(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.EXPRESSION.getField()).isEmpty();

            if (isEmptyExpression) {
                throw new ServiceException(ExecutorError.DOC_KNOWLEDGE_SEARCH_EXPRESSION_IS_BLANK);
            }

            List<ExpressionDTO.Expression> expressions = jsonObject.getBeanList(
                    DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.EXPRESSION.getField(),
                    ExpressionDTO.Expression.class);

            expressions.forEach(e -> {
                e.setField(DocKnowledgeSearchEnum.getMappingByField(e.getField()).getMapping());
                String value = collect.get(getValueByVariables(e.getValue()));
                if (StringUtils.isNotBlank(value)) {
                    e.setValue(value);
                }
            });

            ExpressionDTO vo = new ExpressionDTO(knowledgeInventorySn, knowledgeInventoryList, expressions, logic, Integer.valueOf(topK));

            // TODO: 调用实际的规则检索服务
            // ResultVo<List<DocFileDTO>> resultVo = agentDocClient.ruleRetrieverDto(vo);
            ResultVo<List<DocFileDTO>> resultVo = mockRuleRetriever(vo);

            if (resultVo.getCode() != 0) {
                throw new ServiceException(resultVo.getCode(), resultVo.getMessage());
            }

            List<DocFileDTO> files = resultVo.getData();
            docFiles = files.stream().map(fileInfo -> String.format("%s::%s::%s",
                    fileInfo.getFileId(), fileInfo.getFileName(), "KNOWLEDGE")).collect(Collectors.toList());
            isTrue = !docFiles.isEmpty();
        }

        if (!isTrue) {
            // 获取文档为空时 走未获取文档的输出
            Object nDoc = object.get(1);
            if (nDoc instanceof JSONObject jsonObject) {
                target = jsonObject.getStr(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.TARGET.getField());
            }
        }

        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isNotBlank(target)) {
            result.put(DocKnowledgeSearchEnum.DocKnowledgeSearchOutputFieldEnum.TARGET.getField(), target);
        }
        result.put(DocKnowledgeSearchEnum.DocKnowledgeSearchOutputFieldEnum.DOC_FILE.getField(), docFiles);
        result.put(DocKnowledgeSearchEnum.DocKnowledgeSearchOutputFieldEnum.MATCH_RESULT.getField(), isTrue ? "True" : "False");

        return result;
    }

    /**
     * 结果映射 - 基于backend2逻辑
     */
    private Map<String, Object> resultMap(Object conditions, ResultVo<List<DocFileDTO>> resultVo) {
        Map<String, Object> result = new HashMap<>();
        JSONArray object = JSONUtil.parseArray(conditions);

        if (resultVo.getData() != null && resultVo.getData().isEmpty()) {
            Object o = object.get(1);
            if (o instanceof JSONObject jsonObject) {
                String target = jsonObject.getStr(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.TARGET.getField());
                if (StringUtils.isNotBlank(target)) {
                    result.put(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.TARGET.getField(), target);
                }
                result.put(DocKnowledgeSearchEnum.DocKnowledgeSearchOutputFieldEnum.DOC_FILE.getField(), List.of());
                result.put(DocKnowledgeSearchEnum.DocKnowledgeSearchOutputFieldEnum.MATCH_RESULT.getField(), "False");
                return result;
            }
        }

        Object o = object.get(0);
        if (o instanceof JSONObject jsonObject) {
            String target = jsonObject.getStr(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.TARGET.getField());
            if (StringUtils.isNotBlank(target)) {
                result.put(DocKnowledgeSearchEnum.DocKnowledgeSearchInputFieldEnum.TARGET.getField(), target);
            }
            result.put(DocKnowledgeSearchEnum.DocKnowledgeSearchOutputFieldEnum.DOC_FILE.getField(),
                    resultVo.getData().stream().map(fileInfo -> String.format("%s::%s::%s",
                            fileInfo.getFileId(), fileInfo.getFileName(), "KNOWLEDGE")).collect(Collectors.toList()));
            result.put(DocKnowledgeSearchEnum.DocKnowledgeSearchOutputFieldEnum.MATCH_RESULT.getField(), "True");
            return result;
        }

        return null;
    }

    /**
     * 从变量中获取值 - 基于backend2逻辑
     */
    private String getValueByVariables(String value) {
        String regex = "\\{\\{([^}]+)}}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(value);
        while (matcher.find()) {
            return matcher.group(1);
        }
        return value;
    }

    /**
     * 转换结果格式
     */
    private DocKnowledgeSearchResultDTO convertToResultDTO(Map<String, Object> result) {
        if (result == null) {
            return DocKnowledgeSearchResultDTO.builder()
                    .success(false)
                    .errorMessage("检索结果为空")
                    .build();
        }

        List<String> docFileList = (List<String>) result.get(DocKnowledgeSearchEnum.DocKnowledgeSearchOutputFieldEnum.DOC_FILE.getField());
        String target = (String) result.get(DocKnowledgeSearchEnum.DocKnowledgeSearchOutputFieldEnum.TARGET.getField());
        String matchResult = (String) result.get(DocKnowledgeSearchEnum.DocKnowledgeSearchOutputFieldEnum.MATCH_RESULT.getField());

        return DocKnowledgeSearchResultDTO.builder()
                .docFile(docFileList != null ? docFileList : Collections.emptyList())
                .target(target)
                .matchResult(matchResult != null ? matchResult : "False")
                .success(true)
                .totalCount(docFileList != null ? docFileList.size() : 0)
                .build();
    }

    // ==================== 模拟方法 ====================

    /**
     * 模拟关键词检索
     */
    private ResultVo<List<DocFileDTO>> mockSearchByKeyword(DocSearchByKeywordDTO dto) {
        // TODO: 这里需要调用实际的关键词检索服务
        List<DocFileDTO> result = new ArrayList<>();
        result.add(new DocFileDTO(3001L, "关键词检索文档1.pdf", "keyword_doc_001", "KNOWLEDGE",
                "关键词检索", "这是一个关键词检索的测试文档", ".pdf", 0.9, System.currentTimeMillis()));
        return ResultVo.data(result);
    }

    /**
     * 模拟语义检索
     */
    private ResultVo<List<DocFileDTO>> mockDocumentRetriever(DocumentRetrieverDTO dto) {
        // TODO: 这里需要调用实际的语义检索服务
        List<DocFileDTO> result = new ArrayList<>();
        result.add(new DocFileDTO(2001L, "语义检索文档1.pdf", "embedding_doc_001", "KNOWLEDGE",
                "语义检索", "这是一个语义检索的测试文档", ".pdf", 0.95, System.currentTimeMillis()));
        return ResultVo.data(result);
    }

    /**
     * 模拟规则检索
     */
    private ResultVo<List<DocFileDTO>> mockRuleRetriever(ExpressionDTO dto) {
        // TODO: 这里需要调用实际的规则检索服务
        List<DocFileDTO> result = new ArrayList<>();
        result.add(new DocFileDTO(1001L, "规则检索文档1.pdf", "rule_doc_001", "KNOWLEDGE",
                "规则检索", "这是一个规则检索的测试文档", ".pdf", null, System.currentTimeMillis()));
        return ResultVo.data(result);
    }
}
