package com.ai.framework.workflow.excutor;

import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import com.googlecode.aviator.AviatorEvaluator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.util.Map;

@Slf4j
@Component
public class ConditionNodeExecutor implements NodeExecutor {
    @Override
    public void execute(WorkflowContext context) {
        // 当前节点的KEY
        String nodeKey = context.getCurrentNodeKey();
        // 当前节点的信息
        NodeContext nodeCtx = context.getNodeContexts().get(nodeKey);
        // 当前节点自定义的参数
        Map<String, Object> nodeDef = nodeCtx.getNodeDefinition();
        log.info("nodeExecutor nodeDef:{}", JsonUtils.toJsonString(nodeDef));
        @SuppressWarnings("unchecked")
        Map<String, String> nextMap = (Map<String, String>) nodeDef.get("next");

        // 传递全局变量给表达式引擎
        Map<String, Object> env = context.getGlobalVars();

        Map<String, Object> input = nodeCtx.getInput();

        log.info("env:{},{}", JsonUtils.toJsonString(env), JsonUtils.toBigJsonString(input));

        boolean matched = false;
        for (Map.Entry<String, String> entry : nextMap.entrySet()) {
            String expr = entry.getKey(); // SPL表达式
            Boolean result = (Boolean) AviatorEvaluator.execute(expr, env, true);
            if (Boolean.TRUE.equals(result)) {
                context.setNextNodeKey(entry.getValue());
                matched = true;
                break;
            }
        }
        if (!matched) {
            throw new RuntimeException("No SPL condition matched in node: " + nodeKey);
        }
        nodeCtx.setStatus(NodeStatus.SUCCESS);
        nodeCtx.setEndTime(java.time.LocalDateTime.now());
    }

    @Override
    public String getType() { return "condition"; }
}
