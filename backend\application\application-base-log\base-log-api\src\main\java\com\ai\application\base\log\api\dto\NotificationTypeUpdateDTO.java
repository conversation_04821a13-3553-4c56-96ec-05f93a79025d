package com.ai.application.base.log.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 通知类型配置表
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@Schema(name = "通知类型配置表-添加DTO")
public class NotificationTypeUpdateDTO {
    /**
     * 通知类型id
     */
    @Schema(description = "通知类型id")
    private Integer ntypeId;

    /**
     * 通知类型:10-邮件,20-短信,30-钉钉,40-企微,50-飞书,60-Slack,70-Webhook,80-站内消息
     */
    @Schema(description = "通知类型:10-邮件,20-短信,30-钉钉,40-企微,50-飞书,60-Slack,70-Webhook,80-站内消息")
    @NotNull(message = "通知类型不能为空")
    private Integer ntypeType;

    /**
     * 类型配置名称
     */
    @Schema(description = "类型配置名称")
    @NotBlank(message = "类型配置名称不能为空")
    @Length(max = 20, message = "类型配置名称长度不能超过20")
    private String ntypeName;

    /**
     * 类型配置参数:渠道配置,重试配置等
     */
    @Schema(description = "类型配置参数:渠道配置,重试配置等-对象")
    @Valid
    private MailConfigDTO mailConfig;
}