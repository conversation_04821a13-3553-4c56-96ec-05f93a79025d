package com.ai.application.app.mapper;

import com.ai.application.app.api.entity.AppUser;
import com.ai.application.app.api.vo.AppUserVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 应用用户表 Mapper接口
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Mapper
public interface AppUserMapper extends BaseMapper<AppUser> {
    /**
     * 查询应用用户表
     *
     * @return
     */
    List<AppUserVO> selectAppUserList();

    @Select("select * from app_user where user_sn = #{userSn} and user_status >= 0")
    AppUser findByUserSn(@Param("userSn") String userSn);

    @Select("select * from app_user where app_id=#{appId} and tenant_id=#{tenantId} and user_account = #{userAccount} and user_status >= 0 limit 1")
    AppUser getByUserAccount(@Param("appId") Integer appId,@Param("tenantId") Integer tenantId,@Param("userAccount") String userAccount);

    @Select("select * from app_user where app_id=#{appId} and tenant_id=#{tenantId} and user_status >= 0")
    List<AppUser> queryAppUserByTenantId(@Param("appId") Integer appId,@Param("tenantId") Integer tenantId);

    List<AppUserVO> queryAppUserList(@Param("tenantId") Integer tenantId,@Param("userSnList") List<String> userSnList);
}
