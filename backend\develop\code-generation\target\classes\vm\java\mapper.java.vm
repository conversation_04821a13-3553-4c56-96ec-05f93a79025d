package ${package.Mapper};

import ${cfg.entityPackage}.${entity};
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import ${cfg.voPackage}.${entity}VO;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * $!{table.comment}-Mapper接口
 *
 * <AUTHOR>
 * @since ${date}
 */
@Mapper
public interface ${entity}Mapper extends BaseMapper<${entity}> {
    /**
     * 查询$!{table.comment}
     *
     * @return
     */
    List<${entity}VO> select${entity}List();
}
