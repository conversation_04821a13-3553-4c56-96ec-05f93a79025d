package com.ai.application.base.log.api.mapstruct;
import com.ai.application.base.log.api.entity.ErrorLog;
import com.ai.application.base.log.api.dto.ErrorLogDTO;
import com.ai.application.base.log.api.vo.ErrorLogDetailVO;
import com.ai.application.base.log.api.vo.ErrorLogPageVO;
import com.ai.application.base.log.api.vo.ErrorLogVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 错误日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-07
 */

@Mapper(componentModel = "spring")
public interface ErrorLogMapstruct {

    ErrorLog toEntity(ErrorLogDTO dto);
    List<ErrorLog> toEntityList(List<ErrorLogDTO> dtolist);
    ErrorLogVO toVo(ErrorLog entity);
    ErrorLogDetailVO toDetailVo(ErrorLog entity);
    List<ErrorLogVO> toVoList(List<ErrorLog> entities);
    List<ErrorLogPageVO> toVoPageList(List<ErrorLog> entities);
}
