package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.bo.MasterSkillBO;
import com.ai.application.agent.base.api.dto.AgentUseToolAddDTO;
import com.ai.application.agent.base.api.dto.AgentUseToolListDTO;
import com.ai.application.agent.base.api.dto.AgentUseToolUpdateDTO;
import com.ai.application.agent.base.api.dto.MasterAddDTO;
import com.ai.application.agent.base.api.vo.AgentUseToolListVO;
import java.util.List;

public interface IAgentUseToolService {
    /**
     * 列表
     *
     * @param queryDto
     * @return
     */
    List<AgentUseToolListVO> list(AgentUseToolListDTO queryDto);

    /**
     * 保存
     *
     * @param dto
     */
    void add(AgentUseToolAddDTO dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(AgentUseToolUpdateDTO dto);

    List<MasterSkillBO> toDetail(Integer versionId);
}
