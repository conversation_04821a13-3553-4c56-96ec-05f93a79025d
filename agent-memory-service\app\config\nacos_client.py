# -*- coding: utf-8 -*-
"""
Nacos客户端模块

Nacos服务注册与配置管理客户端，提供：
- Nacos 2.0/3.0 SDK兼容性支持
- gRPC和HTTP协议自适应
- 服务注册与发现功能
- 配置发布、获取和监听
- 动态配置更新回调
- 连接管理和故障恢复
- 多版本SDK降级机制

Project: Agent Memory Service
Author: 张小龙 <<EMAIL>>
Created: 2025-06-01
Version: 1.0.0
License: private
"""

import json
import yaml
import asyncio
import os
import socket
from typing import Optional, Callable, Any, Dict
from loguru import logger

# 导入官方SDK，优先尝试v2.0，降级到v1.0
try:
    # 优先使用nacos-sdk-python v2.0 gRPC版本
    # 注意：导入路径可能因包版本而异，这里使用兼容性更好的方式
    try:
        # 方法1：直接从包导入（推荐方式）
        from nacos_sdk_python.v2.nacos import (
            NacosNamingService, 
            NacosConfigService,
            ClientConfigBuilder, 
            GRPCConfig,
            Instance,
            RegisterInstanceParam,
            DeregisterInstanceParam,
            ConfigParam
        )
        NACOS_V2_AVAILABLE = True
        logger.info("成功导入nacos-sdk-python v2.0 (方法1)")
    except ImportError:
        # 方法2：尝试v2目录导入
        from v2.nacos import (
            NacosNamingService, 
            NacosConfigService,
            ClientConfigBuilder, 
            GRPCConfig,
            Instance,
            RegisterInstanceParam,
            DeregisterInstanceParam,
            ConfigParam
        )
        NACOS_V2_AVAILABLE = True
        logger.info("成功导入nacos-sdk-python v2.0 (方法2)")
except ImportError:
    try:
        # 降级到v1.0 HTTP版本
        import nacos
        NACOS_V2_AVAILABLE = False
        logger.warning("Nacos SDK v2.0不可用，降级使用v1.0版本（仅支持HTTP协议）")
    except ImportError:
        # 如果连v1.0都没有，则报告错误
        logger.error("未找到可用的Nacos SDK，请安装 nacos-sdk-python")
        raise ImportError("请安装 nacos-sdk-python: pip install nacos-sdk-python==2.0.5")

from .settings import settings


class NacosManager:
    """Nacos管理器 - 支持Nacos 3.0"""
    
    def __init__(self):
        self.naming_client: Optional[Any] = None
        self.config_client: Optional[Any] = None
        self.config_callback: Optional[Callable] = None
        self._is_connected = False
        self._use_grpc = False
    
    def _get_effective_ip(self) -> str:
        """
        获取有效的IP地址
        优先级：
        1. 如果nacos配置的IP不为空且不是0.0.0.0，则使用配置的IP
        2. 否则依次尝试环境变量：DOCKER_HOST_IP, HOST_IP, SERVER_IP, NACOS_IP
        3. 最后尝试自动获取本机IP
        """
        nacos_config = settings.nacos
        config_ip = nacos_config.ip
        
        # 检查配置的IP是否有效
        if config_ip and config_ip.strip() and config_ip != "0.0.0.0":
            logger.info(f"使用配置的IP地址: {config_ip}")
            return config_ip
        
        # 尝试从环境变量获取IP
        env_vars = ["DOCKER_HOST_IP", "HOST_IP", "SERVER_IP", "NACOS_IP"]
        for env_var in env_vars:
            env_ip = os.getenv(env_var)
            if env_ip and env_ip.strip() and env_ip != "0.0.0.0":
                logger.info(f"使用环境变量 {env_var} 的IP地址: {env_ip}")
                return env_ip
        
        # 尝试自动获取本机IP
        try:
            # 方法1：连接外部地址获取本机IP
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                auto_ip = s.getsockname()[0]
                logger.info(f"自动获取到本机IP地址: {auto_ip}")
                return auto_ip
        except Exception as e:
            logger.warning(f"自动获取IP失败: {e}")
        
        # 备选方案：使用hostname解析
        try:
            auto_ip = socket.gethostbyname(socket.gethostname())
            if auto_ip and auto_ip != "127.0.0.1":
                logger.info(f"通过hostname获取到IP地址: {auto_ip}")
                return auto_ip
        except Exception as e:
            logger.warning(f"通过hostname获取IP失败: {e}")
        
        # 最后的备选方案
        fallback_ip = "127.0.0.1"
        logger.warning(f"无法获取有效IP，使用备选地址: {fallback_ip}")
        return fallback_ip

    async def init_nacos(self):
        """初始化Nacos客户端"""
        try:
            nacos_config = settings.nacos
            
            if NACOS_V2_AVAILABLE:
                # 使用v2.0 API (支持gRPC)
                await self._init_nacos_v2()
            else:
                # 使用v1.0 API (HTTP only)
                await self._init_nacos_v1()
            
            self._is_connected = True
            logger.info(f"Nacos客户端初始化成功 (协议: {'gRPC' if self._use_grpc else 'HTTP'})")
            
            # 注册服务
            await self.register_service()
            
            # 监听配置变化
            await self.start_config_listener()
            
        except Exception as e:
            logger.error(f"Nacos客户端初始化失败: {e}")
            self._is_connected = False
    
    async def _init_nacos_v2(self):
        """初始化Nacos v2.0客户端（支持gRPC和Nacos 3.0）"""
        nacos_config = settings.nacos
        
        # 构建客户端配置
        client_config_builder = ClientConfigBuilder()
        client_config_builder.server_address(nacos_config.server_addresses)
        
        if nacos_config.namespace:
            client_config_builder.namespace_id(nacos_config.namespace)
        
        if nacos_config.username and nacos_config.password:
            client_config_builder.username(nacos_config.username)
            client_config_builder.password(nacos_config.password)
        
        # 配置gRPC (推荐用于Nacos 3.0)
        grpc_config = GRPCConfig(
            grpc_timeout=5000,  # 5秒超时
            max_receive_message_length=100 * 1024 * 1024,  # 100MB
            max_keep_alive_ms=60 * 1000,  # 60秒
            initial_window_size=10 * 1024 * 1024,  # 10MB
            initial_conn_window_size=10 * 1024 * 1024  # 10MB
        )
        client_config_builder.grpc_config(grpc_config)
        client_config_builder.log_level('INFO')
        
        client_config = client_config_builder.build()
        
        # 创建服务发现客户端
        self.naming_client = await NacosNamingService.create_naming_service(client_config)
        
        # 创建配置管理客户端
        self.config_client = await NacosConfigService.create_config_service(client_config)
        
        self._use_grpc = True
        
        # 测试连接
        await self._test_connection_v2()
    
    async def _init_nacos_v1(self):
        """初始化Nacos v1.0客户端（HTTP协议）"""
        nacos_config = settings.nacos
        
        self.naming_client = nacos.NacosClient(
            server_addresses=nacos_config.server_addresses,
            namespace=nacos_config.namespace,
            username=nacos_config.username if nacos_config.username else None,
            password=nacos_config.password if nacos_config.password else None
        )
        
        self.config_client = self.naming_client
        self._use_grpc = False
        
        # 测试连接
        await self._test_connection_v1()
    
    async def _test_connection_v2(self):
        """测试Nacos v2.0连接"""
        if not self.config_client:
            raise Exception("Nacos v2.0客户端未初始化")
        
        try:
            # 尝试获取配置来测试连接
            await self.config_client.get_config(ConfigParam(
                data_id=settings.nacos.data_id,
                group=settings.nacos.group
            ))
        except Exception as e:
            logger.warning(f"测试连接时获取配置失败，可能是配置不存在: {e}")
    
    async def _test_connection_v1(self):
        """测试Nacos v1.0连接"""
        if not self.config_client:
            raise Exception("Nacos v1.0客户端未初始化")
        
        try:
            # 尝试获取配置来测试连接
            self.config_client.get_config(
                data_id=settings.nacos.data_id,
                group=settings.nacos.group,
                timeout=5
            )
        except Exception as e:
            logger.warning(f"测试连接时获取配置失败，可能是配置不存在: {e}")
    
    async def register_service(self):
        """注册服务到Nacos"""
        if not self.naming_client or not self._is_connected:
            logger.warning("Nacos客户端未连接，跳过服务注册")
            return
        
        try:
            nacos_config = settings.nacos
            app_config = settings.app
            
            # 构建服务元数据
            metadata = {
                "version": app_config.version,
                "framework": "fastapi",
                "protocol": "http"
            }
            
            if NACOS_V2_AVAILABLE and self._use_grpc:
                # 使用v2.0 gRPC API
                await self._register_service_v2(metadata)
            else:
                # 使用v1.0 HTTP API
                await self._register_service_v1(metadata)
                
        except Exception as e:
            logger.error(f"服务注册异常: {e}")
    
    async def _register_service_v2(self, metadata: Dict[str, str]):
        """使用v2.0 API注册服务"""
        nacos_config = settings.nacos
        
        # 获取有效的IP地址
        effective_ip = self._get_effective_ip()
        
        register_param = RegisterInstanceParam(
            service_name=nacos_config.service_name,
            group_name=nacos_config.group,
            ip=effective_ip,
            port=nacos_config.port,
            cluster_name=nacos_config.cluster_name,
            weight=nacos_config.weight,
            metadata=metadata,
            enabled=True,
            healthy=True,
            ephemeral=True
        )
        
        response = await self.naming_client.register_instance(request=register_param)
        
        if response:
            logger.info(f"服务注册成功 (gRPC): {nacos_config.service_name}@{effective_ip}:{nacos_config.port}")
        else:
            logger.error("服务注册失败 (gRPC)")
    
    async def _register_service_v1(self, metadata: Dict[str, str]):
        """使用v1.0 API注册服务"""
        nacos_config = settings.nacos
        
        # 获取有效的IP地址
        effective_ip = self._get_effective_ip()
        
        success = self.naming_client.add_naming_instance(
            service_name=nacos_config.service_name,
            ip=effective_ip,
            port=nacos_config.port,
            cluster_name=nacos_config.cluster_name,
            weight=nacos_config.weight,
            metadata=metadata,
            enable=True,
            healthy=True
        )
        
        if success:
            logger.info(f"服务注册成功 (HTTP): {nacos_config.service_name}@{effective_ip}:{nacos_config.port}")
        else:
            logger.error("服务注册失败 (HTTP)")
    
    async def deregister_service(self):
        """注销服务"""
        if not self.naming_client or not self._is_connected:
            return
        
        try:
            nacos_config = settings.nacos
            
            # 获取有效的IP地址（保持与注册时一致）
            effective_ip = self._get_effective_ip()
            
            if NACOS_V2_AVAILABLE and self._use_grpc:
                # 使用v2.0 gRPC API
                deregister_param = DeregisterInstanceParam(
                    service_name=nacos_config.service_name,
                    group_name=nacos_config.group,
                    ip=effective_ip,
                    port=nacos_config.port,
                    cluster_name=nacos_config.cluster_name,
                    ephemeral=True
                )
                
                response = await self.naming_client.deregister_instance(request=deregister_param)
                
                if response:
                    logger.info(f"服务注销成功 (gRPC): {nacos_config.service_name}")
                else:
                    logger.error("服务注销失败 (gRPC)")
            else:
                # 使用v1.0 HTTP API
                success = self.naming_client.remove_naming_instance(
                    service_name=nacos_config.service_name,
                    ip=effective_ip,
                    port=nacos_config.port,
                    cluster_name=nacos_config.cluster_name
                )
                
                if success:
                    logger.info(f"服务注销成功 (HTTP): {nacos_config.service_name}")
                else:
                    logger.error("服务注销失败 (HTTP)")
                    
        except Exception as e:
            logger.error(f"服务注销异常: {e}")
    
    async def start_config_listener(self):
        """启动配置监听"""
        if not self.config_client or not self._is_connected:
            logger.warning("Nacos客户端未连接，跳过配置监听")
            return
        
        try:
            nacos_config = settings.nacos
            
            if NACOS_V2_AVAILABLE and self._use_grpc:
                # 使用v2.0 gRPC API
                await self._start_config_listener_v2()
            else:
                # 使用v1.0 HTTP API
                await self._start_config_listener_v1()
            
            logger.info(f"开始监听配置变化: {nacos_config.data_id}")
            
        except Exception as e:
            logger.error(f"配置监听启动失败: {e}")
    
    async def _start_config_listener_v2(self):
        """使用v2.0 API启动配置监听"""
        nacos_config = settings.nacos
        
        async def config_callback(tenant, data_id, group, content):
            try:
                logger.info("收到Nacos配置更新 (gRPC)")
                if content:
                    # 尝试解析为YAML格式
                    try:
                        config_dict = yaml.safe_load(content)
                    except yaml.YAMLError:
                        # 如果不是YAML，尝试JSON
                        config_dict = json.loads(content)
                    
                    # 更新本地配置
                    settings.update_config(config_dict)
                    logger.info("配置更新完成 (gRPC)")
                
            except Exception as e:
                logger.error(f"处理配置更新失败 (gRPC): {e}")
        
        # 添加配置监听器
        await self.config_client.add_listener(
            nacos_config.data_id,
            nacos_config.group,
            config_callback
        )
    
    async def _start_config_listener_v1(self):
        """使用v1.0 API启动配置监听"""
        nacos_config = settings.nacos
        
        def config_callback(config_data):
            try:
                logger.info("收到Nacos配置更新 (HTTP)")
                if config_data:
                    # 尝试解析为YAML格式
                    try:
                        config_dict = yaml.safe_load(config_data)
                    except yaml.YAMLError:
                        # 如果不是YAML，尝试JSON
                        config_dict = json.loads(config_data)
                    
                    # 更新本地配置
                    settings.update_config(config_dict)
                    logger.info("配置更新完成 (HTTP)")
                
            except Exception as e:
                logger.error(f"处理配置更新失败 (HTTP): {e}")
        
        # 添加配置监听器
        self.config_client.add_config_watcher(
            data_id=nacos_config.data_id,
            group=nacos_config.group,
            cb=config_callback
        )
    
    async def publish_config(self, config_data: Dict[str, Any]):
        """发布配置到Nacos"""
        if not self.config_client or not self._is_connected:
            logger.warning("Nacos客户端未连接，无法发布配置")
            return False
        
        try:
            nacos_config = settings.nacos
            
            # 将配置转换为YAML格式
            config_content = yaml.safe_dump(config_data, allow_unicode=True)
            
            if NACOS_V2_AVAILABLE and self._use_grpc:
                # 使用v2.0 gRPC API
                success = await self.config_client.publish_config(ConfigParam(
                    data_id=nacos_config.data_id,
                    group=nacos_config.group,
                    content=config_content
                ))
            else:
                # 使用v1.0 HTTP API
                success = self.config_client.publish_config(
                    data_id=nacos_config.data_id,
                    group=nacos_config.group,
                    content=config_content
                )
            
            if success:
                logger.info(f"配置发布成功 ({'gRPC' if self._use_grpc else 'HTTP'})")
            else:
                logger.error(f"配置发布失败 ({'gRPC' if self._use_grpc else 'HTTP'})")
            
            return success
            
        except Exception as e:
            logger.error(f"配置发布异常: {e}")
            return False
    
    async def get_config(self) -> Optional[Dict[str, Any]]:
        """获取远程配置"""
        if not self.config_client or not self._is_connected:
            logger.warning("Nacos客户端未连接，无法获取配置")
            return None
        
        try:
            nacos_config = settings.nacos
            
            if NACOS_V2_AVAILABLE and self._use_grpc:
                # 使用v2.0 gRPC API
                content = await self.config_client.get_config(ConfigParam(
                    data_id=nacos_config.data_id,
                    group=nacos_config.group
                ))
            else:
                # 使用v1.0 HTTP API
                content = self.config_client.get_config(
                    data_id=nacos_config.data_id,
                    group=nacos_config.group,
                    timeout=5
                )
            
            if content:
                # 尝试解析配置
                try:
                    return yaml.safe_load(content)
                except yaml.YAMLError:
                    return json.loads(content)
            
            return None
            
        except Exception as e:
            logger.error(f"获取配置异常: {e}")
            return None
    
    @property
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self._is_connected
    
    @property
    def protocol_info(self) -> str:
        """获取协议信息"""
        return f"{'gRPC' if self._use_grpc else 'HTTP'} (SDK v{'2.0' if NACOS_V2_AVAILABLE else '1.0'})"
    
    async def close(self):
        """关闭Nacos连接"""
        try:
            # 注销服务
            await self.deregister_service()
            
            # 关闭客户端连接
            if NACOS_V2_AVAILABLE and self._use_grpc:
                if self.config_client:
                    await self.config_client.shutdown()
                if self.naming_client:
                    # naming_client 通常不需要显式关闭，gRPC会自动管理连接
                    pass
            
            self._is_connected = False
            logger.info("Nacos连接已关闭")
            
        except Exception as e:
            logger.error(f"关闭Nacos连接异常: {e}")


# 全局Nacos管理器实例
nacos_manager = NacosManager() 