package com.ai.application.task.service.impl;

import com.ai.application.agent.base.api.entity.Agent;
import com.ai.application.agent.base.api.entity.AgentVersion;
import com.ai.application.agent.base.api.enums.AgentTypeEnum;
import com.ai.application.base.file.api.feign.IFileFeignClient;
import com.ai.application.task.api.bo.TaskConfigBO;
import com.ai.application.task.api.dto.TaskAddDTO;
import com.ai.application.task.api.dto.TaskRunResultDTO;
import com.ai.application.task.api.entity.TaskAttachment;
import com.ai.application.task.api.entity.TaskRun;
import com.ai.application.task.api.enums.*;
import com.ai.application.task.api.vo.TaskDetailVO;
import com.ai.application.task.mapper.*;
import com.ai.application.task.util.TaskTemplateUtils;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.util.validator.AssertUtil;
import com.ai.framework.core.vo.ResultVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ai.application.task.api.entity.Task;
import com.ai.application.task.service.ITaskService;
import com.ai.application.task.api.dto.TaskDTO;
import com.ai.application.task.api.dto.query.TaskQueryDTO;
import com.ai.application.task.api.vo.TaskVO;
import com.ai.application.task.api.mapstruct.TaskMapstruct;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.*;

/**
 * 计划任务表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Service
@Slf4j
public class TaskServiceImpl implements ITaskService{

    @Resource
    private TaskMapper taskMapper;

    @Resource
    private TaskMapstruct taskMapstruct;
    @Resource
    private AgentVersionMapper agentVersionMapper;
    @Resource
    private AgentMapper agentMapper;
    @Resource
    private TaskRunMapper taskRunMapper;
    @Resource
    private TaskAttachmentMapper taskAttachmentMapper;
    @Resource
    private IFileFeignClient fileFeignClient;

    @Transactional(readOnly = true)
    @Override
    public PageInfo<TaskVO> page(TaskQueryDTO queryDto) {
        QueryWrapper<Task> queryWrapper = this.buildQuery(queryDto);
        Page<Task> page = new Page<>(queryDto.getPageNo(), queryDto.getPageSize());
        Page<Task> result = this.taskMapper.selectPage(page, queryWrapper);
        List<Task> records = result.getRecords();
        if(CollectionUtils.isEmpty(records)){
            return PageInfo.of(new ArrayList<>());
        }

        List<TaskVO> retList = taskMapstruct.toVoList(records);
        //智能体查询
        List<Integer> agentIds = records.stream().map(Task::getAgentId).toList();
        LambdaQueryWrapper<Agent> queryAgentWrapper = new LambdaQueryWrapper<>();
        queryAgentWrapper.in(Agent::getAgentId,agentIds);
        List<Agent> agents = agentMapper.selectList(queryAgentWrapper);

        //智能体版本查询
        List<Integer> versionIdIds = records.stream().map(Task::getVersionId).toList();
        LambdaQueryWrapper<AgentVersion> queryVersionWrapper = new LambdaQueryWrapper<>();
        queryVersionWrapper.in(AgentVersion::getVersionId,versionIdIds);
        List<AgentVersion> versions = agentVersionMapper.selectList(queryVersionWrapper);

        retList.forEach(taskVO -> {
            taskVO.setAgentName(agents.stream().filter(agent -> agent.getAgentId().equals(taskVO.getAgentId())).findFirst().get().getAgentName());
            taskVO.setVersionNum(versions.stream().filter(version -> version.getVersionId().equals(taskVO.getVersionId())).findFirst().get().getVersionNumber());
        });
        return PageInfo.of(retList);
    }

    @Transactional(readOnly = true)
    @Override
    public List<TaskVO> list(TaskQueryDTO queryDto) {
        QueryWrapper<Task> queryWrapper = this.buildQuery(queryDto);
        return taskMapstruct.toVoList(this.taskMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(TaskAddDTO dto) {
        log.info("任务创建开始,参数={}", JsonUtils.toJsonString(dto));
        Agent agent = agentMapper.selectByAgentSn(dto.getAgentSn());
        AssertUtil.isNotNull(agent, "找不到AgentSn为 " + dto.getAgentSn() + " 的记录");
        AgentVersion agentVersion = agentVersionMapper.selectByVersionSn(dto.getVersionSn());
        AssertUtil.isNotNull(agentVersion, "找不到AgentVersionSn为 " + dto.getVersionSn() + " 的记录");

        //任务表
        String taskSn = UUIDUtil.genRandomSn("task");
        Task task = taskMapstruct.toEntity(dto);
        task.setTenantId(UserContext.getTenantId());
        task.setAgentId(agent.getAgentId());
        task.setVersionId(agentVersion.getVersionId());
        task.setTaskSn(taskSn);
        task.setTaskStatus(TaskStatusEnum.ENABLE.getCode());
        task.setTaskType(TaskTypeEnum.SINGLE_BATCH.getCode());
        task.setCreateUserId(UserContext.getUserId());
        task.setUpdateUserId(UserContext.getUserId());
        task.setStartTime(new Date());
        //数据集配置
        TaskConfigBO taskConfigBO = new TaskConfigBO();
        taskConfigBO.setSessionConfig(dto.getSessionConfig());
        taskConfigBO.setDataSetFileUrl(dto.getDataSetFileUrl());
        taskConfigBO.setDataSetFileSn(dto.getDataSetFileSn());
        task.setTaskConfig(JsonUtils.toJsonString(taskConfigBO));
        taskMapper.insert(task);

        //任务运行表
        TaskRun taskRun = new TaskRun();
        taskRun.setTaskId(task.getTaskId());
        taskRun.setRunType(RunTypeEnum.MANUAL_RUN.getCode());
        taskRun.setRunStatus(RunStatusEnum.RUN.getCode());
        taskRun.setScheduledTime(new Date());
        taskRun.setActualStartTime(new Date());
        taskRunMapper.insert(taskRun);
        //任务运行附件表
        TaskAttachment taskAttachment = new TaskAttachment();
        taskAttachment.setTaskId(task.getTaskId());
        taskAttachment.setAttachStatus(AttachStatusEnum.RUN.getCode());
        taskAttachment.setProcessStartTime(new Date());
        taskAttachmentMapper.insert(taskAttachment);

        //调用智能体接口执行任务
        //todo
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(TaskDTO dto) {
        BusinessAssertUtil.notNull(dto.getTaskId(), "TaskId不能为空");
        Task entity = taskMapper.selectById(dto.getTaskId());
        BusinessAssertUtil.notNull(entity, "找不到TaskId为 " + dto.getTaskId() + " 的记录");

        Task entityList = taskMapstruct.toEntity(dto);
        entityList.setUpdateTime(new Date());
        entity.setUpdateUserId(UserContext.getUserId());
        taskMapper.updateById(entityList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(String taskSn) {
        BusinessAssertUtil.notNull(taskSn, "TaskSn不能为空");

        Task entity = taskMapper.selectByTaskSn(taskSn);
        BusinessAssertUtil.notNull(entity, "找不到taskSn为 " + taskSn + " 的记录");
        entity.setTaskStatus(TaskStatusEnum.DELETE.getCode());
        entity.setUpdateTime(null);
        entity.setUpdateUserId(UserContext.getUserId());
        taskMapper.updateById(entity);

        //任务运行表
        taskRunMapper.queryTaskRunByTaskId(entity.getTaskId()).forEach(taskRun -> {
            taskRun.setRunStatus(RunStatusEnum.STOP.getCode());
            taskRun.setActualEndTime(new Date());
            taskRun.setDuration(Math.toIntExact(new Date().getTime() - taskRun.getActualStartTime().getTime()));
            taskRun.setUpdateTime(null);
            taskRunMapper.updateById(taskRun);
        });

        //任务运行附件表
        taskAttachmentMapper.queryTaskAttachmentByTaskId(entity.getTaskId()).forEach(taskRun -> {
            taskRun.setRunStatus(AttachStatusEnum.SKIP.getCode());
            taskRun.setActualEndTime(new Date());
            taskRun.setDuration(Math.toIntExact(new Date().getTime() - taskRun.getActualStartTime().getTime()));
            taskRun.setUpdateTime(null);
            taskRunMapper.updateById(taskRun);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void stop(String taskSn) {
        BusinessAssertUtil.notNull(taskSn, "TaskSn不能为空");

        Task entity = taskMapper.selectByTaskSn(taskSn);
        BusinessAssertUtil.notNull(entity, "找不到taskSn为 " + taskSn + " 的记录");
        entity.setTaskStatus(TaskStatusEnum.PAUSE.getCode());
        entity.setUpdateTime(null);
        entity.setUpdateUserId(UserContext.getUserId());
        taskMapper.updateById(entity);

        //任务运行表
        taskRunMapper.queryTaskRunByTaskId(entity.getTaskId()).forEach(taskRun -> {
            taskRun.setRunStatus(RunStatusEnum.STOP.getCode());
            taskRun.setActualEndTime(new Date());
            taskRun.setDuration(Math.toIntExact(new Date().getTime() - taskRun.getActualStartTime().getTime()));
            taskRun.setUpdateTime(null);
            taskRunMapper.updateById(taskRun);
        });

        //任务运行附件表
        taskAttachmentMapper.queryTaskAttachmentByTaskId(entity.getTaskId()).forEach(taskRun -> {
            taskRun.setRunStatus(AttachStatusEnum.SKIP.getCode());
            taskRun.setActualEndTime(new Date());
            taskRun.setDuration(Math.toIntExact(new Date().getTime() - taskRun.getActualStartTime().getTime()));
            taskRun.setUpdateTime(null);
            taskRunMapper.updateById(taskRun);
        });
    }

    @Transactional(readOnly = true)
    @Override
    public TaskVO get(Integer id) {
        BusinessAssertUtil.notNull(id, "TaskId不能为空");

        Task entity = taskMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到TaskId为 " + id + " 的记录");

        return taskMapstruct.toVo(entity);
    }

    @Transactional(readOnly = true)
    @Override
    public TaskDetailVO detail(String taskSn) {
        BusinessAssertUtil.notNull(taskSn, "TaskSn不能为空");

        Task entity = taskMapper.selectByTaskSn(taskSn);
        BusinessAssertUtil.notNull(entity, "找不到taskSn为 " + taskSn + " 的记录");

        TaskDetailVO detailVo = taskMapstruct.toDetailVo(entity);
        TaskConfigBO taskConfigBO = JsonUtils.parseObject(detailVo.getTaskConfig(), TaskConfigBO.class);
        detailVo.setDataSetFileUrl(taskConfigBO.getDataSetFileUrl());
        //获取运行结果文件
        ResultVo<String> downloadBytesVoResultVo = fileFeignClient.getUrl(taskConfigBO.getRunResultFileSn(),"","");
        detailVo.setRunResulUrl(downloadBytesVoResultVo.getData());

        return detailVo;
    }

    @Transactional(readOnly = true)
    @Override
    public void getDataSetTemplate(String agentSn, String versionSn, HttpServletResponse response)  {
        BusinessAssertUtil.notNull(agentSn, "智能体Sn不能为空");
        BusinessAssertUtil.notNull(versionSn, "智能体版本Sn不能为空");

        Agent agent = agentMapper.selectByAgentSn(agentSn);
        AssertUtil.isNotNull(agent, "找不到AgentSn为 " + agentSn + " 的记录");
        AgentVersion agentVersion = agentVersionMapper.selectByVersionSn(versionSn);
        AssertUtil.isNotNull(agentVersion, "找不到AgentVersionSn为 " + versionSn + " 的记录");


        Pair<String, byte[]> templateInfo;
        //智能体类型: 10:对话流, 20:工作流, 30:master
        Integer agentType = agent.getAgentType();
        if(AgentTypeEnum.WORK_FLOW.getCode().equals(agentType)){
            templateInfo = TaskTemplateUtils.generateTemplateInfo(agent.getAgentName(), agentVersion.getVersionNumber(), null);
        }else{
            templateInfo = TaskTemplateUtils.generateTemplateInfo(agent.getAgentName(), agentVersion.getVersionNumber(), null);
        }

        try (var input = new ByteArrayInputStream(templateInfo.getRight())) {
            TaskTemplateUtils.downloadResponse(response, templateInfo.getLeft(), input);
        } catch (IOException e) {
            throw new RuntimeException("模版文件流读取失败。");
        }
    }

    /**
     * 保存任务运行结果（智能体执行完成后调用）-通过消息通知方式
     * @param dto
     */
    @Override
    public void saveTaskRunResult(TaskRunResultDTO dto){
        log.info("保存任务运行结果开始,参数={}", JsonUtils.toJsonString(dto));
        Task task = taskMapper.selectByTaskSn(dto.getTaskSn());
        BusinessAssertUtil.notNull(task, "找不到taskSn为 " + dto.getTaskSn() + " 的记录");

        task.setEndTime(new Date());
        task.setRunCount(1);
        task.setUpdateTime(null);
        taskMapper.updateById(task);

        //任务运行表
        taskRunMapper.queryTaskRunByTaskId(task.getTaskId()).forEach(taskRun -> {
            taskRun.setRunStatus(dto.getRunStatus());
            taskRun.setActualEndTime(new Date());
            taskRun.setDuration(Math.toIntExact(new Date().getTime() - taskRun.getActualStartTime().getTime()));
            taskRun.setRunInput(dto.getRunInput());
            taskRun.setRunError(dto.getRunError());
            taskRun.setAgentRunId(Long.valueOf(dto.getAgentRunId()));
            taskRun.setUpdateTime(null);
            taskRunMapper.updateById(taskRun);
        });

        //任务运行附件表
        taskAttachmentMapper.queryTaskAttachmentByTaskId(task.getTaskId()).forEach(taskRun -> {
            taskRun.setRunStatus(dto.getRunStatus());
            taskRun.setActualEndTime(new Date());
            taskRun.setDuration(Math.toIntExact(new Date().getTime() - taskRun.getActualStartTime().getTime()));
            taskRun.setRunInput(dto.getRunInput());
            taskRun.setRunError(dto.getRunError());
            taskRun.setAgentRunId(Long.valueOf(dto.getAgentRunId()));
            taskRun.setUpdateTime(null);
            taskRunMapper.updateById(taskRun);
        });

    }

    private QueryWrapper<Task> buildQuery(TaskQueryDTO queryDto) {
        QueryWrapper<Task> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Task::getTenantId, UserContext.getTenantId());
        queryWrapper.lambda().ne(Task::getTaskStatus, TaskStatusEnum.DELETE.getCode());
        queryWrapper.lambda().like(StringUtils.isNoneBlank(queryDto.getKeyword()),Task::getTaskName, queryDto.getKeyword());
        return queryWrapper;
    }
}