package com.ai.application.app.api.feign;

import com.ai.application.app.api.dto.query.AppUserQueryDTO;
import com.ai.application.app.api.dto.query.AppUserVerifyPasswordDTO;
import com.ai.application.app.api.feign.fallback.AppUserClientFallback;
import com.ai.application.app.api.vo.AppUserDetailVO;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Tag(name = "用户Feign接口", description = "用户基本操作")
@FeignClient(
        value = ServiceConstant.APP,

        contextId = "IAppUserClient"
)
public interface IAppUserClient {
    String API_PREFIX = "/v1/feign/user";

    @PostMapping(API_PREFIX + "/verifyPassword")
    ResultVo<AppUserVO> verifyPassword(@RequestBody @Validated AppUserVerifyPasswordDTO dto);

    @GetMapping(API_PREFIX + "/getUserBySn/{userSn}")
    ResultVo<AppUserDetailVO> getUserBySn(@PathVariable("userSn") String userSn);

    @GetMapping( API_PREFIX + "/getUserById/{userId}")
    ResultVo<AppUserVO> getUserById(@PathVariable("userId") Integer userId);

    @PostMapping(API_PREFIX + "/list")
    ResultVo<List<AppUserVO>> list(@RequestBody @Validated AppUserQueryDTO queryDto);

    @PostMapping(API_PREFIX + "/getUsers/ceshi")
    ResultVo<List<AppUserVO>> ceshi(@RequestBody @Validated AppUserQueryDTO queryDto);
}
