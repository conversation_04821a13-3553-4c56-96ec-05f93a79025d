package com.ai.application.agent.run.service.impl;

import com.ai.application.agent.run.dto.*;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.feign.IMemoryFeignClient;
import com.ai.application.agent.run.service.ILongTermMemoryRetrieveService;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 长期记忆检索服务实现
 */
@Slf4j
@Service
public class LongTermMemoryRetrieveServiceImpl implements ILongTermMemoryRetrieveService {

    private final IMemoryFeignClient memoryFeignClient;

    public LongTermMemoryRetrieveServiceImpl(IMemoryFeignClient memoryFeignClient) {
        this.memoryFeignClient = memoryFeignClient;
    }

    @Override
    public ResultVo<LongTermMemoryRetrieveResultDTO> executeLongTermMemoryRetrieve(LongTermMemoryRetrieveRequestDTO request, String authorization) {
        log.info("LongTermMemoryRetrieveService executeLongTermMemoryRetrieve start, request: {}", JsonUtils.toJsonString(request));

        try {
            // 检查长期记忆是否启用（匹配backend2逻辑）
            if (request.getLongTermMemoryEnabled() != null && !request.getLongTermMemoryEnabled()) {
                log.info("Long term memory is disabled, returning empty list");
                return ResultVo.data(LongTermMemoryRetrieveResultDTO.builder()
                        .success(true)
                        .jsonList(Collections.emptyList())
                        .totalCount(0)
                        .returnCount(0)
                        .build());
            }

            // 参数校验
            validateLongTermMemoryRetrieveRequest(request);

            // 构建记忆搜索请求
            MemorySearchRequestDTO searchRequest = buildMemorySearchRequestDTO(request);

            log.info("Calling memory service to search long-term memories: agentId={}, userId={}, queryContent={}", 
                    request.getAgentId(), request.getUserId(), request.getQueryContent());

            // 调用记忆服务搜索
            MemorySearchResponseDTO searchResponse = memoryFeignClient.searchMemory(searchRequest);

            if (searchResponse == null || !searchResponse.getSuccess()) {
                String errorMsg = searchResponse != null ? searchResponse.getMessage() : "长期记忆搜索服务调用失败";
                throw new ServiceException(ExecutorError.MEMORY_SEARCH_SERVICE_CALL_FAILED.getCode(), errorMsg);
            }

            // 转换结果格式（匹配backend2逻辑）
            LongTermMemoryRetrieveResultDTO retrieveResult = convertToLongTermMemoryResult(searchResponse);

            log.info("LongTermMemoryRetrieveService executeLongTermMemoryRetrieve success, found {} memories", 
                    retrieveResult.getReturnCount());
            return ResultVo.data(retrieveResult);

        } catch (Exception e) {
            log.error("LongTermMemoryRetrieveService executeLongTermMemoryRetrieve error", e);
            LongTermMemoryRetrieveResultDTO errorResult = LongTermMemoryRetrieveResultDTO.builder()
                    .success(false)
                    .jsonList(Collections.emptyList())
                    .errorMessage(e.getMessage())
                    .totalCount(0)
                    .returnCount(0)
                    .build();
            return ResultVo.data(errorResult);
        }
    }

    @Override
    public boolean validateLongTermMemoryRetrieveRequest(LongTermMemoryRetrieveRequestDTO request) {
        if (request == null) {
            return false;
        }

        // 必须有智能体ID和用户ID
        if (StringUtils.isBlank(request.getAgentId()) || StringUtils.isBlank(request.getUserId())) {
            return false;
        }

        // 必须有检索内容
        return StringUtils.isNotBlank(request.getQueryContent());
    }

    @Override
    public Object buildMemorySearchRequest(LongTermMemoryRetrieveRequestDTO request) {
        return buildMemorySearchRequestDTO(request);
    }

    /**
     * 校验长期记忆检索请求
     */
    private void validateLongTermMemoryRetrieveRequest(LongTermMemoryRetrieveRequestDTO request) {
        if (StringUtils.isBlank(request.getAgentId())) {
            throw new ServiceException(ExecutorError.AGENT_ID_IS_BLANK);
        }

        if (StringUtils.isBlank(request.getUserId())) {
            throw new ServiceException(ExecutorError.USER_ID_IS_BLANK);
        }

        if (StringUtils.isBlank(request.getQueryContent())) {
            throw new ServiceException(ExecutorError.LONG_TERM_MEMORY_QUERY_CONTENT_IS_BLANK);
        }
    }

    /**
     * 构建记忆搜索请求DTO
     */
    private MemorySearchRequestDTO buildMemorySearchRequestDTO(LongTermMemoryRetrieveRequestDTO request) {
        // 处理时间范围（根据startDaysBefore计算）
        LocalDateTime endTime = request.getEndTime() != null ? request.getEndTime() : LocalDateTime.now();
        LocalDateTime startTime = request.getStartTime();
        
        if (startTime == null && request.getStartDaysBefore() != null) {
            // 根据startDaysBefore计算开始时间
            int daysBefore = request.getStartDaysBefore() == 9999 ? 365 * 10 : request.getStartDaysBefore(); // 9999表示很久以前，设为10年
            startTime = endTime.minusDays(daysBefore);
        }

        // 处理检索策略（TIME_DECAY影响排序方式）
        String sortBy = request.getSortBy();
        if (StringUtils.isBlank(sortBy)) {
            boolean isTimeDecay = request.getStrategies() != null && request.getStrategies().contains("TIME_DECAY");
            sortBy = isTimeDecay ? "time_desc" : "similarity_desc";
        }

        return MemorySearchRequestDTO.builder()
                .agentId(request.getAgentId())
                .userId(request.getUserId())
                .category(StringUtils.isNotBlank(request.getCategory()) ? request.getCategory() : "long_term")
                .userQuestion(request.getQueryContent())
                .similarityThreshold(request.getSimilarityThreshold() != null ? request.getSimilarityThreshold() : 0.7)
                .startTime(startTime)
                .endTime(endTime)
                .sortBy(sortBy)
                .limit(request.getLimit() != null ? request.getLimit() : 10)
                .build();
    }

    /**
     * 转换搜索结果为长期记忆结果（匹配backend2逻辑）
     */
    private LongTermMemoryRetrieveResultDTO convertToLongTermMemoryResult(MemorySearchResponseDTO searchResponse) {
        MemorySearchResponseDTO.MemorySearchData data = searchResponse.getData();
        if (data == null) {
            return LongTermMemoryRetrieveResultDTO.builder()
                    .success(true)
                    .jsonList(Collections.emptyList())
                    .totalCount(0)
                    .returnCount(0)
                    .build();
        }

        List<MemorySearchResponseDTO.MemoryItemDTO> memories = data.getMemories();
        if (CollectionUtils.isEmpty(memories)) {
            return LongTermMemoryRetrieveResultDTO.builder()
                    .success(true)
                    .jsonList(Collections.emptyList())
                    .totalCount(data.getTotal() != null ? data.getTotal() : 0)
                    .returnCount(0)
                    .build();
        }

        // 转换为长期记忆项（匹配backend2的MemoryItemVo格式）
        List<LongTermMemoryRetrieveResultDTO.LongTermMemoryItemDTO> memoryItems = memories.stream()
                .map(this::convertToLongTermMemoryItem)
                .collect(Collectors.toList());

        // 转换为JSON字符串列表（匹配backend2的jsonList输出）
        List<String> jsonList = memoryItems.stream()
                .map(this::convertMemoryItemToJsonString)
                .collect(Collectors.toList());

        return LongTermMemoryRetrieveResultDTO.builder()
                .success(true)
                .jsonList(jsonList)
                .totalCount(data.getTotal() != null ? data.getTotal() : memories.size())
                .returnCount(memories.size())
                .memoryDetails(memoryItems)
                .build();
    }

    /**
     * 转换为长期记忆项（匹配backend2的MemoryItemVo格式）
     */
    private LongTermMemoryRetrieveResultDTO.LongTermMemoryItemDTO convertToLongTermMemoryItem(MemorySearchResponseDTO.MemoryItemDTO memory) {
        // 构建content Map（匹配backend2逻辑）
        Map<String, Object> content = new HashMap<>();
        content.put("id", memory.getId());
        content.put("agent_id", memory.getAgentId());
        content.put("user_id", memory.getUserId());
        content.put("category", memory.getCategory());
        content.put("user_question", memory.getUserQuestion());
        if (StringUtils.isNotBlank(memory.getQuestionReply())) {
            content.put("question_reply", memory.getQuestionReply());
        }
        content.put("question_time", memory.getQuestionTime());
        if (memory.getSimilarityScore() != null) {
            content.put("similarity_score", memory.getSimilarityScore());
        }
        content.put("created_at", memory.getCreatedAt());
        content.put("updated_at", memory.getUpdatedAt());

        String timestamp = memory.getCreatedAt() != null ? memory.getCreatedAt().toString() : "";

        return LongTermMemoryRetrieveResultDTO.LongTermMemoryItemDTO.builder()
                .content(content)
                .timestamp(timestamp)
                .memoryTime(timestamp) // 添加memoryTime字段（匹配backend2逻辑）
                .build();
    }

    /**
     * 转换记忆项为JSON字符串（匹配backend2逻辑）
     */
    private String convertMemoryItemToJsonString(LongTermMemoryRetrieveResultDTO.LongTermMemoryItemDTO memoryItem) {
        // 匹配backend2逻辑：将content复制一份，并添加memoryTime字段
        Map<String, Object> memory = new HashMap<>(memoryItem.getContent());
        memory.put("memoryTime", memoryItem.getTimestamp());
        
        return JsonUtils.toJsonString(memory);
    }
}
