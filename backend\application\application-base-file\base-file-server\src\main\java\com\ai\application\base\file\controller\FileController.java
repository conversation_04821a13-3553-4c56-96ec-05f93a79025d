package com.ai.application.base.file.controller;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONObject;
import com.ai.application.base.file.api.dto.*;
import com.ai.application.base.file.api.feign.IUrlMdFeignClient;
import com.ai.application.base.file.api.vo.DownloadMdVo;
import com.ai.application.base.file.api.vo.DownloadUrlVo;
import com.ai.application.base.file.api.vo.FileMd5Vo;
import com.ai.application.base.file.service.IFileService;
import com.ai.framework.core.util.string.StringUtil;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.vo.ResultVo;
import com.alibaba.druid.support.json.JSONUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 文件服务
 */
@RestController
@RequestMapping("/v1/operate")
@Slf4j
@Tag(name = "公共文件上传", description = "文件上传相关操作")
public class FileController {
    public static final String TEXT_MARKDOWN = "text/markdown";
    public static final String SOURCE = "第三方";
    public static final String MARKDOWN = "markdown";
    public static final String DATA = "data";
    public static final String MD = "md";
    @Resource
    private IUrlMdFeignClient urlMdFeignClient;


    private final IFileService fileService;

    public FileController(IFileService fileService) {
        this.fileService = fileService;
    }


    @PostMapping("/scrape")
    public ResultVo<DownloadMdVo> getStreamResult(@Validated @RequestBody UrlMdDto dto) throws IOException {

        // 调用远程服务获取URL的MD5和内容
        UrlMdFeignDto urlMdFeignDto=new UrlMdFeignDto();
        urlMdFeignDto.setUrl(dto.getUrl());
        urlMdFeignDto.setFormats(dto.getFormats());
        JSONObject urlMd = urlMdFeignClient.getUrlMd(urlMdFeignDto);
        log.info("获取URL的MD5和内容: {}", JSONUtils.toJSONString(urlMd));

        JSONObject data = (JSONObject) urlMd.get(DATA);
        String markdown = (String) data.get(MARKDOWN);
        String fileSn = UUIDUtil.genRandomSn(MD);
        if (StringUtil.isNotEmpty(dto.getFileSn())) {
            fileSn = dto.getFileSn();
        }
        fileService.processFileUpload(markdown.getBytes(), fileSn, TEXT_MARKDOWN, fileSn, SOURCE);

        log.info("获取URL的MD5和内容: {}", JSONUtils.toJSONString(markdown));


        DownloadMdVo vo = new DownloadMdVo();
        vo.setFileSn(fileSn);
        vo.setUrl(fileService.getUrl(fileSn, null, null));

        return ResultVo.data(vo);
    }

    /**
     * 文件上传
     */
    @PostMapping("/upload")
    @Operation(summary = "文件上传 文件服务")
    public ResultVo<DownloadUrlVo> upload(@RequestParam("file") MultipartFile file, @RequestParam("fileSn") String fileSn,
                                          @RequestParam(value = "type", required = false) String type) throws IOException {

        String fileHash = DigestUtils.md5Hex(file.getBytes());

        String url = fileService.uploadFileReturnUrl(file, fileSn, type);
        DownloadUrlVo downloadUrlVo = new DownloadUrlVo();
        downloadUrlVo.setUrl(url);
        downloadUrlVo.setHash(fileHash);
        return ResultVo.data(downloadUrlVo);
    }

    /**
     * 获取文件下载url
     */
    @GetMapping("/download/url")
    @Operation(summary = "获取文件下载链接")
    public ResultVo<String> getUrl(@RequestParam("fileSn") String fileSn, @RequestParam(required = false, value = "type") String type, @RequestParam(required = false, value = "fileName") String fileName) {
        // 通过oss获取文件url
        return ResultVo.data(fileService.getUrl(fileSn, type, fileName));
    }

    /**
     * 获取当前用户历史上传文件的MD5
     *
     * @return
     */
    @GetMapping("/history/md5")
    public ResultVo<FileMd5Vo> historyMd5() {
        return ResultVo.data(fileService.historyMd5());
    }

    /**
     * 前端通知文件上传成功
     */
    @PostMapping("/upload/notice")
    @Operation(summary = "文件保存数据库")
    public ResultVo<String> uploadFile(@RequestBody @Valid FileUploadNoticeDto uploadInfo) {
//      this.checkIfChanged(uploadInfo.getDataSetId(), uploadInfo.getFileSn(), uploadInfo.getEmbeddingConfig());

        fileService.uploadFile(uploadInfo, false);
        return ResultVo.success("ok");
    }


    @DeleteMapping("/delete")
    @Operation(summary = "文件删除操作")
    public ResultVo<String> deleteFile(@RequestBody @Valid FileDeleteDto fileDeleteDto) {
        fileService.deleteFile(fileDeleteDto);
        return ResultVo.success("ok");
    }


    private static final String CONFIG_PREFIX = "CONFIG::";


    @GetMapping("/{fileSn}")
    @Operation(summary = "文件信息查询操作")
    public ResultVo<DocFileDto> getDocFileByFileSn(@PathVariable("fileSn") String fileSn) {
        return fileService.getDocFileByFileSn(fileSn).map(ResultVo::data).orElseGet(ResultVo::new);
    }


}
