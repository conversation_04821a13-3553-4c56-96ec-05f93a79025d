package com.ai.application.task.api.vo;

import com.ai.application.task.api.bo.TaskBatchConfigBO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 计划任务表
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Data
@Schema(name = "任务详情-批任务")
public class TaskBatchDetailVO {

    /**
     * 任务序列号
     */
    @Schema(description = "任务序列号")
    private String taskSn;

    /**
     * 任务名称
     */
    @Schema(description = "任务名称")
    private String taskName;

    /**
     * 任务描述
     */
    @Schema(description = "任务描述")
    private String taskDesc;

    /**
     * 任务类型:11-单次,13-单次批量,21-周期性
     */
    @Schema(description = "任务类型:11-单次,13-单次批量,21-周期性")
    private Integer taskType;

    /**
     * 任务状态:1-启用,0-禁用,2-暂停,-1-删除
     */
    @Schema(description = "任务状态:1-启用,0-禁用,2-暂停,-1-删除")
    private Integer taskStatus;

    /**
     * cron表达式
     */
    @Schema(description = "cron表达式")
    private String cronExpression;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 下次执行时间
     */
    @Schema(description = "下次执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date nextRunTime;

    /**
     * 最后执行时间
     */
    @Schema(description = "最后执行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastRunTime;

    /**
     * 已执行次数
     */
    @Schema(description = "已执行次数")
    private Integer runCount;

    /**
     * 最大执行次数
     */
    @Schema(description = "最大执行次数")
    private Integer maxRunCount;

    /**
     * 超时时间(秒)
     */
    @Schema(description = "超时时间(秒)")
    private Integer timeoutSeconds;

    /**
     * 失败重试次数
     */
    @Schema(description = "失败重试次数")
    private Integer retryCount;

    /**
     * 重试间隔(秒)
     */
    @Schema(description = "重试间隔(秒)")
    private Integer retryInterval;

    /**
     * 任务输入参数
     */
    @Schema(description = "任务输入参数")
    private String taskInput;

    /**
     * 任务配置参数
     */
    @Schema(description = "任务配置参数")
    private String taskConfig;

    /**
     * 通知配置
     */
    @Schema(description = "通知配置")
    private String notificationConfig;

    /**
     * 关联智能体id
     */
    @Schema(description = "关联智能体id")
    private Integer agentId;

    /**
     * 智能体版本id
     */
    @Schema(description = "智能体版本id")
    private Integer versionId;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Integer tenantId;

    /**
     * 创建用户id
     */
    @Schema(description = "创建用户id")
    private Integer createUserId;

    /**
     * 更新用户id
     */
    @Schema(description = "更新用户id")
    private Integer updateUserId;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Schema(description = "任务配置信息")
    private TaskBatchConfigBO taskBatchConfigData;

}