package com.ai.application.knowledge.table.service.impl;

import com.ai.application.app.api.dto.query.AppUserQueryDTO;
import com.ai.application.app.api.feign.IAppUserClient;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.application.knowledge.table.dto.AgentByTableDto;
import com.ai.application.knowledge.table.dto.TableCreateDto;
import com.ai.application.knowledge.table.dto.TableDataArithmeticClearDto;
import com.ai.application.knowledge.table.dto.TableListDto;
import com.ai.application.knowledge.table.entity.KnowledgeTable;
import com.ai.application.knowledge.table.enums.TableStatusEnum;
import com.ai.application.knowledge.table.enums.TableTypeEnum;
import com.ai.application.knowledge.table.errors.AgentDocError;
import com.ai.application.knowledge.table.feign.TableArithmeticFeignClient;
import com.ai.application.knowledge.table.mapper.KnowledgeTableMapper;
import com.ai.application.knowledge.table.service.IKnowledgeTableService;
import com.ai.application.knowledge.table.vo.AgentByTableVo;
import com.ai.application.knowledge.table.vo.TableDetailVo;
import com.ai.application.knowledge.table.vo.TableListVo;
import com.ai.application.tenant.authorize.api.dto.ResourceAddReqDTO;
import com.ai.application.tenant.authorize.api.dto.ResourceGrantReqDTO;
import com.ai.application.tenant.authorize.api.enums.GrantObjectTypeEnum;
import com.ai.application.tenant.authorize.api.enums.GrantTypeEnum;
import com.ai.application.tenant.authorize.api.enums.ResourceTypeEnum;
import com.ai.application.tenant.authorize.api.feign.ITenantAuthorizeClient;
import com.ai.application.tenant.authorize.api.vo.ResourceVO;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.util.validator.AssertUtil;
import com.ai.framework.core.vo.ResultVo;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 智能表格表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
@Slf4j
public class KnowledgeTableServiceImpl extends ServiceImpl<KnowledgeTableMapper, KnowledgeTable> implements IKnowledgeTableService {

    @Autowired
    private TableArithmeticFeignClient tableArithmeticFeignClient;

    @Resource
    private IAppUserClient userClient;

    @Resource
    private ITenantAuthorizeClient tenantAuthorizeClient;

    @Override
    public ResultVo<PageInfo<TableListVo>> list(TableListDto dto) {
        // 租户处理
        dto.setTenantId(UserContext.getTenantId());

        //询满足姓名模糊查询的用户
        likeNameQuery(dto);

        //权限处理
        permissionHandler(dto);

        // 使用 PageHelper 进行分页查询
        PageHelper.startPage(dto.getPageNo(), dto.getPageSize());

        //查询列表
        List<TableListVo> result = getBaseMapper().list(dto);

        // 获取所有的创建人姓名和更新人姓名
        List<Integer> creators = result.stream().map(TableListVo::getCreateUserId).distinct().toList();

        List<Integer> updaters = result.stream().map(TableListVo::getUpdateUserId).distinct().toList();

        List<Integer> updaterAndCreator = new ArrayList<>();
        updaterAndCreator.addAll(creators);
        updaterAndCreator.addAll(updaters);

        // 获取创建人名字
        AppUserQueryDTO appUserQuery = new AppUserQueryDTO();
        appUserQuery.setUserIds(updaterAndCreator);
        log.info("查询姓名模糊查询的用户 入参：{}", JSON.toJSONString(appUserQuery));
        List<AppUserVO> list = userClient.list(appUserQuery).getData();
        log.info("查询姓名模糊查询的用户 出参：{}", JSON.toJSONString((list)));

        Map<Integer, AppUserVO> wordMap = list.stream().collect(Collectors.toMap(AppUserVO::getUserId, vo -> vo, (o1, o2) -> o1));

        result.forEach(f -> {
            f.setCreator(wordMap.get(f.getCreateUserId()).getUserName());
            f.setUpdater(wordMap.get(f.getUpdateUserId()).getUserName());
        });


        return ResultVo.data(new PageInfo<>(result));
    }

    @Override
    public ResultVo<TableDetailVo> detail(String tableSn) {
        // 根据 tableSn 查询表格信息
        KnowledgeTable knowledgeTable = checkTable(tableSn);
        // 转换为 DetailVo
        TableDetailVo detailVo = new TableDetailVo();
        BeanUtils.copyProperties(knowledgeTable, detailVo);
        //创建人 更新人
        AppUserVO createUser = userClient.getUserById(detailVo.getCreateUserId()).getData();
        detailVo.setCreator(createUser.getUserName());
        AppUserVO updateUser = userClient.getUserById(detailVo.getUpdateUserId()).getData();
        detailVo.setUpdater(updateUser.getUserName());
        return ResultVo.data(detailVo);
    }


    @Override
    public ResultVo<TableDetailVo> detailById(Integer tableId) {
        KnowledgeTable knowledgeTable = this.baseMapper.selectOne(Wrappers.lambdaQuery(KnowledgeTable.class).eq(KnowledgeTable::getTableId, tableId).last(" limit 1 "));
        if (knowledgeTable == null) {
            throw new ServiceException(AgentDocError.INTELLIGENT_TABLE_IS_EXISTS.getCode(), AgentDocError.INTELLIGENT_TABLE_IS_EXISTS.getMessage());
        }
        // 转换为 DetailVo
        TableDetailVo detailVo = new TableDetailVo();
        BeanUtils.copyProperties(knowledgeTable, detailVo);
        return ResultVo.data(detailVo);
    }


    @Override
    public ResultVo<String> create(TableCreateDto dto) {
        // 生成唯一表格SN
        String tableSn = UUIDUtil.genRandomSn("table");

        //  校验表格名称
        if (ObjectUtils.isNotEmpty(this.baseMapper.selectOne(Wrappers.lambdaQuery(KnowledgeTable.class).eq(KnowledgeTable::getTableName, dto.getTableName()).eq(KnowledgeTable::getTableStatus, TableStatusEnum.VALID.getCode()).last(" limit 1 ")))) {
            return ResultVo.fail("表格名称已存在");
        }
        // 构建知识表实体
        KnowledgeTable knowledgeTable = new KnowledgeTable().setTableName(dto.getTableName()).setTableDesc(dto.getTableDesc()).setTableType(TableTypeEnum.PUBLIC_DATA_TABLE.getCode()).setTableStatus(TableStatusEnum.VALID.getCode()).setTenantId(UserContext.getTenantId()).setCreateUserId(UserContext.getUserId()).setUpdateUserId(UserContext.getUserId()).setTableSn(tableSn);

        // 保存数据并返回结果
        boolean saved = this.save(knowledgeTable);

        //授权
        ResourceAddReqDTO resourceAddReqDTO = new ResourceAddReqDTO();
        resourceAddReqDTO.setResourceObjectId(knowledgeTable.getTableId());
        resourceAddReqDTO.setResourceTenantId(UserContext.getTenantId());
        resourceAddReqDTO.setResourceType(30);
        tenantAuthorizeClient.addResource(resourceAddReqDTO);


        return saved ? ResultVo.data(knowledgeTable.getTableSn(), "智能表格创建成功") : ResultVo.fail("智能表格创建失败");
    }

    @Override
    public ResultVo<String> update(TableCreateDto dto) {
        //校验 tableSn 查询表格信息
        KnowledgeTable existingTable = checkTable(dto.getTableSn());

        // 查询同名的有效表格
        KnowledgeTable table = this.baseMapper.selectOne(Wrappers.lambdaQuery(KnowledgeTable.class).eq(KnowledgeTable::getTableName, dto.getTableName()).eq(KnowledgeTable::getTableStatus, TableStatusEnum.VALID.getCode()).ne(KnowledgeTable::getTableSn, dto.getTableSn()).last(" limit 1 "));

        // 如果存在同名表格，则不允许更新
        if (ObjectUtils.isNotEmpty(table)) {
            return ResultVo.fail("表格名称已存在");
        }

        // 属性拷贝
        BeanUtils.copyProperties(dto, existingTable);

        // 更新固定字段（如最后修改用户、时间等）
        updateBase(existingTable);

        // 执行更新操作
        boolean updated = this.updateById(existingTable);

        return updated ? ResultVo.data(existingTable.getTableSn(), "智能表格更新成功") : ResultVo.fail("智能表格更新失败");
    }

    @Override
    public ResultVo<String> delete(String tableSn) {
        //校验 tableSn 查询表格信息
        KnowledgeTable existingTable = checkTable(tableSn);

        //TODO 已关联启用agent无法删除

        // 更新固定字段（如最后修改用户、时间等）
        updateBase(existingTable);

        //删除
        existingTable.setTableStatus(TableStatusEnum.DELETED.getCode());
        boolean deleted = this.updateById(existingTable);

        //算法删除
        TableDataArithmeticClearDto arithmeticClearDto = new TableDataArithmeticClearDto();
        arithmeticClearDto.setTableId(Long.valueOf(existingTable.getTableId()));
        tableArithmeticFeignClient.clearTable(arithmeticClearDto);
        log.info("clearTable算法已删除");

        return deleted ? ResultVo.data(existingTable.getTableSn(), "删除成功") : ResultVo.fail("删除失败");
    }

    @Override
    public KnowledgeTable checkTable(String tableSn) {
        KnowledgeTable knowledgeTable = this.baseMapper.selectOne(Wrappers.lambdaQuery(KnowledgeTable.class).eq(KnowledgeTable::getTableSn, tableSn).last(" limit 1 "));
        if (knowledgeTable == null) {
            throw new ServiceException(AgentDocError.INTELLIGENT_TABLE_IS_EXISTS.getCode(), AgentDocError.INTELLIGENT_TABLE_IS_EXISTS.getMessage());
        }
        return knowledgeTable;
    }

    @Override
    public ResultVo<List<AgentByTableVo>> agentByBb(AgentByTableDto dto) {
        return null;
    }

    private void updateBase(KnowledgeTable table) {
        table.setUpdateUserId(UserContext.getUserId());
        table.setUpdateTime(new Date());
    }

    public void permissionHandler(TableListDto dto) {
        // 构建资源授权请求参数
        ResourceGrantReqDTO resourceGrantReqDTO = new ResourceGrantReqDTO();
        resourceGrantReqDTO.setResourceType(ResourceTypeEnum.TABLE.getCode());
        resourceGrantReqDTO.setGrantObjectType(GrantObjectTypeEnum.USER.getCode());
        Integer grantObjectId = UserContext.getUserId();
        List<Integer> grantTypes = Lists.newArrayList();
        grantTypes.add(GrantTypeEnum.OWNER.getCode());
        grantTypes.add(GrantTypeEnum.MANAGE.getCode());
        grantTypes.add(GrantTypeEnum.COORDINATION.getCode());
        resourceGrantReqDTO.setGrantObjectId(grantObjectId);
        resourceGrantReqDTO.setGrantTypes(grantTypes);
        // 查询授权资源列表
        ResultVo<List<ResourceVO>> listResultVo = tenantAuthorizeClient.queryGrantResourceList(resourceGrantReqDTO);
        AssertUtil.isTrue(listResultVo.isSuccess(), "获取知识库授权资源失败");
        List<ResourceVO> listResource = listResultVo.getData();
        if (CollectionUtils.isEmpty(listResource)) {
            return;
        }
        dto.setPermissionIds(listResource.stream().map(ResourceVO::getResourceObjectId).toList());
    }

    public void likeNameQuery(TableListDto dto) {
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(dto.getKeyword())) {
            AppUserQueryDTO appUserQuery = new AppUserQueryDTO();
            appUserQuery.setUserName(dto.getKeyword());
            log.info("查询满足姓名模糊查询的用户 入参：{}", JSON.toJSONString((appUserQuery)));
            List<AppUserVO> list = userClient.list(appUserQuery).getData();
            log.info("查询满足姓名模糊查询的用户 出参：{}", JSON.toJSONString(list));
            dto.setUserIdsByName(list.stream().map(AppUserVO::getUserId).toList());
        }
    }
}
