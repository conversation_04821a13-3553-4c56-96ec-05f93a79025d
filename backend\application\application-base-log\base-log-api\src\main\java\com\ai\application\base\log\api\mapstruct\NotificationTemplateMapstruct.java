package com.ai.application.base.log.api.mapstruct;
import com.ai.application.base.log.api.dto.NotificationTemplateAddDTO;
import com.ai.application.base.log.api.dto.NotificationTemplateUpdateDTO;
import com.ai.application.base.log.api.entity.NotificationTemplate;
import com.ai.application.base.log.api.dto.NotificationTemplateDTO;
import com.ai.application.base.log.api.vo.NotificationTemplatePageVO;
import com.ai.application.base.log.api.vo.NotificationTemplateVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 通知模板配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */

@Mapper(componentModel = "spring")
public interface NotificationTemplateMapstruct {

    NotificationTemplate toEntity(NotificationTemplateDTO dto);
    NotificationTemplate toEntity(NotificationTemplateAddDTO dto);
    NotificationTemplate toEntity(NotificationTemplateUpdateDTO dto);
    List<NotificationTemplate> toEntityList(List<NotificationTemplateDTO> dtolist);
    NotificationTemplateVO toVo(NotificationTemplate entity);
    List<NotificationTemplateVO> toVoList(List<NotificationTemplate> entities);
    List<NotificationTemplatePageVO> toPageVoList(List<NotificationTemplate> entities);
}
