package com.ai.application.knowledge.table.service.impl;

import cn.hutool.core.lang.Dict;
import com.ai.application.app.api.feign.IAppUserClient;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.application.base.file.api.dto.AppFileBatchDto;
import com.ai.application.base.file.api.dto.DocFileDto;
import com.ai.application.base.file.api.entity.AppFile;
import com.ai.application.base.file.api.feign.IFileFeignClient;
import com.ai.application.base.model.api.feign.IModelClient;
import com.ai.application.base.model.api.vo.ModelInfoApiVO;
import com.ai.application.knowledge.table.dto.*;
import com.ai.application.knowledge.table.entity.KnowledgeBase;
import com.ai.application.knowledge.table.entity.KnowledgeFile;
import com.ai.application.knowledge.table.enums.ChunkTypeEnum;
import com.ai.application.knowledge.table.enums.KfStatusEnum;
import com.ai.application.knowledge.table.errors.AgentDocError;
import com.ai.application.knowledge.table.feign.DocArithmeticFeignClient;
import com.ai.application.knowledge.table.mapper.KnowledgeFileMapper;
import com.ai.application.knowledge.table.service.IKnowledgeBaseService;
import com.ai.application.knowledge.table.service.IKnowledgeExtendService;
import com.ai.application.knowledge.table.service.IKnowledgeFileService;
import com.ai.application.knowledge.table.util.RedisKeyManager;
import com.ai.application.knowledge.table.vo.*;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.exception.BusinessException;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.docx.DocxUtil;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.redis.utils.RedisUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 知识库文件表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
@Slf4j
public class KnowledgeFileServiceImpl extends ServiceImpl<KnowledgeFileMapper, KnowledgeFile> implements IKnowledgeFileService {

    @Resource
    private IKnowledgeBaseService knowledgeBaseService;

    @Resource
    private IKnowledgeExtendService KnowledgeExtendService;

    @Resource
    private IFileFeignClient fileFeignClient;

    @Resource
    private DocArithmeticFeignClient docArithmeticFeignClient;

    @Resource
    private IAppUserClient userClient;

    @Resource
    private IModelClient modelClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultVo<String> batchCreate(FileCreateDto dto) {
        //校验知识库是否存在
        KnowledgeBase base = knowledgeBaseService.checkBase(dto.getKbSn());
        // 先检查是否已存在相同fileId的知识文件
        checkFileBind(dto);
        // 分段规则 跟随知识库
        handleParse(dto, base);
        //批量保存
        batchSave(dto, base);
        knowledgeBaseService.update(Wrappers.<KnowledgeBase>lambdaUpdate().set(KnowledgeBase::getKbFiles, base.getKbFiles() + dto.getFileInfo().size()).set(KnowledgeBase::getUpdateTime, new Date()).set(KnowledgeBase::getUpdateUserId, UserContext.getUserId()).eq(KnowledgeBase::getKbSn, dto.getKbSn()));
        return ResultVo.data("操作成功");
    }

    @Override
    public ResultVo<String> update(FileUpdateDto dto) {
        //校验知识库是否存在
        knowledgeBaseService.checkBase(dto.getKbSn());
        //校验知识是否存在
        KnowledgeFile file = checkFile(dto.getFileId());
        if (ObjectUtils.isNotEmpty(dto.getKfSummary())) {
            //更新摘要
            update(Wrappers.lambdaUpdate(KnowledgeFile.class).set(KnowledgeFile::getKfSummary, dto.getKfSummary()).set(KnowledgeFile::getUpdateTime, new Date()).set(KnowledgeFile::getUpdateUserId, UserContext.getUserId()).eq(KnowledgeFile::getFileId, dto.getFileId()));
        }
        if (ObjectUtils.isNotEmpty(dto.getKfSummary())) {
            //更新深度解析
            BaseDetailParserDto parserDto = JsonUtils.parseObject(file.getKfParser(), BaseDetailParserDto.class);
            if (ObjectUtils.isNotEmpty(parserDto)) {
                parserDto.setDeepParse(dto.getDeepParse());
                update(Wrappers.lambdaUpdate(KnowledgeFile.class).set(KnowledgeFile::getKfParser, JSON.toJSONString(parserDto)).set(KnowledgeFile::getUpdateTime, new Date()).set(KnowledgeFile::getUpdateUserId, UserContext.getUserId()).eq(KnowledgeFile::getFileId, dto.getFileId()));
            }
        }
        //更新知识库
        updateBaseByUpdateUser(dto.getKbSn());
        return ResultVo.data("操作成功");
    }

    @Override
    public ResultVo<String> batchDelete(FileBatchDeleteDto dto) {
        //校验知识库是否存在
        KnowledgeBase knowledgeBase = knowledgeBaseService.checkBase(dto.getKbSn());
        //知识删除
        update(Wrappers.lambdaUpdate(KnowledgeFile.class).set(KnowledgeFile::getUpdateTime, new Date()).set(KnowledgeFile::getKfStatus, KfStatusEnum.DEPRECATED.getCode()).in(KnowledgeFile::getFileId, dto.getFileId()));
        //文件删除
        fileFeignClient.deleteFiles(dto.getFileId());
        // 删除缓存
        List<String> fileSnList = null;
        fileSnList.forEach(f -> {
            // 获取 Redis Key
            String redisKey = RedisKeyManager.generateKey(dto.getKbSn(), f);
            RedisUtils.deleteKeys(redisKey);
        });
        //调用算法服务进行批量删除
        FileDeleteEmbeddingDto embeddingDto = new FileDeleteEmbeddingDto();
        embeddingDto.setKb_id(String.valueOf(knowledgeBase.getKbId()));

//        ResultVo<FileDeleteEmbeddingVo> resultVo = docFeignClient.delete_files(embeddingDto);
//        if (!ResultVo.isSuccess(resultVo)) {
//            log.error("调用算法embedding接口失败 msg:{}", resultVo.getMessage());
//            throw new ServiceException(AgentDocError.EMBEDDING_INTERFACE_ERROR.getCode(), AgentDocError.EMBEDDING_INTERFACE_ERROR.getMessage());
//        }
        //更新知识库
        updateBaseByUpdateUser(dto.getKbSn());
        return ResultVo.data("操作成功");
    }

    @Override
    public ResultVo<FileDetailVo> detail(FileDetailDto dto) {
        FileDetailVo fileDetailVo = new FileDetailVo();
        //校验知识库是否存在
        KnowledgeBase knowledgeBase = knowledgeBaseService.checkBase(dto.getKbSn());
        //校验知识是否存在
        KnowledgeFile file = checkFile(dto.getFileId());
        //获取文件信息
        DocFileDto appFile = fileFeignClient.getDocFileById(file.getFileId()).getData();
        //对象转换
        BeanUtils.copyProperties(file, fileDetailVo);
        BeanUtils.copyProperties(appFile, fileDetailVo);
        fileDetailVo.setKbSn(knowledgeBase.getKbSn());
        //创建人 更新人姓名处理
        AppUserVO createUser = userClient.getUserById(fileDetailVo.getCreateUserId()).getData();
        fileDetailVo.setCreator(createUser.getUserName());
        AppUserVO updateUser = userClient.getUserById(fileDetailVo.getUpdateUserId()).getData();
        fileDetailVo.setUpdater(updateUser.getUserName());

        return ResultVo.data(fileDetailVo);
    }

    @Override
    public ResultVo<FileDetailVo> fileDetailById(FileByIdFeignDto dto) {
        FileDetailDto detailDto = new FileDetailDto();
        detailDto.setKbSn(dto.getKbSn());
        detailDto.setFileId(String.valueOf(dto.getFileId()));
        return detail(detailDto);
    }

    @Override
    public ResultVo<String> embedding(EmbeddingFeignDto dto) {
        FileCreateDto createDto = new FileCreateDto();
        BeanUtils.copyProperties(dto, createDto);
        return batchCreate(createDto);
    }

    @Override
    public ResultVo<PageInfo<FileListVo>> list(FileListDto dto) {
        //校验知识库是否存在
        knowledgeBaseService.checkBase(dto.getKbSn());
        //设置分页参数
        PageHelper.startPage(dto.getPageNo(), dto.getPageSize());
        //查询列表
        List<FileListVo> list = getBaseMapper().allList(dto);
        return ResultVo.data(new PageInfo<>(list));
    }

    @Override
    public ResultVo<String> retryEmbedding(FileRetryEmbeddingDto dto) {
        //校验知识库是否存在
        KnowledgeBase knowledgeBase = knowledgeBaseService.checkBase(dto.getKbSn());
        //校验知识是否存在
        KnowledgeFile file = checkFile(dto.getFileId());
        //查询文件详细信息
        DocFileDto appFile = fileFeignClient.getDocFileById(file.getFileId()).getData();
        //构造算法参数
        buildEmbeddingParam(Collections.singletonList(file), knowledgeBase, Collections.singletonList(appFile));
        //重置知识进度
        update(Wrappers.lambdaUpdate(KnowledgeFile.class).set(KnowledgeFile::getUpdateTime, new Date()).set(KnowledgeFile::getUpdateUserId, UserContext.getUserId()).set(KnowledgeFile::getKfStatus, KfStatusEnum.PROCESSING.getCode()).set(KnowledgeFile::getKfProgress, 0).set(KnowledgeFile::getKfError, null).set(KnowledgeFile::getKfChunks, 0).set(KnowledgeFile::getKfVectors, 0).set(KnowledgeFile::getKfSummary, null).eq(KnowledgeFile::getFileId, file.getFileId()));
        return ResultVo.data("操作成功");
    }


    @Override
    public ResultVo<List<KnowledgeFile>> getStatus(FileStatusDto dto) {
        List<KnowledgeFile> fileList = new ArrayList<>();
        //校验知识库是否存在
        KnowledgeBase kbase = knowledgeBaseService.checkBase(dto.getKbSn());
        // TODO 算法获取
//        ListBaseEmbeddingDto listBaseEmbeddingDto = new ListBaseEmbeddingDto();
//        listBaseEmbeddingDto.setUser_id("ragflow-kb-16257e6978814101a61ca74");
//        listBaseEmbeddingDto.setKb_sn(kbase.getKbSn());
//        listBaseEmbeddingDto.setDesc("true");
//        log.info("同步算法知识库列表 入参：{}", JSON.toJSONString(listBaseEmbeddingDto));
//        ListBaseEmbeddingVo listVo = docArithmeticFeignClient.kbList(listBaseEmbeddingDto);
//        log.info("同步算法知识库列表 出参：{}", JSON.toJSONString(listVo));

//        //更新文件最新状态
//        if (ObjectUtils.isNotEmpty(embeddingStatusVo.getFile_info_list())) {
//            embeddingStatusVo.getFile_info_list().forEach(file -> {
//                KnowledgeFile knowledgeFile = new KnowledgeFile();
//                knowledgeFile.setKfId(Long.valueOf(file.getFile_id()));
//                knowledgeFile.setKfChunks(file.getKf_chunks());
//                knowledgeFile.setKfVectors(file.getKf_vectors());
//                knowledgeFile.setKfProgress(file.getKf_progress());
//                knowledgeFile.setKfError(file.getKf_error());
//                knowledgeFile.setUpdateTime(new Date());
//                knowledgeFile.setUpdateUserId(UserContext.getUserId());
//                knowledgeFile.setKfStatus(file.getKf_status());
//                knowledgeFile.setKfSummary(file.getKf_summary());
//                fileList.add(knowledgeFile);
//            });
//            updateBatchById(fileList);
//        }
        return ResultVo.data(fileList);
    }

    @Override
    public ResultVo<FileChunksListVo> listChunks(FileChunksListDto dto) {
        // 校验知识库是否存在
        KnowledgeBase kbase = knowledgeBaseService.checkBase(dto.getKbSn());
        //校验知识是否存在
        log.info("查询文件信息 入参：{}", JSON.toJSONString(dto.getFileSn()));
        DocFileDto docFile = fileFeignClient.getDocFileByFileSn(dto.getFileSn()).getData();
        log.info("查询文件信息 出参：{}", JSON.toJSONString(docFile));
        // 获取 Redis Key
        String redisKey = RedisKeyManager.generateKey(dto.getKbSn(), dto.getFileSn());

        // 从缓存获取数据
        FileChunksListVo fileChunksListVo = getFromCacheOrMock(redisKey, kbase, docFile);

        // 应用筛选条件
        if (ObjectUtils.isNotEmpty(dto.getKeyword()) && ObjectUtils.isNotEmpty(dto.getType())) {
            ChunkTypeEnum chunkType = ChunkTypeEnum.fromCode(dto.getType());
            applyFilter(fileChunksListVo, dto.getKeyword(), chunkType);
        }

        return ResultVo.data(fileChunksListVo);
    }

    /**
     * 从Redis缓存获取文件切片列表，若不存在则生成模拟数据并写入缓存
     *
     * @param redisKey Redis 缓存键
     * @return 文件切片列表对象
     */
    private FileChunksListVo getFromCacheOrMock(String redisKey, KnowledgeBase kbase, DocFileDto docFile) {
//        // 删除已有缓存（调试用），正式环境可移除
//        RedisUtils.deleteKeys(redisKey);

        String json = RedisUtils.getCacheObject(redisKey);
        if (ObjectUtils.isEmpty(json)) {
//            // TODO 调用算法接口获取文件列表（模拟数据用于演示）
//            FileChunksEmbeddingDto dto = new FileChunksEmbeddingDto();
//            dto.setFile_id(String.valueOf(docFile.getFileId()));
//            dto.setKb_id(String.valueOf(kbase.getKbId()));
//            log.info("调用算法获取文件的chunks 入参：{}", JSON.toJSONString(dto));
//            FileChunksEmbeddingVo fileChunksEmbeddingVo = docFeignClient.get_chunks(dto).getData();
//            log.info("调用算法获取文件的chunks 出参：{}", JSON.toJSONString(fileChunksEmbeddingVo));
            json = "{\"code\":0,\"textNum\":2,\"imgNum\":1,\"formNum\":1,\"data\":{\"textList\":[{\"id\":\"text1\",\"baseInfo\":{\"kbId\":\"kb1\",\"fileId\":\"file1\",\"nodeId\":\"node1\",\"nodeIdIndex\":\"1/3\",\"preNodeId\":\"\",\"nextNodeId\":\"node2\"},\"contentInfo\":{\"text\":\"这是第一个文本内容\",\"keywordsWithScores\":{\"java\":1,\"code\":0.8},\"length\":20,\"type\":\"text\"},\"multimodalInfo\":{\"fileUrl\":\"\",\"fileSn\":\"\"}}],\"imgList\":[{\"id\":\"img1\",\"baseInfo\":{\"kbId\":\"kb1\",\"fileId\":\"file1\",\"nodeId\":\"node4\",\"nodeIdIndex\":\"1/1\",\"preNodeId\":\"\",\"nextNodeId\":\"\"},\"contentInfo\":{\"text\":\"图片描述\",\"keywordsWithScores\":{\"关键字\":1,\"哈哈\":0.7},\"length\":4,\"type\":\"image\"},\"multimodalInfo\":{\"fileUrl\":\"http://example.com/image.jpg\",\"fileSn\":\"sn123\"}}],\"formList\":[{\"id\":\"text1\",\"baseInfo\":{\"kbId\":\"kb1\",\"fileId\":\"file1\",\"nodeId\":\"node1\",\"nodeIdIndex\":\"1/3\",\"preNodeId\":\"\",\"nextNodeId\":\"node2\"},\"contentInfo\":{\"text\":\"这是第一个文本内容\",\"keywordsWithScores\":{\"java\":1,\"code\":0.8},\"length\":20,\"type\":\"text\"},\"multimodalInfo\":{\"fileUrl\":\"\",\"fileSn\":\"\"}}]}}";
            FileChunksListVo fileChunksListVo = JSON.parseObject(json, FileChunksListVo.class);
            // 放入缓存
            RedisUtils.setCacheObject(redisKey, JSON.toJSONString(fileChunksListVo));
            return fileChunksListVo;
        } else {
            return JSON.parseObject(json, FileChunksListVo.class);
        }
    }

    /**
     * 对文件切片列表应用筛选条件
     *
     * @param fileChunksListVo 原始文件切片列表
     * @param keyword          关键字
     * @param chunkType        切片类型
     */
    private void applyFilter(FileChunksListVo fileChunksListVo, String keyword, ChunkTypeEnum chunkType) {
        FileChunksListVo.ContentList contentList = fileChunksListVo.getData();

        switch (chunkType) {
            case TEXT:
                List<FileChunksListVo.ContentNode> filteredTextList = contentList.getTextList().stream().filter(t -> t.getContentInfo().getKeywordsWithScores() != null && t.getContentInfo().getKeywordsWithScores().containsKey(keyword)).collect(Collectors.toList());
                contentList.setTextList(filteredTextList);
                contentList.setImgList(new ArrayList<>());
                contentList.setFormList(new ArrayList<>());
                break;

            case IMAGE:
                List<FileChunksListVo.ContentNode> filteredImgList = contentList.getImgList().stream().filter(i -> i.getContentInfo().getText() != null && i.getContentInfo().getText().contains(keyword)).collect(Collectors.toList());
                contentList.setTextList(new ArrayList<>());
                contentList.setImgList(filteredImgList);
                contentList.setFormList(new ArrayList<>());
                break;

            case FORM:
                List<FileChunksListVo.ContentNode> filteredFormList = contentList.getFormList().stream().filter(frm -> frm.getContentInfo().getText() != null && frm.getContentInfo().getText().contains(keyword)).collect(Collectors.toList());
                contentList.setTextList(new ArrayList<>());
                contentList.setFormList(filteredFormList);
                contentList.setImgList(new ArrayList<>());
                break;

            default:
                break;
        }
    }

    @Override
    public ResultVo<String> updateChunks(FileChunksUpdateDto dto) {
        // 校验知识库是否存在
        KnowledgeBase knowledgeBase = knowledgeBaseService.checkBase(dto.getKbSn());

        //校验知识是否存在
        log.info("查询文件信息 入参：{}", JSON.toJSONString(dto));
        DocFileDto docFile = fileFeignClient.getDocFileByFileSn(dto.getFileSn()).getData();
        log.info("查询文件信息 出参：{}", JSON.toJSONString(docFile));

        // 获取 Redis Key
        String redisKey = RedisKeyManager.generateKey(dto.getKbSn(), dto.getFileSn());

        // 获取缓存数据
        String json = RedisUtils.getCacheObject(redisKey);
        if (json == null || json.isEmpty()) {
            return ResultVo.fail("缓存中未找到对应的文件切片数据");
        }
        FileChunksListVo fileChunksListVo = JSON.parseObject(json, FileChunksListVo.class);
        if (fileChunksListVo == null) {
            return ResultVo.fail("编辑失败");
        }

        // 只处理文本类型
        List<FileChunksListVo.ContentNode> textList = fileChunksListVo.getData().getTextList();
        if (textList == null || textList.isEmpty()) {
            return ResultVo.fail("未能找到匹配的文本片段进行更新");
        }
        // 查找并更新指定的 ContentNode
        boolean updated = false;
        for (FileChunksListVo.ContentNode node : textList) {
            if (dto.getNodeId().equals(node.getBaseInfo().getNodeId())) {
                // 更新内容信息
                if (ObjectUtils.isNotEmpty(dto.getContentInfo().getText())) {
                    node.getContentInfo().setText(dto.getContentInfo().getText());
                }
                if (ObjectUtils.isNotEmpty(dto.getContentInfo().getKeywordsWithScores())) {
                    node.getContentInfo().setKeywordsWithScores(dto.getContentInfo().getKeywordsWithScores());
                }
                if (ObjectUtils.isNotEmpty(dto.getContentInfo().getLength())) {
                    node.getContentInfo().setLength(dto.getContentInfo().getLength());
                }
                updated = true;
                break;
            }
        }

        if (!updated) {
            return ResultVo.fail("未能找到匹配的文本片段进行更新");
        }

        // 将更新后的数据写回缓存
        RedisUtils.setCacheObject(redisKey, JSON.toJSONString(fileChunksListVo));

//        //TODO  算法chunk编辑
//        FileEditEmbeddingDto fileEditEmbeddingDto = new FileEditEmbeddingDto();
//        FileEditEmbeddingDto.ContentNode content_node = new FileEditEmbeddingDto.ContentNode();
//        FileEditEmbeddingDto.BaseInfo base_info = new FileEditEmbeddingDto.BaseInfo();
//        base_info.setKb_id(String.valueOf(knowledgeBase.getKbId()));
//        base_info.setFile_id(String.valueOf(docFile.getFileId()));
//        base_info.setNode_id(dto.getNodeId());
//        content_node.setBase_info(base_info);
//        FileEditEmbeddingDto.ContentNode contentNode = new FileEditEmbeddingDto.ContentNode();
//        FileEditEmbeddingDto.ContentInfo content_info = new FileEditEmbeddingDto.ContentInfo();
//        BeanUtils.copyProperties(dto.getContentInfo(), content_info);
//        contentNode.setContent_info(content_info);
//        fileEditEmbeddingDto.setContent_node(content_node);
//        log.info("算法chunk编辑 入参：{}", JSON.toJSONString((fileEditEmbeddingDto)));
//        FileEditEmbeddingVo vo = docFeignClient.edit_chunk(fileEditEmbeddingDto).getData();
//        log.info("算法chunk编辑 入参：{}", JSON.toJSONString((vo)));
        return ResultVo.success("编辑成功");
    }

    @Override
    public ResultVo<FileChunksListVo.ContentNode> detailChunks(FileChunksDetailVo dto) {
        // 校验知识库是否存在
        knowledgeBaseService.checkBase(dto.getKbSn());

        // 获取 Redis Key
        String redisKey = RedisKeyManager.generateKey(dto.getKbSn(), dto.getFileSn());

        // 获取缓存数据
        String json = RedisUtils.getCacheObject(redisKey);
        if (json == null || json.isEmpty()) {
            return ResultVo.fail("缓存中未找到对应的文件切片数据");
        }

        FileChunksListVo fileChunksListVo = JSON.parseObject(json, FileChunksListVo.class);
        if (fileChunksListVo == null) {
            log.error("解析缓存数据时发生错误，文件切片列表为空");
            return ResultVo.fail("解析缓存数据失败");
        }

        List<FileChunksListVo.ContentNode> targetList;
        switch (dto.getType()) {
            case "text":
                targetList = fileChunksListVo.getData().getTextList();
                break;
            case "image":
                targetList = fileChunksListVo.getData().getImgList();
                break;
            case "form":
                targetList = fileChunksListVo.getData().getFormList();
                break;
            default:
                return ResultVo.fail("不支持的片段类型");
        }

        Optional<FileChunksListVo.ContentNode> matchingNode = targetList.stream().filter(node -> node.getBaseInfo() != null && node.getBaseInfo().getNodeId() != null && node.getBaseInfo().getNodeId().equals(dto.getNodeId())).findFirst();

        return matchingNode.map(ResultVo::data).orElseGet(() -> ResultVo.fail("未找到匹配的片段信息"));

    }

    @Override
    public void exportChunks(FileChunksListDto dto, HttpServletResponse response) {
        // 获取导出数据
        FileChunksListVo fileChunksListVo = listChunks(dto).getData();
        List<FileChunksListVo.ContentNode> contentNodeList = fileChunksListVo.getData().getTextList();
        // 创建文档对象
        try (XWPFDocument document = new XWPFDocument()) {
            // 写入内容到Word文档
            for (FileChunksListVo.ContentNode contentNode : contentNodeList) {
                FileChunksListVo.ContentInfo contentInfo = contentNode.getContentInfo();
                if (contentInfo == null || contentInfo.getText() == null) {
                    continue;
                }
                // 使用Markdown格式写入内容
                DocxUtil.addContentWithMarkdown(document, contentInfo.getText());
            }
            // 设置响应头
            String fileName = URLEncoder.encode("file_chunks_export", StandardCharsets.UTF_8);
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName + ".doc");
            response.setHeader("Content-Type", "application/msword");
            // 输出文件流
            try (OutputStream outputStream = response.getOutputStream()) {
                document.write(outputStream);
            }
        } catch (Exception e) {
            log.error("文档导出失败", e);
            throw new RuntimeException("文档导出失败", e);
        }
    }

    @Override
    public ResultVo<String> deleteById(DeleteBySnFeignDto dto) {
        FileBatchDeleteDto deleteDto = new FileBatchDeleteDto();
        deleteDto.setKbSn(dto.getKbSn());
        deleteDto.setFileId(Collections.singletonList(dto.getFileId()));
        batchDelete(deleteDto);
        return ResultVo.data("操作成功");
    }

    private void handleParse(FileCreateDto dto, KnowledgeBase base) {
        BaseDetailParserDto detailParser;
        if (dto.isSplitRuleFollow()) {
            //分段规则处理
            detailParser = KnowledgeExtendService.parserByKb(base.getKbId());
            detailParser.setSplitRuleFollow(true);
        } else {
            detailParser = JsonUtils.parseObject(dto.getKfParser(), BaseDetailParserDto.class);
        }
        if (ObjectUtils.isNotEmpty(dto.getDeepParse())) {
            Objects.requireNonNull(detailParser).setDeepParse(dto.getDeepParse());
        }
        dto.setKfParser(JSON.toJSONString(detailParser));
    }

    private void batchFileHandle(FileCreateDto dto) {
        AppFileBatchDto batchDto = new AppFileBatchDto();
        //文件存储
        List<DocFileDto> appFiles = new ArrayList<>();
        dto.getFileInfo().forEach(f -> {
            DocFileDto appFile = new DocFileDto();
            BeanUtils.copyProperties(f, appFile);
            appFile.setFileSn(UUIDUtil.genRandomSn("file"));
            appFiles.add(appFile);
        });
        batchDto.setDocFileList(appFiles);
        fileFeignClient.batchFileSave(batchDto);
    }


    private void batchSave(FileCreateDto dto, KnowledgeBase base) {
        //获取文件SN
        List<String> fileSnList = dto.getFileInfo().stream().map(AppFile::getFileSn).toList();
        //根据fileSnList查询文件信息
        List<DocFileDto> docFileList = fileFeignClient.getBatchFileByFileSn(fileSnList).getData();
        List<KnowledgeFile> list = new ArrayList<>();
        dto.getFileInfo().forEach(f -> {
            KnowledgeFile knowledgeFile = new KnowledgeFile();
            knowledgeFile.setKbId(base.getKbId());
            knowledgeFile.setUpdateUserId(UserContext.getUserId());
            knowledgeFile.setCreateUserId(UserContext.getUserId());
            knowledgeFile.setKfStatus(KfStatusEnum.NEW_FILE.getCode());
            DocFileDto docFileDto = docFileList.stream().filter(docFile -> docFile.getFileSn().equals(f.getFileSn())).findFirst().orElse(null);
            if (ObjectUtils.isEmpty(docFileDto)) {
                throw new BusinessException("文件不存在");
            }
            knowledgeFile.setFileId(docFileDto.getFileId());
            knowledgeFile.setKfParser(dto.getKfParser());
            list.add(knowledgeFile);
        });
        //检验重复上传集合中去除
        list.removeIf(knowledgeFile -> getOne(Wrappers.lambdaQuery(KnowledgeFile.class).eq(KnowledgeFile::getKbId, knowledgeFile.getKbId()).eq(KnowledgeFile::getFileId, knowledgeFile.getFileId())) != null);
        saveBatch(list);
        if (ObjectUtils.isEmpty(list)) {
            return;
        }
        knowledgeBaseService.update(Wrappers.<KnowledgeBase>lambdaUpdate().set(KnowledgeBase::getKbSizes, docFileList.stream().mapToLong(DocFileDto::getFileSize).sum()).eq(KnowledgeBase::getKbSn, dto.getKbSn()));
        //构造算法参数
        buildEmbeddingParam(list, base, docFileList);
    }

    /**
     * 更新知识库更新人信息
     *
     * @param kbSn 知识库编号
     */
    private void updateBaseByUpdateUser(String kbSn) {
        knowledgeBaseService.update(Wrappers.lambdaUpdate(KnowledgeBase.class).set(KnowledgeBase::getUpdateTime, new Date()).set(KnowledgeBase::getUpdateUserId, UserContext.getUserId()).eq(KnowledgeBase::getKbSn, kbSn));
    }

    public KnowledgeFile checkFile(String fileId) {
        KnowledgeFile file = getOne(Wrappers.lambdaQuery(KnowledgeFile.class).eq(KnowledgeFile::getFileId, fileId));
        if (ObjectUtils.isEmpty(file)) {
            throw new ServiceException(AgentDocError.KNOWLEDGE_FILE_NOT_FOUND.getCode(), AgentDocError.KNOWLEDGE_FILE_NOT_FOUND.getMessage());
        }
        return file;
    }

    public void buildEmbeddingParam(List<KnowledgeFile> file, KnowledgeBase knowledgeBase, List<DocFileDto> appFiles) {
        EmbeddingDto embeddingDto = new EmbeddingDto();
        //知识库ID处理
        embeddingDto.setKb_id(String.valueOf(knowledgeBase.getKbId()));
        List<EmbeddingDto.FileInfo> fileInfos = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(appFiles)) {
            appFiles.forEach(appFile -> {
                //文件信息处理
                EmbeddingDto.FileInfo fileInfo = new EmbeddingDto.FileInfo();
                fileInfo.setFile_sn(appFile.getFileSn());
                fileInfo.setFile_url(appFile.getFileUrl());
                EmbeddingDto.EmbeddingConfig config = new EmbeddingDto.EmbeddingConfig();
                KnowledgeFile fileOne = file.stream().filter(f -> f.getFileId().equals(appFile.getFileId())).findFirst().orElse(null);
                BaseDetailParserDto parserDto = JsonUtils.parseObject(Objects.requireNonNull(fileOne).getKfParser(), BaseDetailParserDto.class);
                config.setSplit_rule(Objects.requireNonNull(parserDto).getSplitRule());
                config.setSplitter(Objects.requireNonNull(parserDto).getSplitter());
                config.setWord_count_limit(Objects.requireNonNull(parserDto).getWordCountLimit());
                config.setWord_count_overlap(Objects.requireNonNull(parserDto).getWordCountOverlap());
                boolean hasOcrImage = Optional.ofNullable(parserDto.getDeepParse()).orElse(Collections.emptyList()).contains("ocrImage");
                boolean hasOcrTable = Optional.ofNullable(parserDto.getDeepParse()).orElse(Collections.emptyList()).contains("ocrTable");
                boolean hasOcrWord = Optional.ofNullable(parserDto.getDeepParse()).orElse(Collections.emptyList()).contains("ocrWord");
                config.setOcr_image(hasOcrImage);
                config.setOcr_table(hasOcrTable);
                config.setWord_enhanced(hasOcrWord);
                if (parserDto.getSeparatorContent() != null && !parserDto.getSeparatorContent().isEmpty()) {
                    String firstElement = parserDto.getSeparatorContent().get(0);
                    if (firstElement != null && firstElement.startsWith("[")) {
                        parserDto.getSeparatorContent().set(0, firstElement.substring(1));
                    }
                    int lastIndex = parserDto.getSeparatorContent().size() - 1;
                    String lastElement = parserDto.getSeparatorContent().get(lastIndex);
                    if (lastElement != null && lastElement.endsWith("]")) {
                        parserDto.getSeparatorContent().set(lastIndex, lastElement.substring(0, lastElement.length() - 1));
                    }
                }
                config.setSeparator_content(parserDto.getSeparatorContent());
                fileInfo.setConfig(config);
                fileInfos.add(fileInfo);
            });
        }
        embeddingDto.setFile_info_list(fileInfos);
        //模型信息处理
        BaseDetailParserDto detailParser = KnowledgeExtendService.parserByKb(knowledgeBase.getKbId());
        ModelInfoApiVO modelInfoVO = modelClient.getModelBySn(detailParser.getModelSn()).getData();
        EmbeddingDto.ModelInfo modelInfo = new EmbeddingDto.ModelInfo();
        modelInfo.setModel_id(modelInfoVO.getModelId());
        modelInfo.setModel_name(modelInfo.getModel_name());
        modelInfo.setModel_type(modelInfoVO.getModelType());
        modelInfo.setModel_engine(modelInfoVO.getModelEngine());
        modelInfo.setModel_conig(JsonUtils.toBigJsonString(Dict.create().set("temperature", 0.7).set("max_tokens", 2048)));
        embeddingDto.setLlm_config(modelInfo);
        //TODO 调用算法服务进行embedding
//        ResultVo<EmbeddingVo> resultVo = docFeignClient.indexing(embeddingDto);
//        if (!ResultVo.isSuccess(resultVo)) {
//            log.error("调用算法embedding接口失败 msg:{}", resultVo.getMessage());
//            throw new ServiceException(AgentDocError.EMBEDDING_INTERFACE_ERROR.getCode(), AgentDocError.EMBEDDING_INTERFACE_ERROR.getMessage());
//        }
    }

    private void checkFileBind(FileCreateDto dto) {
        List<String> existFileIds = getBaseMapper().selectObjs(Wrappers.lambdaQuery(KnowledgeFile.class).select(KnowledgeFile::getFileId).in(KnowledgeFile::getFileId, dto.getFileInfo().stream().map(f -> f.getFileId()).collect(Collectors.toList()))).stream().map(Object::toString).toList();
        // 如果文件ID已经存在，则跳过
        if (ObjectUtils.isNotEmpty(existFileIds)) {
            throw new ServiceException(AgentDocError.FILE_EXIST.getCode(), AgentDocError.FILE_EXIST.getMessage());
        }
    }
}
