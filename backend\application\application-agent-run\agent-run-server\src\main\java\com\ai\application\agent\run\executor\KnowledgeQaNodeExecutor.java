package com.ai.application.agent.run.executor;

import com.ai.application.agent.run.dto.KnowledgeQaRequestDTO;
import com.ai.application.agent.run.dto.KnowledgeQaResultDTO;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.service.IKnowledgeQaService;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import com.ai.framework.workflow.excutor.NodeExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 知识问答节点执行器
 * 用于在工作流中执行知识问答功能
 */
@Slf4j
@Component
public class KnowledgeQaNodeExecutor implements NodeExecutor {

    @Autowired
    private IKnowledgeQaService knowledgeQaService;

    @Override
    public void execute(WorkflowContext context) {
        String nodeKey = context.getCurrentNodeKey();
        NodeContext nodeCtx = context.getNodeContexts().get(nodeKey);
        Map<String, Object> nodeDef = nodeCtx.getNodeDefinition();

        log.info("KnowledgeQaNodeExecutor execute start, nodeKey: {}, nodeDef: {}", nodeKey, JsonUtils.toJsonString(nodeDef));

        try {
            // 设置节点状态为运行中
            nodeCtx.setStatus(NodeStatus.RUNNING);

            // 初始化节点输出
            if (nodeCtx.getOutput() == null) {
                nodeCtx.setOutput(new HashMap<>());
            }

            // 从节点定义中获取输入参数
            Map<String, Object> inputParameters = (Map<String, Object>) nodeDef.get("inputParameters");
            if (inputParameters == null) {
                throw new ServiceException(ExecutorError.NODE_DEFINITION_IS_NULL);
            }

            // 构建知识问答请求
            KnowledgeQaRequestDTO request = buildKnowledgeQaRequest(inputParameters, context);

            // 执行知识问答
            // TODO 这里要不要待定
            String authorization = (String) context.getGlobalVars().get("authorization");
            ResultVo<KnowledgeQaResultDTO> result = knowledgeQaService.executeKnowledgeQa(request, authorization);

            if (result.getCode() != 0) {
                throw new ServiceException(result.getCode(), result.getMessage());
            }

            KnowledgeQaResultDTO qaResult = result.getData();
            if (qaResult == null) {
                throw new ServiceException(ExecutorError.KNOWLEDGE_QA_RESULT_IS_NULL);
            }

            // 构建输出结果
            Map<String, Object> outputResult = buildOutputResult(qaResult);

            // 将结果写入输出参数
            writeOutputParameters(nodeDef, context, outputResult);

            // 设置节点输出
            nodeCtx.getOutput().putAll(outputResult);

            // 设置节点状态为成功
            nodeCtx.setStatus(NodeStatus.SUCCESS);
            nodeCtx.setEndTime(java.time.LocalDateTime.now());

            log.info("KnowledgeQaNodeExecutor execute success, result: {}", JsonUtils.toJsonString(outputResult));

        } catch (Exception e) {
            log.error("KnowledgeQaNodeExecutor execute error", e);
            nodeCtx.setStatus(NodeStatus.FAILED);
            nodeCtx.setErrorMsg("知识问答执行失败: " + e.getMessage());
            nodeCtx.setEndTime(java.time.LocalDateTime.now());
            throw e;
        }
    }

    /**
     * 构建知识问答请求
     */
    private KnowledgeQaRequestDTO buildKnowledgeQaRequest(Map<String, Object> inputParameters, WorkflowContext context) {
        // 获取参数值，支持变量替换
        String content = getParameterValue(inputParameters, "content", context);
        String questionFocus = getParameterValue(inputParameters, "questionFocus", context);
        String llmModelSn = getParameterValue(inputParameters, "llmModelSn", context);
        String responseMode = getParameterValue(inputParameters, "responseMode", context);
        String summaryPrompt = getParameterValue(inputParameters, "summaryPrompt", context);
        String qaModel = getParameterValue(inputParameters, "qaModel", context);
        String knowledgeType = getParameterValue(inputParameters, "knowledgeType", context);

        // 获取知识库编号列表
        Object knowledgeInventorySnObj = inputParameters.get("knowledgeInventorySn");
        List<String> knowledgeInventorySn = parseStringList(knowledgeInventorySnObj, context);

        // 获取知识编号列表
        Object knowledgeSnObj = inputParameters.get("knowledgeSn");
        List<String> knowledgeSn = parseStringList(knowledgeSnObj, context);

        // 获取扩展内容
        Object expand = getParameterObject(inputParameters, "expand", context);

        // 参数校验
        validateParameters(content, llmModelSn, summaryPrompt, qaModel, knowledgeInventorySn, knowledgeSn);

        return KnowledgeQaRequestDTO.builder()
                .content(content)
                .questionFocus(questionFocus)
                .llmModelSn(llmModelSn)
                .responseMode(responseMode)
                .summaryPrompt(summaryPrompt)
                .qaModel(qaModel)
                .knowledgeType(knowledgeType)
                .knowledgeInventorySn(knowledgeInventorySn)
                .knowledgeSn(knowledgeSn)
                .expand(expand)
                .build();
    }

    /**
     * 参数校验
     */
    private void validateParameters(String content, String llmModelSn, String summaryPrompt, 
                                  String qaModel, List<String> knowledgeInventorySn, List<String> knowledgeSn) {
        if (StringUtils.isBlank(content)) {
            throw new ServiceException(ExecutorError.KNOWLEDGE_QA_CONTENT_IS_BLANK);
        }
        if (StringUtils.isBlank(llmModelSn)) {
            throw new ServiceException(ExecutorError.KNOWLEDGE_QA_MODEL_IS_BLANK);
        }
        if (StringUtils.isBlank(summaryPrompt)) {
            throw new ServiceException(ExecutorError.KNOWLEDGE_QA_SUMMARY_PROMPT_IS_BLANK);
        }

        // 根据问答模式校验相应参数
        if ("knowledgeInventory".equals(qaModel)) {
            if (CollectionUtils.isEmpty(knowledgeInventorySn)) {
                throw new ServiceException(ExecutorError.KNOWLEDGE_INVENTORY_SN_IS_NULL);
            }
        } else if ("knowledge".equals(qaModel)) {
            if (CollectionUtils.isEmpty(knowledgeSn)) {
                throw new ServiceException(ExecutorError.KNOWLEDGE_SN_IS_NULL);
            }
        }
    }

    /**
     * 解析字符串列表
     */
    private List<String> parseStringList(Object obj, WorkflowContext context) {
        if (obj == null) {
            return new ArrayList<>();
        }

        List<String> result = new ArrayList<>();
        
        if (obj instanceof List) {
            List<?> list = (List<?>) obj;
            for (Object item : list) {
                if (item != null) {
                    String value = item.toString();
                    // 支持变量替换
                    if (value.startsWith("$")) {
                        String varName = value.substring(1);
                        Object varValue = context.getGlobalVars().get(varName);
                        if (varValue != null) {
                            result.add(varValue.toString());
                        }
                    } else {
                        result.add(value);
                    }
                }
            }
        } else {
            String value = obj.toString();
            // 支持变量替换
            if (value.startsWith("$")) {
                String varName = value.substring(1);
                Object varValue = context.getGlobalVars().get(varName);
                if (varValue != null) {
                    result.add(varValue.toString());
                }
            } else {
                result.add(value);
            }
        }

        return result;
    }

    /**
     * 获取参数值，支持变量替换
     */
    private String getParameterValue(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        String strValue = value.toString();
        
        // 如果是变量引用（以$开头），从全局变量中获取
        if (strValue.startsWith("$")) {
            String varName = strValue.substring(1);
            Object varValue = context.getGlobalVars().get(varName);
            return varValue != null ? varValue.toString() : null;
        }
        
        return strValue;
    }

    /**
     * 获取参数对象，支持变量替换
     */
    private Object getParameterObject(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        if (value instanceof String) {
            String strValue = value.toString();
            // 如果是变量引用（以$开头），从全局变量中获取
            if (strValue.startsWith("$")) {
                String varName = strValue.substring(1);
                return context.getGlobalVars().get(varName);
            }
        }
        
        return value;
    }

    /**
     * 构建输出结果
     */
    private Map<String, Object> buildOutputResult(KnowledgeQaResultDTO qaResult) {
        Map<String, Object> result = new HashMap<>();
        
        // 设置主要输出
        result.put("output", qaResult.getAnswer() != null ? qaResult.getAnswer() : "");
        result.put("answer", qaResult.getAnswer() != null ? qaResult.getAnswer() : "");
        
        // 设置参考信息
        if (qaResult.getReferences() != null) {
            result.put("reference", qaResult.getReferences());
            result.put("references", qaResult.getReferences());
        } else {
            result.put("reference", Collections.emptyList());
            result.put("references", Collections.emptyList());
        }
        
        // 设置执行状态
        result.put("success", qaResult.getSuccess() != null ? qaResult.getSuccess() : false);
        result.put("errorMessage", qaResult.getErrorMessage());

        return result;
    }

    /**
     * 写入输出参数
     */
    private void writeOutputParameters(Map<String, Object> nodeDef, WorkflowContext context, Map<String, Object> result) {
        Map<String, Object> outputParameters = (Map<String, Object>) nodeDef.get("outputParameters");
        if (outputParameters != null) {
            for (Map.Entry<String, Object> entry : outputParameters.entrySet()) {
                String outputKey = entry.getKey();
                String variableName = entry.getValue().toString();
                
                Object resultValue = result.get(outputKey);
                if (resultValue != null) {
                    context.setVar(variableName, resultValue);
                    log.info("Set variable {} = {}", variableName, resultValue);
                }
            }
        }
    }

    @Override
    public String getType() {
        return "KNOWLEDGE_QA";
    }
}
