package com.ai.application.base.file.api.vo;

import com.ai.application.base.file.api.entity.AppFile;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FileInfoVo {

    /**
     * 文件Sn
     */
    private String fileSn;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文档类型
     */
    private Integer docType;

    private String datasetId;

    // 知识对应文档路径
    private String knowledgePath;
    
    public static FileInfoVo from(AppFile docFile) {
        return FileInfoVo.builder().fileSn(docFile.getFileSn())
                .fileName(docFile.getFileName())
                .docType(docFile.getFileType())

                .build();
    }



}
