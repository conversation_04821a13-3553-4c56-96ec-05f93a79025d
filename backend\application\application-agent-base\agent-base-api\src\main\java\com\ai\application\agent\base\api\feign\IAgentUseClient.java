package com.ai.application.agent.base.api.feign;

import com.ai.application.agent.base.api.dto.query.*;
import com.ai.application.agent.base.api.feign.fallback.IAgentUseFallback;
import com.ai.application.agent.base.api.vo.*;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.vo.ResultVo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Tag(name = "智能体使用Feign接口", description = "智能体使用")
@FeignClient(
        value = ServiceConstant.AGENT_BASE,
        fallback = IAgentUseFallback.class,
        contextId = "IAgentUseClient"
)
public interface IAgentUseClient {
    String API_PREFIX = "/v1/feign/agent/use";

    @Operation(summary = "根据mcpId查使用agent信息")
    @PostMapping(API_PREFIX + "/mcp/{mcpId}")
    ResultVo<PageInfo<AgentUseMcpQueryVO>> selectUseMcpByPage(@RequestBody @Validated AgentUseMcpQueryDTO dto);

    @Operation(summary = "根据toolId查使用agent信息")
    @PostMapping(API_PREFIX + "/tool/{toolId}")
    ResultVo<PageInfo<AgentUseToolQueryVO>> selectUseToolByPage(@RequestBody @Validated AgentUseToolQueryDTO dto);

    @Operation(summary = "根据tableId查使用agent信息")
    @PostMapping(API_PREFIX + "/knowledgeTable/{tableId}")
    ResultVo<PageInfo<AgentUseKnowledgeTableQueryVO>> selectUseTableByPage(@RequestBody @Validated AgentUseKnowledgeTableQueryDTO dto);

    @Operation(summary = "根据docId查使用agent信息")
    @PostMapping(API_PREFIX + "/knowledgeDoc/{docId}")
    ResultVo<PageInfo<AgentUseKnowledgeDocQueryVO>> selectUseDocByPage(@RequestBody @Validated AgentUseKnowledgeDocQueryDTO dto);

    @Operation(summary = "根据dictId查使用agent信息")
    @PostMapping(API_PREFIX + "/knowledgeDict/{dictId}")
    ResultVo<PageInfo<AgentUseKnowledgeDictQueryVO>> selectUseDictByPage(@RequestBody @Validated AgentUseKnowledgeDictQueryDTO dto);
}