package com.ai.application.agent.base.api.feign;

import com.ai.application.agent.base.api.dto.FindAgentByUseDTO;
import com.ai.application.agent.base.api.feign.fallback.IAgentClientFallback;
import com.ai.application.agent.base.api.vo.FindAgentByUseVO;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Tag(name = "智能体关联接口", description = "智能体关联基本操作")
@FeignClient(
        value = ServiceConstant.AGENT_BASE,
        fallback = IAgentClientFallback.class,
        contextId = "IAgentUseClient"
)
public interface IAgentUseClient {
    String API_PREFIX = "/v1/feign/agent/use";

    @Operation(summary = "根据使用ID与类型查agent信息")
    @GetMapping(API_PREFIX + "/findAgentByUse")
    ResultVo<List<FindAgentByUseVO>> findAgentByUse(@RequestParam FindAgentByUseDTO dto);
}