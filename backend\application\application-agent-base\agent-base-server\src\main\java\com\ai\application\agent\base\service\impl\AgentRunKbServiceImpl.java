package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.dto.AgentRunKbListDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ai.application.agent.base.mapper.AgentRunKbMapper;
import com.ai.application.agent.base.api.entity.AgentRunKb;
import com.ai.application.agent.base.service.IAgentRunKbService;
import com.ai.application.agent.base.api.dto.AgentRunKbDTO;
import com.ai.application.agent.base.api.vo.AgentRunKbVO;
import com.ai.application.agent.base.api.mapstruct.AgentRunKbMapstruct;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Date;

/**
 * 智能体知识库检索记录表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
public class AgentRunKbServiceImpl implements IAgentRunKbService{

    @Resource
    private AgentRunKbMapper agentRunKbMapper;

    @Resource
    private AgentRunKbMapstruct agentRunKbMapstruct;

    @Transactional(readOnly = true)
    @Override
    public List<AgentRunKbVO> list(AgentRunKbListDTO queryDto) {
        LambdaQueryWrapper<AgentRunKb> queryWrapper = this.buildQuery(queryDto);
        return agentRunKbMapstruct.toVoList(this.agentRunKbMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(AgentRunKbDTO dto) {
        dto.setRunId(null);
        AgentRunKb entity = agentRunKbMapstruct.toEntity(dto);
        entity.setCreateTime(new Date());

        agentRunKbMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AgentRunKbDTO dto) {
        BusinessAssertUtil.notNull(dto.getRunId(), "RunId不能为空");

        AgentRunKb entity = agentRunKbMapper.selectById(dto.getRunId());
        BusinessAssertUtil.notNull(entity, "找不到RunId为 " + dto.getRunId() + " 的记录");

        AgentRunKb entityList = agentRunKbMapstruct.toEntity(dto);
        entityList.setUpdateTime(new Date());
        agentRunKbMapper.updateById(entityList);
    }

    @Transactional(readOnly = true)
    @Override
    public AgentRunKbVO get(Integer id) {
        BusinessAssertUtil.notNull(id, "RunId不能为空");

        AgentRunKb entity = agentRunKbMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到RunId为 " + id + " 的记录");

        return agentRunKbMapstruct.toVo(entity);
    }

    private LambdaQueryWrapper<AgentRunKb> buildQuery(AgentRunKbListDTO queryDto) {
        LambdaQueryWrapper<AgentRunKb> queryWrapper = new LambdaQueryWrapper<>();
        return queryWrapper;
    }
}