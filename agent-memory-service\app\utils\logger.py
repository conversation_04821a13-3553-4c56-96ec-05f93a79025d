# -*- coding: utf-8 -*-
"""
日志工具模块

基于loguru的日志系统配置，提供：
- 控制台日志输出（彩色、结构化）
- 文件日志轮转和压缩
- 错误日志单独记录
- 可配置的日志级别和格式
- 日志目录自动创建
- 异常回溯和诊断信息

Project: Agent Memory Service
Author: 张小龙 <<EMAIL>>
Created: 2025-06-01
Version: 1.0.0
License: private
"""

import sys
from pathlib import Path
from loguru import logger

from app.config.settings import settings


def setup_logger():
    """设置日志配置"""
    
    # 移除默认的日志处理器
    logger.remove()
    
    # 获取日志配置
    log_config = settings.logging
    
    # 添加控制台日志处理器
    logger.add(
        sys.stdout,
        format=log_config.format,
        level=log_config.level,
        colorize=True,
        backtrace=True,
        diagnose=True
    )
    
    # 创建日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 添加文件日志处理器
    logger.add(
        log_dir / "agentmemory.log",
        format=log_config.format,
        level=log_config.level,
        rotation=log_config.rotation,
        retention=log_config.retention,
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    # 添加错误日志文件
    logger.add(
        log_dir / "error.log",
        format=log_config.format,
        level="ERROR",
        rotation=log_config.rotation,
        retention=log_config.retention,
        compression="zip",
        backtrace=True,
        diagnose=True
    )
    
    logger.info("日志系统初始化完成")


def get_logger(name: str = None):
    """获取日志记录器"""
    if name:
        return logger.bind(name=name)
    return logger


# 初始化日志系统
setup_logger() 