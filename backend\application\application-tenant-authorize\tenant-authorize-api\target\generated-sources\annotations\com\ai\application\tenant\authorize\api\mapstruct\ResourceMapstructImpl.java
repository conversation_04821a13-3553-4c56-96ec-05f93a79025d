package com.ai.application.tenant.authorize.api.mapstruct;

import com.ai.application.tenant.authorize.api.dto.ResourceDTO;
import com.ai.application.tenant.authorize.api.entity.Resource;
import com.ai.application.tenant.authorize.api.vo.ResourceVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-14T11:00:29+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class ResourceMapstructImpl implements ResourceMapstruct {

    @Override
    public Resource toEntity(ResourceDTO dto) {
        if ( dto == null ) {
            return null;
        }

        Resource resource = new Resource();

        resource.setResourceId( dto.getResourceId() );
        resource.setResourceType( dto.getResourceType() );
        resource.setResourceObjectId( dto.getResourceObjectId() );
        resource.setResourceTenantId( dto.getResourceTenantId() );
        resource.setResourceStatus( dto.getResourceStatus() );
        resource.setResourceScope( dto.getResourceScope() );
        resource.setCreateTime( dto.getCreateTime() );
        resource.setUpdateTime( dto.getUpdateTime() );

        return resource;
    }

    @Override
    public List<Resource> toEntityList(List<ResourceDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<Resource> list = new ArrayList<Resource>( dtolist.size() );
        for ( ResourceDTO resourceDTO : dtolist ) {
            list.add( toEntity( resourceDTO ) );
        }

        return list;
    }

    @Override
    public ResourceVO toVo(Resource entity) {
        if ( entity == null ) {
            return null;
        }

        ResourceVO resourceVO = new ResourceVO();

        resourceVO.setResourceId( entity.getResourceId() );
        resourceVO.setResourceType( entity.getResourceType() );
        resourceVO.setResourceObjectId( entity.getResourceObjectId() );
        resourceVO.setResourceTenantId( entity.getResourceTenantId() );
        resourceVO.setResourceStatus( entity.getResourceStatus() );
        resourceVO.setResourceScope( entity.getResourceScope() );
        resourceVO.setCreateTime( entity.getCreateTime() );
        resourceVO.setUpdateTime( entity.getUpdateTime() );

        return resourceVO;
    }

    @Override
    public List<ResourceVO> toVoList(List<Resource> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ResourceVO> list = new ArrayList<ResourceVO>( entities.size() );
        for ( Resource resource : entities ) {
            list.add( toVo( resource ) );
        }

        return list;
    }
}
