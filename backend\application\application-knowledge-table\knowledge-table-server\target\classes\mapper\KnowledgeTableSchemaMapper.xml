<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.knowledge.table.mapper.KnowledgeTableSchemaMapper">

    <select id="list" resultType="com.ai.application.knowledge.table.vo.TableManageListVo"
            parameterType="com.ai.application.knowledge.table.dto.TableManageListDto">
        SELECT
            ks.field_sn AS fieldSn,
            ks.field_name AS fieldName,
            ks.field_type AS fieldType,
            ks.field_desc AS fieldDesc,
            ks.field_key AS fieldKey,
            ks.field_sort AS fieldSort,
            ks.field_status AS fieldStatus,
            ks.create_time AS createTime,
            ks.update_time AS updateTime
        FROM knowledge_table_schema ks
        WHERE ks.field_status != 0
            and ks.table_id = #{tableId}
            <if test="keyword != null and keyword != ''">
                AND (ks.field_name LIKE CONCAT('%', #{keyword}, '%')
                OR ks.field_desc LIKE CONCAT('%', #{keyword}, '%'))
            </if>
            <if test="fieldType != null and fieldType != ''">
                AND ks.field_type = #{fieldType}
            </if>
            <if test="createTimeStart != null and createTimeEnd != null">
                AND ks.create_time BETWEEN #{createTimeStart} AND #{createTimeEnd}
            </if>
            <if test="updateTimeStart != null and updateTimeEnd != null">
                AND ks.update_time BETWEEN #{updateTimeStart} AND #{updateTimeEnd}
            </if>
        ORDER BY ks.field_sort    </select>
</mapper>
