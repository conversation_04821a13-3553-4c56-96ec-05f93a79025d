package com.ai.application.base.log.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 通知模板配置表
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@Schema(name = "通知模板配置表更新DTO")
public class NotificationTemplateUpdateDTO {
    /**
     * 通知模板id
     */
    @Schema(description = "通知模板id")
    @NotNull(message = "通知模板id不能为空")
    private Integer ntplId;

    /**
     * 模板名称
     */
    @Schema(description = "模板名称")
    @NotBlank(message = "模板名称不能为空")
    @Length(max = 20, message = "模板名称长度不能超过20个字符")
    private String ntplName;

    /**
     * 模板内容
     */
    @Schema(description = "模板内容")
    @NotBlank(message = "模板内容不能为空")
    @Length(max = 200, message = "模板内容长度不能超过200个字符")
    private String ntplContent;

    /**
     * 模板配置参数
     */
    @Schema(description = "模板配置参数")
    @Valid
    private TemplateConfigDTO templateConfig;


}