package com.ai.application.admin.mapper;

import com.ai.application.admin.api.entity.TenantDepartment;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 租户部门表 Mapper接口
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Mapper
public interface TenantDepartmentMapper extends BaseMapper<TenantDepartment> {

    @Select("select * from tenant_department where tenant_id=#{tenantId} and parent_id=0 and dept_status >= 0 limit 1")
    TenantDepartment getTenantRootDepartment(@Param("tenantId") Integer tenantId);

    @Select("select * from tenant_department where tenant_id=#{tenantId} and parent_id=#{parentId} and dept_status >= 0 order by dept_sort")
    List<TenantDepartment> queryTenantSubDepartment(@Param("tenantId") Integer tenantId,@Param("parentId") Integer parentId);

    @Select("select count() from app_user where tenant_id=#{tenantId} and dept_id=#{deptId} and user_status >= 0")
    int getDepartmentUserCount(@Param("tenantId") Integer tenantId,@Param("deptId") Integer deptId);
}
