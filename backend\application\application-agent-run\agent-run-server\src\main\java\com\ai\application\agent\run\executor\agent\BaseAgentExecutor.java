package com.ai.application.agent.run.executor.agent;

import com.ai.application.agent.run.executor.AgentExecutionContext;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Agent 执行器基类
 */
@Slf4j
public abstract class BaseAgentExecutor {

    /**
     * 执行智能体逻辑
     *
     * @param executionContext 执行上下文
     * @return 执行结果
     */
    public abstract Map<String, Object> execute(AgentExecutionContext executionContext);

    /**
     * 获取智能体类型
     */
    public abstract String getAgentType();

    /**
     * 创建新的会话编号（普通模式）
     */
    protected String newSessionSn(String authorization, String agentSn) {
        // 这里应该调用实际的会话服务创建会话
        // 目前返回模拟的会话编号
        return "session_" + System.currentTimeMillis();
    }

    /**
     * 创建新的会话编号（调试模式）
     */
    protected String newDebugSessionSn(String authorization, String agentSn, String modelSn, String prompt, Double temperature) {
        // 这里应该调用实际的会话服务创建调试会话
        // 目前返回模拟的调试会话编号
        return "debug_session_" + System.currentTimeMillis();
    }
}
