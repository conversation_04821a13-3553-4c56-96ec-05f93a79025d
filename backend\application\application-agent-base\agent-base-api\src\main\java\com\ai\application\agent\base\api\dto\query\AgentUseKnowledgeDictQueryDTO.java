package com.ai.application.agent.base.api.dto.query;

import com.ai.framework.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Schema(name = "AgentUseDicQueryDTO")
@Data
public class AgentUseKnowledgeDictQueryDTO extends PageParam {
    @Schema(description = "词典ID")
    private String dictId;
}
