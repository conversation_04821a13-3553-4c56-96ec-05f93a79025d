package com.ai.application.app.auth.granter;

import com.ai.framework.core.util.spring.SpringUtil;
import lombok.AllArgsConstructor;
import org.springframework.util.Assert;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * TokenGranterBuilder
 */
@AllArgsConstructor
public class TokenGranterBuilder {

    /**
     * TokenGranter缓存池
     */
    private static final Map<Integer, ITokenGranter> GRANTER_POOL = new ConcurrentHashMap<>();

    static {
        GRANTER_POOL.put(PasswordTokenGranter.GRANT_TYPE, SpringUtil.getBean(PasswordTokenGranter.class));
    }

    /**
     * 获取 TokenGranter
     *
     * @param grantType 授权类型
     * @return ITokenGranter
     */
    public static ITokenGranter getGranter(Integer grantType) {
        ITokenGranter tokenGranter = GRANTER_POOL.get(grantType);
        Assert.notNull(tokenGranter, "no grantType was found");
        return tokenGranter;
    }
}
