Index: application/application-agent-run/agent-run-server/pom.xml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+><?xml version=\"1.0\" encoding=\"UTF-8\"?>\r\n<project xmlns=\"http://maven.apache.org/POM/4.0.0\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\r\n\txsi:schemaLocation=\"http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd\">\r\n\t<modelVersion>4.0.0</modelVersion>\r\n\r\n\t<parent>\r\n\t\t<groupId>com.ai.application</groupId>\r\n\t\t<artifactId>application-agent-run</artifactId>\r\n\t\t<version>${revision}</version>\r\n\t\t<relativePath>../pom.xml</relativePath>\r\n\t</parent>\r\n\r\n\t<artifactId>agent-run-server</artifactId>\r\n\t<name>${project.artifactId}</name>\r\n\t<version>${revision}</version>\r\n\t<packaging>jar</packaging>\r\n\r\n\t<dependencies>\r\n\t\t<dependency>\r\n\t\t\t<groupId>com.ai.application</groupId>\r\n\t\t\t<artifactId>agent-run-api</artifactId>\r\n\t\t</dependency>\r\n\r\n\t\t<dependency>\r\n\t\t\t<groupId>com.ai.application</groupId>\r\n\t\t\t<artifactId>base-model-api</artifactId>\r\n\t\t</dependency>\r\n\r\n\t\t<dependency>\r\n\t\t\t<groupId>com.ai.framework.workflow</groupId>\r\n\t\t\t<artifactId>framework-workflow</artifactId>\r\n\t\t</dependency>\r\n\r\n\t\t<dependency>\r\n\t\t\t<groupId>org.springframework.boot</groupId>\r\n\t\t\t<artifactId>spring-boot-starter-test</artifactId>\r\n\t\t\t<scope>test</scope>\r\n\t\t\t<exclusions>\r\n\t\t\t\t<exclusion>\r\n\t\t\t\t\t<groupId>org.junit.vintage</groupId>\r\n\t\t\t\t\t<artifactId>junit-vintage-engine</artifactId>\r\n\t\t\t\t</exclusion>\r\n\t\t\t</exclusions>\r\n\t\t</dependency>\r\n\t</dependencies>\r\n\r\n\t<build>\r\n\t\t<plugins>\r\n\t\t\t<plugin>\r\n\t\t\t\t<groupId>org.springframework.boot</groupId>\r\n\t\t\t\t<artifactId>spring-boot-maven-plugin</artifactId>\r\n\t\t\t\t<version>${spring.maven.version}</version>\r\n\t\t\t\t<executions>\r\n\t\t\t\t\t<execution>\r\n\t\t\t\t\t\t<goals>\r\n\t\t\t\t\t\t\t<goal>repackage</goal>\r\n\t\t\t\t\t\t</goals>\r\n\t\t\t\t\t</execution>\r\n\t\t\t\t</executions>\r\n\t\t\t</plugin>\r\n\t\t</plugins>\r\n\t</build>\r\n\r\n</project>\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/application/application-agent-run/agent-run-server/pom.xml b/application/application-agent-run/agent-run-server/pom.xml
--- a/application/application-agent-run/agent-run-server/pom.xml	(revision 27ed3acd0bd29b025bcb2499817b2608a5835ce1)
+++ b/application/application-agent-run/agent-run-server/pom.xml	(date 1749808772812)
@@ -42,6 +42,15 @@
 				</exclusion>
 			</exclusions>
 		</dependency>
+
+        <dependency>
+            <groupId>com.ai.application</groupId>
+            <artifactId>base-file-api</artifactId>
+        </dependency>
+		<dependency>
+			<groupId>com.ai.application</groupId>
+			<artifactId>knowledge-doc-server</artifactId>
+		</dependency>
 	</dependencies>
 
 	<build>
Index: application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/errors/ExecutorError.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.ai.application.agent.run.errors;\r\n\r\n\r\nimport com.ai.framework.core.enums.IErrorCode;\r\n\r\n/**\r\n * 执行器错误枚举\r\n * 统一管理 Agent 和 LLM 节点执行器的错误码\r\n */\r\npublic enum ExecutorError implements IErrorCode {\r\n\r\n    // ==================== Agent 节点执行器错误 (51001-51020) ====================\r\n    \r\n    /**\r\n     * Agent节点缺少输入参数配置\r\n     */\r\n    AGENT_NODE_MISSING_INPUT_PARAMETERS(51001, \"Agent节点缺少inputParameters配置\"),\r\n\r\n    /**\r\n     * Agent节点执行错误\r\n     */\r\n    AGENT_NODE_EXECUTOR_ERROR(51002, \"Agent节点执行错误\"),\r\n\r\n    /**\r\n     * Agent类型为空\r\n     */\r\n    AGENT_TYPE_IS_NULL(51003, \"agentType 不能为空\"),\r\n\r\n    /**\r\n     * Agent消息内容为空\r\n     */\r\n    AGENT_MSG_CONTENT_IS_NULL(51004, \"msgContent 不能为空\"),\r\n\r\n    /**\r\n     * Agent返回结果为空\r\n     */\r\n    AGENT_RESPONSE_IS_NULL(51005, \"Agent返回结果为空\"),\r\n\r\n\r\n\r\n    // ==================== LLM 节点执行器错误 (51021-51040) ====================\r\n\r\n    /**\r\n     * LLM节点缺少输入参数配置\r\n     */\r\n    LLM_NODE_MISSING_INPUT_PARAMETERS(51021, \"LLM节点缺少inputParameters配置\"),\r\n\r\n    /**\r\n     * prompt 不能为空\r\n     */\r\n    LLM_PROMPT_IS_BLANK(51022, \"prompt 不能为空\"),\r\n\r\n    /**\r\n     * model 不能为空\r\n     */\r\n    LLM_MODEL_IS_BLANK(51023, \"model 不能为空\"),\r\n\r\n    /**\r\n     * 调用大模型失败\r\n     */\r\n    LLM_CALL_FAILED(51024, \"调用大模型失败\"),\r\n\r\n    /**\r\n     * 大模型返回结果为空\r\n     */\r\n    LLM_RESPONSE_IS_NULL(51025, \"大模型返回结果为空\")\r\n\r\n    ;\r\n\r\n    private final int code;\r\n    private final String message;\r\n\r\n    ExecutorError(int code, String message) {\r\n        this.code = code;\r\n        this.message = message;\r\n    }\r\n\r\n    @Override\r\n    public Integer getCode() {\r\n        return code;\r\n    }\r\n\r\n    @Override\r\n    public String getMessage() {\r\n        return message;\r\n    }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/errors/ExecutorError.java b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/errors/ExecutorError.java
--- a/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/errors/ExecutorError.java	(revision 27ed3acd0bd29b025bcb2499817b2608a5835ce1)
+++ b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/errors/ExecutorError.java	(date 1749816769250)
@@ -63,7 +63,112 @@
     /**
      * 大模型返回结果为空
      */
-    LLM_RESPONSE_IS_NULL(51025, "大模型返回结果为空")
+    LLM_RESPONSE_IS_NULL(51025, "大模型返回结果为空"),
+
+    // ==================== 知识添加执行器错误 (51061-51080) ====================
+
+    /**
+     * 知识库编号为空
+     */
+    KNOWLEDGE_INVENTORY_SN_IS_NULL(51061, "知识库编号不能为空"),
+
+    /**
+     * 知识库不存在
+     */
+    KNOWLEDGE_INVENTORY_NOT_FOUND(51062, "知识库不存在"),
+
+    /**
+     * 文件嵌入失败
+     */
+    KNOWLEDGE_FILE_EMBEDDING_FAILED(51063, "文件embedding失败"),
+
+    /**
+     * 文件嵌入状态查询失败
+     */
+    KNOWLEDGE_FILE_STATUS_QUERY_FAILED(51064, "文件embedding状态查询失败"),
+
+    /**
+     * 文件上传失败
+     */
+    KNOWLEDGE_FILE_UPLOAD_FAILED(51065, "文件上传失败"),
+
+    /**
+     * 分段规则配置错误
+     */
+    KNOWLEDGE_SPLIT_RULE_CONFIG_ERROR(51066, "分段规则配置错误"),
+
+    /**
+     * 深度解析参数错误
+     */
+    KNOWLEDGE_DEEP_PARSE_PARAM_ERROR(51067, "深度解析参数错误"),
+
+    /**
+     * 文件重复检查失败
+     */
+    KNOWLEDGE_FILE_DUPLICATE_CHECK_FAILED(51068, "文件重复检查失败"),
+
+    /**
+     * 文件删除失败
+     */
+    KNOWLEDGE_FILE_DELETE_FAILED(51069, "文件删除失败"),
+
+    /**
+     * 嵌入轮询超时
+     */
+    KNOWLEDGE_EMBEDDING_POLLING_TIMEOUT(51070, "嵌入轮询超时"),
+
+    KNOWLEDGE_INVENTORY_IS_NOT_FOUND(51071,"知识库未找到"),
+    // ==================== 通用执行器错误 (51041-51060) ====================
+
+    /**
+     * 节点定义为空
+     */
+    NODE_DEFINITION_IS_NULL(51041, "节点定义为空"),
+
+    /**
+     * 节点类型不支持
+     */
+    NODE_TYPE_NOT_SUPPORTED(51042, "节点类型不支持"),
+
+    /**
+     * 参数解析错误
+     */
+    PARAMETER_PARSE_ERROR(51043, "参数解析错误"),
+
+    /**
+     * 变量替换错误
+     */
+    VARIABLE_REPLACEMENT_ERROR(51044, "变量替换错误"),
+
+    /**
+     * 输出参数映射错误
+     */
+    OUTPUT_PARAMETER_MAPPING_ERROR(51045, "输出参数映射错误"),
+
+    /**
+     * 节点执行超时
+     */
+    NODE_EXECUTION_TIMEOUT(51046, "节点执行超时"),
+
+    /**
+     * 节点执行被中断
+     */
+    NODE_EXECUTION_INTERRUPTED(51047, "节点执行被中断"),
+
+    /**
+     * 工作流上下文为空
+     */
+    WORKFLOW_CONTEXT_IS_NULL(51048, "工作流上下文为空"),
+
+    /**
+     * 节点上下文为空
+     */
+    NODE_CONTEXT_IS_NULL(51049, "节点上下文为空"),
+
+    /**
+     * 执行器初始化失败
+     */
+    EXECUTOR_INITIALIZATION_FAILED(51050, "执行器初始化失败")
 
     ;
 
Index: application/application-base-file/base-file-api/src/main/java/com/ai/application/base/file/api/dto/FileInfoDto.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.ai.application.base.file.api.dto;\r\n\r\nimport lombok.AllArgsConstructor;\r\nimport lombok.Builder;\r\nimport lombok.Data;\r\nimport lombok.NoArgsConstructor;\r\n\r\n@Data\r\n@Builder\r\n@AllArgsConstructor\r\n@NoArgsConstructor\r\npublic class FileInfoDto {\r\n\r\n    private String fileSn;\r\n\r\n    private String fileName;\r\n\r\n    private String fileType;\r\n\r\n    private String fileMd5;\r\n\r\n    private String docType;\r\n\r\n    private String tags;\r\n\r\n    private String formatFileSn;\r\n\r\n    private Long fileId;\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/application/application-base-file/base-file-api/src/main/java/com/ai/application/base/file/api/dto/FileInfoDto.java b/application/application-base-file/base-file-api/src/main/java/com/ai/application/base/file/api/dto/FileInfoDto.java
--- a/application/application-base-file/base-file-api/src/main/java/com/ai/application/base/file/api/dto/FileInfoDto.java	(revision 27ed3acd0bd29b025bcb2499817b2608a5835ce1)
+++ b/application/application-base-file/base-file-api/src/main/java/com/ai/application/base/file/api/dto/FileInfoDto.java	(date 1749816515202)
@@ -15,7 +15,7 @@
 
     private String fileName;
 
-    private String fileType;
+    private Integer fileType;
 
     private String fileMd5;
 
@@ -25,5 +25,7 @@
 
     private String formatFileSn;
 
-    private Long fileId;
+    private Integer fileId;
+
+    private Integer status;
 }
Index: application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/service/IKnowledgeService.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/service/IKnowledgeService.java b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/service/IKnowledgeService.java
new file mode 100644
--- /dev/null	(date 1749808647468)
+++ b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/service/IKnowledgeService.java	(date 1749808647468)
@@ -0,0 +1,94 @@
+package com.ai.application.agent.run.service;
+
+import com.ai.application.agent.run.dto.KnowledgeFileEmbeddingDTO;
+import com.ai.application.base.file.api.dto.FileInfoDto;
+import com.ai.framework.core.vo.ResultVo;
+
+/**
+ * 知识库服务接口
+ */
+public interface IKnowledgeService {
+
+    /**
+     * 检查知识库是否存在
+     *
+     * @param knowledgeInventory 知识库编号
+     * @return 知识库信息
+     */
+    KnowledgeInventoryInfo checkKnowledgeInventory(String knowledgeInventory);
+
+    /**
+     * 文件嵌入处理
+     *
+     * @param embeddingDto 嵌入请求
+     * @param authorization 授权头
+     * @return 嵌入结果
+     */
+    ResultVo<FileInfoDto> fileEmbedding(KnowledgeFileEmbeddingDTO embeddingDto, String authorization);
+
+    /**
+     * 查询文件嵌入状态
+     *
+     * @param fileId 文件ID
+     * @return 文件状态信息
+     */
+    ResultVo<FileStatusInfo> getFileStatus(Long fileId);
+
+    /**
+     * 根据知识库ID和MD5查询文件
+     *
+     * @param knowledgeInventory 知识库编号
+     * @param fileMd5 文件MD5
+     * @return 文件列表
+     */
+    ResultVo<java.util.List<FileInfoDto>> findFilesByDatasetIdAndMd5(String knowledgeInventory, String fileMd5);
+
+    /**
+     * 删除文件
+     *
+     * @param fileSn 文件编号
+     * @param datasetId 知识库ID
+     * @return 删除结果
+     */
+    ResultVo<String> deleteFile(String fileSn, String datasetId);
+
+    /**
+     * 知识库信息
+     */
+    class KnowledgeInventoryInfo {
+        private String modelSn;
+        private Integer splitRule;
+        private Integer splitter;
+        private Integer wordCountLimit;
+        private Integer wordCountOverlap;
+        private String separatorContent;
+
+        // Getters and Setters
+        public String getModelSn() { return modelSn; }
+        public void setModelSn(String modelSn) { this.modelSn = modelSn; }
+        public Integer getSplitRule() { return splitRule; }
+        public void setSplitRule(Integer splitRule) { this.splitRule = splitRule; }
+        public Integer getSplitter() { return splitter; }
+        public void setSplitter(Integer splitter) { this.splitter = splitter; }
+        public Integer getWordCountLimit() { return wordCountLimit; }
+        public void setWordCountLimit(Integer wordCountLimit) { this.wordCountLimit = wordCountLimit; }
+        public Integer getWordCountOverlap() { return wordCountOverlap; }
+        public void setWordCountOverlap(Integer wordCountOverlap) { this.wordCountOverlap = wordCountOverlap; }
+        public String getSeparatorContent() { return separatorContent; }
+        public void setSeparatorContent(String separatorContent) { this.separatorContent = separatorContent; }
+    }
+
+    /**
+     * 文件状态信息
+     */
+    class FileStatusInfo {
+        private Integer status; // 1-处理中, 2-成功, 3-失败
+        private String errorReason;
+
+        // Getters and Setters
+        public Integer getStatus() { return status; }
+        public void setStatus(Integer status) { this.status = status; }
+        public String getErrorReason() { return errorReason; }
+        public void setErrorReason(String errorReason) { this.errorReason = errorReason; }
+    }
+}
Index: application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/service/impl/KnowledgeServiceImpl.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/service/impl/KnowledgeServiceImpl.java b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/service/impl/KnowledgeServiceImpl.java
new file mode 100644
--- /dev/null	(date 1749808647459)
+++ b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/service/impl/KnowledgeServiceImpl.java	(date 1749808647459)
@@ -0,0 +1,139 @@
+package com.ai.application.agent.run.service.impl;
+
+import com.ai.application.agent.run.dto.KnowledgeFileEmbeddingDTO;
+import com.ai.application.agent.run.service.IKnowledgeService;
+import com.ai.application.base.file.api.dto.FileInfoDto;
+import com.ai.framework.core.util.json.JsonUtils;
+import com.ai.framework.core.vo.ResultVo;
+import lombok.extern.slf4j.Slf4j;
+import org.springframework.stereotype.Service;
+
+import java.util.ArrayList;
+import java.util.List;
+
+/**
+ * 知识库服务实现类
+ */
+@Slf4j
+@Service
+public class KnowledgeServiceImpl implements IKnowledgeService {
+
+    @Override
+    public KnowledgeInventoryInfo checkKnowledgeInventory(String knowledgeInventory) {
+        log.info("KnowledgeService checkKnowledgeInventory start, knowledgeInventory: {}", knowledgeInventory);
+
+        // 模拟检查知识库是否存在（实际应该调用真实的知识库服务）
+        if ("invalid_kb".equals(knowledgeInventory)) {
+            throw new RuntimeException("知识库不存在: " + knowledgeInventory);
+        }
+
+        // 模拟返回知识库信息
+        KnowledgeInventoryInfo info = new KnowledgeInventoryInfo();
+        info.setModelSn("default-embedding-model");
+        info.setSplitRule(1);
+        info.setSplitter(1);
+        info.setWordCountLimit(500);
+        info.setWordCountOverlap(50);
+        info.setSeparatorContent("[\"\n\", \"\n\n\", \" \"]");
+
+        log.info("KnowledgeService checkKnowledgeInventory success, info: {}", JsonUtils.toJsonString(info));
+        return info;
+    }
+
+    @Override
+    public ResultVo<FileInfoDto> fileEmbedding(KnowledgeFileEmbeddingDTO embeddingDto, String authorization) {
+        log.info("KnowledgeService fileEmbedding start, request: {}", JsonUtils.toJsonString(embeddingDto));
+
+        try {
+            // 模拟文件嵌入处理（实际应该调用真实的嵌入服务）
+            FileInfoDto fileInfo = embeddingDto.getFileInfo();
+            
+            // 模拟嵌入处理
+            FileInfoDto result = new FileInfoDto();
+            result.setFileId(System.currentTimeMillis()); // 模拟生成文件ID
+            result.setFileSn(fileInfo.getFileSn());
+            result.setFileName(fileInfo.getFileName());
+            result.setFileType(fileInfo.getFileType());
+            result.setFileMd5(fileInfo.getFileMd5());
+            result.setDocType(fileInfo.getDocType());
+            result.setFormatFileSn(fileInfo.getFormatFileSn());
+
+            log.info("KnowledgeService fileEmbedding success, result: {}", JsonUtils.toJsonString(result));
+            return ResultVo.data(result);
+
+        } catch (Exception e) {
+            log.error("KnowledgeService fileEmbedding error", e);
+            return ResultVo.fail("文件嵌入失败: " + e.getMessage());
+        }
+    }
+
+    @Override
+    public ResultVo<FileStatusInfo> getFileStatus(Long fileId) {
+        log.info("KnowledgeService getFileStatus start, fileId: {}", fileId);
+
+        try {
+            // 模拟查询文件状态（实际应该调用真实的文件服务）
+            FileStatusInfo statusInfo = new FileStatusInfo();
+            
+            // 模拟不同的状态
+            if (fileId % 3 == 0) {
+                statusInfo.setStatus(3); // 失败
+                statusInfo.setErrorReason("模拟嵌入失败");
+            } else {
+                statusInfo.setStatus(2); // 成功
+            }
+
+            log.info("KnowledgeService getFileStatus success, status: {}", JsonUtils.toJsonString(statusInfo));
+            return ResultVo.data(statusInfo);
+
+        } catch (Exception e) {
+            log.error("KnowledgeService getFileStatus error", e);
+            return ResultVo.fail("查询文件状态失败: " + e.getMessage());
+        }
+    }
+
+    @Override
+    public ResultVo<List<FileInfoDto>> findFilesByDatasetIdAndMd5(String knowledgeInventory, String fileMd5) {
+        log.info("KnowledgeService findFilesByDatasetIdAndMd5 start, knowledgeInventory: {}, fileMd5: {}", 
+                knowledgeInventory, fileMd5);
+
+        try {
+            // 模拟查询重复文件（实际应该调用真实的文件服务）
+            List<FileInfoDto> files = new ArrayList<>();
+            
+            // 模拟某些MD5已存在
+            if ("duplicate_md5".equals(fileMd5)) {
+                FileInfoDto existingFile = new FileInfoDto();
+                existingFile.setFileId(12345L);
+                existingFile.setFileSn("existing_file_sn");
+                existingFile.setFileName("existing_file.txt");
+                existingFile.setFileType("text/plain");
+                existingFile.setFileMd5(fileMd5);
+                existingFile.setDocType("txt");
+                files.add(existingFile);
+            }
+
+            log.info("KnowledgeService findFilesByDatasetIdAndMd5 success, found {} files", files.size());
+            return ResultVo.data(files);
+
+        } catch (Exception e) {
+            log.error("KnowledgeService findFilesByDatasetIdAndMd5 error", e);
+            return ResultVo.fail("查询重复文件失败: " + e.getMessage());
+        }
+    }
+
+    @Override
+    public ResultVo<String> deleteFile(String fileSn, String datasetId) {
+        log.info("KnowledgeService deleteFile start, fileSn: {}, datasetId: {}", fileSn, datasetId);
+
+        try {
+            // 模拟删除文件（实际应该调用真实的文件服务）
+            log.info("KnowledgeService deleteFile success, fileSn: {}", fileSn);
+            return ResultVo.data("删除成功");
+
+        } catch (Exception e) {
+            log.error("KnowledgeService deleteFile error", e);
+            return ResultVo.fail("删除文件失败: " + e.getMessage());
+        }
+    }
+}
Index: application/application-agent-run/agent-run-server/src/test/java/com/ai/application/agent/run/errors/ExecutorErrorTest.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/application/application-agent-run/agent-run-server/src/test/java/com/ai/application/agent/run/errors/ExecutorErrorTest.java b/application/application-agent-run/agent-run-server/src/test/java/com/ai/application/agent/run/errors/ExecutorErrorTest.java
new file mode 100644
--- /dev/null	(date 1749808647449)
+++ b/application/application-agent-run/agent-run-server/src/test/java/com/ai/application/agent/run/errors/ExecutorErrorTest.java	(date 1749808647449)
@@ -0,0 +1,168 @@
+package com.ai.application.agent.run.errors;
+
+import org.junit.jupiter.api.Test;
+
+import static org.junit.jupiter.api.Assertions.*;
+
+/**
+ * 执行器错误枚举单元测试
+ */
+class ExecutorErrorTest {
+
+    @Test
+    void testAgentNodeExecutorErrors() {
+        // 测试 Agent 节点执行器错误
+        assertEquals(51001, ExecutorError.AGENT_NODE_MISSING_INPUT_PARAMETERS.getCode());
+        assertEquals("Agent节点缺少inputParameters配置", ExecutorError.AGENT_NODE_MISSING_INPUT_PARAMETERS.getMessage());
+
+        assertEquals(51002, ExecutorError.AGENT_NODE_EXECUTOR_ERROR.getCode());
+        assertEquals("Agent节点执行错误", ExecutorError.AGENT_NODE_EXECUTOR_ERROR.getMessage());
+
+        assertEquals(51003, ExecutorError.AGENT_TYPE_IS_NULL.getCode());
+        assertEquals("agentType 不能为空", ExecutorError.AGENT_TYPE_IS_NULL.getMessage());
+
+        assertEquals(51004, ExecutorError.AGENT_MSG_CONTENT_IS_NULL.getCode());
+        assertEquals("msgContent 不能为空", ExecutorError.AGENT_MSG_CONTENT_IS_NULL.getMessage());
+
+        assertEquals(51005, ExecutorError.AGENT_RESPONSE_IS_NULL.getCode());
+        assertEquals("Agent返回结果为空", ExecutorError.AGENT_RESPONSE_IS_NULL.getMessage());
+    }
+
+    @Test
+    void testLlmNodeExecutorErrors() {
+        // 测试 LLM 节点执行器错误
+        assertEquals(51021, ExecutorError.LLM_NODE_MISSING_INPUT_PARAMETERS.getCode());
+        assertEquals("LLM节点缺少inputParameters配置", ExecutorError.LLM_NODE_MISSING_INPUT_PARAMETERS.getMessage());
+
+        assertEquals(51022, ExecutorError.LLM_PROMPT_IS_BLANK.getCode());
+        assertEquals("prompt 不能为空", ExecutorError.LLM_PROMPT_IS_BLANK.getMessage());
+
+        assertEquals(51023, ExecutorError.LLM_MODEL_IS_BLANK.getCode());
+        assertEquals("model 不能为空", ExecutorError.LLM_MODEL_IS_BLANK.getMessage());
+
+        assertEquals(51024, ExecutorError.LLM_CALL_FAILED.getCode());
+        assertEquals("调用大模型失败", ExecutorError.LLM_CALL_FAILED.getMessage());
+
+        assertEquals(51025, ExecutorError.LLM_RESPONSE_IS_NULL.getCode());
+        assertEquals("大模型返回结果为空", ExecutorError.LLM_RESPONSE_IS_NULL.getMessage());
+    }
+
+    @Test
+    void testKnowledgeAddExecutorErrors() {
+        // 测试知识添加执行器错误
+        assertEquals(51061, ExecutorError.KNOWLEDGE_INVENTORY_SN_IS_NULL.getCode());
+        assertEquals("知识库编号不能为空", ExecutorError.KNOWLEDGE_INVENTORY_SN_IS_NULL.getMessage());
+
+        assertEquals(51062, ExecutorError.KNOWLEDGE_INVENTORY_NOT_FOUND.getCode());
+        assertEquals("知识库不存在", ExecutorError.KNOWLEDGE_INVENTORY_NOT_FOUND.getMessage());
+
+        assertEquals(51063, ExecutorError.KNOWLEDGE_FILE_EMBEDDING_FAILED.getCode());
+        assertEquals("文件嵌入失败", ExecutorError.KNOWLEDGE_FILE_EMBEDDING_FAILED.getMessage());
+
+        assertEquals(51064, ExecutorError.KNOWLEDGE_FILE_STATUS_QUERY_FAILED.getCode());
+        assertEquals("文件嵌入状态查询失败", ExecutorError.KNOWLEDGE_FILE_STATUS_QUERY_FAILED.getMessage());
+
+        assertEquals(51065, ExecutorError.KNOWLEDGE_FILE_UPLOAD_FAILED.getCode());
+        assertEquals("文件上传失败", ExecutorError.KNOWLEDGE_FILE_UPLOAD_FAILED.getMessage());
+
+        assertEquals(51066, ExecutorError.KNOWLEDGE_SPLIT_RULE_CONFIG_ERROR.getCode());
+        assertEquals("分段规则配置错误", ExecutorError.KNOWLEDGE_SPLIT_RULE_CONFIG_ERROR.getMessage());
+
+        assertEquals(51067, ExecutorError.KNOWLEDGE_DEEP_PARSE_PARAM_ERROR.getCode());
+        assertEquals("深度解析参数错误", ExecutorError.KNOWLEDGE_DEEP_PARSE_PARAM_ERROR.getMessage());
+
+        assertEquals(51068, ExecutorError.KNOWLEDGE_FILE_DUPLICATE_CHECK_FAILED.getCode());
+        assertEquals("文件重复检查失败", ExecutorError.KNOWLEDGE_FILE_DUPLICATE_CHECK_FAILED.getMessage());
+
+        assertEquals(51069, ExecutorError.KNOWLEDGE_FILE_DELETE_FAILED.getCode());
+        assertEquals("文件删除失败", ExecutorError.KNOWLEDGE_FILE_DELETE_FAILED.getMessage());
+
+        assertEquals(51070, ExecutorError.KNOWLEDGE_EMBEDDING_POLLING_TIMEOUT.getCode());
+        assertEquals("嵌入轮询超时", ExecutorError.KNOWLEDGE_EMBEDDING_POLLING_TIMEOUT.getMessage());
+    }
+
+    @Test
+    void testCommonExecutorErrors() {
+        // 测试通用执行器错误
+        assertEquals(51041, ExecutorError.NODE_DEFINITION_IS_NULL.getCode());
+        assertEquals("节点定义为空", ExecutorError.NODE_DEFINITION_IS_NULL.getMessage());
+
+        assertEquals(51042, ExecutorError.NODE_TYPE_NOT_SUPPORTED.getCode());
+        assertEquals("节点类型不支持", ExecutorError.NODE_TYPE_NOT_SUPPORTED.getMessage());
+
+        assertEquals(51043, ExecutorError.PARAMETER_PARSE_ERROR.getCode());
+        assertEquals("参数解析错误", ExecutorError.PARAMETER_PARSE_ERROR.getMessage());
+
+        assertEquals(51044, ExecutorError.VARIABLE_REPLACEMENT_ERROR.getCode());
+        assertEquals("变量替换错误", ExecutorError.VARIABLE_REPLACEMENT_ERROR.getMessage());
+
+        assertEquals(51045, ExecutorError.OUTPUT_PARAMETER_MAPPING_ERROR.getCode());
+        assertEquals("输出参数映射错误", ExecutorError.OUTPUT_PARAMETER_MAPPING_ERROR.getMessage());
+
+        assertEquals(51046, ExecutorError.NODE_EXECUTION_TIMEOUT.getCode());
+        assertEquals("节点执行超时", ExecutorError.NODE_EXECUTION_TIMEOUT.getMessage());
+
+        assertEquals(51047, ExecutorError.NODE_EXECUTION_INTERRUPTED.getCode());
+        assertEquals("节点执行被中断", ExecutorError.NODE_EXECUTION_INTERRUPTED.getMessage());
+
+        assertEquals(51048, ExecutorError.WORKFLOW_CONTEXT_IS_NULL.getCode());
+        assertEquals("工作流上下文为空", ExecutorError.WORKFLOW_CONTEXT_IS_NULL.getMessage());
+
+        assertEquals(51049, ExecutorError.NODE_CONTEXT_IS_NULL.getCode());
+        assertEquals("节点上下文为空", ExecutorError.NODE_CONTEXT_IS_NULL.getMessage());
+
+        assertEquals(51050, ExecutorError.EXECUTOR_INITIALIZATION_FAILED.getCode());
+        assertEquals("执行器初始化失败", ExecutorError.EXECUTOR_INITIALIZATION_FAILED.getMessage());
+    }
+
+    @Test
+    void testErrorCodeRanges() {
+        // 验证错误码范围分配
+        // Agent 错误: 51001-51020
+        assertTrue(ExecutorError.AGENT_NODE_MISSING_INPUT_PARAMETERS.getCode() >= 51001);
+        assertTrue(ExecutorError.AGENT_RESPONSE_IS_NULL.getCode() <= 51020);
+
+        // LLM 错误: 51021-51040
+        assertTrue(ExecutorError.LLM_NODE_MISSING_INPUT_PARAMETERS.getCode() >= 51021);
+        assertTrue(ExecutorError.LLM_RESPONSE_IS_NULL.getCode() <= 51040);
+
+        // 通用错误: 51041-51060
+        assertTrue(ExecutorError.NODE_DEFINITION_IS_NULL.getCode() >= 51041);
+        assertTrue(ExecutorError.EXECUTOR_INITIALIZATION_FAILED.getCode() <= 51060);
+
+        // 知识添加错误: 51061-51080
+        assertTrue(ExecutorError.KNOWLEDGE_INVENTORY_SN_IS_NULL.getCode() >= 51061);
+        assertTrue(ExecutorError.KNOWLEDGE_EMBEDDING_POLLING_TIMEOUT.getCode() <= 51080);
+    }
+
+    @Test
+    void testErrorCodeUniqueness() {
+        // 验证所有错误码都是唯一的
+        ExecutorError[] errors = ExecutorError.values();
+        for (int i = 0; i < errors.length; i++) {
+            for (int j = i + 1; j < errors.length; j++) {
+                assertNotEquals(errors[i].getCode(), errors[j].getCode(),
+                    String.format("错误码重复: %s 和 %s 都使用了错误码 %d",
+                        errors[i].name(), errors[j].name(), errors[i].getCode()));
+            }
+        }
+    }
+
+    @Test
+    void testErrorMessageNotEmpty() {
+        // 验证所有错误消息都不为空
+        for (ExecutorError error : ExecutorError.values()) {
+            assertNotNull(error.getMessage(), "错误消息不能为空: " + error.name());
+            assertFalse(error.getMessage().trim().isEmpty(), "错误消息不能为空字符串: " + error.name());
+        }
+    }
+
+    @Test
+    void testErrorEnumImplementsIErrorCode() {
+        // 验证枚举实现了 IErrorCode 接口
+        assertTrue(ExecutorError.AGENT_NODE_MISSING_INPUT_PARAMETERS instanceof com.ai.framework.core.enums.IErrorCode);
+        assertTrue(ExecutorError.LLM_PROMPT_IS_BLANK instanceof com.ai.framework.core.enums.IErrorCode);
+        assertTrue(ExecutorError.NODE_DEFINITION_IS_NULL instanceof com.ai.framework.core.enums.IErrorCode);
+        assertTrue(ExecutorError.KNOWLEDGE_INVENTORY_SN_IS_NULL instanceof com.ai.framework.core.enums.IErrorCode);
+    }
+}
Index: application/application-agent-run/agent-run-server/src/test/java/com/ai/application/agent/run/executor/KnowledgeAddNodeExecutorTest.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/application/application-agent-run/agent-run-server/src/test/java/com/ai/application/agent/run/executor/KnowledgeAddNodeExecutorTest.java b/application/application-agent-run/agent-run-server/src/test/java/com/ai/application/agent/run/executor/KnowledgeAddNodeExecutorTest.java
new file mode 100644
--- /dev/null	(date 1749808647506)
+++ b/application/application-agent-run/agent-run-server/src/test/java/com/ai/application/agent/run/executor/KnowledgeAddNodeExecutorTest.java	(date 1749808647506)
@@ -0,0 +1,322 @@
+package com.ai.application.agent.run.executor;
+
+import com.ai.application.agent.run.errors.ExecutorError;
+import com.ai.application.agent.run.feign.IFileProcessClient;
+import com.ai.application.agent.run.service.IKnowledgeService;
+import com.ai.application.base.file.api.dto.FileInfoDto;
+import com.ai.framework.core.exception.ServiceException;
+import com.ai.framework.core.vo.ResultVo;
+import com.ai.framework.workflow.context.NodeContext;
+import com.ai.framework.workflow.context.WorkflowContext;
+import com.ai.framework.workflow.enums.NodeStatus;
+import org.junit.jupiter.api.BeforeEach;
+import org.junit.jupiter.api.Test;
+import org.junit.jupiter.api.extension.ExtendWith;
+import org.mockito.InjectMocks;
+import org.mockito.Mock;
+import org.mockito.junit.jupiter.MockitoExtension;
+
+import java.util.*;
+
+import static org.junit.jupiter.api.Assertions.*;
+import static org.mockito.ArgumentMatchers.*;
+import static org.mockito.Mockito.*;
+
+/**
+ * 知识添加节点执行器单元测试
+ */
+@ExtendWith(MockitoExtension.class)
+class KnowledgeAddNodeExecutorTest {
+
+    @Mock
+    private IKnowledgeService knowledgeService;
+
+    @Mock
+    private IFileProcessClient fileProcessClient;
+
+    @InjectMocks
+    private KnowledgeAddNodeExecutor knowledgeAddNodeExecutor;
+
+    private WorkflowContext workflowContext;
+    private NodeContext nodeContext;
+    private Map<String, Object> nodeDefinition;
+    private Map<String, Object> inputParameters;
+    private Map<String, Object> outputParameters;
+
+    @BeforeEach
+    void setUp() {
+        // 初始化工作流上下文
+        workflowContext = new WorkflowContext();
+        workflowContext.setWorkflowInstanceId(12345L);
+        workflowContext.setCurrentNodeKey("knowledge_add_node_001");
+        workflowContext.setGlobalVars(new HashMap<>());
+        workflowContext.getGlobalVars().put("authorization", "Bearer test-token");
+        workflowContext.getGlobalVars().put("tenantName", "TEST_TENANT");
+
+        // 初始化节点上下文
+        nodeContext = new NodeContext();
+        nodeContext.setNodeKey("knowledge_add_node_001");
+        nodeContext.setStatus(NodeStatus.INIT);
+        nodeContext.setOutput(new HashMap<>());
+
+        // 初始化输入参数
+        inputParameters = new HashMap<>();
+        inputParameters.put("knowledgeInventorySn", "test_knowledge_base");
+        inputParameters.put("continueWhenErr", "0");
+        inputParameters.put("knowledgeType", "sn");
+        inputParameters.put("input", "这是一个测试文本内容");
+        inputParameters.put("deepParse", Arrays.asList("parseWord", "parseImage"));
+        inputParameters.put("splitRuleType", "0");
+
+        // 初始化输出参数
+        outputParameters = new HashMap<>();
+        outputParameters.put("embeddingFiles", "embeddingFilesVar");
+        outputParameters.put("knowledgeInventorySn", "knowledgeInventorySnVar");
+        outputParameters.put("resultMessage", "resultMessageVar");
+
+        // 初始化节点定义
+        nodeDefinition = new HashMap<>();
+        nodeDefinition.put("inputParameters", inputParameters);
+        nodeDefinition.put("outputParameters", outputParameters);
+        nodeContext.setNodeDefinition(nodeDefinition);
+
+        // 设置节点上下文
+        workflowContext.getNodeContexts().put("knowledge_add_node_001", nodeContext);
+    }
+
+    @Test
+    void testExecuteSuccessWithTextInput() {
+        // 准备测试数据
+        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
+        inventoryInfo.setModelSn("test-model");
+        inventoryInfo.setSplitRule(1);
+        inventoryInfo.setSplitter(1);
+        inventoryInfo.setWordCountLimit(500);
+        inventoryInfo.setWordCountOverlap(50);
+        inventoryInfo.setSeparatorContent("[\"\n\", \"\n\n\", \" \"]");
+
+        FileInfoDto embeddedFile = new FileInfoDto();
+        embeddedFile.setFileId(12345L);
+        embeddedFile.setFileSn("test_file_sn");
+        embeddedFile.setFileName("test_file.txt");
+        embeddedFile.setFileType("text/plain");
+        embeddedFile.setFileMd5("test_md5");
+
+        IKnowledgeService.FileStatusInfo statusInfo = new IKnowledgeService.FileStatusInfo();
+        statusInfo.setStatus(2); // 成功状态
+
+        // 模拟服务调用
+        when(knowledgeService.checkKnowledgeInventory(anyString())).thenReturn(inventoryInfo);
+        when(fileProcessClient.processFileUpload(any(), anyString(), anyString(), anyString(), anyString()))
+                .thenReturn(ResultVo.data("上传成功"));
+        when(knowledgeService.findFilesByDatasetIdAndMd5(anyString(), anyString()))
+                .thenReturn(ResultVo.data(new ArrayList<>()));
+        when(knowledgeService.fileEmbedding(any(), anyString())).thenReturn(ResultVo.data(embeddedFile));
+        when(knowledgeService.getFileStatus(anyLong())).thenReturn(ResultVo.data(statusInfo));
+
+        // 执行测试
+        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));
+
+        // 验证结果
+        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
+        assertNotNull(nodeContext.getOutput().get("embeddingFiles"));
+        assertNotNull(nodeContext.getOutput().get("resultMessage"));
+        assertNotNull(nodeContext.getEndTime());
+
+        // 验证服务调用
+        verify(knowledgeService, times(1)).checkKnowledgeInventory("test_knowledge_base");
+        verify(knowledgeService, times(1)).fileEmbedding(any(), eq("Bearer test-token"));
+        verify(knowledgeService, times(1)).getFileStatus(12345L);
+    }
+
+    @Test
+    void testExecuteSuccessWithFileInput() {
+        // 准备测试数据 - 文件类型输入
+        inputParameters.put("input", "test_file_sn::test_file.pdf::DOC");
+
+        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
+        inventoryInfo.setModelSn("test-model");
+        inventoryInfo.setSplitRule(1);
+        inventoryInfo.setSplitter(1);
+        inventoryInfo.setWordCountLimit(500);
+        inventoryInfo.setWordCountOverlap(50);
+
+        IFileProcessClient.ProcessFileInfo processFileInfo = new IFileProcessClient.ProcessFileInfo();
+        processFileInfo.setFileSn("test_file_sn");
+        processFileInfo.setFileName("test_file.pdf");
+        processFileInfo.setFileType("application/pdf");
+        processFileInfo.setDataMd5("test_file_md5");
+
+        FileInfoDto embeddedFile = new FileInfoDto();
+        embeddedFile.setFileId(12345L);
+        embeddedFile.setFileSn("test_file_sn");
+        embeddedFile.setFileName("test_file.pdf");
+
+        IKnowledgeService.FileStatusInfo statusInfo = new IKnowledgeService.FileStatusInfo();
+        statusInfo.setStatus(2); // 成功状态
+
+        // 模拟服务调用
+        when(knowledgeService.checkKnowledgeInventory(anyString())).thenReturn(inventoryInfo);
+        when(fileProcessClient.findProcessFileByFileSn("test_file_sn"))
+                .thenReturn(ResultVo.data(Arrays.asList(processFileInfo)));
+        when(knowledgeService.findFilesByDatasetIdAndMd5(anyString(), anyString()))
+                .thenReturn(ResultVo.data(new ArrayList<>()));
+        when(knowledgeService.fileEmbedding(any(), anyString())).thenReturn(ResultVo.data(embeddedFile));
+        when(knowledgeService.getFileStatus(anyLong())).thenReturn(ResultVo.data(statusInfo));
+
+        // 执行测试
+        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));
+
+        // 验证结果
+        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
+        verify(fileProcessClient, times(1)).findProcessFileByFileSn("test_file_sn");
+    }
+
+    @Test
+    void testExecuteWithDuplicateFile() {
+        // 准备测试数据 - 重复文件
+        FileInfoDto existingFile = new FileInfoDto();
+        existingFile.setFileId(99999L);
+        existingFile.setFileSn("existing_file_sn");
+        existingFile.setFileName("existing_file.txt");
+
+        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
+        inventoryInfo.setModelSn("test-model");
+
+        IKnowledgeService.FileStatusInfo statusInfo = new IKnowledgeService.FileStatusInfo();
+        statusInfo.setStatus(2); // 成功状态
+
+        // 模拟服务调用
+        when(knowledgeService.checkKnowledgeInventory(anyString())).thenReturn(inventoryInfo);
+        when(fileProcessClient.processFileUpload(any(), anyString(), anyString(), anyString(), anyString()))
+                .thenReturn(ResultVo.data("上传成功"));
+        when(knowledgeService.findFilesByDatasetIdAndMd5(anyString(), anyString()))
+                .thenReturn(ResultVo.data(Arrays.asList(existingFile)));
+        when(knowledgeService.getFileStatus(99999L)).thenReturn(ResultVo.data(statusInfo));
+
+        // 执行测试
+        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));
+
+        // 验证结果
+        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
+        // 验证没有调用文件嵌入（因为文件已存在）
+        verify(knowledgeService, never()).fileEmbedding(any(), anyString());
+    }
+
+    @Test
+    void testExecuteMissingInputParameters() {
+        // 准备测试数据 - 缺少输入参数
+        nodeDefinition.remove("inputParameters");
+
+        // 执行测试并验证异常
+        ServiceException exception = assertThrows(ServiceException.class, 
+            () -> knowledgeAddNodeExecutor.execute(workflowContext));
+
+        assertEquals(ExecutorError.NODE_DEFINITION_IS_NULL.getCode(), exception.getCode());
+        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
+        assertNotNull(nodeContext.getErrorMsg());
+        assertNotNull(nodeContext.getEndTime());
+    }
+
+    @Test
+    void testExecuteBlankKnowledgeInventory() {
+        // 准备测试数据 - 空的知识库编号
+        inputParameters.put("knowledgeInventorySn", "");
+
+        // 执行测试并验证异常
+        ServiceException exception = assertThrows(ServiceException.class, 
+            () -> knowledgeAddNodeExecutor.execute(workflowContext));
+
+        assertEquals(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), exception.getCode());
+        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
+    }
+
+    @Test
+    void testExecuteNullInput() {
+        // 准备测试数据 - 空的输入
+        inputParameters.put("input", null);
+
+        // 执行测试并验证异常
+        ServiceException exception = assertThrows(ServiceException.class, 
+            () -> knowledgeAddNodeExecutor.execute(workflowContext));
+
+        assertEquals(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), exception.getCode());
+        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
+    }
+
+    @Test
+    void testExecuteWithVariableReplacement() {
+        // 准备测试数据 - 使用变量替换
+        workflowContext.getGlobalVars().put("knowledgeBase", "variable_knowledge_base");
+        workflowContext.getGlobalVars().put("textContent", "这是来自变量的文本内容");
+        
+        inputParameters.put("knowledgeInventorySn", "$knowledgeBase");
+        inputParameters.put("input", "$textContent");
+
+        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
+        inventoryInfo.setModelSn("test-model");
+
+        FileInfoDto embeddedFile = new FileInfoDto();
+        embeddedFile.setFileId(12345L);
+
+        IKnowledgeService.FileStatusInfo statusInfo = new IKnowledgeService.FileStatusInfo();
+        statusInfo.setStatus(2);
+
+        // 模拟服务调用
+        when(knowledgeService.checkKnowledgeInventory("variable_knowledge_base")).thenReturn(inventoryInfo);
+        when(fileProcessClient.processFileUpload(any(), anyString(), anyString(), anyString(), anyString()))
+                .thenReturn(ResultVo.data("上传成功"));
+        when(knowledgeService.findFilesByDatasetIdAndMd5(anyString(), anyString()))
+                .thenReturn(ResultVo.data(new ArrayList<>()));
+        when(knowledgeService.fileEmbedding(any(), anyString())).thenReturn(ResultVo.data(embeddedFile));
+        when(knowledgeService.getFileStatus(anyLong())).thenReturn(ResultVo.data(statusInfo));
+
+        // 执行测试
+        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));
+
+        // 验证结果
+        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
+        verify(knowledgeService, times(1)).checkKnowledgeInventory("variable_knowledge_base");
+    }
+
+    @Test
+    void testExecuteWithContinueWhenError() {
+        // 准备测试数据 - 遇到错误继续执行
+        inputParameters.put("continueWhenErr", "1");
+        inputParameters.put("input", Arrays.asList("正常文本", "错误文本"));
+
+        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
+        inventoryInfo.setModelSn("test-model");
+
+        FileInfoDto embeddedFile = new FileInfoDto();
+        embeddedFile.setFileId(12345L);
+        embeddedFile.setFileName("success_file.txt");
+
+        IKnowledgeService.FileStatusInfo statusInfo = new IKnowledgeService.FileStatusInfo();
+        statusInfo.setStatus(2);
+
+        // 模拟服务调用
+        when(knowledgeService.checkKnowledgeInventory(anyString())).thenReturn(inventoryInfo);
+        when(fileProcessClient.processFileUpload(any(), anyString(), anyString(), anyString(), anyString()))
+                .thenReturn(ResultVo.data("上传成功"))
+                .thenThrow(new RuntimeException("上传失败"));
+        when(knowledgeService.findFilesByDatasetIdAndMd5(anyString(), anyString()))
+                .thenReturn(ResultVo.data(new ArrayList<>()));
+        when(knowledgeService.fileEmbedding(any(), anyString())).thenReturn(ResultVo.data(embeddedFile));
+        when(knowledgeService.getFileStatus(anyLong())).thenReturn(ResultVo.data(statusInfo));
+
+        // 执行测试
+        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));
+
+        // 验证结果
+        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
+        Map<String, String> errorMsgs = (Map<String, String>) nodeContext.getOutput().get("errorMsg");
+        assertNotNull(errorMsgs);
+        assertFalse(errorMsgs.isEmpty());
+    }
+
+    @Test
+    void testGetType() {
+        assertEquals("KNOWLEDGE_ADD", knowledgeAddNodeExecutor.getType());
+    }
+}
Index: application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/FormAgentExecutor.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.ai.application.agent.run.executor.agent;\r\n\r\nimport com.ai.application.agent.base.api.dto.AgentChatDTO;\r\nimport com.ai.application.agent.base.api.feign.IAgentClient;\r\nimport com.ai.application.agent.base.api.vo.AgentChatVO;\r\nimport com.ai.application.agent.run.errors.AgentNodeExecutorError;\r\nimport com.ai.application.agent.run.executor.AgentExecutionContext;\r\nimport com.ai.framework.core.exception.ServiceException;\r\nimport com.ai.framework.core.util.json.JsonUtils;\r\nimport com.ai.framework.core.vo.ResultVo;\r\nimport com.fasterxml.jackson.databind.JsonNode;\r\nimport lombok.extern.slf4j.Slf4j;\r\nimport org.apache.commons.lang3.StringUtils;\r\nimport org.springframework.beans.factory.annotation.Autowired;\r\nimport org.springframework.stereotype.Component;\r\n\r\nimport java.util.HashMap;\r\nimport java.util.Map;\r\nimport java.util.Objects;\r\n\r\n/**\r\n * 表单型智能体执行器\r\n */\r\n@Slf4j\r\n@Component\r\npublic class FormAgentExecutor extends BaseAgentExecutor {\r\n\r\n    @Autowired\r\n    private IAgentClient agentClient;\r\n\r\n    @Override\r\n    public Map<String, Object> execute(AgentExecutionContext executionContext) {\r\n        String processInstanceId = executionContext.getProcessInstanceId();\r\n        String agentSn = executionContext.getParameterAsString(\"agentSn\");\r\n        String agentType = executionContext.getParameterAsString(\"agentType\");\r\n        String sessionSn = executionContext.getParameterAsString(\"sessionSn\");\r\n        String msgContent = executionContext.getParameterAsString(\"msgContent\");\r\n        String docFileSn = executionContext.getParameterAsString(\"doc\");\r\n        String imageFileSn = executionContext.getParameterAsString(\"image\");\r\n        String auth = executionContext.getAuthorization();\r\n        \r\n        // 会话管理\r\n        boolean contextMemorised = Objects.equals(\"true\", executionContext.getParameterAsString(\"contextMemorised\"));\r\n        if (sessionSn == null || !contextMemorised) {\r\n            String modelSn = executionContext.getParameterAsString(\"model\");\r\n            String prompt = executionContext.getParameterAsString(\"sysPrompt\");\r\n            String temperature = executionContext.getParameterAsString(\"temperature\");\r\n            \r\n            if (StringUtils.isNotBlank(prompt) || StringUtils.isNotBlank(modelSn) || StringUtils.isNotBlank(temperature)) {\r\n                sessionSn = newDebugSessionSn(auth, agentSn, modelSn, prompt, \r\n                    temperature != null ? Double.parseDouble(temperature) : 0.6);\r\n            } else {\r\n                sessionSn = newSessionSn(auth, agentSn);\r\n            }\r\n        }\r\n\r\n        // 构建智能体请求（表单智能体可能需要附件）\r\n        AgentChatDTO chatDto = AgentChatDTO.builder()\r\n                .agentSn(agentSn)\r\n                .agentType(agentType)\r\n                .msgContent(msgContent)\r\n                .msgType(\"text\")\r\n                .delayInMs(20L)\r\n                .fromCode(\"Workflow\")\r\n                .processId(executionContext.getProcessId())\r\n                .sessionSn(sessionSn)\r\n                .debug(executionContext.isDebugRun())\r\n                .build();\r\n\r\n        log.info(\"[{}] {} agent form request body {}\", processInstanceId, agentType, JsonUtils.toJsonString(chatDto));\r\n\r\n        // 调用智能体服务\r\n        ResultVo<AgentChatVO> result = agentClient.chat(chatDto);\r\n\r\n        if (result == null || !Objects.equals(result.getCode(), 0)) {\r\n            String errorMsg = result != null ? result.getMessage() : \"调用文档智能体失败\";\r\n            log.error(\"[{}] {} agent doc error {}\", processInstanceId, agentType, errorMsg);\r\n            throw new ServiceException(51004,\"调用文档智能体失败: \" + errorMsg);\r\n        }\r\n\r\n        AgentChatVO agentChatVo = result.getData();\r\n        if (agentChatVo == null) {\r\n            throw new ServiceException(AgentNodeExecutorError.AGENT_RESPONSE_IS_NULL);\r\n        }\r\n        // 解析返回结果\r\n        String message = parseMsgContent(agentChatVo);\r\n        \r\n        // 表单智能体特有的逻辑：解析表单数据和收集状态\r\n        boolean isAllCollected = parseFormCollectionStatus(agentChatVo);\r\n        Map<String, Object> collectedData = parseFormData(agentChatVo);\r\n        \r\n        log.info(\"[{}] {} agent form response, message: {}, isAllCollected: {}\", \r\n                processInstanceId, agentType, message, isAllCollected);\r\n\r\n        // 构建返回结果\r\n        Map<String, Object> resultMap = new HashMap<>();\r\n        resultMap.put(\"message\", message);\r\n        resultMap.put(\"sessionSn\", sessionSn);\r\n        resultMap.put(\"success\", agentChatVo.getSuccess());\r\n        resultMap.put(\"isAllCollected\", isAllCollected);\r\n        resultMap.put(\"collectedData\", collectedData);\r\n        \r\n        // 根据表单收集状态确定下一步流向\r\n        String target = isAllCollected ? \"collected\" : \"collecting\";\r\n        resultMap.put(\"target\", target);\r\n        \r\n        return resultMap;\r\n    }\r\n\r\n    /**\r\n     * 解析消息内容\r\n     */\r\n    private String parseMsgContent(AgentChatVO agentChatVo) {\r\n        if (agentChatVo.getContent() != null) {\r\n            JsonNode answerNode = agentChatVo.getContent().get(\"answer\");\r\n            if (answerNode != null && answerNode.isTextual()) {\r\n                return answerNode.asText();\r\n            }\r\n        }\r\n        \r\n        return StringUtils.defaultIfBlank(agentChatVo.getReply(), \"\");\r\n    }\r\n\r\n    /**\r\n     * 解析表单收集状态\r\n     */\r\n    private boolean parseFormCollectionStatus(AgentChatVO agentChatVo) {\r\n        if (agentChatVo.getContent() != null) {\r\n            JsonNode statusNode = agentChatVo.getContent().get(\"isAllRequiredCollected\");\r\n            if (statusNode != null && statusNode.isBoolean()) {\r\n                return statusNode.asBoolean();\r\n            }\r\n        }\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * 解析表单数据\r\n     */\r\n    private Map<String, Object> parseFormData(AgentChatVO agentChatVo) {\r\n        Map<String, Object> formData = new HashMap<>();\r\n        \r\n        if (agentChatVo.getContent() != null) {\r\n            JsonNode formNode = agentChatVo.getContent().get(\"form\");\r\n            if (formNode != null && formNode.isArray()) {\r\n                for (JsonNode item : formNode) {\r\n                    String name = item.has(\"name\") ? item.get(\"name\").asText() : null;\r\n                    String value = item.has(\"value\") ? item.get(\"value\").asText() : null;\r\n                    if (name != null && value != null) {\r\n                        formData.put(name, value);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        \r\n        return formData;\r\n    }\r\n\r\n    @Override\r\n    public String getAgentType() {\r\n        return \"form\";\r\n    }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/FormAgentExecutor.java b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/FormAgentExecutor.java
--- a/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/FormAgentExecutor.java	(revision 27ed3acd0bd29b025bcb2499817b2608a5835ce1)
+++ b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/FormAgentExecutor.java	(date 1749808647398)
@@ -3,7 +3,7 @@
 import com.ai.application.agent.base.api.dto.AgentChatDTO;
 import com.ai.application.agent.base.api.feign.IAgentClient;
 import com.ai.application.agent.base.api.vo.AgentChatVO;
-import com.ai.application.agent.run.errors.AgentNodeExecutorError;
+import com.ai.application.agent.run.errors.ExecutorError;
 import com.ai.application.agent.run.executor.AgentExecutionContext;
 import com.ai.framework.core.exception.ServiceException;
 import com.ai.framework.core.util.json.JsonUtils;
@@ -80,7 +80,7 @@
 
         AgentChatVO agentChatVo = result.getData();
         if (agentChatVo == null) {
-            throw new ServiceException(AgentNodeExecutorError.AGENT_RESPONSE_IS_NULL);
+            throw new ServiceException(ExecutorError.AGENT_RESPONSE_IS_NULL);
         }
         // 解析返回结果
         String message = parseMsgContent(agentChatVo);
Index: application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/DocAgentExecutor.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.ai.application.agent.run.executor.agent;\r\n\r\nimport com.ai.application.agent.base.api.dto.AgentChatDTO;\r\nimport com.ai.application.agent.base.api.feign.IAgentClient;\r\nimport com.ai.application.agent.base.api.vo.AgentChatVO;\r\nimport com.ai.application.agent.run.errors.AgentNodeExecutorError;\r\nimport com.ai.application.agent.run.executor.AgentExecutionContext;\r\nimport com.ai.framework.core.exception.ServiceException;\r\nimport com.ai.framework.core.util.json.JsonUtils;\r\nimport com.ai.framework.core.vo.ResultVo;\r\nimport com.fasterxml.jackson.databind.JsonNode;\r\nimport lombok.extern.slf4j.Slf4j;\r\nimport org.apache.commons.lang3.StringUtils;\r\nimport org.springframework.beans.factory.annotation.Autowired;\r\nimport org.springframework.stereotype.Component;\r\n\r\nimport java.util.ArrayList;\r\nimport java.util.HashMap;\r\nimport java.util.List;\r\nimport java.util.Map;\r\nimport java.util.Objects;\r\n\r\n/**\r\n * 文档问答智能体执行器\r\n */\r\n@Slf4j\r\n@Component\r\npublic class DocAgentExecutor extends BaseAgentExecutor {\r\n\r\n    @Autowired\r\n    private IAgentClient agentClient;\r\n\r\n    @Override\r\n    public Map<String, Object> execute(AgentExecutionContext executionContext) {\r\n        String processInstanceId = executionContext.getProcessInstanceId();\r\n        String agentSn = executionContext.getParameterAsString(\"agentSn\");\r\n        String agentType = executionContext.getParameterAsString(\"agentType\");\r\n        String sessionSn = executionContext.getParameterAsString(\"sessionSn\");\r\n        String msgContent = executionContext.getParameterAsString(\"msgContent\");\r\n        String auth = executionContext.getAuthorization();\r\n        \r\n        // 会话管理\r\n        boolean contextMemorised = Objects.equals(\"true\", executionContext.getParameterAsString(\"contextMemorised\"));\r\n        if (sessionSn == null || !contextMemorised) {\r\n            String modelSn = executionContext.getParameterAsString(\"model\");\r\n            String prompt = executionContext.getParameterAsString(\"sysPrompt\");\r\n            String temperature = executionContext.getParameterAsString(\"temperature\");\r\n            \r\n            if (StringUtils.isNotBlank(prompt) || StringUtils.isNotBlank(modelSn) || StringUtils.isNotBlank(temperature)) {\r\n                sessionSn = newDebugSessionSn(auth, agentSn, modelSn, prompt, \r\n                    temperature != null ? Double.parseDouble(temperature) : 0.6);\r\n            } else {\r\n                sessionSn = newSessionSn(auth, agentSn);\r\n            }\r\n        }\r\n\r\n        // 构建智能体请求\r\n        AgentChatDTO chatDto = AgentChatDTO.builder()\r\n                .agentSn(agentSn)\r\n                .agentType(agentType)\r\n                .msgContent(msgContent)\r\n                .msgType(\"text\")\r\n                .delayInMs(20L)\r\n                .fromCode(\"Workflow\")\r\n                .processId(executionContext.getProcessId())\r\n                .sessionSn(sessionSn)\r\n                .debug(executionContext.isDebugRun())\r\n                .build();\r\n\r\n        log.info(\"[{}] {} agent doc request body {}\", processInstanceId, agentType, JsonUtils.toJsonString(chatDto));\r\n\r\n        // 调用智能体服务\r\n        ResultVo<AgentChatVO> result = agentClient.chat(chatDto);\r\n\r\n        if (result == null || !Objects.equals(result.getCode(), 0)) {\r\n            String errorMsg = result != null ? result.getMessage() : \"调用文档智能体失败\";\r\n            log.error(\"[{}] {} agent doc error {}\", processInstanceId, agentType, errorMsg);\r\n            throw new ServiceException(51004,\"调用文档智能体失败: \" + errorMsg);\r\n        }\r\n\r\n        AgentChatVO agentChatVo = result.getData();\r\n        if (agentChatVo == null) {\r\n            throw new ServiceException(AgentNodeExecutorError.AGENT_RESPONSE_IS_NULL);\r\n        }\r\n\r\n        // 解析返回结果\r\n        String message = parseMsgContent(agentChatVo);\r\n        \r\n        // 文档智能体特有的逻辑：解析引用信息\r\n        List<Object> referenceList = parseReferenceInfo(agentChatVo);\r\n        \r\n        log.info(\"[{}] {} agent doc response, message: {}, references: {}\", \r\n                processInstanceId, agentType, message, referenceList.size());\r\n\r\n        // 构建返回结果\r\n        Map<String, Object> resultMap = new HashMap<>();\r\n        resultMap.put(\"message\", message);\r\n        resultMap.put(\"sessionSn\", sessionSn);\r\n        resultMap.put(\"success\", agentChatVo.getSuccess());\r\n        resultMap.put(\"reference\", referenceList);\r\n        \r\n        return resultMap;\r\n    }\r\n\r\n    /**\r\n     * 解析消息内容\r\n     */\r\n    private String parseMsgContent(AgentChatVO agentChatVo) {\r\n        if (agentChatVo.getContent() != null) {\r\n            JsonNode answerNode = agentChatVo.getContent().get(\"answer\");\r\n            if (answerNode != null && answerNode.isTextual()) {\r\n                return answerNode.asText();\r\n            }\r\n        }\r\n        \r\n        return StringUtils.defaultIfBlank(agentChatVo.getReply(), \"\");\r\n    }\r\n\r\n    /**\r\n     * 解析引用信息\r\n     */\r\n    private List<Object> parseReferenceInfo(AgentChatVO agentChatVo) {\r\n        List<Object> referenceList = new ArrayList<>();\r\n        \r\n        if (agentChatVo.getContent() != null) {\r\n            JsonNode pagesNode = agentChatVo.getContent().get(\"pages\");\r\n            if (pagesNode != null && pagesNode.isArray()) {\r\n                for (JsonNode pageNode : pagesNode) {\r\n                    Map<String, Object> pageInfo = new HashMap<>();\r\n                    \r\n                    if (pageNode.has(\"title\")) {\r\n                        pageInfo.put(\"title\", pageNode.get(\"title\").asText());\r\n                    }\r\n                    if (pageNode.has(\"content\")) {\r\n                        pageInfo.put(\"content\", pageNode.get(\"content\").asText());\r\n                    }\r\n                    if (pageNode.has(\"url\")) {\r\n                        pageInfo.put(\"url\", pageNode.get(\"url\").asText());\r\n                    }\r\n                    if (pageNode.has(\"score\")) {\r\n                        pageInfo.put(\"score\", pageNode.get(\"score\").asDouble());\r\n                    }\r\n                    \r\n                    if (!pageInfo.isEmpty()) {\r\n                        referenceList.add(pageInfo);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        \r\n        return referenceList;\r\n    }\r\n\r\n    @Override\r\n    public String getAgentType() {\r\n        return \"doc\";\r\n    }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/DocAgentExecutor.java b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/DocAgentExecutor.java
--- a/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/DocAgentExecutor.java	(revision 27ed3acd0bd29b025bcb2499817b2608a5835ce1)
+++ b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/DocAgentExecutor.java	(date 1749808647338)
@@ -3,7 +3,7 @@
 import com.ai.application.agent.base.api.dto.AgentChatDTO;
 import com.ai.application.agent.base.api.feign.IAgentClient;
 import com.ai.application.agent.base.api.vo.AgentChatVO;
-import com.ai.application.agent.run.errors.AgentNodeExecutorError;
+import com.ai.application.agent.run.errors.ExecutorError;
 import com.ai.application.agent.run.executor.AgentExecutionContext;
 import com.ai.framework.core.exception.ServiceException;
 import com.ai.framework.core.util.json.JsonUtils;
@@ -80,7 +80,7 @@
 
         AgentChatVO agentChatVo = result.getData();
         if (agentChatVo == null) {
-            throw new ServiceException(AgentNodeExecutorError.AGENT_RESPONSE_IS_NULL);
+            throw new ServiceException(ExecutorError.AGENT_RESPONSE_IS_NULL);
         }
 
         // 解析返回结果
Index: application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/ChatAgentExecutor.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.ai.application.agent.run.executor.agent;\r\n\r\nimport com.ai.application.agent.base.api.dto.AgentChatDTO;\r\nimport com.ai.application.agent.base.api.feign.IAgentClient;\r\nimport com.ai.application.agent.base.api.vo.AgentChatVO;\r\nimport com.ai.application.agent.run.errors.AgentNodeExecutorError;\r\nimport com.ai.application.agent.run.executor.AgentExecutionContext;\r\nimport com.ai.framework.core.exception.ServiceException;\r\nimport com.ai.framework.core.util.json.JsonUtils;\r\nimport com.ai.framework.core.vo.ResultVo;\r\nimport com.fasterxml.jackson.databind.JsonNode;\r\nimport lombok.extern.slf4j.Slf4j;\r\nimport org.apache.commons.lang3.StringUtils;\r\nimport org.springframework.beans.factory.annotation.Autowired;\r\nimport org.springframework.stereotype.Component;\r\n\r\nimport java.util.HashMap;\r\nimport java.util.Map;\r\nimport java.util.Objects;\r\n\r\n/**\r\n * 对话型智能体执行器\r\n */\r\n@Slf4j\r\n@Component\r\npublic class ChatAgentExecutor extends BaseAgentExecutor {\r\n\r\n    @Autowired\r\n    private IAgentClient agentClient;\r\n\r\n    @Override\r\n    public Map<String, Object> execute(AgentExecutionContext executionContext) {\r\n        String processInstanceId = executionContext.getProcessInstanceId();\r\n        String agentSn = executionContext.getParameterAsString(\"agentSn\");\r\n        String agentType = executionContext.getParameterAsString(\"agentType\");\r\n        String sessionSn = executionContext.getParameterAsString(\"sessionSn\");\r\n        String msgContent = executionContext.getParameterAsString(\"msgContent\");\r\n        String auth = executionContext.getAuthorization();\r\n        \r\n        // 会话管理\r\n        boolean contextMemorised = Objects.equals(\"true\", executionContext.getParameterAsString(\"contextMemorised\"));\r\n        if (sessionSn == null || !contextMemorised) {\r\n            String modelSn = executionContext.getParameterAsString(\"model\");\r\n            String prompt = executionContext.getParameterAsString(\"sysPrompt\");\r\n            String temperature = executionContext.getParameterAsString(\"temperature\");\r\n            \r\n            if (StringUtils.isNotBlank(prompt) || StringUtils.isNotBlank(modelSn) || StringUtils.isNotBlank(temperature)) {\r\n                sessionSn = newDebugSessionSn(auth, agentSn, modelSn, prompt, \r\n                    temperature != null ? Double.parseDouble(temperature) : 0.6);\r\n            } else {\r\n                sessionSn = newSessionSn(auth, agentSn);\r\n            }\r\n        }\r\n\r\n        // 构建智能体请求\r\n        AgentChatDTO chatDto = AgentChatDTO.builder()\r\n                .agentSn(agentSn)\r\n                .agentType(agentType)\r\n                .msgContent(msgContent)\r\n                .msgType(\"text\")\r\n                .delayInMs(20L)\r\n                .fromCode(\"Workflow\")\r\n                .processId(executionContext.getProcessId())\r\n                .sessionSn(sessionSn)\r\n                .debug(executionContext.isDebugRun())\r\n                .build();\r\n\r\n        log.info(\"[{}] {} agent chat request body {}\", processInstanceId, agentType, JsonUtils.toJsonString(chatDto));\r\n\r\n        // 调用智能体服务\r\n        ResultVo<AgentChatVO> result = agentClient.chat(chatDto);\r\n\r\n        if (result == null || !Objects.equals(result.getCode(), 0)) {\r\n            String errorMsg = result != null ? result.getMessage() : \"调用文档智能体失败\";\r\n            log.error(\"[{}] {} agent doc error {}\", processInstanceId, agentType, errorMsg);\r\n            throw new ServiceException(51004,\"调用文档智能体失败: \" + errorMsg);\r\n        }\r\n\r\n        AgentChatVO agentChatVo = result.getData();\r\n        if (agentChatVo == null) {\r\n            throw new ServiceException(AgentNodeExecutorError.AGENT_RESPONSE_IS_NULL);\r\n        }\r\n\r\n        // 解析返回结果\r\n        String message = parseMsgContent(agentChatVo);\r\n        log.info(\"[{}] {} agent chat response {}\", processInstanceId, agentType, message);\r\n\r\n        // 构建返回结果\r\n        Map<String, Object> resultMap = new HashMap<>();\r\n        resultMap.put(\"message\", message);\r\n        resultMap.put(\"sessionSn\", sessionSn);\r\n        resultMap.put(\"success\", agentChatVo.getSuccess());\r\n        \r\n        return resultMap;\r\n    }\r\n\r\n    /**\r\n     * 解析消息内容\r\n     */\r\n    private String parseMsgContent(AgentChatVO agentChatVo) {\r\n        if (agentChatVo.getContent() != null) {\r\n            JsonNode answerNode = agentChatVo.getContent().get(\"answer\");\r\n            if (answerNode != null && answerNode.isTextual()) {\r\n                return answerNode.asText();\r\n            }\r\n        }\r\n        \r\n        // 如果没有content或answer，返回reply字段\r\n        return StringUtils.defaultIfBlank(agentChatVo.getReply(), \"\");\r\n    }\r\n\r\n    @Override\r\n    public String getAgentType() {\r\n        return \"chat\";\r\n    }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/ChatAgentExecutor.java b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/ChatAgentExecutor.java
--- a/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/ChatAgentExecutor.java	(revision 27ed3acd0bd29b025bcb2499817b2608a5835ce1)
+++ b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/ChatAgentExecutor.java	(date 1749808647351)
@@ -3,7 +3,7 @@
 import com.ai.application.agent.base.api.dto.AgentChatDTO;
 import com.ai.application.agent.base.api.feign.IAgentClient;
 import com.ai.application.agent.base.api.vo.AgentChatVO;
-import com.ai.application.agent.run.errors.AgentNodeExecutorError;
+import com.ai.application.agent.run.errors.ExecutorError;
 import com.ai.application.agent.run.executor.AgentExecutionContext;
 import com.ai.framework.core.exception.ServiceException;
 import com.ai.framework.core.util.json.JsonUtils;
@@ -78,7 +78,7 @@
 
         AgentChatVO agentChatVo = result.getData();
         if (agentChatVo == null) {
-            throw new ServiceException(AgentNodeExecutorError.AGENT_RESPONSE_IS_NULL);
+            throw new ServiceException(ExecutorError.AGENT_RESPONSE_IS_NULL);
         }
 
         // 解析返回结果
Index: application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/feign/IFileProcessClient.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/feign/IFileProcessClient.java b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/feign/IFileProcessClient.java
new file mode 100644
--- /dev/null	(date 1749808647485)
+++ b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/feign/IFileProcessClient.java	(date 1749808647485)
@@ -0,0 +1,66 @@
+package com.ai.application.agent.run.feign;
+
+import com.ai.application.base.file.api.dto.FileInfoDto;
+import com.ai.framework.core.constants.ServiceConstant;
+import com.ai.framework.core.vo.ResultVo;
+import org.springframework.cloud.openfeign.FeignClient;
+import org.springframework.web.bind.annotation.*;
+
+import java.util.List;
+
+/**
+ * 文件处理客户端接口
+ */
+@FeignClient(
+        value = ServiceConstant.BASE_FILE,
+        contextId = "IFileProcessClient",url = "http://localhost:6042"
+)
+public interface IFileProcessClient {
+    String API_PREFIX = "/v1/feign/file";
+
+    /**
+     * 根据文件编号查询流程文件
+     *
+     * @param fileSn 文件编号
+     * @return 文件信息列表
+     */
+    @GetMapping(value = API_PREFIX + "/process/findByFileSn")
+    ResultVo<List<ProcessFileInfo>> findProcessFileByFileSn(@RequestParam("fileSn") String fileSn);
+
+    /**
+     * 流程文件上传
+     *
+     * @param content 文件内容
+     * @param fileSn 文件编号
+     * @param fileType 文件类型
+     * @param fileName 文件名
+     * @param source 来源
+     * @return 上传结果
+     */
+    @PostMapping(value = API_PREFIX + "/process/upload")
+    ResultVo<String> processFileUpload(@RequestBody byte[] content,
+                                      @RequestParam("fileSn") String fileSn,
+                                      @RequestParam("fileType") String fileType,
+                                      @RequestParam("fileName") String fileName,
+                                      @RequestParam("source") String source);
+
+    /**
+     * 流程文件信息
+     */
+    class ProcessFileInfo {
+        private String fileSn;
+        private String fileName;
+        private String fileType;
+        private String dataMd5;
+
+        // Getters and Setters
+        public String getFileSn() { return fileSn; }
+        public void setFileSn(String fileSn) { this.fileSn = fileSn; }
+        public String getFileName() { return fileName; }
+        public void setFileName(String fileName) { this.fileName = fileName; }
+        public String getFileType() { return fileType; }
+        public void setFileType(String fileType) { this.fileType = fileType; }
+        public String getDataMd5() { return dataMd5; }
+        public void setDataMd5(String dataMd5) { this.dataMd5 = dataMd5; }
+    }
+}
Index: application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/dto/KnowledgeFileEmbeddingDTO.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/dto/KnowledgeFileEmbeddingDTO.java b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/dto/KnowledgeFileEmbeddingDTO.java
new file mode 100644
--- /dev/null	(date 1749808647476)
+++ b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/dto/KnowledgeFileEmbeddingDTO.java	(date 1749808647476)
@@ -0,0 +1,126 @@
+package com.ai.application.agent.run.dto;
+
+import com.ai.application.base.file.api.dto.FileInfoDto;
+import io.swagger.v3.oas.annotations.media.Schema;
+import lombok.AllArgsConstructor;
+import lombok.Data;
+import lombok.NoArgsConstructor;
+import lombok.experimental.SuperBuilder;
+
+import java.util.List;
+
+/**
+ * 知识文件嵌入 DTO
+ */
+@Data
+@AllArgsConstructor
+@NoArgsConstructor
+@SuperBuilder
+@Schema(name = "KnowledgeFileEmbeddingDTO")
+public class KnowledgeFileEmbeddingDTO {
+
+    /**
+     * 文件信息
+     */
+    @Schema(description = "文件信息")
+    private FileInfoDto fileInfo;
+
+    /**
+     * 嵌入配置
+     */
+    @Schema(description = "嵌入配置")
+    private EmbeddingConfig embeddingConfig;
+
+    /**
+     * 知识库ID
+     */
+    @Schema(description = "知识库ID")
+    private String datasetId;
+
+    /**
+     * 模型编号
+     */
+    @Schema(description = "模型编号")
+    private String modelSn;
+
+    /**
+     * 上传类型
+     */
+    @Schema(description = "上传类型")
+    private Integer uploadType;
+
+    /**
+     * 嵌入类型
+     */
+    @Schema(description = "嵌入类型")
+    private String embeddingType;
+
+    /**
+     * 分段规则类型
+     */
+    @Schema(description = "分段规则类型")
+    private Integer splitRuleType;
+
+    /**
+     * 嵌入配置
+     */
+    @Data
+    @AllArgsConstructor
+    @NoArgsConstructor
+    public static class EmbeddingConfig {
+        
+        /**
+         * OCR表格识别
+         */
+        @Schema(description = "OCR表格识别")
+        private Boolean ocrTable;
+
+        /**
+         * OCR图片识别
+         */
+        @Schema(description = "OCR图片识别")
+        private Boolean ocrImage;
+
+        /**
+         * OCR文字识别
+         */
+        @Schema(description = "OCR文字识别")
+        private Boolean ocrWord;
+
+        /**
+         * 分段规则
+         */
+        @Schema(description = "分段规则")
+        private Integer splitRule;
+
+        /**
+         * 切分器类型
+         */
+        @Schema(description = "切分器类型")
+        private Integer splitter;
+
+        /**
+         * 分段字数上限
+         */
+        @Schema(description = "分段字数上限")
+        private Integer wordCountLimit;
+
+        /**
+         * 重叠区域字数
+         */
+        @Schema(description = "重叠区域字数")
+        private Integer wordCountOverlap;
+
+        /**
+         * 分隔符列表
+         */
+        @Schema(description = "分隔符列表")
+        private List<String> separators;
+
+        /**
+         * 优先级
+         */
+        @Schema(description = "优先级")
+        private Integer priority;
+    }
+}
Index: application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/dto/KnowledgeEmbeddingParamsDTO.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/dto/KnowledgeEmbeddingParamsDTO.java b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/dto/KnowledgeEmbeddingParamsDTO.java
new file mode 100644
--- /dev/null	(date 1749808647438)
+++ b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/dto/KnowledgeEmbeddingParamsDTO.java	(date 1749808647438)
@@ -0,0 +1,80 @@
+package com.ai.application.agent.run.dto;
+
+import io.swagger.v3.oas.annotations.media.Schema;
+import lombok.AllArgsConstructor;
+import lombok.Data;
+import lombok.NoArgsConstructor;
+import lombok.experimental.SuperBuilder;
+
+import java.util.List;
+
+/**
+ * 知识添加嵌入参数 DTO
+ */
+@Data
+@AllArgsConstructor
+@NoArgsConstructor
+@SuperBuilder
+@Schema(name = "KnowledgeEmbeddingParamsDTO")
+public class KnowledgeEmbeddingParamsDTO {
+
+    /**
+     * 知识库编号
+     */
+    @Schema(description = "知识库编号")
+    private String knowledgeInventory;
+
+    /**
+     * 深度解析选项
+     */
+    @Schema(description = "深度解析选项")
+    private List<String> deepParse;
+
+    /**
+     * 分段规则类型
+     */
+    @Schema(description = "分段规则类型")
+    private String splitRuleType;
+
+    /**
+     * 遇到错误是否继续 (0-不继续, 1-继续)
+     */
+    @Schema(description = "遇到错误是否继续")
+    private Integer continueWhenErr;
+
+    /**
+     * 分段规则
+     */
+    @Schema(description = "分段规则")
+    private Integer splitRule;
+
+    /**
+     * 切分器类型
+     */
+    @Schema(description = "切分器类型")
+    private Integer splitter;
+
+    /**
+     * 分段字数上限
+     */
+    @Schema(description = "分段字数上限")
+    private Integer wordCountLimit;
+
+    /**
+     * 重叠区域字数
+     */
+    @Schema(description = "重叠区域字数")
+    private Integer wordCountOverlap;
+
+    /**
+     * 分隔符内容
+     */
+    @Schema(description = "分隔符内容")
+    private String separatorContent;
+
+    /**
+     * 知识类型
+     */
+    @Schema(description = "知识类型")
+    private String knowledgeType;
+}
Index: application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/dto/KnowledgeEmbeddingResultDTO.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/dto/KnowledgeEmbeddingResultDTO.java b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/dto/KnowledgeEmbeddingResultDTO.java
new file mode 100644
--- /dev/null	(date 1749808647426)
+++ b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/dto/KnowledgeEmbeddingResultDTO.java	(date 1749808647426)
@@ -0,0 +1,40 @@
+package com.ai.application.agent.run.dto;
+
+import com.ai.application.base.file.api.dto.FileInfoDto;
+import io.swagger.v3.oas.annotations.media.Schema;
+import lombok.AllArgsConstructor;
+import lombok.Data;
+import lombok.NoArgsConstructor;
+import lombok.experimental.SuperBuilder;
+
+import java.util.List;
+import java.util.Map;
+
+/**
+ * 知识嵌入结果 DTO
+ */
+@Data
+@AllArgsConstructor
+@NoArgsConstructor
+@SuperBuilder
+@Schema(name = "KnowledgeEmbeddingResultDTO")
+public class KnowledgeEmbeddingResultDTO {
+
+    /**
+     * 成功嵌入的文件列表
+     */
+    @Schema(description = "成功嵌入的文件列表")
+    private List<FileInfoDto> files;
+
+    /**
+     * 知识库ID
+     */
+    @Schema(description = "知识库ID")
+    private String datasetId;
+
+    /**
+     * 错误消息映射 (文件名 -> 错误信息)
+     */
+    @Schema(description = "错误消息映射")
+    private Map<String, String> errorMsgs;
+}
Index: application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/KnowledgeAddNodeExecutor.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/KnowledgeAddNodeExecutor.java b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/KnowledgeAddNodeExecutor.java
new file mode 100644
--- /dev/null	(date 1749816983497)
+++ b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/KnowledgeAddNodeExecutor.java	(date 1749816983497)
@@ -0,0 +1,628 @@
+package com.ai.application.agent.run.executor;
+
+import cn.hutool.json.JSONUtil;
+import com.ai.application.agent.run.dto.KnowledgeEmbeddingParamsDTO;
+import com.ai.application.agent.run.dto.KnowledgeEmbeddingResultDTO;
+import com.ai.application.agent.run.dto.KnowledgeFileEmbeddingDTO;
+import com.ai.application.agent.run.errors.ExecutorError;
+import com.ai.application.agent.run.feign.IFileProcessClient;
+import com.ai.application.agent.run.service.IKnowledgeService;
+import com.ai.application.base.file.api.dto.DocFileDto;
+import com.ai.application.base.file.api.dto.FileInfoDto;
+import com.ai.application.base.file.api.feign.IFileFeignClient;
+import com.ai.application.knowledge.table.dto.DeleteBySnFeignDto;
+import com.ai.application.knowledge.table.feign.DocFeignClient;
+import com.ai.application.knowledge.table.vo.BaseDetailVo;
+import com.ai.framework.core.exception.ServiceException;
+import com.ai.framework.core.util.json.JsonUtils;
+import com.ai.framework.core.vo.ResultVo;
+import com.ai.framework.workflow.context.NodeContext;
+import com.ai.framework.workflow.context.WorkflowContext;
+import com.ai.framework.workflow.enums.NodeStatus;
+import com.ai.framework.workflow.excutor.NodeExecutor;
+import lombok.extern.slf4j.Slf4j;
+import org.apache.commons.codec.digest.DigestUtils;
+import org.apache.commons.lang3.RandomStringUtils;
+import org.apache.commons.lang3.StringUtils;
+import org.springframework.beans.factory.annotation.Autowired;
+import org.springframework.stereotype.Component;
+
+import java.util.*;
+import java.util.concurrent.TimeUnit;
+import java.util.stream.Collectors;
+
+/**
+ * 知识添加节点执行器
+ * 用于在工作流中添加知识到知识库
+ */
+@Slf4j
+@Component
+public class KnowledgeAddNodeExecutor implements NodeExecutor {
+
+    @Autowired
+    private DocFeignClient docFeignClient;
+
+    @Autowired
+    private IFileFeignClient fileFeignClient;
+
+    @Override
+    public void execute(WorkflowContext context) {
+        String nodeKey = context.getCurrentNodeKey();
+        NodeContext nodeCtx = context.getNodeContexts().get(nodeKey);
+        Map<String, Object> nodeDef = nodeCtx.getNodeDefinition();
+
+        log.info("KnowledgeAddNodeExecutor execute start, nodeKey: {}, nodeDef: {}", nodeKey, JsonUtils.toJsonString(nodeDef));
+
+        try {
+            // 设置节点状态为运行中
+            nodeCtx.setStatus(NodeStatus.RUNNING);
+
+            // 初始化节点输出
+            if (nodeCtx.getOutput() == null) {
+                nodeCtx.setOutput(new HashMap<>());
+            }
+
+            // 从节点定义中获取输入参数
+            Map<String, Object> inputParameters = (Map<String, Object>) nodeDef.get("inputParameters");
+            if (inputParameters == null) {
+                throw new ServiceException(ExecutorError.NODE_DEFINITION_IS_NULL);
+            }
+
+            // 获取参数值，支持变量替换
+            String knowledgeInventory = getParameterValue(inputParameters, "knowledgeInventorySn", context);
+            String continueWhenErr = getParameterValue(inputParameters, "continueWhenErr", context);
+            String knowledgeType = getParameterValue(inputParameters, "knowledgeType", context);
+            Object inputValue = getParameterObject(inputParameters, "input", context);
+            Object deepParseParam = inputParameters.get("deepParse");
+            Object separatorContent = inputParameters.get("separatorContent");
+
+            // 参数校验
+            if (StringUtils.isBlank(knowledgeInventory)) {
+                throw new ServiceException(ExecutorError.KNOWLEDGE_INVENTORY_SN_IS_NULL);
+            }
+            if (inputValue == null) {
+                throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "input 不能为空");
+            }
+
+            // 处理输入数据
+            List<FileInfoDto> files = processInputData(inputValue, context);
+            if (files.isEmpty()) {
+                throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "没有有效的输入文件");
+            }
+
+            // 构建嵌入参数
+            KnowledgeEmbeddingParamsDTO embeddingParams = buildEmbeddingParams(
+                    knowledgeInventory, continueWhenErr, knowledgeType, deepParseParam, separatorContent, inputParameters, context);
+
+            // 执行文件嵌入
+            KnowledgeEmbeddingResultDTO embeddingResult = executeFileEmbedding(files, embeddingParams, context);
+
+            // 构建输出结果
+            Map<String, Object> result = buildOutputResult(embeddingResult, files.size());
+
+            // 将结果写入输出参数
+            writeOutputParameters(nodeDef, context, result);
+
+            // 设置节点输出
+            nodeCtx.getOutput().putAll(result);
+
+            // 设置节点状态为成功
+            nodeCtx.setStatus(NodeStatus.SUCCESS);
+            nodeCtx.setEndTime(java.time.LocalDateTime.now());
+
+            log.info("KnowledgeAddNodeExecutor execute success, result: {}", JsonUtils.toJsonString(result));
+
+        } catch (Exception e) {
+            log.error("KnowledgeAddNodeExecutor execute error", e);
+            nodeCtx.setStatus(NodeStatus.FAILED);
+            nodeCtx.setErrorMsg("知识添加执行失败: " + e.getMessage());
+            nodeCtx.setEndTime(java.time.LocalDateTime.now());
+            throw e;
+        }
+    }
+
+    /**
+     * 处理输入数据，转换为文件信息列表
+     */
+    private List<FileInfoDto> processInputData(Object inputValue, WorkflowContext context) {
+        List<FileInfoDto> files = new ArrayList<>();
+        String tenantName = getTenantName(context);
+        String processInstanceId = context.getWorkflowInstanceId() != null ? 
+                context.getWorkflowInstanceId().toString() : "unknown";
+
+        if (inputValue instanceof List) {
+            // 数组类型输入
+            List<?> inputList = (List<?>) inputValue;
+            for (Object item : inputList) {
+                if (item != null) {
+                    Optional<FileInfoDto> fileInfo = createFileInfoFromInput(
+                            tenantName, processInstanceId, item.toString());
+                    fileInfo.ifPresent(files::add);
+                }
+            }
+        } else {
+            // 单个输入
+            Optional<FileInfoDto> fileInfo = createFileInfoFromInput(
+                    tenantName, processInstanceId, inputValue.toString());
+            fileInfo.ifPresent(files::add);
+        }
+
+        return files;
+    }
+
+    /**
+     * 从输入内容创建文件信息
+     */
+    private Optional<FileInfoDto> createFileInfoFromInput(String tenantName, String processInstanceId, String inputContent) {
+        if (StringUtils.isBlank(inputContent)) {
+            log.info("KnowledgeAddNodeExecutor empty input, processInstanceId: [{}]", processInstanceId);
+            return Optional.empty();
+        }
+
+        if (StringUtils.isBlank(tenantName)) {
+            tenantName = "XBOT";
+        }
+
+        FileInfoDto fileInfo = new FileInfoDto();
+        fileInfo.setFormatFileSn(inputContent);
+
+        // 判断是否为文件类型（包含::分隔符）
+        if (inputContent.contains("::")) {
+            // 文件类型：查询已上传的文件
+            String fileSn = inputContent.split("::")[0];
+            try {
+                ResultVo<DocFileDto> result = fileFeignClient.getDocFileByFileSn(fileSn);
+
+                if (result.getCode() == 0 && result.getData() != null && !Objects.isNull(result.getData())) {
+                    DocFileDto processFile = result.getData();
+                    fileInfo.setFileName(processFile.getFileName());
+                    fileInfo.setFileId(processFile.getFileId());
+                    fileInfo.setFileSn(processFile.getFileSn());
+                    fileInfo.setDocType(getFileExtension(processFile.getFileName()));
+                    fileInfo.setFileType(processFile.getFileType());
+                } else {
+                    log.warn("Process file not found for fileSn: {}", fileSn);
+                    return Optional.empty();
+                }
+            } catch (Exception e) {
+                log.error("Error querying process file: {}", e.getMessage());
+                return Optional.empty();
+            }
+        } else {
+            // 文本类型：创建文本文件
+            String filename = String.format("%s-%s.%s", RandomStringUtils.randomAlphanumeric(8), "xbot-chain-text", "txt");
+            String fileType = "text/plain";
+            String fileSn = String.format("%s/%s", tenantName, UUID.randomUUID().toString().replaceAll("-", ""));
+            String formatFileSn = String.format("%s::%s::%s", fileSn, filename, "DOC");
+
+            try {
+                ResultVo<String> uploadResult = fileFeignClient.processFileUploadCompa(
+                        inputContent.getBytes(), fileSn, fileType, filename, "knowledge",null);
+                
+                if (uploadResult.getCode() != 0) {
+                    log.error("File upload failed: {}", uploadResult.getMessage());
+                    return Optional.empty();
+                }
+
+                fileInfo.setFileName(filename);
+                fileInfo.setFileSn(fileSn);
+                fileInfo.setDocType("txt");
+                fileInfo.setFormatFileSn(formatFileSn);
+            } catch (Exception e) {
+                log.error("Error uploading text file: {}", e.getMessage());
+                return Optional.empty();
+            }
+        }
+
+        return Optional.of(fileInfo);
+    }
+
+    /**
+     * 获取文件扩展名
+     */
+    private String getFileExtension(String fileName) {
+        if (StringUtils.isBlank(fileName)) {
+            return "txt";
+        }
+        int lastDotIndex = fileName.lastIndexOf('.');
+        return lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1) : "txt";
+    }
+
+    /**
+     * 获取租户名称
+     */
+    private String getTenantName(WorkflowContext context) {
+        // TODO 这里需要从上下文中获取租户信息
+//        Object tenantName = context.getGlobalVars().get("tenantName");
+//        return tenantName != null ? tenantName.toString() : "XBOT";
+
+        return null;
+    }
+
+    /**
+     * 获取参数值，支持变量替换
+     */
+    private String getParameterValue(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
+        Object value = inputParameters.get(paramName);
+        if (value == null) {
+            return null;
+        }
+
+        String strValue = value.toString();
+        
+        // 如果是变量引用（以$开头），从全局变量中获取
+        if (strValue.startsWith("$")) {
+            String varName = strValue.substring(1);
+            Object varValue = context.getGlobalVars().get(varName);
+            return varValue != null ? varValue.toString() : null;
+        }
+        
+        return strValue;
+    }
+
+    /**
+     * 获取参数对象，支持变量替换
+     */
+    private Object getParameterObject(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
+        Object value = inputParameters.get(paramName);
+        if (value == null) {
+            return null;
+        }
+
+        if (value instanceof String) {
+            String strValue = value.toString();
+            // 如果是变量引用（以$开头），从全局变量中获取
+            if (strValue.startsWith("$")) {
+                String varName = strValue.substring(1);
+                return context.getGlobalVars().get(varName);
+            }
+        }
+        
+        return value;
+    }
+
+    /**
+     * 构建嵌入参数
+     */
+    private KnowledgeEmbeddingParamsDTO buildEmbeddingParams(String knowledgeInventory, String continueWhenErr,
+                                                           String knowledgeType, Object deepParseParam, Object separatorContent,
+                                                           Map<String, Object> inputParameters, WorkflowContext context) {
+        KnowledgeEmbeddingParamsDTO params = new KnowledgeEmbeddingParamsDTO();
+        params.setKnowledgeInventory(knowledgeInventory);
+        params.setContinueWhenErr(StringUtils.isNotBlank(continueWhenErr) ? Integer.parseInt(continueWhenErr) : 0);
+        params.setKnowledgeType(StringUtils.isNotBlank(knowledgeType) ? knowledgeType : "sn");
+
+        // 处理深度解析参数
+        List<String> deepParse = new ArrayList<>();
+        if (deepParseParam instanceof List) {
+            List<?> deepParseList = (List<?>) deepParseParam;
+            for (Object item : deepParseList) {
+                if (item != null) {
+                    deepParse.add(item.toString());
+                }
+            }
+        }
+        params.setDeepParse(deepParse);
+
+        // 处理分段规则
+        buildSplitRule(inputParameters, params, separatorContent, context);
+
+        return params;
+    }
+
+    /**
+     * 构建分段规则
+     */
+    private void buildSplitRule(Map<String, Object> inputParameters, KnowledgeEmbeddingParamsDTO params,
+                               Object separatorObj, WorkflowContext context) {
+        String splitRuleType = getParameterValue(inputParameters, "splitRuleType", context);
+
+        if (StringUtils.isNotBlank(splitRuleType) && "1".equals(splitRuleType)) {
+            // 自定义分段规则
+            String splitRule = getParameterValue(inputParameters, "splitRule", context);
+            String splitter = getParameterValue(inputParameters, "splitter", context);
+            String wordCountLimit = getParameterValue(inputParameters, "wordCountLimit", context);
+            String wordCountOverlap = getParameterValue(inputParameters, "wordCountOverlap", context);
+
+            if (StringUtils.isBlank(splitRule)) {
+                throw new ServiceException(ExecutorError.KNOWLEDGE_SPLIT_RULE_CONFIG_ERROR);
+            }
+
+            params.setSplitRuleType(splitRuleType);
+            params.setSplitRule(Integer.parseInt(splitRule));
+
+            if ("1".equals(splitRule)) {
+                if (StringUtils.isBlank(splitter)) {
+                    throw new ServiceException(ExecutorError.KNOWLEDGE_SPLIT_RULE_CONFIG_ERROR.getCode(), "切分器不能为空");
+                }
+                if (StringUtils.isBlank(wordCountLimit)) {
+                    throw new ServiceException(ExecutorError.KNOWLEDGE_SPLIT_RULE_CONFIG_ERROR.getCode(), "分段字数上限不能为空");
+                }
+
+                params.setSplitter(Integer.parseInt(splitter));
+                params.setWordCountLimit(Integer.parseInt(wordCountLimit));
+
+                if (!"1".equals(splitter) && StringUtils.isBlank(wordCountOverlap)) {
+                    throw new ServiceException(ExecutorError.KNOWLEDGE_SPLIT_RULE_CONFIG_ERROR.getCode(), "重叠区域字数不能为空");
+                }
+                if (StringUtils.isNotBlank(wordCountOverlap)) {
+                    params.setWordCountOverlap(Integer.parseInt(wordCountOverlap));
+                }
+
+                if ("3".equals(splitter)) {
+                    if (separatorObj == null) {
+                        throw new ServiceException(ExecutorError.KNOWLEDGE_SPLIT_RULE_CONFIG_ERROR.getCode(), "分隔符设置不能为空");
+                    }
+                    params.setSeparatorContent(JSONUtil.toJsonStr(separatorObj));
+                }
+            }
+        } else {
+            // 跟随知识库设置
+            IKnowledgeService.KnowledgeInventoryInfo inventoryInfo =
+                    knowledgeService.checkKnowledgeInventory(params.getKnowledgeInventory());
+            params.setSplitRule(inventoryInfo.getSplitRule());
+            params.setSplitter(inventoryInfo.getSplitter());
+            params.setWordCountLimit(inventoryInfo.getWordCountLimit());
+            params.setWordCountOverlap(inventoryInfo.getWordCountOverlap());
+            params.setSeparatorContent(inventoryInfo.getSeparatorContent());
+            params.setSplitRuleType("0");
+        }
+    }
+
+    /**
+     * 执行文件嵌入
+     */
+    private KnowledgeEmbeddingResultDTO executeFileEmbedding(List<FileInfoDto> files,
+                                                           KnowledgeEmbeddingParamsDTO embeddingParams,
+                                                           WorkflowContext context) {
+        KnowledgeEmbeddingResultDTO result = new KnowledgeEmbeddingResultDTO();
+        List<FileInfoDto> successEmbedding = new ArrayList<>();
+        Map<String, String> errorMsg = new HashMap<>();
+        String authorization = (String) context.getGlobalVars().get("authorization");
+
+        Integer continueWhenErr = embeddingParams.getContinueWhenErr();
+        String knowledgeInventory = embeddingParams.getKnowledgeInventory();
+
+        if (continueWhenErr == 1) {
+            // 如果选择报错继续，try catch接收
+            files.forEach(fileInfoDto -> {
+                try {
+                    ResultVo<BaseDetailVo> baseDetailVoResultVoResult =  docFeignClient.detailBySn(knowledgeInventory);
+                    if (baseDetailVoResultVoResult.getCode() == 0 && baseDetailVoResultVoResult.getData() != null) {
+                        BaseDetailVo baseDetailVo = baseDetailVoResultVoResult.getData();
+                        if(Objects.isNull(baseDetailVo) || StringUtils.isEmpty(baseDetailVo.getKbSn())){
+                            throw new ServiceException(ExecutorError.KNOWLEDGE_INVENTORY_IS_NOT_FOUND);
+                        }
+                    }
+                    executeFileEmbeddingForSingleFile(embeddingParams, authorization, fileInfoDto, successEmbedding);
+                } catch (Exception e) {
+                    errorMsg.put(fileInfoDto.getFormatFileSn(), e.toString());
+                    log.error("KnowledgeAddNodeExecutor, file embedding failed，err: [{}]", e.toString());
+                }
+            });
+        } else {
+            // 如果选择报错不继续，直接循环
+            files.forEach(fileInfoDto -> {
+                ResultVo<BaseDetailVo> baseDetailVoResultVoResult =  docFeignClient.detailBySn(knowledgeInventory);
+                if (baseDetailVoResultVoResult.getCode() == 0 && baseDetailVoResultVoResult.getData() != null) {
+                    BaseDetailVo baseDetailVo = baseDetailVoResultVoResult.getData();
+                    if(Objects.isNull(baseDetailVo) || StringUtils.isEmpty(baseDetailVo.getKbSn())){
+                        throw new ServiceException(ExecutorError.KNOWLEDGE_INVENTORY_IS_NOT_FOUND);
+                    }
+                }
+                executeFileEmbeddingForSingleFile(embeddingParams, authorization, fileInfoDto, successEmbedding);
+            });
+        }
+
+        result.setDatasetId(knowledgeInventory);
+        result.setErrorMsgs(errorMsg);
+        result.setFiles(successEmbedding);
+
+        return result;
+    }
+
+    /**
+     * 执行单个文件的嵌入
+     */
+    private void executeFileEmbeddingForSingleFile(KnowledgeEmbeddingParamsDTO embeddingParams, String authorization,
+                                                  FileInfoDto fileInfoDto, List<FileInfoDto> successEmbedding) {
+        String knowledgeInventory = embeddingParams.getKnowledgeInventory();
+
+        // TODO 这里待提供替换 相同文件上传两次如何处理
+        // 校验是否是重复的md5
+
+        ResultVo<List<FileInfoDto>> duplicateResult =
+                knowledgeService.findFilesByDatasetIdAndMd5(knowledgeInventory, fileInfoDto.getFileMd5());
+
+        if (duplicateResult.getCode() == 0 && duplicateResult.getData() != null && !duplicateResult.getData().isEmpty()) {
+            log.info("doc file [{}] already exist in knowledge inventory [{}], return doc file",
+                    fileInfoDto.getFileSn(), knowledgeInventory);
+
+            // 如果知识库中md5的文件说明重复了，不再embedding
+            FileInfoDto existingFile = duplicateResult.getData().get(0);
+
+
+            if(existingFile != null){
+                if (existingFile.getStatus() == 3) { // 失败状态
+                    // TODO 这里待提供替换
+                    // 对于重复的文件，查询状态，如果是失败的，先删除该文件，再进行embedding
+                    DeleteBySnFeignDto deleteBySnFeignDto = new DeleteBySnFeignDto();
+                    deleteBySnFeignDto.setKbSn(knowledgeInventory);
+                    deleteBySnFeignDto.setFileId(fileInfoDto.getFileId());
+                    docFeignClient.deleteById(deleteBySnFeignDto);
+
+                    actualExecuteEmbedding(embeddingParams, authorization, fileInfoDto, successEmbedding);
+                    return;
+                }
+            }
+
+
+            successEmbedding.add(existingFile);
+            return;
+        }
+
+        actualExecuteEmbedding(embeddingParams, authorization, fileInfoDto, successEmbedding);
+    }
+
+    /**
+     * 实际执行嵌入
+     */
+    private void actualExecuteEmbedding(KnowledgeEmbeddingParamsDTO embeddingParams, String authorization,
+                                       FileInfoDto fileInfoDto, List<FileInfoDto> successEmbedding) {
+        KnowledgeFileEmbeddingDTO embeddingReq = prepareEmbeddingRequest(embeddingParams, fileInfoDto);
+        log.info("KnowledgeAddNodeExecutor call fileEmbedding, request param: {}", JsonUtils.toJsonString(embeddingReq));
+
+        // TODO 这里待提供替换
+        ResultVo<FileInfoDto> result = docFeignClient.fileEmbedding(embeddingReq, authorization);
+        if (result.getCode() != 0) {
+            throw new ServiceException(result.getCode(), result.getMessage());
+        }
+
+        FileInfoDto embeddedFile = result.getData();
+        if (embeddedFile == null) {
+            throw new ServiceException(ExecutorError.KNOWLEDGE_FILE_EMBEDDING_FAILED);
+        }
+
+        // 轮询embedding状态
+        pollEmbeddingStatus(embeddedFile.getFileId());
+        successEmbedding.add(embeddedFile);
+    }
+
+    /**
+     * 准备embedding请求
+     */
+    private KnowledgeFileEmbeddingDTO prepareEmbeddingRequest(KnowledgeEmbeddingParamsDTO embeddingParams,
+                                                            FileInfoDto fileInfoDto) {
+        String knowledgeInventory = embeddingParams.getKnowledgeInventory();
+        List<String> deepParse = embeddingParams.getDeepParse();
+        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo =
+                knowledgeService.checkKnowledgeInventory(knowledgeInventory);
+
+        KnowledgeFileEmbeddingDTO embeddingRequest = new KnowledgeFileEmbeddingDTO();
+        embeddingRequest.setDatasetId(knowledgeInventory);
+        embeddingRequest.setFileInfo(fileInfoDto);
+        embeddingRequest.setModelSn(inventoryInfo.getModelSn());
+        embeddingRequest.setEmbeddingType("PROCESS");
+        embeddingRequest.setUploadType(1); // PROCESS类型
+        embeddingRequest.setSplitRuleType(
+                embeddingParams.getSplitRuleType() != null ? Integer.parseInt(embeddingParams.getSplitRuleType()) : null);
+
+        // 构建嵌入配置
+        KnowledgeFileEmbeddingDTO.EmbeddingConfig embeddingConfig = new KnowledgeFileEmbeddingDTO.EmbeddingConfig();
+        embeddingConfig.setOcrTable(false);
+        embeddingConfig.setOcrImage(false);
+        embeddingConfig.setOcrWord(false);
+
+        if (deepParse != null && !deepParse.isEmpty()) {
+            for (String deep : deepParse) {
+                switch (deep) {
+                    case "parseForm":
+                        embeddingConfig.setOcrTable(true);
+                        break;
+                    case "parseImage":
+                        embeddingConfig.setOcrImage(true);
+                        break;
+                    case "parseWord":
+                        embeddingConfig.setOcrWord(true);
+                        break;
+                }
+            }
+        }
+
+        // 设置分段规则
+        String splitRuleType = embeddingParams.getSplitRuleType();
+        if ("1".equals(splitRuleType) || "0".equals(splitRuleType)) {
+            embeddingConfig.setSplitRule(embeddingParams.getSplitRule());
+            embeddingConfig.setSplitter(embeddingParams.getSplitter());
+            embeddingConfig.setWordCountLimit(embeddingParams.getWordCountLimit());
+            embeddingConfig.setWordCountOverlap(embeddingParams.getWordCountOverlap());
+
+            if (StringUtils.isNotBlank(embeddingParams.getSeparatorContent())) {
+                embeddingConfig.setSeparators(JSONUtil.toList(embeddingParams.getSeparatorContent(), String.class));
+            } else {
+                embeddingConfig.setSeparators(new ArrayList<>());
+            }
+        }
+
+        embeddingRequest.setEmbeddingConfig(embeddingConfig);
+        return embeddingRequest;
+    }
+
+    /**
+     * 轮询embedding状态
+     */
+    private void pollEmbeddingStatus(Long fileId) {
+        long pollingInterval = 3000; // 3秒
+        int maxPollingAttempts = 1000;
+
+        for (int attempt = 1; attempt <= maxPollingAttempts; attempt++) {
+            // TODO 这里待提供替换
+            ResultVo<IKnowledgeService.FileStatusInfo> statusResult = knowledgeService.getFileStatus(fileId);
+
+            if (statusResult.getCode() == 0 && statusResult.getData() != null) {
+                IKnowledgeService.FileStatusInfo statusInfo = statusResult.getData();
+
+                if (statusInfo.getStatus() == 2) { // 成功
+                    break;
+                } else if (statusInfo.getStatus() == 3) { // 失败
+                    throw new ServiceException(ExecutorError.KNOWLEDGE_FILE_EMBEDDING_FAILED.getCode(),
+                            "文件embedding失败: " + statusInfo.getErrorReason());
+                }
+            }
+
+            try {
+                TimeUnit.MILLISECONDS.sleep(pollingInterval);
+            } catch (InterruptedException e) {
+                log.error("thread sleep interrupted failed, {}", e.toString());
+                Thread.currentThread().interrupt();
+                break;
+            }
+        }
+    }
+
+    /**
+     * 构建输出结果
+     */
+    private Map<String, Object> buildOutputResult(KnowledgeEmbeddingResultDTO embeddingResult, int totalFiles) {
+        int succeededCount = embeddingResult.getFiles().size();
+        int failedCount = totalFiles - succeededCount;
+        String resultMessage = String.format("成功:%d，失败:%d", succeededCount, failedCount);
+
+        Map<String, Object> result = new HashMap<>();
+        result.put("embeddingFiles", embeddingResult.getFiles().stream()
+                .map(fileInfo -> String.format("%s::%s::%s", fileInfo.getFileId(), fileInfo.getFileName(), "KNOWLEDGE"))
+                .collect(Collectors.toList()));
+        result.put("knowledgeInventorySn", embeddingResult.getDatasetId());
+        result.put("errorMsg", embeddingResult.getErrorMsgs());
+        result.put("resultMessage", resultMessage);
+        result.put("knowledgeNames", embeddingResult.getFiles().stream()
+                .map(FileInfoDto::getFileName)
+                .collect(Collectors.toList()));
+
+        return result;
+    }
+
+    /**
+     * 写入输出参数
+     */
+    private void writeOutputParameters(Map<String, Object> nodeDef, WorkflowContext context, Map<String, Object> result) {
+        Map<String, Object> outputParameters = (Map<String, Object>) nodeDef.get("outputParameters");
+        if (outputParameters != null) {
+            for (Map.Entry<String, Object> entry : outputParameters.entrySet()) {
+                String outputKey = entry.getKey();
+                String variableName = entry.getValue().toString();
+
+                Object resultValue = result.get(outputKey);
+                if (resultValue != null) {
+                    context.setVar(variableName, resultValue);
+                    log.info("Set variable {} = {}", variableName, resultValue);
+                }
+            }
+        }
+    }
+
+    @Override
+    public String getType() {
+        return "KNOWLEDGE_ADD";
+    }
+}
Index: application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/ProcessAgentExecutor.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.ai.application.agent.run.executor.agent;\r\n\r\nimport com.ai.application.agent.base.api.dto.AgentChatDTO;\r\nimport com.ai.application.agent.base.api.feign.IAgentClient;\r\nimport com.ai.application.agent.base.api.vo.AgentChatVO;\r\nimport com.ai.application.agent.run.errors.AgentNodeExecutorError;\r\nimport com.ai.application.agent.run.executor.AgentExecutionContext;\r\nimport com.ai.framework.core.exception.ServiceException;\r\nimport com.ai.framework.core.util.json.JsonUtils;\r\nimport com.ai.framework.core.vo.ResultVo;\r\nimport com.fasterxml.jackson.databind.JsonNode;\r\nimport lombok.extern.slf4j.Slf4j;\r\nimport org.apache.commons.lang3.StringUtils;\r\nimport org.springframework.beans.factory.annotation.Autowired;\r\nimport org.springframework.stereotype.Component;\r\n\r\nimport java.util.ArrayList;\r\nimport java.util.HashMap;\r\nimport java.util.List;\r\nimport java.util.Map;\r\nimport java.util.Objects;\r\n\r\n/**\r\n * 流程型智能体执行器\r\n */\r\n@Slf4j\r\n@Component\r\npublic class ProcessAgentExecutor extends BaseAgentExecutor {\r\n\r\n    @Autowired\r\n    private IAgentClient agentClient;\r\n\r\n    @Override\r\n    public Map<String, Object> execute(AgentExecutionContext executionContext) {\r\n        String processInstanceId = executionContext.getProcessInstanceId();\r\n        String agentSn = executionContext.getParameterAsString(\"agentSn\");\r\n        String agentType = executionContext.getParameterAsString(\"agentType\");\r\n        String sessionSn = executionContext.getParameterAsString(\"sessionSn\");\r\n        String msgContent = executionContext.getParameterAsString(\"msgContent\");\r\n        String auth = executionContext.getAuthorization();\r\n        \r\n        // 会话管理\r\n        boolean contextMemorised = Objects.equals(\"true\", executionContext.getParameterAsString(\"contextMemorised\"));\r\n        if (sessionSn == null || !contextMemorised) {\r\n            String modelSn = executionContext.getParameterAsString(\"model\");\r\n            String prompt = executionContext.getParameterAsString(\"sysPrompt\");\r\n            String temperature = executionContext.getParameterAsString(\"temperature\");\r\n            \r\n            if (StringUtils.isNotBlank(prompt) || StringUtils.isNotBlank(modelSn) || StringUtils.isNotBlank(temperature)) {\r\n                sessionSn = newDebugSessionSn(auth, agentSn, modelSn, prompt, \r\n                    temperature != null ? Double.parseDouble(temperature) : 0.6);\r\n            } else {\r\n                sessionSn = newSessionSn(auth, agentSn);\r\n            }\r\n        }\r\n\r\n        // 构建智能体请求\r\n        AgentChatDTO chatDto = AgentChatDTO.builder()\r\n                .agentSn(agentSn)\r\n                .agentType(agentType)\r\n                .msgContent(msgContent)\r\n                .msgType(\"text\")\r\n                .delayInMs(20L)\r\n                .fromCode(\"Workflow\")\r\n                .processId(executionContext.getProcessId())\r\n                .sessionSn(sessionSn)\r\n                .debug(executionContext.isDebugRun())\r\n                .build();\r\n\r\n        log.info(\"[{}] {} agent process request body {}\", processInstanceId, agentType, JsonUtils.toJsonString(chatDto));\r\n\r\n        // 调用智能体服务\r\n        ResultVo<AgentChatVO> result = agentClient.chat(chatDto);\r\n\r\n        if (result == null || !Objects.equals(result.getCode(), 0)) {\r\n            String errorMsg = result != null ? result.getMessage() : \"调用文档智能体失败\";\r\n            log.error(\"[{}] {} agent doc error {}\", processInstanceId, agentType, errorMsg);\r\n            throw new ServiceException(51004,\"调用文档智能体失败: \" + errorMsg);\r\n        }\r\n\r\n        AgentChatVO agentChatVo = result.getData();\r\n        if (agentChatVo == null) {\r\n            throw new ServiceException(AgentNodeExecutorError.AGENT_RESPONSE_IS_NULL);\r\n        }\r\n\r\n        // 解析返回结果\r\n        String message = parseMsgContent(agentChatVo);\r\n        \r\n        // 流程智能体特有的逻辑：解析流程节点信息\r\n        List<Object> processNodes = parseProcessNodes(agentChatVo);\r\n        Map<String, Object> processDebug = parseProcessDebug(agentChatVo);\r\n        \r\n        log.info(\"[{}] {} agent process response, message: {}, nodes: {}\", \r\n                processInstanceId, agentType, message, processNodes.size());\r\n\r\n        // 构建返回结果\r\n        Map<String, Object> resultMap = new HashMap<>();\r\n        resultMap.put(\"message\", message);\r\n        resultMap.put(\"sessionSn\", sessionSn);\r\n        resultMap.put(\"success\", agentChatVo.getSuccess());\r\n        resultMap.put(\"processNodes\", processNodes);\r\n        resultMap.put(\"processDebug\", processDebug);\r\n        \r\n        return resultMap;\r\n    }\r\n\r\n    /**\r\n     * 解析消息内容\r\n     */\r\n    private String parseMsgContent(AgentChatVO agentChatVo) {\r\n        if (agentChatVo.getContent() != null) {\r\n            JsonNode answerNode = agentChatVo.getContent().get(\"answer\");\r\n            if (answerNode != null && answerNode.isTextual()) {\r\n                return answerNode.asText();\r\n            }\r\n        }\r\n        \r\n        return StringUtils.defaultIfBlank(agentChatVo.getReply(), \"\");\r\n    }\r\n\r\n    /**\r\n     * 解析流程节点信息\r\n     */\r\n    private List<Object> parseProcessNodes(AgentChatVO agentChatVo) {\r\n        List<Object> nodesList = new ArrayList<>();\r\n        \r\n        if (agentChatVo.getContent() != null) {\r\n            JsonNode nodesNode = agentChatVo.getContent().get(\"nodes\");\r\n            if (nodesNode != null && nodesNode.isArray()) {\r\n                for (JsonNode nodeNode : nodesNode) {\r\n                    Map<String, Object> nodeInfo = new HashMap<>();\r\n                    \r\n                    if (nodeNode.has(\"id\")) {\r\n                        nodeInfo.put(\"id\", nodeNode.get(\"id\").asText());\r\n                    }\r\n                    if (nodeNode.has(\"name\")) {\r\n                        nodeInfo.put(\"name\", nodeNode.get(\"name\").asText());\r\n                    }\r\n                    if (nodeNode.has(\"type\")) {\r\n                        nodeInfo.put(\"type\", nodeNode.get(\"type\").asText());\r\n                    }\r\n                    if (nodeNode.has(\"status\")) {\r\n                        nodeInfo.put(\"status\", nodeNode.get(\"status\").asText());\r\n                    }\r\n                    if (nodeNode.has(\"output\")) {\r\n                        nodeInfo.put(\"output\", nodeNode.get(\"output\"));\r\n                    }\r\n                    \r\n                    if (!nodeInfo.isEmpty()) {\r\n                        nodesList.add(nodeInfo);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        \r\n        return nodesList;\r\n    }\r\n\r\n    /**\r\n     * 解析流程调试信息\r\n     */\r\n    private Map<String, Object> parseProcessDebug(AgentChatVO agentChatVo) {\r\n        Map<String, Object> debugInfo = new HashMap<>();\r\n        \r\n        if (agentChatVo.getContent() != null) {\r\n            JsonNode debugNode = agentChatVo.getContent().get(\"processDebug\");\r\n            if (debugNode != null && debugNode.isObject()) {\r\n                if (debugNode.has(\"processId\")) {\r\n                    debugInfo.put(\"processId\", debugNode.get(\"processId\").asText());\r\n                }\r\n                if (debugNode.has(\"instanceId\")) {\r\n                    debugInfo.put(\"instanceId\", debugNode.get(\"instanceId\").asText());\r\n                }\r\n                if (debugNode.has(\"status\")) {\r\n                    debugInfo.put(\"status\", debugNode.get(\"status\").asText());\r\n                }\r\n                if (debugNode.has(\"startTime\")) {\r\n                    debugInfo.put(\"startTime\", debugNode.get(\"startTime\").asText());\r\n                }\r\n                if (debugNode.has(\"endTime\")) {\r\n                    debugInfo.put(\"endTime\", debugNode.get(\"endTime\").asText());\r\n                }\r\n            }\r\n        }\r\n        \r\n        return debugInfo;\r\n    }\r\n\r\n    @Override\r\n    public String getAgentType() {\r\n        return \"process\";\r\n    }\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/ProcessAgentExecutor.java b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/ProcessAgentExecutor.java
--- a/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/ProcessAgentExecutor.java	(revision 27ed3acd0bd29b025bcb2499817b2608a5835ce1)
+++ b/application/application-agent-run/agent-run-server/src/main/java/com/ai/application/agent/run/executor/agent/ProcessAgentExecutor.java	(date 1749808647494)
@@ -3,7 +3,7 @@
 import com.ai.application.agent.base.api.dto.AgentChatDTO;
 import com.ai.application.agent.base.api.feign.IAgentClient;
 import com.ai.application.agent.base.api.vo.AgentChatVO;
-import com.ai.application.agent.run.errors.AgentNodeExecutorError;
+import com.ai.application.agent.run.errors.ExecutorError;
 import com.ai.application.agent.run.executor.AgentExecutionContext;
 import com.ai.framework.core.exception.ServiceException;
 import com.ai.framework.core.util.json.JsonUtils;
@@ -80,7 +80,7 @@
 
         AgentChatVO agentChatVo = result.getData();
         if (agentChatVo == null) {
-            throw new ServiceException(AgentNodeExecutorError.AGENT_RESPONSE_IS_NULL);
+            throw new ServiceException(ExecutorError.AGENT_RESPONSE_IS_NULL);
         }
 
         // 解析返回结果
Index: application/application-agent-run/agent-run-server/src/test/java/com/ai/application/agent/run/executor/KnowledgeAddNodeExecutorIntegrationTest.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/application/application-agent-run/agent-run-server/src/test/java/com/ai/application/agent/run/executor/KnowledgeAddNodeExecutorIntegrationTest.java b/application/application-agent-run/agent-run-server/src/test/java/com/ai/application/agent/run/executor/KnowledgeAddNodeExecutorIntegrationTest.java
new file mode 100644
--- /dev/null	(date 1749808647518)
+++ b/application/application-agent-run/agent-run-server/src/test/java/com/ai/application/agent/run/executor/KnowledgeAddNodeExecutorIntegrationTest.java	(date 1749808647518)
@@ -0,0 +1,322 @@
+package com.ai.application.agent.run.executor;
+
+import com.ai.application.agent.run.service.IKnowledgeService;
+import com.ai.framework.workflow.context.NodeContext;
+import com.ai.framework.workflow.context.WorkflowContext;
+import com.ai.framework.workflow.enums.NodeStatus;
+import lombok.extern.slf4j.Slf4j;
+import org.junit.jupiter.api.BeforeEach;
+import org.junit.jupiter.api.Test;
+import org.springframework.boot.test.mock.mockito.MockBean;
+import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
+
+import java.util.*;
+
+import static org.junit.jupiter.api.Assertions.*;
+import static org.mockito.ArgumentMatchers.any;
+import static org.mockito.ArgumentMatchers.anyString;
+import static org.mockito.Mockito.when;
+
+/**
+ * 知识添加节点执行器集成测试
+ * 模拟真实的工作流场景
+ */
+@SpringJUnitConfig
+@Slf4j
+class KnowledgeAddNodeExecutorIntegrationTest {
+
+    @MockBean
+    private IKnowledgeService knowledgeService;
+
+    private KnowledgeAddNodeExecutor knowledgeAddNodeExecutor;
+    private WorkflowContext workflowContext;
+
+    @BeforeEach
+    void setUp() {
+        knowledgeAddNodeExecutor = new KnowledgeAddNodeExecutor();
+        setupWorkflowContext();
+    }
+
+    private void setupWorkflowContext() {
+        // 模拟真实的工作流上下文
+        workflowContext = new WorkflowContext();
+        workflowContext.setWorkflowInstanceId(12345L);
+        workflowContext.setCurrentNodeKey("knowledge_add_001");
+        
+        // 设置全局变量
+        Map<String, Object> globalVars = new HashMap<>();
+        globalVars.put("authorization", "Bearer test-token");
+        globalVars.put("tenantName", "TEST_TENANT");
+        globalVars.put("knowledgeBase", "kb_001");
+        globalVars.put("documentContent", "这是一个测试文档的内容，用于验证知识添加功能。");
+        workflowContext.setGlobalVars(globalVars);
+
+        // 创建节点上下文
+        NodeContext nodeContext = new NodeContext();
+        nodeContext.setNodeKey("knowledge_add_001");
+        nodeContext.setStatus(NodeStatus.INIT);
+        nodeContext.setOutput(new HashMap<>());
+
+        // 设置节点定义
+        Map<String, Object> nodeDefinition = new HashMap<>();
+        
+        // 输入参数
+        Map<String, Object> inputParameters = new HashMap<>();
+        inputParameters.put("knowledgeInventorySn", "$knowledgeBase");
+        inputParameters.put("input", "$documentContent");
+        inputParameters.put("continueWhenErr", "0");
+        inputParameters.put("knowledgeType", "sn");
+        inputParameters.put("deepParse", Arrays.asList("parseWord", "parseImage"));
+        inputParameters.put("splitRuleType", "0");
+        nodeDefinition.put("inputParameters", inputParameters);
+        
+        // 输出参数
+        Map<String, Object> outputParameters = new HashMap<>();
+        outputParameters.put("embeddingFiles", "embeddingFilesResult");
+        outputParameters.put("knowledgeInventorySn", "knowledgeInventoryResult");
+        outputParameters.put("resultMessage", "processResult");
+        outputParameters.put("knowledgeNames", "knowledgeNamesResult");
+        nodeDefinition.put("outputParameters", outputParameters);
+        
+        nodeContext.setNodeDefinition(nodeDefinition);
+        workflowContext.getNodeContexts().put("knowledge_add_001", nodeContext);
+    }
+
+    @Test
+    void testRealWorldKnowledgeAddWorkflow() {
+        // 模拟知识库服务返回
+        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
+        inventoryInfo.setModelSn("embedding-model-v1");
+        inventoryInfo.setSplitRule(1);
+        inventoryInfo.setSplitter(1);
+        inventoryInfo.setWordCountLimit(500);
+        inventoryInfo.setWordCountOverlap(50);
+        inventoryInfo.setSeparatorContent("[\"\n\", \"\n\n\", \" \"]");
+
+        when(knowledgeService.checkKnowledgeInventory("kb_001")).thenReturn(inventoryInfo);
+
+        // 执行知识添加节点
+        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));
+
+        // 验证执行结果
+        NodeContext nodeContext = workflowContext.getNodeContexts().get("knowledge_add_001");
+        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
+        
+        // 验证输出结果
+        assertNotNull(nodeContext.getOutput().get("embeddingFiles"));
+        assertNotNull(nodeContext.getOutput().get("resultMessage"));
+        assertNotNull(nodeContext.getOutput().get("knowledgeNames"));
+        
+        // 验证全局变量被正确设置
+        assertNotNull(workflowContext.getGlobalVars().get("embeddingFilesResult"));
+        assertNotNull(workflowContext.getGlobalVars().get("processResult"));
+        
+        // 验证节点执行时间被设置
+        assertNotNull(nodeContext.getEndTime());
+    }
+
+    @Test
+    void testKnowledgeAddWithCustomSplitRule() {
+        // 修改输入参数使用自定义分段规则
+        NodeContext nodeContext = workflowContext.getNodeContexts().get("knowledge_add_001");
+        Map<String, Object> nodeDefinition = nodeContext.getNodeDefinition();
+        Map<String, Object> inputParameters = (Map<String, Object>) nodeDefinition.get("inputParameters");
+        
+        // 设置自定义分段规则
+        inputParameters.put("splitRuleType", "1");
+        inputParameters.put("splitRule", "1");
+        inputParameters.put("splitter", "2");
+        inputParameters.put("wordCountLimit", "300");
+        inputParameters.put("wordCountOverlap", "30");
+        
+        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
+        inventoryInfo.setModelSn("embedding-model-v1");
+
+        when(knowledgeService.checkKnowledgeInventory(anyString())).thenReturn(inventoryInfo);
+
+        // 执行知识添加节点
+        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));
+
+        // 验证执行结果
+        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
+        assertNotNull(nodeContext.getOutput().get("resultMessage"));
+    }
+
+    @Test
+    void testKnowledgeAddWithMultipleDocuments() {
+        // 修改输入参数为多个文档
+        NodeContext nodeContext = workflowContext.getNodeContexts().get("knowledge_add_001");
+        Map<String, Object> nodeDefinition = nodeContext.getNodeDefinition();
+        Map<String, Object> inputParameters = (Map<String, Object>) nodeDefinition.get("inputParameters");
+        
+        // 设置多个文档输入
+        List<String> documents = Arrays.asList(
+            "第一个文档：关于人工智能的基础知识",
+            "第二个文档：机器学习算法详解",
+            "第三个文档：深度学习应用案例"
+        );
+        workflowContext.getGlobalVars().put("multipleDocuments", documents);
+        inputParameters.put("input", "$multipleDocuments");
+
+        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
+        inventoryInfo.setModelSn("embedding-model-v1");
+
+        when(knowledgeService.checkKnowledgeInventory(anyString())).thenReturn(inventoryInfo);
+
+        // 执行知识添加节点
+        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));
+
+        // 验证执行结果
+        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
+        
+        // 验证处理了多个文档
+        List<String> embeddingFiles = (List<String>) nodeContext.getOutput().get("embeddingFiles");
+        assertNotNull(embeddingFiles);
+        
+        String resultMessage = (String) nodeContext.getOutput().get("resultMessage");
+        assertNotNull(resultMessage);
+        assertTrue(resultMessage.contains("成功"));
+    }
+
+    @Test
+    void testKnowledgeAddWithDeepParsing() {
+        // 修改输入参数启用深度解析
+        NodeContext nodeContext = workflowContext.getNodeContexts().get("knowledge_add_001");
+        Map<String, Object> nodeDefinition = nodeContext.getNodeDefinition();
+        Map<String, Object> inputParameters = (Map<String, Object>) nodeDefinition.get("inputParameters");
+        
+        // 设置深度解析选项
+        inputParameters.put("deepParse", Arrays.asList("parseForm", "parseImage", "parseWord"));
+
+        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
+        inventoryInfo.setModelSn("embedding-model-v1");
+
+        when(knowledgeService.checkKnowledgeInventory(anyString())).thenReturn(inventoryInfo);
+
+        // 执行知识添加节点
+        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));
+
+        // 验证执行结果
+        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
+        assertNotNull(nodeContext.getOutput().get("resultMessage"));
+    }
+
+    @Test
+    void testKnowledgeAddFailure() {
+        // 模拟知识库服务失败
+        when(knowledgeService.checkKnowledgeInventory(anyString()))
+                .thenThrow(new RuntimeException("知识库不存在"));
+
+        // 执行知识添加节点并验证异常
+        assertThrows(Exception.class, () -> knowledgeAddNodeExecutor.execute(workflowContext));
+
+        // 验证节点状态
+        NodeContext nodeContext = workflowContext.getNodeContexts().get("knowledge_add_001");
+        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
+        assertNotNull(nodeContext.getErrorMsg());
+        assertTrue(nodeContext.getErrorMsg().contains("知识添加执行失败"));
+    }
+
+    @Test
+    void testCompleteKnowledgeWorkflowScenario() {
+        // 模拟完整的知识添加工作流场景
+        // 1. 设置复杂的输入数据
+        workflowContext.getGlobalVars().put("companyKnowledge", "公司产品手册内容...");
+        workflowContext.getGlobalVars().put("targetKnowledgeBase", "company_kb_2024");
+
+        // 2. 修改知识添加节点配置
+        NodeContext nodeContext = workflowContext.getNodeContexts().get("knowledge_add_001");
+        Map<String, Object> nodeDefinition = nodeContext.getNodeDefinition();
+        Map<String, Object> inputParameters = (Map<String, Object>) nodeDefinition.get("inputParameters");
+        
+        inputParameters.put("knowledgeInventorySn", "$targetKnowledgeBase");
+        inputParameters.put("input", "$companyKnowledge");
+        inputParameters.put("continueWhenErr", "1"); // 遇到错误继续
+        inputParameters.put("splitRuleType", "1"); // 自定义分段规则
+        inputParameters.put("splitRule", "1");
+        inputParameters.put("splitter", "2");
+        inputParameters.put("wordCountLimit", "800");
+        inputParameters.put("wordCountOverlap", "80");
+
+        // 3. 模拟知识库服务返回
+        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
+        inventoryInfo.setModelSn("company-embedding-model");
+        inventoryInfo.setSplitRule(1);
+        inventoryInfo.setSplitter(2);
+        inventoryInfo.setWordCountLimit(800);
+        inventoryInfo.setWordCountOverlap(80);
+
+        when(knowledgeService.checkKnowledgeInventory("company_kb_2024")).thenReturn(inventoryInfo);
+
+        // 4. 执行知识添加节点
+        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));
+
+        // 5. 验证完整的执行结果
+        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
+        assertNotNull(nodeContext.getOutput().get("embeddingFiles"));
+        assertNotNull(nodeContext.getOutput().get("resultMessage"));
+        
+        // 6. 验证全局变量传递
+        assertEquals("company_kb_2024", workflowContext.getGlobalVars().get("knowledgeInventoryResult"));
+        assertNotNull(workflowContext.getGlobalVars().get("embeddingFilesResult"));
+        
+        // 7. 验证可以传递给下一个节点
+        String resultMessage = (String) workflowContext.getGlobalVars().get("processResult");
+        assertNotNull(resultMessage);
+        assertTrue(resultMessage.contains("成功"));
+    }
+
+    @Test
+    void testRealWorldKnowledgeAddScenarioFromBackend2() {
+        // 模拟真实的 backend2 场景
+        // 基于您提供的真实入参进行测试
+        workflowContext.getGlobalVars().put("d3ef893c5c", "这是从LLM节点输出的文本内容");
+        workflowContext.getGlobalVars().put("realKnowledgeBase", "f6935b89-09a4-4d96-9dfc-e3e04a00cf66");
+
+        // 修改知识添加节点配置（完全按照 backend2 的真实配置）
+        NodeContext nodeContext = workflowContext.getNodeContexts().get("knowledge_add_001");
+        Map<String, Object> nodeDefinition = nodeContext.getNodeDefinition();
+        Map<String, Object> inputParameters = (Map<String, Object>) nodeDefinition.get("inputParameters");
+
+        // 设置真实的 backend2 参数
+        inputParameters.put("input", "{{d3ef893c5c}}"); // 使用变量引用语法
+        inputParameters.put("knowledgeInventorySn", "$realKnowledgeBase");
+        inputParameters.put("deepParse", Arrays.asList("parseForm", "parseImage"));
+        inputParameters.put("continueWhenErr", "0");
+        inputParameters.put("knowledgeType", "sn");
+        inputParameters.put("splitRuleType", 0);
+        inputParameters.put("splitRule", 0);
+
+        // 设置输出参数（按照 backend2 的真实配置）
+        Map<String, Object> outputParameters = (Map<String, Object>) nodeDefinition.get("outputParameters");
+        outputParameters.put("embeddingFiles", "2309baf83c");
+        outputParameters.put("knowledgeNames", "_currentDate");
+
+        // 模拟知识库服务返回
+        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
+        inventoryInfo.setModelSn("localModel");
+        inventoryInfo.setSplitRule(0);
+        inventoryInfo.setSplitter(1);
+        inventoryInfo.setWordCountLimit(500);
+        inventoryInfo.setWordCountOverlap(50);
+
+        when(knowledgeService.checkKnowledgeInventory("f6935b89-09a4-4d96-9dfc-e3e04a00cf66")).thenReturn(inventoryInfo);
+
+        // 执行知识添加节点
+        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));
+
+        // 验证执行结果
+        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
+        assertNotNull(nodeContext.getOutput().get("embeddingFiles"));
+        assertNotNull(nodeContext.getOutput().get("knowledgeNames"));
+
+        // 验证全局变量被正确设置（按照 backend2 的输出变量）
+        assertNotNull(workflowContext.getGlobalVars().get("2309baf83c"));
+        assertNotNull(workflowContext.getGlobalVars().get("_currentDate"));
+
+        // 验证节点执行时间被设置
+        assertNotNull(nodeContext.getEndTime());
+
+        log.info("Real world backend2 scenario test completed successfully");
+    }
+}
