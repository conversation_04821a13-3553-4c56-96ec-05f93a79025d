package com.ai.application.app.service.impl;

import com.ai.application.app.api.dto.UserLoginDTO;
import com.ai.application.app.api.vo.GetUserInfoVO;
import com.ai.application.app.api.vo.UserLoginVO;
import com.ai.application.app.service.IUserService;
import com.ai.framework.core.util.uuid.UUIDUtil;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.UUID;

@Service
public class UserServiceImpl implements IUserService {
    /**
     * 用户登录
     * @param dto
     * @return
     */
    @Override
    public UserLoginVO login(UserLoginDTO dto) {
        UserLoginVO userLoginVo = new UserLoginVO();
        userLoginVo.setRefreshToken(UUID.randomUUID().toString());
        userLoginVo.setToken(UUID.randomUUID().toString());
        return userLoginVo;
    }

    @Override
    public void logout() {
        return;
    }

    /**
     * 获取用户信息
     * @return
     */
    @Override
    public GetUserInfoVO getUserInfo() {
        GetUserInfoVO userInfoVO = new GetUserInfoVO();
        userInfoVO.setUserName("thor");
        userInfoVO.setEmail("<EMAIL>");
        userInfoVO.setNickname("leiZi");
        userInfoVO.setTenantId(1);
        userInfoVO.setTenantSn(UUID.randomUUID().toString());
        userInfoVO.setUserSn(UUID.randomUUID().toString());
        userInfoVO.setResourceCodes(List.of("index:login", "index:chat"));

        GetUserInfoVO.Role role = new GetUserInfoVO.Role();
        role.setRoleName("admin");
        role.setCode(UUIDUtil.genRandomSn("role"));
        userInfoVO.setRoles(List.of(role));

        return userInfoVO;
    }
}
