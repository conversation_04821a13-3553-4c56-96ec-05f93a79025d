package ${cfg.mappingPackage};
import ${cfg.entityPackage}.${entity};
import ${cfg.dtoPackage}.${entity}DTO;
import ${cfg.voPackage}.${entity}VO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * $!{table.comment}
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */

@Mapper(componentModel = "spring")
public interface ${entity}Mapstruct {

    ${entity} toEntity(${entity}DTO dto);
    List<${entity}> toEntityList(List<${entity}DTO> dtolist);
    ${entity}VO toVo(${entity} entity);
    List<${entity}VO> toVoList(List<${entity}> entities);
}
