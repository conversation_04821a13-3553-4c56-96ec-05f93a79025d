package com.ai.application.tenant.authorize.feign;

import com.ai.application.tenant.authorize.api.dto.ResourceAddReqDTO;
import com.ai.application.tenant.authorize.api.dto.ResourceGrantReqDTO;
import com.ai.application.tenant.authorize.api.feign.ITenantAuthorizeClient;
import com.ai.application.tenant.authorize.api.vo.ResourceVO;
import com.ai.application.tenant.authorize.service.IAuthorizationService;
import com.ai.framework.core.vo.ResultVo;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@AllArgsConstructor
public class TenantAuthorizeClient implements ITenantAuthorizeClient {
    private final IAuthorizationService authorizationService;

    @Override
    public ResultVo<List<ResourceVO>> queryGrantResourceList(@RequestBody @Validated ResourceGrantReqDTO dto){
        return ResultVo.data(authorizationService.queryGrantResourceList(dto));
    }

    @Override
    public ResultVo<Void> addResource(@RequestBody @Validated ResourceAddReqDTO dto){
        authorizationService.addResource(dto);
        return ResultVo.success("添加成功");
    }
}