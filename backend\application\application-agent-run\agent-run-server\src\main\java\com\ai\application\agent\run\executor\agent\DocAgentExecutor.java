package com.ai.application.agent.run.executor.agent;

import com.ai.application.agent.base.api.dto.AgentChatDTO;
import com.ai.application.agent.base.api.feign.IAgentClient;
import com.ai.application.agent.base.api.vo.AgentChatVO;
import com.ai.application.agent.run.errors.AgentNodeExecutorError;
import com.ai.application.agent.run.executor.AgentExecutionContext;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 文档问答智能体执行器
 */
@Slf4j
@Component
public class DocAgentExecutor extends BaseAgentExecutor {

    @Autowired
    private IAgentClient agentClient;

    @Override
    public Map<String, Object> execute(AgentExecutionContext executionContext) {
        String processInstanceId = executionContext.getProcessInstanceId();
        String agentSn = executionContext.getParameterAsString("agentSn");
        String agentType = executionContext.getParameterAsString("agentType");
        String sessionSn = executionContext.getParameterAsString("sessionSn");
        String msgContent = executionContext.getParameterAsString("msgContent");
        String auth = executionContext.getAuthorization();
        
        // 会话管理
        boolean contextMemorised = Objects.equals("true", executionContext.getParameterAsString("contextMemorised"));
        if (sessionSn == null || !contextMemorised) {
            String modelSn = executionContext.getParameterAsString("model");
            String prompt = executionContext.getParameterAsString("sysPrompt");
            String temperature = executionContext.getParameterAsString("temperature");
            
            if (StringUtils.isNotBlank(prompt) || StringUtils.isNotBlank(modelSn) || StringUtils.isNotBlank(temperature)) {
                sessionSn = newDebugSessionSn(auth, agentSn, modelSn, prompt, 
                    temperature != null ? Double.parseDouble(temperature) : 0.6);
            } else {
                sessionSn = newSessionSn(auth, agentSn);
            }
        }

        // 构建智能体请求
        AgentChatDTO chatDto = AgentChatDTO.builder()
                .agentSn(agentSn)
                .agentType(agentType)
                .msgContent(msgContent)
                .msgType("text")
                .delayInMs(20L)
                .fromCode("Workflow")
                .processId(executionContext.getProcessId())
                .sessionSn(sessionSn)
                .debug(executionContext.isDebugRun())
                .build();

        log.info("[{}] {} agent doc request body {}", processInstanceId, agentType, JsonUtils.toJsonString(chatDto));

        // 调用智能体服务
        ResultVo<AgentChatVO> result = agentClient.chat(chatDto);

        if (result == null || !Objects.equals(result.getCode(), 0)) {
            String errorMsg = result != null ? result.getMessage() : "调用文档智能体失败";
            log.error("[{}] {} agent doc error {}", processInstanceId, agentType, errorMsg);
            throw new ServiceException(51004,"调用文档智能体失败: " + errorMsg);
        }

        AgentChatVO agentChatVo = result.getData();
        if (agentChatVo == null) {
            throw new ServiceException(AgentNodeExecutorError.AGENT_RESPONSE_IS_NULL);
        }

        // 解析返回结果
        String message = parseMsgContent(agentChatVo);
        
        // 文档智能体特有的逻辑：解析引用信息
        List<Object> referenceList = parseReferenceInfo(agentChatVo);
        
        log.info("[{}] {} agent doc response, message: {}, references: {}", 
                processInstanceId, agentType, message, referenceList.size());

        // 构建返回结果
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("message", message);
        resultMap.put("sessionSn", sessionSn);
        resultMap.put("success", agentChatVo.getSuccess());
        resultMap.put("reference", referenceList);
        
        return resultMap;
    }

    /**
     * 解析消息内容
     */
    private String parseMsgContent(AgentChatVO agentChatVo) {
        if (agentChatVo.getContent() != null) {
            JsonNode answerNode = agentChatVo.getContent().get("answer");
            if (answerNode != null && answerNode.isTextual()) {
                return answerNode.asText();
            }
        }
        
        return StringUtils.defaultIfBlank(agentChatVo.getReply(), "");
    }

    /**
     * 解析引用信息
     */
    private List<Object> parseReferenceInfo(AgentChatVO agentChatVo) {
        List<Object> referenceList = new ArrayList<>();
        
        if (agentChatVo.getContent() != null) {
            JsonNode pagesNode = agentChatVo.getContent().get("pages");
            if (pagesNode != null && pagesNode.isArray()) {
                for (JsonNode pageNode : pagesNode) {
                    Map<String, Object> pageInfo = new HashMap<>();
                    
                    if (pageNode.has("title")) {
                        pageInfo.put("title", pageNode.get("title").asText());
                    }
                    if (pageNode.has("content")) {
                        pageInfo.put("content", pageNode.get("content").asText());
                    }
                    if (pageNode.has("url")) {
                        pageInfo.put("url", pageNode.get("url").asText());
                    }
                    if (pageNode.has("score")) {
                        pageInfo.put("score", pageNode.get("score").asDouble());
                    }
                    
                    if (!pageInfo.isEmpty()) {
                        referenceList.add(pageInfo);
                    }
                }
            }
        }
        
        return referenceList;
    }

    @Override
    public String getAgentType() {
        return "doc";
    }
}
