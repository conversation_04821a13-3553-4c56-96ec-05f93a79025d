package com.ai.application.agent.base.mapper;

import com.ai.application.agent.base.api.dto.AgentStatDTO;
import com.ai.application.agent.base.api.entity.Agent;
import com.ai.application.agent.base.api.vo.AgentTokensStatisticsDetailVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 智能体表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface AgentMapper extends BaseMapper<Agent> {
    @Select("select * from agent where agent_sn = #{agentSn}")
    Agent selectByAgentSn(@Param("agentSn") String agentSn);

    List<AgentTokensStatisticsDetailVO> queryTokensFrequentAgent(AgentStatDTO dto);
    List<AgentTokensStatisticsDetailVO> queryLastTokensAgent(AgentStatDTO dto);
    Integer getAgentTotalTokens(AgentStatDTO dto);
}
