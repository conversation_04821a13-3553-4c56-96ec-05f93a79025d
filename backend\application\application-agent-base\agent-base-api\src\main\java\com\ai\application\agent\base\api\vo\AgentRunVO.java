package com.ai.application.agent.base.api.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 智能体运行记录表
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@Schema(name = "")
public class AgentRunVO {
    /**
     * 运行记录id
     */
    @Schema(description = "运行记录id")
    private Integer runId;

    /**
     * 运行类型:10-用户触发,20-事件触发,30-定时触发,40-API调用
     */
    @Schema(description = "运行类型:10-用户触发,20-事件触发,30-定时触发,40-API调用")
    private Integer runType;

    /**
     * 运行模式:10-调试,20-正常单次,30-批量
     */
    @Schema(description = "运行模式:10-调试,20-正常单次,30-批量")
    private Integer runMode;

    /**
     * 运行状态:1-运行中,2-成功,3-失败,4-中断,5-超时
     */
    @Schema(description = "运行状态:1-运行中,2-成功,3-失败,4-中断,5-超时")
    private Integer runStatus;

    /**
     * 输入内容
     */
    @Schema(description = "输入内容")
    private String runInput;

    /**
     * 输出内容
     */
    @Schema(description = "输出内容")
    private String runOutput;

    /**
     * 运行元数据
     */
    @Schema(description = "运行元数据")
    private String runMetadata;

    /**
     * 执行时长(毫秒)
     */
    @Schema(description = "执行时长(毫秒)")
    private Integer runDuration;

    /**
     * 消耗tokens
     */
    @Schema(description = "消耗tokens")
    private Integer runTokens;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date runStartTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date runEndTime;

    /**
     * 智能体id
     */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
     * 智能体版本id
     */
    @Schema(description = "智能体版本id")
    private Integer versionId;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Integer tenantId;

    /**
     * 触发用户id
     */
    @Schema(description = "触发用户id")
    private Integer userId;

    /**
     * token id
     */
    @Schema(description = "token id")
    private Integer tokenId;

    /**
     * 请求id
     */
    @Schema(description = "请求id")
    private Integer requestId;

    /**
     * 会话id
     */
    @Schema(description = "会话id")
    private Integer sessionId;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}