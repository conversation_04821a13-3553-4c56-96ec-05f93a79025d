package com.ai.application.agent.run.executor;

import com.ai.framework.core.context.UserContext;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 执行上下文接口
 */
public interface ExecutionContext {

    /**
     * 工作流id
     * @return 工作流id
     */
    String getProcessId();

    /**
     * 工作流名称
     * @return 工作流名称
     */
    default String getProcessName() {
        return "";
    }

    /**
     * 工作流触发方式名称
     */
    default String getProcessTriggerName() {
        return StringUtils.EMPTY;
    }

    /**
     * 子工作流触发方式
     * @return 工作流名称
     */
    default String getProcessTriggerType() {
        return "SUBPROCESS";
    }

    /**
     * 子工作流触发方式
     * @return 工作流名称
     */
    default String getProcessTriggerId() {
        return StringUtils.EMPTY;
    }

    /**
     * 工作流实例id
     * @return 工作流实例id
     */
    String getProcessInstanceId();

    /**
     * 工作流节点id
     * @return 工作流实例id
     */
    default String getNodeId() {
        return "";
    }

    /**
     * 工作流节点名称
     * @return 工作流实例id
     */
    default String getNodeName() {
        return "";
    }

    /**
     * 获取所有参数
     *
     * @return 参数映射
     */
    Map<String, Object> getParameters();

    /**
     * 参数string
     *
     * @param parameter 参数名词
     * @return string
     */
    String getParameterAsString(String parameter);

    /**
     * 获取用户信息
     * @return UserContextItem
     */
    UserContext.UserContextItem getUserInfo();

    /**
     * 获取access token
     * @return Header Authorization
     */
    String getAuthorization();

    /**
     * 运行模块
     *
     * @return agent or process
     */
    default String getModule() {
        return null;
    }

    /**
     * 是否调试运行
     */
    default boolean isDebugRun() {
        return false;
    }

    /**
     * 获取变量列表
     */
    default List<Object> getVariables() {
        return Collections.emptyList();
    }

    /**
     * 发送输出增量
     */
    default void sendOutputDeltas(Map<String, String> outputDeltaMapping) {
        // 默认实现为空
    }
}
