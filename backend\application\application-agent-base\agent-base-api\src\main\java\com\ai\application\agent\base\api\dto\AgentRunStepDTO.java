package com.ai.application.agent.base.api.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

/**
 * 智能体运行步骤表
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@Schema(name = "智能体运行步骤表DTO")
public class AgentRunStepDTO {
    /**
     * 步骤id
     */
    @Schema(description = "步骤id")
    private Integer stepId;

    /**
     * 步骤类型:10-LLM调用,20-工具调用,30-知识库检索,40-工作流节点,50-条件判断,60-循环处理
     */
    @Schema(description = "步骤类型:10-LLM调用,20-工具调用,30-知识库检索,40-工作流节点,50-条件判断,60-循环处理")
    private Integer stepType;

    /**
     * 步骤名称
     */
    @Schema(description = "步骤名称")
    private String stepName;

    /**
     * 步骤顺序
     */
    @Schema(description = "步骤顺序")
    private Integer stepOrder;

    /**
     * 步骤状态:1-执行中,2-成功,3-失败,4-跳过
     */
    @Schema(description = "步骤状态:1-执行中,2-成功,3-失败,4-跳过")
    private Integer stepStatus;

    /**
     * 步骤输入
     */
    @Schema(description = "步骤输入")
    private String stepInput;

    /**
     * 步骤输出
     */
    @Schema(description = "步骤输出")
    private String stepOutput;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String stepError;

    /**
     * 执行时长(毫秒)
     */
    @Schema(description = "执行时长(毫秒)")
    private Integer stepDuration;

    /**
     * 消耗tokens
     */
    @Schema(description = "消耗tokens")
    private Integer stepTokens;

    /**
     * 步骤元数据
     */
    @Schema(description = "步骤元数据")
    private String stepMetadata;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private Date stepStartTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private Date stepEndTime;

    /**
     * 运行记录id
     */
    @Schema(description = "运行记录id")
    private Integer runId;

    /**
     * 父步骤id
     */
    @Schema(description = "父步骤id")
    private Integer parentStepId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}