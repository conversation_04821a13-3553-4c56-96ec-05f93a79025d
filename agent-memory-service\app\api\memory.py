# -*- coding: utf-8 -*-
"""
记忆相关API接口

智能体记忆模块的REST API接口，提供：
- 记忆添加接口 (POST /add)
- 记忆搜索接口 (POST /search) 
- 记忆清除接口 (DELETE /clear)
- 记忆详情查询 (GET /{memory_id})
- 相似记忆搜索 (GET /similar/{agent_id}/{user_id})
- 最近记忆获取 (GET /recent/{agent_id}/{user_id})
- 记忆统计信息 (GET /statistics/{agent_id}/{user_id})

Project: Agent Memory Service
Author: 张小龙 <<EMAIL>>
Created: 2025-06-01
Version: 1.0.0
License: private
"""

from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query, Path
from loguru import logger

from app.models.schemas import (
    AddMemoryRequest, AddMemoryResponse,
    SearchMemoryRequest, SearchMemoryResponse,
    ClearMemoryRequest, ClearMemoryResponse,
    MemoryDetailResponse, MemoryListResponse, 
    MemoryStatisticsResponse
)
from app.services.memory_service import memory_service
from app.config.settings import settings

router = APIRouter(prefix="", tags=["记忆管理"])


@router.post(
    "/add",
    response_model=AddMemoryResponse,
    summary="添加记忆",
    description="向智能体记忆库中添加新的记忆记录"
)
async def add_memory(request: AddMemoryRequest):
    """添加记忆"""
    try:
        logger.info(f"收到添加记忆请求: {request.agent_id}")
        
        # 添加记忆
        memory_id = await memory_service.add_memory(request)
        
        return AddMemoryResponse(
            success=True,
            message="记忆添加成功",
            data={
                "memory_id": memory_id,
                "agent_id": request.agent_id,
                "user_id": request.user_id,
                "category": request.category
            }
        )
        
    except Exception as e:
        logger.error(f"添加记忆失败: {e}")
        raise HTTPException(status_code=500, detail=f"添加记忆失败: {str(e)}")


@router.post(
    "/search",
    response_model=SearchMemoryResponse,
    summary="搜索记忆",
    description="根据条件搜索智能体记忆，支持向量相似度检索和混合过滤"
)
async def search_memories(request: SearchMemoryRequest):
    """搜索记忆"""
    try:
        logger.info(f"收到搜索记忆请求: {request.agent_id}")
        
        # 搜索记忆
        memories, total = await memory_service.search_memories(request)
        
        return SearchMemoryResponse(
            success=True,
            message="记忆搜索成功",
            data={
                "total": total,
                "memories": memories
            }
        )
        
    except Exception as e:
        logger.error(f"搜索记忆失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索记忆失败: {str(e)}")


@router.delete(
    "/clear",
    response_model=ClearMemoryResponse,
    summary="清除记忆",
    description="根据条件清除智能体记忆记录"
)
async def clear_memories(request: ClearMemoryRequest):
    """清除记忆"""
    try:
        logger.info(f"收到清除记忆请求: {request.agent_id}")
        
        # 检查是否有过滤条件
        if not request.has_conditions():
            raise HTTPException(
                status_code=400, 
                detail="清除记忆必须指定至少一个过滤条件（agent_id、user_id、category、时间范围）"
            )
        
        # 清除记忆
        deleted_count = await memory_service.clear_memories(request)
        
        return ClearMemoryResponse(
            success=True,
            message=f"记忆清除成功，删除了{deleted_count}条记录",
            data={
                "deleted_count": deleted_count
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清除记忆失败: {e}")
        raise HTTPException(status_code=500, detail=f"清除记忆失败: {str(e)}")


@router.get(
    "/{memory_id}",
    response_model=MemoryDetailResponse,
    summary="获取记忆详情",
    description="根据记忆ID获取记忆详细信息"
)
async def get_memory(
    memory_id: str = Path(..., description="记忆ID")
):
    """获取记忆详情"""
    try:
        logger.info(f"获取记忆详情: {memory_id}")
        
        memory = await memory_service.get_memory_by_id(memory_id)
        
        if not memory:
            raise HTTPException(status_code=404, detail="记忆不存在")
        
        return MemoryDetailResponse(
            success=True,
            message=f"找到记忆",
            data={
                "memory_id": memory_id,
                "memory": memory
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取记忆详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取记忆详情失败: {str(e)}")


@router.get(
    "/similar/{agent_id}/{user_id}",
    response_model=MemoryListResponse,
    summary="搜索相似记忆",
    description="根据问题搜索相似的记忆记录"
)
async def search_similar_memories(
    agent_id: str = Path(..., description="智能体ID"),
    user_id: str = Path(..., description="用户ID"),
    question: str = Query(..., description="查询问题"),
    similarity_threshold: Optional[float] = Query(None, description="相似度阈值", ge=0.0, le=1.0),
    limit: int = Query(10, description="返回数量限制", ge=1, le=100),
    category: Optional[str] = Query(None, description="记忆类别")
):
    """搜索相似记忆"""
    try:
        logger.info(f"搜索相似记忆: agent_id={agent_id}, user_id={user_id}")
        
        memories = await memory_service.search_similar_memories(
            agent_id=agent_id,
            user_id=user_id,
            question=question,
            similarity_threshold=similarity_threshold,
            limit=limit,
            category=category
        )

        return MemoryListResponse(
            success=True,
            message=f"找到相似记忆",
            data={
                "total": len(memories),
                "memories": memories
            }
        )      
        
    except Exception as e:
        logger.error(f"搜索相似记忆失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索相似记忆失败: {str(e)}")


@router.get(
    "/recent/{agent_id}/{user_id}",
    response_model=MemoryListResponse,
    summary="获取最近记忆",
    description="获取指定时间范围内的最近记忆"
)
async def get_recent_memories(
    agent_id: str = Path(..., description="智能体ID"),
    user_id: str = Path(..., description="用户ID"),
    days: int = Query(7, description="天数", ge=1, le=365),
    limit: int = Query(10, description="返回数量限制", ge=1, le=100),
    category: Optional[str] = Query(None, description="记忆类别")
):
    """获取最近记忆"""
    try:
        logger.info(f"获取最近记忆: agent_id={agent_id}, user_id={user_id}, days={days}")
        
        memories = await memory_service.get_recent_memories(
            agent_id=agent_id,
            user_id=user_id,
            days=days,
            limit=limit,
            category=category
        )
        
        return MemoryListResponse(
            success=True,
            message=f"找到最近记忆",
            data={
                "total": len(memories),
                "memories": memories
            }
        )
        
    except Exception as e:
        logger.error(f"获取最近记忆失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取最近记忆失败: {str(e)}")


@router.get(
    "/statistics/{agent_id}/{user_id}",
    response_model=MemoryStatisticsResponse,
    summary="获取记忆统计",
    description="获取智能体和用户的记忆统计信息"
)
async def get_memory_statistics(
    agent_id: str = Path(..., description="智能体ID"),
    user_id: str = Path(..., description="用户ID")
):
    """获取记忆统计"""
    try:
        logger.info(f"获取记忆统计: agent_id={agent_id}, user_id={user_id}")
        
        statistics = await memory_service.get_memory_statistics(agent_id, user_id)
        
        return MemoryStatisticsResponse(
            success=True,
            message="统计信息获取成功",
            data=statistics
        )
        
    except Exception as e:
        logger.error(f"获取记忆统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取记忆统计失败: {str(e)}") 