package com.ai.application.admin.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 模型信息表
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@Schema(name = "模型信息表DTO-添加")
public class ModelAddDTO {
    /**
     * 模型名称
     */
    @Schema(description = "模型名称")
    @Length(max = 64,message = "模型名称长度不能超过64个字符")
    private String modelName;
    /**
     * 调用名称：比如gpt-3.5-tubro
     */
    @Schema(description = "调用名称：比如gpt-3.5-tubro")
    @Length(max = 64,message = "调用名称长度不能超过64个字符")
    private String modelEngine;

    /**
     * 模型类型10文本,11推理,20图文,21生图,22图图,31stt,32tts,33音音,40视频理解,41视频生成，42视频混合,50WM,70嵌入,71重排,72OCR
     */
    @Schema(description = "模型类型10文本,11推理,20图文,21生图,22图图,31stt,32tts,33音音,40视频理解,41视频生成，42视频混合,50WM,70嵌入,71重排,72OCR")
    @NotNull(message = "模型类型不能为空")
    private Integer modelType;
    /**
     * 0:不支持 1:function 2:tools 3:both
     */
    @Schema(description = "0:不支持 1:function 2:tools 3:both")
    private Integer modelToolcall = 0;
    /**
     * 模型描述
     */
    @Schema(description = "模型描述")
    @Length(max = 255,message = "模型描述长度不能超过255个字符")
    private String modelDesc;

    /**
     * 模型基础配置参数
     */
    @Schema(description = "模型基础配置参数(json格式)")
    private String modelConfig;

    /**
     * 供应商id
     */
    @Schema(description = "供应商id")
    @NotBlank(message = "供应商Sn不能为空")
    private String supplierSn;
}