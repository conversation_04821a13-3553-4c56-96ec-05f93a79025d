package bo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Schema(description = "消息")
@Data
public class MasterContentBO {
    @Schema(description = "文本内容")
    private String text;

    @Schema(description = "异常文本内容")
    private String errorText;

    @Schema(description = "文档召回")
    private List<Knowledge<Doc>> docs;

    @Schema(description = "文档召回")
    private List<Knowledge<Image>> images;

    @Schema(description = "词库召回")
    private List<Word> words;

    @Schema(description = "执行步骤")
    private List<Step> steps;

    @Schema(description = "计划类型 10文档召回 20function call")
    private Integer planType;

    @Data
    public static class Step {
        @Schema(description = "步骤类型")
        private Integer type;

        @Schema(description = "步骤名称")
        private String stepName;

        @Schema(description = "步骤状态 running complete")
        private String stepStatus;

        @Schema(description = "functionCall")
        private FunctionCall functionCall;
    }

    @Data
    public static class FunctionCall {
        @Schema(description = "运行名称")
        private String funcName;

        @Schema(description = "运行状态 running, complete")
        private String funcStatus;

        @Schema(description = "工具调用")
        private List<String> messages;
    }

    @Data
    public static class Knowledge<T> {
        @Schema(description = "文档ID")
        private String fileId;

        @Schema(description = "文档名称")
        private String fileName;

        @Schema(description = "文档URL")
        private String url;

        @Schema(description = "文档域名")
        private String domain;

        @Schema(description = "文档类型 pdf, doc, docx, txt, xls, xlsx,jpg")
        private String fileType;

        @Schema(description = "图片分页")
        private List<T> page;
    }

    @Data
    public static class Doc {
        @Schema(description = "文档页面")
        private Integer pageNo;

        @Schema(description = "分页内容")
        private String pageContent;

        @Schema(description = "评分")
        private Double score;
    }

    @Data
    public static class Image {
        @Schema(description = "页码")
        private Integer pageNo;

        @Schema(description = "得分")
        private Double score;

        @Schema(description = "图片编码")
        private String imageSn;

        @Schema(description = "图片名称")
        private String imageName;

        @Schema(description = "下载地址")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String downloadUrl;
    }

    @Data
    public static class Word {
        @Schema(description = "词库")
        private String dictName;

        @Schema(description = "词库编码")
        private String dictSn;

        @Schema(description = "词")
        private String wordName;

        @Schema(description = "原词")
        private String originWord;

        @Schema(description = "匹配词的下标")
        private int[] range;
    }
}