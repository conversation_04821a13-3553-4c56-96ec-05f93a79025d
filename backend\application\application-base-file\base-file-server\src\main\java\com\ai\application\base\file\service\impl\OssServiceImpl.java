package com.ai.application.base.file.service.impl;

import com.ai.application.base.file.config.MinioConfig;
import com.ai.application.base.file.service.IOssService;
import com.ai.application.base.file.api.dto.BatchUploadFileDto;
import com.ai.application.base.file.api.vo.DownloadUrlVo;
import com.ai.application.base.file.api.vo.UploadCertificateVo;
import com.ai.application.base.file.api.vo.UploadFileVo;

import com.ai.framework.core.enums.ErrorCodeEnum;
import com.ai.framework.core.exception.ServiceException;

import com.ai.framework.core.util.date.DateUtil;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.list.CollectionUtils;
import com.ai.framework.core.util.string.StringUtil;
import com.ai.framework.core.util.validator.AssertUtil;
import com.aliyun.oss.common.auth.Credentials;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.auth.STSAssumeRoleSessionCredentialsProvider;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.google.common.collect.Lists;
import io.minio.*;
import io.minio.errors.*;
import io.minio.http.Method;
import io.minio.messages.DeleteError;
import io.minio.messages.DeleteObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class OssServiceImpl implements IOssService {

    public static final String PUBLIC = "public";
    @Value("${oss.source}")
    String ossSource;

    @Value("${oss.accessKeyId}")
    String ossAccessKeyId;

    @Value("${oss.accessKeySecret}")
    String ossAccessKeySecret;

    @Value("${oss.regionId}")
    String ossRegionId;

    @Value("${oss.roleArn}")
    String ossRoleArn;

    @Value("${oss.bucketName}")
    String ossBucketName;

    @Value("${oss.bucketPublicName:public-agent}")
    private String ossPublicBucketName;


    @Value("${oss.bucketPublicUrl:http://172.28.0.147:8001}")
    String ossPublicBucketUrl;


    @Value("${oss.private.endpoint}")
    String privateEndpoint;

    @Value("${oss.endpoint}")
    String ossEndPoint;

    @Autowired
    private MinioClient minioClient;

    @Autowired
    private MinioConfig minioConfig;
    @Autowired
    private Environment environment;

//    @Override
//    public boolean exists(String key) {
//        return s3Client.doesObjectExist(ossBucketName, key);
//    }






    @Override
    public void uploadFile(byte[] content, long length, String fileSn, String fileType) {
        try {
            ensureBucketExists(ossBucketName);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try (InputStream inputStream = new ByteArrayInputStream(content)) {
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(ossBucketName)
                    .object(fileSn)
                    .stream(inputStream, content.length, -1)
                    .contentType(fileType)
                    .build());
        } catch (IOException | ErrorResponseException | InsufficientDataException | InternalException |
                 InvalidKeyException | InvalidResponseException | NoSuchAlgorithmException | ServerException |
                 XmlParserException e) {
            log.info("上传文件失败，文件MIME 类型：{}, 错误信息：{}{}",fileType, e.getMessage(),e);
            throw new RuntimeException(e);
        }

    }


    @Override
    public void uploadFile(MultipartFile file, long length, String fileSn, String fileType) {
        try {
            ensureBucketExists(ossBucketName);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        String originalFilename = file.getOriginalFilename();
        String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        String objectName = UUID.randomUUID().toString() + fileExtension;

        try (InputStream inputStream = file.getInputStream()) {
            minioClient.putObject(PutObjectArgs.builder()
                    .bucket(ossBucketName)
                    .object(fileSn)
                    .stream(inputStream, file.getSize(), -1)
                    .contentType(file.getContentType())
                    .build());
        } catch (IOException | ErrorResponseException | InsufficientDataException | InternalException |
                 InvalidKeyException | InvalidResponseException | NoSuchAlgorithmException | ServerException |
                 XmlParserException e) {
            log.info("上传文件失败，文件名：{}, 错误信息：{}{}", file.getOriginalFilename(), e.getMessage(),e);
            throw new RuntimeException(e);
        }

    }


    /**
     * 下载文件为字节数组
     * @param objectName 对象名称（文件路径）
     * @return 文件字节数组
     */
    public byte[] downloadAsBytes(String objectName)  {
        try (InputStream stream = minioClient.getObject(
                GetObjectArgs.builder()
                        .bucket(ossBucketName)
                        .object(objectName)
                        .build())) {

            return toByteArray(stream);
        } catch (ServerException e) {
            throw new RuntimeException(e);
        } catch (InsufficientDataException e) {
            throw new RuntimeException(e);
        } catch (ErrorResponseException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (InvalidKeyException e) {
            throw new RuntimeException(e);
        } catch (InvalidResponseException e) {
            throw new RuntimeException(e);
        } catch (XmlParserException e) {
            throw new RuntimeException(e);
        } catch (InternalException e) {
            throw new RuntimeException(e);
        }
    }
    // 将输入流转换为字节数组
    private byte[] toByteArray(InputStream input) throws IOException {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        byte[] buffer = new byte[4096]; // 4KB缓冲区
        int bytesRead;

        while ((bytesRead = input.read(buffer)) != -1) {
            output.write(buffer, 0, bytesRead);
        }

        return output.toByteArray();
    }


    @Override
    public void batchDelete(List<String> keys) {
        if(CollectionUtils.isEmpty(keys)){
            return;
        }
        //此批量删除方法每次仅支持1000个key
        List<List<String>> deleteList = Lists.partition(keys, 1000);
        for(List<String> objects : deleteList){
            deleteFiles(objects);
        }
    }

    /**
     * 批量删除文件
     * @param objectNames 要删除的文件名列表
     * @param bucketName 存储桶名称
     * @return 删除失败的文件列表
     */
    public List<String> deleteFiles(List<String> objectNames, String bucketName) {
        List<String> failedDeletes = new ArrayList<>();
        List<DeleteObject> objects = new ArrayList<>();

        // 转换为 DeleteObject 列表
        for (String objectName : objectNames) {
            objects.add(new DeleteObject(objectName));
        }

        try {
            // 执行批量删除
            Iterable<Result<DeleteError>> results = minioClient.removeObjects(
                    RemoveObjectsArgs.builder()
                            .bucket(bucketName)
                            .objects(objects)
                            .build());

            // 检查删除结果
            for (Result<DeleteError> result : results) {
                DeleteError error = result.get();
                failedDeletes.add(error.objectName());
            }
        } catch (Exception e) {
            // 如果整个批量操作失败，将所有文件标记为失败
            failedDeletes.addAll(objectNames);
        }
        return failedDeletes;
    }

    /**
     * 使用默认存储桶批量删除文件
     * @param objectNames 要删除的文件名列表
     * @return 删除失败的文件列表
     */
    public List<String> deleteFiles(List<String> objectNames) {
        return deleteFiles(objectNames, minioConfig.getDefaultBucket());
    }


//    @Override
//    public UploadFileVo uploadFileStream(InputStream inputStream, String fileName) {
//        StringBuffer fileBuffer = new StringBuffer();
//        String property = environment.getProperty("spring.profiles.active");
//        String date = DateUtil.format(new Date(), "yyyyMMdd");
//        StringBuffer fileAppend = fileBuffer.append(property).append("/").append(date).append("/").append(fileName);
//        fileName = fileAppend.toString();
//
//        PutObjectResult putObjectResult = s3Client.putObject(ossBucketName, fileName, inputStream, null);
//        String downloadUrl = this.getDownloadUrl(fileName, "");
//        log.info("updateFile:{}, {}, {}",JsonUtils.toJsonString(putObjectResult), fileName, downloadUrl);
//
//        UploadFileVo uploadFileVo = new UploadFileVo();
//        uploadFileVo.setETag(putObjectResult.getETag());
//        uploadFileVo.setFileSn(fileName);
//        uploadFileVo.setContentMD5(putObjectResult.getContentMd5());
//
//        return uploadFileVo;
//    }

//    @Override
//    public List<UploadFileVo> batchUploadFileUrl(List<BatchUploadFileDto> batchUploadFiles) {
//        String property = environment.getProperty("spring.profiles.active");
//        AssertUtil.isNotNull(batchUploadFiles, "上传文件不能为空");
//
//        List<UploadFileVo> uploadFileVos = new ArrayList<>();
//        batchUploadFiles.stream().forEach(item->{
//            try {
//                URL url = new URL(item.getFileUrl());
//                URLConnection connection = url.openConnection();
//                InputStream inputStream = connection.getInputStream();
//                UploadFileVo uploadFileVo = new UploadFileVo();
//                String fileSn = property + "/" + item.getFileSn();
//                PutObjectResult putObjectResult = s3Client.putObject(ossBucketName, fileSn, inputStream, null);
//                uploadFileVo.setFileSn(fileSn);
//                uploadFileVo.setETag(putObjectResult.getETag());
//                uploadFileVos.add(uploadFileVo);
//            } catch (IOException e) {
//                throw new ServiceException(ErrorCodeEnum.BIZ_VALIDATE_ERROR.getCode(), e.getMessage());
//            }
//        });
//
//        return uploadFileVos;
//    }


    @Override
    public String getDownloadUrl(String fileSn, String type) {
        return this.getDownloadUrlByFileName(fileSn, type, null);
    }

    @Override
    public String getDownloadUrlByFileName(String fileSn, String type, String fileName) {
        try {
            return getFileUrl(fileSn);
        } catch (Exception e) {
            log.info("获取下载链接失败，文件名：{}, 错误信息：{}{}", fileSn, e.getMessage(),e);
            throw new RuntimeException(e);
        }
//        //超时时间
//        Date expiration = new Date(new Date().getTime() + 24 * 3600 * 1000L);
//        //类型为下载
//        if(!"preview".equals(type)){
//            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(ossBucketName, fileSn);
//            request.setExpiration(expiration);
//
//            ResponseHeaderOverrides responseHeaderOverrides = new ResponseHeaderOverrides();
//            responseHeaderOverrides.setContentType("application/octet-stream");
//            // 自定义文件名
//            if (!StringUtil.isBlank(fileName)) {
//                String contentDisposition = "attachment;filename*=UTF-8''" +
//                        URLEncoder.encode(fileName, StandardCharsets.UTF_8);
//                responseHeaderOverrides.setContentDisposition(contentDisposition);
//            }
//            request.setResponseHeaders(responseHeaderOverrides);
//
//            URL url = s3Client.generatePresignedUrl(request);
//            return url.toString();
//        }
//
//        //类型为预览
//        return s3Client.generatePresignedUrl(ossBucketName, fileSn, expiration).toString();
    }

    @Override
    public UploadCertificateVo uploadCertificate() {
        STSAssumeRoleSessionCredentialsProvider provider;
        try{
            provider = CredentialsProviderFactory.newSTSAssumeRoleSessionCredentialsProvider(
                    ossRegionId, ossAccessKeyId, ossAccessKeySecret, ossRoleArn);
        }catch (Exception e){
            log.error("OSS provider创建失败", e);
            throw new ServiceException(ErrorCodeEnum.OSS_ERROR);
        }

        provider.withExpiredDuration(1200);

        Credentials credentials = provider.getCredentials();
        // 通过oss获取临时凭证等信息
        UploadCertificateVo vo = new UploadCertificateVo();

        vo.setAccessKey(credentials.getAccessKeyId());
        vo.setAccessSecret(credentials.getSecretAccessKey());
        vo.setStsToken(credentials.getSecurityToken());
        vo.setRegion(ossRegionId);
        vo.setBucketName(ossBucketName);
        vo.setPrivateEndpoint(privateEndpoint);

        return vo;
    }



//    @Override
//    public UploadFileVo uploadFileStreamBackUrl(InputStream inputStream, String fileName) {
//        StringBuffer fileBuffer = new StringBuffer();
//        String property = environment.getProperty("spring.profiles.active");
//        String date = DateUtil.format(new Date(), "yyyyMMdd");
//        StringBuffer fileAppend = fileBuffer.append(property).append("/").append(date).append("/").append(fileName);
//        fileName = fileAppend.toString();
//
//        PutObjectResult putObjectResult = s3Client.putObject(ossBucketName, fileName, inputStream, null);
//        String downloadUrl = this.getDownloadUrl(fileName, "");
//        log.info("updateFile:{}, {}, {}",JsonUtils.toJsonString(putObjectResult), fileName, downloadUrl);
//
//        UploadFileVo uploadFileVo = new UploadFileVo();
//        uploadFileVo.setETag(putObjectResult.getETag());
//        uploadFileVo.setFileSn(fileName);
//        uploadFileVo.setContentMD5(putObjectResult.getContentMd5());
//        uploadFileVo.setDownloadUrl(downloadUrl);
//
//        return uploadFileVo;
//    }

//    @Override
//    public UploadFileVo uploadFileStreamBackUrl(InputStream inputStream, String fileSn, String fileName) {
//        StringBuffer fileBuffer = new StringBuffer();
//        String property = environment.getProperty("spring.profiles.active");
//        String date = DateUtil.format(new Date(), "yyyyMMdd");
//        StringBuffer fileAppend = fileBuffer.append(property).append("/").append(date).append("/").append(fileSn);
//        fileSn = fileAppend.toString();
//
//        PutObjectResult putObjectResult = s3Client.putObject(ossBucketName, fileSn, inputStream, null);
//        String downloadUrl = this.getDownloadUrlByFileName(fileSn, "",fileName);
//        log.info("updateFile:{}, {}, {}",JsonUtils.toJsonString(putObjectResult), fileName, downloadUrl);
//
//        UploadFileVo uploadFileVo = new UploadFileVo();
//        uploadFileVo.setETag(putObjectResult.getETag());
//        uploadFileVo.setFileSn(fileName);
//        uploadFileVo.setContentMD5(putObjectResult.getContentMd5());
//        uploadFileVo.setDownloadUrl(downloadUrl);
//
//        return uploadFileVo;
//    }




    // 创建存储桶（如果不存在）
    public void ensureBucketExists(String bucketName) throws Exception {
        boolean found = minioClient.bucketExists(BucketExistsArgs.builder()
                .bucket(bucketName)
                .build());

        if (!found) {
            minioClient.makeBucket(MakeBucketArgs.builder()
                    .bucket(bucketName)
                    .build());
        }
    }

    // 上传文件（使用默认存储桶）
//    public String uploadFile(MultipartFile file) throws Exception {
//        return uploadFile(file, minioConfig.getDefaultBucket());
//    }

    // 上传文件到指定存储桶
    public String uploadFileReturnUrl(MultipartFile file, long length, String fileSn, String type) {
        try {
            ensureBucketExists(ossBucketName);
            String fileUrl=null;

            String originalFilename = file.getOriginalFilename();
//        String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
//        String objectName = UUID.randomUUID().toString() + fileExtension;
        if( StringUtil.isNotEmpty(type)|| PUBLIC.equals(type)){
            try (InputStream inputStream = file.getInputStream()) {
                minioClient.putObject(PutObjectArgs.builder()
                        .bucket(ossPublicBucketName)
                        .object(fileSn)
                        .stream(inputStream, file.getSize(), -1)
                        .contentType(file.getContentType())
                        .build());
            }
            String publicUrl = String.format("%s/%s/%s", ossPublicBucketUrl, ossPublicBucketName, fileSn);
            System.out.println("Permanent access URL: " + publicUrl);
             fileUrl = publicUrl;
        }else{
            try (InputStream inputStream = file.getInputStream()) {
                minioClient.putObject(PutObjectArgs.builder()
                        .bucket(ossBucketName)
                        .object(fileSn)
                        .stream(inputStream, file.getSize(), -1)
                        .contentType(file.getContentType())
                        .build());
            }
             fileUrl = getFileUrl(fileSn, ossBucketName);
        }
        return fileUrl;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    // 获取文件访问URL（7天有效期）
    public String getFileUrl(String objectName) throws Exception {
        return getFileUrl(objectName, minioConfig.getDefaultBucket());
    }

    public String getFileUrl(String objectName, String bucketName) throws Exception {



        return minioClient.getPresignedObjectUrl(
                GetPresignedObjectUrlArgs.builder()
                        .method(Method.GET)
                        .bucket(bucketName)
                        .object(objectName)
                        .expiry(7, TimeUnit.DAYS)
                        .build());
    }

    // 上传本地文件
    public void uploadLocalFile(String filePath) throws Exception {
        uploadLocalFile(filePath, minioConfig.getDefaultBucket());
    }

    public void uploadLocalFile(String filePath, String bucketName) throws Exception {
        ensureBucketExists(bucketName);
        String objectName = filePath.substring(filePath.lastIndexOf("/") + 1);

        minioClient.uploadObject(
                UploadObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .filename(filePath)
                        .build());
    }

    // 删除文件
    public void deleteFile(String objectName) throws Exception {
        deleteFile(objectName, minioConfig.getDefaultBucket());
    }

    public void deleteFile(String objectName, String bucketName) throws Exception {
        minioClient.removeObject(
                RemoveObjectArgs.builder()
                        .bucket(bucketName)
                        .object(objectName)
                        .build());
    }
}
