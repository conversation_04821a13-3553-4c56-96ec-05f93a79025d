package com.ai.application.base.log.api.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 错误类型:100系统异常210使用异常-定时任务220使用异常-工作流230使用异常-工具240使用异常-MCP250使用异常-模型
 */
@Getter
public enum ErrorTypeEnum {
    SYSTEM_ERROR(100, "系统错误"),
    TASK_ERROR(210, "使用异常-定时任务"),
    WORK_ERROR(220, "使用异常-工作流"),
    TOOLS_ERROR(230, "使用异常-工具"),
    MCP_ERROR(240, "使用异常-MCP"),
    MODEL_ERROR(250, "使用异常-模型"),

    ;

    private Integer code;
    private String title;

    ErrorTypeEnum(Integer code, String title) {
        this.code = code;
        this.title = title;
    }

    public static String getTitle(Integer code) {
        for(ErrorTypeEnum vo :values() ) {
            if (Objects.equals(vo.code, code)) {
                return vo.title;
            }
        }
        return null;
    }
}
