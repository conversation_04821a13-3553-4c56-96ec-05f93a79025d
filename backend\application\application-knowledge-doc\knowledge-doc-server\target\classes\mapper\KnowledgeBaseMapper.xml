<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.knowledge.table.mapper.KnowledgeBaseMapper">

    <select id="allList" resultType="com.ai.application.knowledge.table.vo.BaseListVo"
            parameterType="com.ai.application.knowledge.table.dto.BaseListDto">
        select kb.kb_id as kbId,
        kb.kb_sn as kbSn,
        kb.kb_name as kbName,
        kb.kb_logo as kbLogo,
        kb.kb_desc as kbDesc,
        kb.kb_files as kbFiles,
        kb.kb_sizes as kbSizes,
        kb.kb_agents as kbAgents,
        kb.kb_status as kbStatus,
        kb.tenant_id as tenantId,
        kb.create_user_id as userId
        from knowledge_base kb
        where kb.kb_status = 1 and kb.tenant_id = #{tenantId}
        <if test="keyword != null and keyword != ''">
            and (kb.kb_name like concat('%', #{keyword}, '%')
            or kb.kb_desc like concat('%', #{keyword}, '%'))
        </if>
        <if test="userIdsByName != null and userIdsByName.size() > 0">
            and kb.create_user_id in
            <foreach item="item" index="index" collection="userIdsByName" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by kb.create_time desc
    </select>
        </mapper>
