package com.ai.application.base.file.api.dto;

import cn.hutool.core.bean.BeanUtil;
import com.ai.application.base.file.api.entity.AppFile;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class UrlMdDto {

    /**
     * 文件id
     */

    private String url;

    /**
     * 文件sn
     */

    private List<String> formats;

    private String fileSn;


}
