package ${cfg.voPackage};

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * $!{table.comment}
 *
 * <AUTHOR>
 * @since ${date}
 */
@Data
@Schema(name = "$!{field.comment}")
public class ${entity}VO {
    #foreach($field in ${table.fields})
        #if(${field.keyFlag})
            #set($keyPropertyName=${field.propertyName})
        #end
        #if("$!field.comment" != "")
    /**
     * ${field.comment}
     */
    #end
    @Schema(description = "$!{field.comment}")
    #if("${field.propertyType}" == "Date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    #end
    private ${field.propertyType} ${field.propertyName};

    #end
}