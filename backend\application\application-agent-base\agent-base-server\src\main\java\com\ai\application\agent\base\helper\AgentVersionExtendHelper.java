package com.ai.application.agent.base.helper;

import com.ai.application.agent.base.api.entity.AgentVersionExtend;
import com.ai.framework.core.util.list.CollectionUtils;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class AgentVersionExtendHelper {
    // 获取Agent版本扩展
    public static Map<String, String> getVersionExtendItemValue(List<AgentVersionExtend> agentVersionExtends) {
        if (CollectionUtils.isEmpty(agentVersionExtends)) {
            return Collections.emptyMap();
        }
        return agentVersionExtends.stream().collect(Collectors.toMap(AgentVersionExtend::getItemValue, AgentVersionExtend::getItemName));
    }
}
