package com.ai.application.base.log.api.mapstruct;

import com.ai.application.base.log.api.dto.NotificationDeliveryLogDTO;
import com.ai.application.base.log.api.entity.NotificationDeliveryLog;
import com.ai.application.base.log.api.vo.NotificationDeliveryLogVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T09:54:07+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class NotificationDeliveryLogMapstructImpl implements NotificationDeliveryLogMapstruct {

    @Override
    public NotificationDeliveryLog toEntity(NotificationDeliveryLogDTO dto) {
        if ( dto == null ) {
            return null;
        }

        NotificationDeliveryLog notificationDeliveryLog = new NotificationDeliveryLog();

        notificationDeliveryLog.setDeliveryId( dto.getDeliveryId() );
        notificationDeliveryLog.setDeliverySn( dto.getDeliverySn() );
        notificationDeliveryLog.setDeliveryType( dto.getDeliveryType() );
        notificationDeliveryLog.setDeliveryPriority( dto.getDeliveryPriority() );
        notificationDeliveryLog.setDeliveryStatus( dto.getDeliveryStatus() );
        notificationDeliveryLog.setDeliveryPlanTime( dto.getDeliveryPlanTime() );
        notificationDeliveryLog.setDeliveryDoneTime( dto.getDeliveryDoneTime() );
        notificationDeliveryLog.setDeliveryResult( dto.getDeliveryResult() );
        notificationDeliveryLog.setMessageType( dto.getMessageType() );
        notificationDeliveryLog.setMessageTitle( dto.getMessageTitle() );
        notificationDeliveryLog.setMessageContent( dto.getMessageContent() );
        notificationDeliveryLog.setMessageSourceId( dto.getMessageSourceId() );
        notificationDeliveryLog.setRecipientType( dto.getRecipientType() );
        notificationDeliveryLog.setRecipientIds( dto.getRecipientIds() );
        notificationDeliveryLog.setNtypeId( dto.getNtypeId() );
        notificationDeliveryLog.setNtplId( dto.getNtplId() );
        notificationDeliveryLog.setTenantId( dto.getTenantId() );
        notificationDeliveryLog.setCreateTime( dto.getCreateTime() );
        notificationDeliveryLog.setUpdateTime( dto.getUpdateTime() );

        return notificationDeliveryLog;
    }

    @Override
    public List<NotificationDeliveryLog> toEntityList(List<NotificationDeliveryLogDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<NotificationDeliveryLog> list = new ArrayList<NotificationDeliveryLog>( dtolist.size() );
        for ( NotificationDeliveryLogDTO notificationDeliveryLogDTO : dtolist ) {
            list.add( toEntity( notificationDeliveryLogDTO ) );
        }

        return list;
    }

    @Override
    public NotificationDeliveryLogVO toVo(NotificationDeliveryLog entity) {
        if ( entity == null ) {
            return null;
        }

        NotificationDeliveryLogVO notificationDeliveryLogVO = new NotificationDeliveryLogVO();

        notificationDeliveryLogVO.setDeliveryId( entity.getDeliveryId() );
        notificationDeliveryLogVO.setDeliverySn( entity.getDeliverySn() );
        notificationDeliveryLogVO.setDeliveryType( entity.getDeliveryType() );
        notificationDeliveryLogVO.setDeliveryPriority( entity.getDeliveryPriority() );
        notificationDeliveryLogVO.setDeliveryStatus( entity.getDeliveryStatus() );
        notificationDeliveryLogVO.setDeliveryPlanTime( entity.getDeliveryPlanTime() );
        notificationDeliveryLogVO.setDeliveryDoneTime( entity.getDeliveryDoneTime() );
        notificationDeliveryLogVO.setDeliveryResult( entity.getDeliveryResult() );
        notificationDeliveryLogVO.setMessageType( entity.getMessageType() );
        notificationDeliveryLogVO.setMessageTitle( entity.getMessageTitle() );
        notificationDeliveryLogVO.setMessageContent( entity.getMessageContent() );
        notificationDeliveryLogVO.setMessageSourceId( entity.getMessageSourceId() );
        notificationDeliveryLogVO.setRecipientType( entity.getRecipientType() );
        notificationDeliveryLogVO.setRecipientIds( entity.getRecipientIds() );
        notificationDeliveryLogVO.setNtypeId( entity.getNtypeId() );
        notificationDeliveryLogVO.setNtplId( entity.getNtplId() );
        notificationDeliveryLogVO.setTenantId( entity.getTenantId() );
        notificationDeliveryLogVO.setCreateTime( entity.getCreateTime() );
        notificationDeliveryLogVO.setUpdateTime( entity.getUpdateTime() );

        return notificationDeliveryLogVO;
    }

    @Override
    public List<NotificationDeliveryLogVO> toVoList(List<NotificationDeliveryLog> entities) {
        if ( entities == null ) {
            return null;
        }

        List<NotificationDeliveryLogVO> list = new ArrayList<NotificationDeliveryLogVO>( entities.size() );
        for ( NotificationDeliveryLog notificationDeliveryLog : entities ) {
            list.add( toVo( notificationDeliveryLog ) );
        }

        return list;
    }
}
