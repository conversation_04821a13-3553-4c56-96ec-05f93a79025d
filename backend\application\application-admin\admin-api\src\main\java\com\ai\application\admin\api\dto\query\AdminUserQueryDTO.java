package com.ai.application.admin.api.dto.query;

import com.ai.framework.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 应用用户表 查询条件
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(name = "应用用户表QueryDTO")
public class AdminUserQueryDTO extends PageParam {
    /**
     * 用户名称
     */
    @Schema(description = "搜索关键词(用户名/)")
    private String keyword;


    @Schema(description = "用户状态 1-启用 0-禁用 -1-删除")
    private Integer userStatus = 1;
}