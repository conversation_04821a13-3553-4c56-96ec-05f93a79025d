package com.ai.application.skill.mcp.service;

import com.ai.application.skill.mcp.api.dto.McpToolDTO;
import com.ai.application.skill.mcp.api.dto.query.McpToolQueryDTO;
import com.ai.application.skill.mcp.api.vo.McpToolVO;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Set;

/**
 * MCP工具表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
public interface IMcpToolService {

        /**
         * 分页
         *
         * @param queryDto
         * @return
         */
        List<McpToolVO> tools(McpToolQueryDTO queryDto);

        /**
         * 列表
         *
         * @param sort
         * @param queryDto
         * @return
         */
        List<McpToolVO> list(McpToolQueryDTO queryDto);

        /**
         * 保存
         *
         * @param dto
         */
        void add(McpToolDTO dto);

        /**
         * 更新
         *
         * @param dto
         */
        void update(McpToolDTO dto);

        /**
         * 查看
         *
         * @param id
         * @return
         */
        McpToolVO get(Integer id);
}