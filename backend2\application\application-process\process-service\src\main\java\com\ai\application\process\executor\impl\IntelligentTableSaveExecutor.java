package com.ai.application.process.executor.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.ai.application.agent.base.api.dto.IntelligentTableSaveDto;
import com.ai.application.agent.doc.api.feign.IAgentDocClient;
import com.ai.application.process.enums.ProcessErrorCodeEnum;
import com.ai.application.process.executor.BaseExecutor;
import com.ai.application.process.executor.ExecutionContext;
import com.ai.application.user.api.dto.auth.AuthorizedDataVo;
import com.ai.application.user.api.feign.DataAuthClient;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.list.CollectionUtils;
import com.ai.framework.core.vo.ResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.ai.application.user.api.enums.AuthDataTypeEnum.SMART_TABLE;


@Component
@Slf4j
public class IntelligentTableSaveExecutor implements BaseExecutor {

    private final IAgentDocClient agentDocClient;

    private final DataAuthClient dataAuthClient;


    public IntelligentTableSaveExecutor(IAgentDocClient agentDocClient, DataAuthClient dataAuthClient) {
        this.agentDocClient = agentDocClient;
        this.dataAuthClient = dataAuthClient;
    }

    @Override
    public Map<String, Object> execute(ExecutionContext executionContext) {

        Map<String, Object> parameters = executionContext.getParameters();

        String intelligentTableSn = executionContext.getParameterAsString("intelligentTableSn");
        ServiceException.throwIf(StringUtils.isBlank(intelligentTableSn), ProcessErrorCodeEnum.INTELLIGENT_TABLE_SN_IS_BLANK);
        AuthorizedDataVo authorizedDataVo = dataAuthClient.selectDataAuthByData(SMART_TABLE.getId(), intelligentTableSn);
        if (CollectionUtils.isEmpty(authorizedDataVo.getAuthorizedPermissions())){
            throw new ServiceException(ProcessErrorCodeEnum.INTELLIGENT_TABLE_NO_AUTH);
        }
        Integer type = (Integer) parameters.get("type");
        Object data = parameters.get("data");
        Integer overwrite = (Integer) parameters.get("overwrite");
        JSONArray jsonArray = new JSONArray();
        if (Objects.isNull(data)) {
            throw new ServiceException(ProcessErrorCodeEnum.INTELLIGENT_TABLE_DATA_IS_NULL);
        }
        String strData = String.valueOf(data);
        if (StringUtils.isBlank(strData) || "null".equals(strData) || "[]".equals(strData)) {
            throw new ServiceException(ProcessErrorCodeEnum.INTELLIGENT_TABLE_DATA_IS_NULL);
        }
        if (data instanceof Map<?,?> jsonData) {
            jsonArray.add(JSONObject.from(jsonData));
        } else if (data instanceof List<?> array) {
            jsonArray = JSONArray.from(array);
        } else {
            // 格式不正确的数据
            try {
                jsonArray.add(JSONObject.parseObject(strData));
            } catch (Exception e) {
                throw new ServiceException(ProcessErrorCodeEnum.INTELLIGENT_TABLE_DATA_FORMAT_ERROR);
            }
        }
        // 数据结构校验，删除所有空数据
        this.check(jsonArray, type);
        IntelligentTableSaveDto dto = IntelligentTableSaveDto.builder()
                .data(jsonArray)
                .intelligentTableSn(intelligentTableSn)
                .overwrite(overwrite)
                .build();

        ResultVo<List<String>> jsonObjectResultVo = agentDocClient.saveIntelligentTable(dto);
        if (jsonObjectResultVo.isSuccess()) {
            Map<String, Object> result = new HashMap<>();
            result.put("output", CollectionUtils.isNotEmpty(jsonObjectResultVo.getData()) ? jsonObjectResultVo.getData() : "");
            return result;
        } else {
            throw new ServiceException(jsonObjectResultVo.getCode(), jsonObjectResultVo.getMessage());
        }

    }

    private void check(JSONArray jsonArray, Integer type) {
        if (0 == type) {
            Object object = jsonArray.get(0);
            List<Object> list = this.getList(object);
            if (CollectionUtils.isEmpty(list)) {
                throw new ServiceException(ProcessErrorCodeEnum.INTELLIGENT_TABLE_DATA_IMPORT_ALL_FIELD_IS_NULL);
            }
        } else {
            jsonArray.forEach(jsonObject -> {
                if (!(jsonObject instanceof String || jsonObject instanceof Map)) {
                    throw new ServiceException(ProcessErrorCodeEnum.INTELLIGENT_TABLE_DATA_FORMAT_ERROR);
                } else if (jsonObject instanceof String string) {
                    try {
                        JSONObject.parseObject(string);
                    } catch (Exception e) {
                        throw new ServiceException(ProcessErrorCodeEnum.INTELLIGENT_TABLE_DATA_FORMAT_ERROR);
                    }
                }
            });
            // 删除所有空数据
            jsonArray.removeIf(jsonObject -> {
                List<Object> list = this.getList(jsonObject);
                return CollectionUtils.isEmpty(list);
            });
        }
    }

    @NotNull
    private List<Object> getList(Object object) {
        Map<String, Object> map;
        if (object instanceof String str) {
            map = JSONObject.parseObject(str, Map.class);
        } else {
            map = (Map<String, Object>) object;
        }
        List<Object> list = new ArrayList<>();
        map.forEach((key, value) -> {
            if (Objects.nonNull(value)) {
                String str = value.toString();
                if (StringUtils.isNotBlank(str) && !"null".equals(str) && !"[]".equals(str)) {
                    list.add(value);
                }
            }
        });
        return list;
    }

    @Override
    public String getId() {
        return "SMART_TABLE_UPDATE";
    }
}
