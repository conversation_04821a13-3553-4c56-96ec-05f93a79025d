package com.ai.application.tenant.authorize.api.mapstruct;

import com.ai.application.tenant.authorize.api.dto.ResourceGrantDTO;
import com.ai.application.tenant.authorize.api.entity.ResourceGrant;
import com.ai.application.tenant.authorize.api.vo.ResourceGrantVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-14T11:00:28+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class ResourceGrantMapstructImpl implements ResourceGrantMapstruct {

    @Override
    public ResourceGrant toEntity(ResourceGrantDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ResourceGrant resourceGrant = new ResourceGrant();

        resourceGrant.setGrantId( dto.getGrantId() );
        resourceGrant.setGrantType( dto.getGrantType() );
        resourceGrant.setGrantStatus( dto.getGrantStatus() );
        resourceGrant.setGrantObjectType( dto.getGrantObjectType() );
        resourceGrant.setGrantObjectId( dto.getGrantObjectId() );
        resourceGrant.setGrantConfig( dto.getGrantConfig() );
        resourceGrant.setResourceId( dto.getResourceId() );
        resourceGrant.setResourceType( dto.getResourceType() );
        resourceGrant.setResourceObjectId( dto.getResourceObjectId() );
        resourceGrant.setCreateTime( dto.getCreateTime() );
        resourceGrant.setUpdateTime( dto.getUpdateTime() );

        return resourceGrant;
    }

    @Override
    public List<ResourceGrant> toEntityList(List<ResourceGrantDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<ResourceGrant> list = new ArrayList<ResourceGrant>( dtolist.size() );
        for ( ResourceGrantDTO resourceGrantDTO : dtolist ) {
            list.add( toEntity( resourceGrantDTO ) );
        }

        return list;
    }

    @Override
    public ResourceGrantVO toVo(ResourceGrant entity) {
        if ( entity == null ) {
            return null;
        }

        ResourceGrantVO resourceGrantVO = new ResourceGrantVO();

        resourceGrantVO.setGrantId( entity.getGrantId() );
        resourceGrantVO.setGrantType( entity.getGrantType() );
        resourceGrantVO.setGrantStatus( entity.getGrantStatus() );
        resourceGrantVO.setGrantObjectType( entity.getGrantObjectType() );
        resourceGrantVO.setGrantObjectId( entity.getGrantObjectId() );
        resourceGrantVO.setGrantConfig( entity.getGrantConfig() );
        resourceGrantVO.setResourceId( entity.getResourceId() );
        resourceGrantVO.setResourceType( entity.getResourceType() );
        resourceGrantVO.setResourceObjectId( entity.getResourceObjectId() );
        resourceGrantVO.setCreateTime( entity.getCreateTime() );
        resourceGrantVO.setUpdateTime( entity.getUpdateTime() );

        return resourceGrantVO;
    }

    @Override
    public List<ResourceGrantVO> toVoList(List<ResourceGrant> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ResourceGrantVO> list = new ArrayList<ResourceGrantVO>( entities.size() );
        for ( ResourceGrant resourceGrant : entities ) {
            list.add( toVo( resourceGrant ) );
        }

        return list;
    }
}
