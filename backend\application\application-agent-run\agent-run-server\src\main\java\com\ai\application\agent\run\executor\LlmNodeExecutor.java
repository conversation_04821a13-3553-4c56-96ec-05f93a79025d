package com.ai.application.agent.run.executor;

import cn.hutool.json.JSONUtil;
import com.ai.application.agent.run.dto.ProcessChatDTO;
import com.ai.application.agent.run.errors.AgentNodeExecutorError;
import com.ai.application.agent.run.errors.LlmNodeExecutorError;
import com.ai.application.agent.run.feign.ILlmChatClient;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import com.ai.framework.workflow.excutor.NodeExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * LLM 节点执行器
 * 用于在工作流中调用大模型
 */
@Slf4j
@Component
public class LlmNodeExecutor implements NodeExecutor {

    @Autowired
    private ILlmChatClient llmChatClient;

    @Override
    public void execute(WorkflowContext context) {
        String nodeKey = context.getCurrentNodeKey();
        NodeContext nodeCtx = context.getNodeContexts().get(nodeKey);
        Map<String, Object> nodeDef = nodeCtx.getNodeDefinition();

        log.info("LlmNodeExecutor execute start, nodeKey: {}, nodeDef: {}", nodeKey, JsonUtils.toJsonString(nodeDef));

        try {
            // 设置节点状态为运行中
            nodeCtx.setStatus(NodeStatus.RUNNING);

            // 初始化节点输出
            if (nodeCtx.getOutput() == null) {
                nodeCtx.setOutput(new HashMap<>());
            }

            // 从节点定义中获取输入参数
            Map<String, Object> inputParameters = (Map<String, Object>) nodeDef.get("inputParameters");
            if (inputParameters == null) {
                throw new ServiceException(LlmNodeExecutorError.LLM_NODE_MISSING_INPUT_PARAMETERS);
            }

            // 获取参数值，支持变量替换
            String sysPrompt = getParameterValue(inputParameters, "sysPrompt", context);
            String prompt = getParameterValue(inputParameters, "prompt", context);
            String temperature = getParameterValue(inputParameters, "temperature", context);
            String model = getParameterValue(inputParameters, "model", context);
            String timeoutStr = getParameterValue(inputParameters, "timeoutInSeconds", context);

            // 参数校验
            if (StringUtils.isBlank(prompt)) {
                throw new ServiceException(LlmNodeExecutorError.LLM_PROMPT_IS_BLANK);
            }
            if (StringUtils.isBlank(model)) {
                throw new ServiceException(LlmNodeExecutorError.LLM_MODEL_IS_BLANK);
            }

            // 处理系统提示词（参考 backend2 的逻辑）
            String sysPromptValue = sysPrompt;
            if (StringUtils.isNotEmpty(sysPromptValue)) {
                // 如果系统提示词不是 JSON 格式，转换为 JSON 数组格式
                if (!sysPromptValue.trim().startsWith("[")) {
                    sysPromptValue = JSONUtil.toJsonStr(java.util.Collections.singletonList(sysPromptValue));
                }
            }

            // 构建大模型请求
            ProcessChatDTO chatDto = ProcessChatDTO.builder()
                    .sysPrompt(sysPromptValue)
                    .prompt(prompt)
                    .temperature(temperature)
                    .model(model)
                    .timeoutInSeconds(timeoutStr != null ? Integer.parseInt(timeoutStr) : null)
                    .build();

            log.info("LlmNodeExecutor request: {}", JsonUtils.toJsonString(chatDto));

            // 调用大模型服务
            String authorization = (String) context.getGlobalVars().get("authorization");
            ResultVo<String> result = llmChatClient.chat(chatDto, authorization);

            if (result == null || !Objects.equals(result.getCode(), 0)) {
                String errorMsg = result != null ? result.getMessage() : "调用大模型失败";
                throw new ServiceException(LlmNodeExecutorError.LLM_CALL_FAILED.getCode(), "调用大模型失败: " + errorMsg);
            }

            String reply = result.getData();
            if (reply == null) {
                throw new ServiceException(LlmNodeExecutorError.LLM_RESPONSE_IS_NULL);
            }

            log.info("LlmNodeExecutor reply: {}", reply);

            // 将结果写入输出参数
            Map<String, Object> outputParameters = (Map<String, Object>) nodeDef.get("outputParameters");
            if (outputParameters != null) {
                for (Map.Entry<String, Object> entry : outputParameters.entrySet()) {
                    String outputKey = entry.getKey();
                    String variableName = entry.getValue().toString();
                    
                    if ("message".equals(outputKey)) {
                        context.setVar(variableName, reply);
                        log.info("Set variable {} = {}", variableName, reply);
                    }
                }
            }

            // 设置节点输出
            nodeCtx.getOutput().put("message", reply);
            nodeCtx.getOutput().put("success", true);

            // 设置节点状态为成功
            nodeCtx.setStatus(NodeStatus.SUCCESS);
            nodeCtx.setEndTime(java.time.LocalDateTime.now());

            log.info("LlmNodeExecutor execute success, reply: {}", reply);

        } catch (Exception e) {
            log.error("LlmNodeExecutor execute error", e);
            nodeCtx.setStatus(NodeStatus.FAILED);
            nodeCtx.setErrorMsg("LLM执行失败: " + e.getMessage());
            nodeCtx.setEndTime(java.time.LocalDateTime.now());
            throw e;
        }
    }

    /**
     * 获取参数值，支持变量替换
     */
    private String getParameterValue(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        String strValue = value.toString();
        
        // 如果是变量引用（以$开头），从全局变量中获取
        if (strValue.startsWith("$")) {
            String varName = strValue.substring(1);
            Object varValue = context.getGlobalVars().get(varName);
            return varValue != null ? varValue.toString() : null;
        }
        
        return strValue;
    }

    @Override
    public String getType() {
        return "LLM";
    }
}
