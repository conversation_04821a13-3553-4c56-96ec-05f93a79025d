package com.ai.application.agent.run.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 文档关键词检索DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DocSearchByKeywordDTO implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 5776187663717594575L;

    private List<VocabularyWordDTO> keywords;

    private List<String> knowledgeInventorySns;

    private Integer topK;

    private String fieldName;

    /**
     * 词汇DTO
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class VocabularyWordDTO implements Serializable {
        
        @Serial
        private static final long serialVersionUID = 1L;
        
        /**
         * 词库
         */
        private String vocabularyName;

        /**
         * 词
         */
        private String name;

        /**
         * 别名
         */
        private List<String> alias;

        /**
         * 释义
         */
        private String definition;
    }
}
