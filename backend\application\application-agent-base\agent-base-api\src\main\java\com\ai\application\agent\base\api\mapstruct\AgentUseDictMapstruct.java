package com.ai.application.agent.base.api.mapstruct;
import com.ai.application.agent.base.api.dto.AgentUseDictUpdateDTO;
import com.ai.application.agent.base.api.entity.AgentUseDict;
import com.ai.application.agent.base.api.dto.AgentUseDictAddDTO;
import com.ai.application.agent.base.api.vo.AgentUseDictListVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 智能体关联字典表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-07
 */

@Mapper(componentModel = "spring")
public interface AgentUseDictMapstruct {

    AgentUseDict toAddEntity(AgentUseDictAddDTO dto);
    AgentUseDict toUpdateEntity(AgentUseDictUpdateDTO dto);
    AgentUseDictListVO toVo(AgentUseDict entity);
    List<AgentUseDictListVO> toVoList(List<AgentUseDict> entities);
}
