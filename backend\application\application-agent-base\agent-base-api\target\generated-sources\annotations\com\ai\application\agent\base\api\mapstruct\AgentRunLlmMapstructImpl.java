package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.dto.AgentRunLlmDTO;
import com.ai.application.agent.base.api.entity.AgentRunLlm;
import com.ai.application.agent.base.api.vo.AgentRunLlmVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-14T11:00:34+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentRunLlmMapstructImpl implements AgentRunLlmMapstruct {

    @Override
    public AgentRunLlm toEntity(AgentRunLlmDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentRunLlm agentRunLlm = new AgentRunLlm();

        agentRunLlm.setLlmRunId( dto.getLlmRunId() );
        agentRunLlm.setLlmModelName( dto.getLlmModelName() );
        agentRunLlm.setLlmPrompt( dto.getLlmPrompt() );
        agentRunLlm.setLlmResponse( dto.getLlmResponse() );
        agentRunLlm.setLlmMessages( dto.getLlmMessages() );
        agentRunLlm.setLlmStatus( dto.getLlmStatus() );
        agentRunLlm.setLlmError( dto.getLlmError() );
        agentRunLlm.setLlmDuration( dto.getLlmDuration() );
        agentRunLlm.setLlmPromptTokens( dto.getLlmPromptTokens() );
        agentRunLlm.setLlmCompletionTokens( dto.getLlmCompletionTokens() );
        agentRunLlm.setLlmTotalTokens( dto.getLlmTotalTokens() );
        agentRunLlm.setLlmConfig( dto.getLlmConfig() );
        agentRunLlm.setLlmStartTime( dto.getLlmStartTime() );
        agentRunLlm.setLlmEndTime( dto.getLlmEndTime() );
        agentRunLlm.setRunId( dto.getRunId() );
        agentRunLlm.setStepId( dto.getStepId() );
        agentRunLlm.setModelId( dto.getModelId() );
        agentRunLlm.setCreateTime( dto.getCreateTime() );
        agentRunLlm.setUpdateTime( dto.getUpdateTime() );

        return agentRunLlm;
    }

    @Override
    public List<AgentRunLlm> toEntityList(List<AgentRunLlmDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<AgentRunLlm> list = new ArrayList<AgentRunLlm>( dtolist.size() );
        for ( AgentRunLlmDTO agentRunLlmDTO : dtolist ) {
            list.add( toEntity( agentRunLlmDTO ) );
        }

        return list;
    }

    @Override
    public AgentRunLlmVO toVo(AgentRunLlm entity) {
        if ( entity == null ) {
            return null;
        }

        AgentRunLlmVO agentRunLlmVO = new AgentRunLlmVO();

        agentRunLlmVO.setLlmRunId( entity.getLlmRunId() );
        agentRunLlmVO.setLlmModelName( entity.getLlmModelName() );
        agentRunLlmVO.setLlmPrompt( entity.getLlmPrompt() );
        agentRunLlmVO.setLlmResponse( entity.getLlmResponse() );
        agentRunLlmVO.setLlmMessages( entity.getLlmMessages() );
        agentRunLlmVO.setLlmStatus( entity.getLlmStatus() );
        agentRunLlmVO.setLlmError( entity.getLlmError() );
        agentRunLlmVO.setLlmDuration( entity.getLlmDuration() );
        agentRunLlmVO.setLlmPromptTokens( entity.getLlmPromptTokens() );
        agentRunLlmVO.setLlmCompletionTokens( entity.getLlmCompletionTokens() );
        agentRunLlmVO.setLlmTotalTokens( entity.getLlmTotalTokens() );
        agentRunLlmVO.setLlmConfig( entity.getLlmConfig() );
        agentRunLlmVO.setLlmStartTime( entity.getLlmStartTime() );
        agentRunLlmVO.setLlmEndTime( entity.getLlmEndTime() );
        agentRunLlmVO.setRunId( entity.getRunId() );
        agentRunLlmVO.setStepId( entity.getStepId() );
        agentRunLlmVO.setModelId( entity.getModelId() );
        agentRunLlmVO.setCreateTime( entity.getCreateTime() );
        agentRunLlmVO.setUpdateTime( entity.getUpdateTime() );

        return agentRunLlmVO;
    }

    @Override
    public List<AgentRunLlmVO> toVoList(List<AgentRunLlm> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AgentRunLlmVO> list = new ArrayList<AgentRunLlmVO>( entities.size() );
        for ( AgentRunLlm agentRunLlm : entities ) {
            list.add( toVo( agentRunLlm ) );
        }

        return list;
    }
}
