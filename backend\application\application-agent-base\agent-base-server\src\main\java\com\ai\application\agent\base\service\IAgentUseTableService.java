package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.dto.AgentUseTableUpdateDTO;
import com.github.pagehelper.PageInfo;
import com.ai.application.agent.base.api.dto.AgentUseTableAddDTO;
import com.ai.application.agent.base.api.dto.AgentUseTableListDTO;
import com.ai.application.agent.base.api.vo.AgentUseTableListVO;
import java.util.List;

/**
 * 智能体关联智能表格表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
public interface IAgentUseTableService {

        /**
         * 列表
         *
         * @param queryDto
         * @return
         */
        List<AgentUseTableListVO> list(AgentUseTableListDTO queryDto);

        /**
         * 保存
         *
         * @param dto
         */
        void add(AgentUseTableAddDTO dto);

        /**
         * 更新
         *
         * @param dto
         */
        void update(AgentUseTableUpdateDTO dto);
}