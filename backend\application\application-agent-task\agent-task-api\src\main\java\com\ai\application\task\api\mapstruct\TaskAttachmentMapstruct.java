package com.ai.application.task.api.mapstruct;
import com.ai.application.task.api.entity.TaskAttachment;
import com.ai.application.task.api.dto.TaskAttachmentDTO;
import com.ai.application.task.api.vo.TaskAttachmentVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 任务附件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-07
 */

@Mapper(componentModel = "spring")
public interface TaskAttachmentMapstruct {

    TaskAttachment toEntity(TaskAttachmentDTO dto);
    List<TaskAttachment> toEntityList(List<TaskAttachmentDTO> dtolist);
    TaskAttachmentVO toVo(TaskAttachment entity);
    List<TaskAttachmentVO> toVoList(List<TaskAttachment> entities);
}
