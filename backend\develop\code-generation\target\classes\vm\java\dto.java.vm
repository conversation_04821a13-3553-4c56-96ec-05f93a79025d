package ${cfg.dtoPackage};

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

/**
 * $!{table.comment}
 *
 * <AUTHOR>
 * @since ${date}
 */
@Data
@Schema(name = "$!{table.comment}DTO")
    #set($ignoreFields = ${cfg.dtoIgnoreFields})
public class ${entity}DTO {
    #if("$ignoreFields" == "")
        #foreach($field in ${table.fields})
            #if(${field.keyFlag})
                #set($keyPropertyName=${field.propertyName})
            #end
            #if("$!field.comment" != "")
    /**
     * ${field.comment}
     */
            #end
    @Schema(description = "$!{field.comment}")
    private ${field.propertyType} ${field.propertyName};

        #end
    #else
        #foreach($field in ${table.fields})
            #set($flag = "0")
            #foreach($ignoreName in ${cfg.dtoIgnoreFields.split(",")})
                #if("$ignoreName" == "${field.propertyName}")
                    #set($flag = "1")
                    #break
                #end
            #end
            #if($flag == "0")
                #if(${field.keyFlag})
                    #set($keyPropertyName=${field.propertyName})
                #end
                #if("$!field.comment" != "")
    /**
     * ${field.comment}
     */
                #end
    @Schema(description = "$!{field.comment} ${field.propertyName}")
    private ${field.propertyType} ${field.propertyName};

            #end
        #end
    #end
}