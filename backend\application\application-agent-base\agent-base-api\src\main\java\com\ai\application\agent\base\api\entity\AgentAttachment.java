package com.ai.application.agent.base.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 智能体附件表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("agent_attachment")
public class AgentAttachment implements Serializable {
        @Schema(description = "")
    @TableId(type = IdType.AUTO)
private Integer attachId;

    /**
    * 状态 0失效 1有效
    */
    @Schema(description = "状态 0失效 1有效")
    private Integer attachStatus;

    /**
    * 智能体id
    */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
    * 智能体版本id
    */
    @Schema(description = "智能体版本id")
    private Integer versionId;

    /**
    * 文件id
    */
    @Schema(description = "文件id")
    private Integer fileId;

    /**
    * 额外属性
    */
    @Schema(description = "额外属性")
    private String fileExtend;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}