package com.ai.application.admin;

import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.launch.AiApplication;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

@EnableDiscoveryClient
@ComponentScan(basePackages = {"com.ai"})
@SpringBootApplication(exclude= {DataSourceAutoConfiguration.class})
@EnableConfigurationProperties(DynamicDataSourceProperties.class)
@MapperScan(basePackages = {"com.ai.application.admin.mapper"})
@EnableFeignClients("com.ai")
public class AdminApplication {
    public static void main(String[] args) {
        AiApplication.run(ServiceConstant.ADMIN, AdminApplication.class, args);
    }
}