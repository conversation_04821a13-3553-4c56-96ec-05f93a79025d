package com.ai.application.agent.run.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 长期记忆检索请求DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "LongTermMemoryRetrieveRequestDTO")
public class LongTermMemoryRetrieveRequestDTO {

    /**
     * 智能体ID
     */
    @Schema(description = "智能体ID")
    private String agentId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 检索内容
     */
    @Schema(description = "检索内容")
    private String queryContent;

    /**
     * 检索策略列表
     */
    @Schema(description = "检索策略列表")
    private List<String> strategies;

    /**
     * 检索开始的时间，多少天前
     */
    @Schema(description = "检索开始的时间，多少天前")
    private Integer startDaysBefore;

    /**
     * 检索结果数量限制
     */
    @Schema(description = "检索结果数量限制")
    private Integer limit;

    /**
     * 长期记忆是否启用
     */
    @Schema(description = "长期记忆是否启用")
    private Boolean longTermMemoryEnabled;

    /**
     * 会话编号（调试模式使用）
     */
    @Schema(description = "会话编号（调试模式使用）")
    private String sessionSn;

    /**
     * 是否调试运行
     */
    @Schema(description = "是否调试运行")
    private Boolean debugRun;

    /**
     * 记忆类别（用于agent-memory-service检索）
     */
    @Schema(description = "记忆类别")
    private String category;

    /**
     * 相似度阈值
     */
    @Schema(description = "相似度阈值")
    private Double similarityThreshold;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    /**
     * 排序方式
     */
    @Schema(description = "排序方式")
    private String sortBy;
}
