package com.ai.application.agent.base.mapper;

import com.ai.application.agent.base.api.entity.AgentVersionExtend;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import java.util.List;

/**
 * agent版本扩展信息-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface AgentVersionExtendMapper extends BaseMapper<AgentVersionExtend> {
    @Select("select * from agent_version_extend where version_id = #{versionId}")
    List<AgentVersionExtend> findByVersionId(@Param("versionId") Integer versionId);
}
