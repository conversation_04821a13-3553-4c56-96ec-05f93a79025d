package com.ai.application.knowledge.table.feign;

import com.ai.application.knowledge.table.dto.TableDataCreateDto;
import com.ai.application.knowledge.table.dto.TableDataListDto;
import com.ai.application.knowledge.table.service.IKnowledgeTableDataService;
import com.ai.application.knowledge.table.service.IKnowledgeTableService;
import com.ai.application.knowledge.table.vo.TableDataListVo;
import com.ai.application.knowledge.table.vo.TableDetailVo;
import com.ai.framework.core.vo.ResultVo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Slf4j
public class TableFeignClient implements ITableFeignClient {

    private final IKnowledgeTableService iKnowledgeTableService;

    private final IKnowledgeTableDataService knowledgeTableDataService;

    public TableFeignClient(IKnowledgeTableService iKnowledgeTableService, IKnowledgeTableDataService knowledgeTableDataService) {
        this.iKnowledgeTableService = iKnowledgeTableService;
        this.knowledgeTableDataService = knowledgeTableDataService;
    }

    @Operation(summary = "根据表格ID获取详情")
    @GetMapping("/detailById/{tableId}")
    public ResultVo<TableDetailVo> detailById(@PathVariable("tableId") Integer tableId) {
        return iKnowledgeTableService.detailById(tableId);
    }

    @Operation(summary = "数据插入")
    @PostMapping("/data/create")
    public ResultVo<String> dataCreate(@RequestBody TableDataCreateDto dto) {
        return knowledgeTableDataService.create(dto);
    }

    @Operation(summary = "数据查询")
    @PostMapping("/data/query")
    public ResultVo<List<TableDataListVo>> dataQuery(@RequestBody TableDataListDto dto) {
        return knowledgeTableDataService.noPageList(dto);
    }
}
