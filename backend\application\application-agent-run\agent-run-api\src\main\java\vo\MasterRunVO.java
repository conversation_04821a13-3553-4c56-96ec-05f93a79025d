package vo;

import bo.MasterContentBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(name = "智能规划返参")
@Data
public class MasterRunVO {
    @Schema(description = "会话编码")
    private String sessionSn;

    @Schema(description = "执行ID")
    private Integer runId;

    @Schema(description = "消息时间")
    private String messageTime;

    /**
     * 消息状态
     */
    @Schema(description = "消息状态 success正常 fail异常")
    private String messageStatus;

    /**
     * 文档内容
     */
    @Schema(description = "内容")
    private MasterContentBO content;
}