<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.agent.base.mapper.AgentUseDictMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.agent.base.api.entity.AgentUseDict">
                    <id column="ad_id" property="adId" />
                    <result column="ad_status" property="adStatus" />
                    <result column="agent_id" property="agentId" />
                    <result column="version_id" property="versionId" />
                    <result column="dict_id" property="dictId" />
                    <result column="dict_extend" property="dictExtend" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        ad_id, ad_status, agent_id, version_id, dict_id, dict_extend, create_time, update_time
    </sql>

    <select id="selectUseDictByPage" resultType="com.ai.application.agent.base.api.vo.AgentUseKnowledgeDictQueryVO">
        select
        <include refid="com.ai.application.agent.base.mapper.AgentUseDictMapper.Base_Column_List"></include>
        from agent_use_dict
        order by create_time desc;
    </select>
</mapper>