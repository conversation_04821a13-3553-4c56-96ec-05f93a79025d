package com.ai.application.tenant.service.impl;

import com.ai.application.app.api.dto.query.AppUserQueryDTO;
import com.ai.application.app.api.feign.IAppUserClient;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.application.tenant.api.dto.TenantDepartmentDTO;
import com.ai.application.tenant.api.dto.TenantDepartmentMoveDTO;
import com.ai.application.tenant.api.dto.query.TenantDepartmentQueryDTO;
import com.ai.application.tenant.api.entity.TenantDepartment;
import com.ai.application.tenant.api.mapstruct.TenantDepartmentMapstruct;
import com.ai.application.tenant.api.vo.TenantDepartmentVO;
import com.ai.application.tenant.api.vo.TenantDepartmentTreeVO;
import com.ai.application.tenant.mapper.TenantDepartmentMapper;
import com.ai.application.tenant.service.ITenantDepartmentService;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.enums.StatusEnum;
import com.ai.framework.core.util.BusinessAssertUtil;
import com.ai.framework.core.util.validator.AssertUtil;
import com.ai.framework.core.vo.ResultVo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 租户部门表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
public class TenantDepartmentServiceImpl implements ITenantDepartmentService {

    @Resource
    private TenantDepartmentMapper tenantDepartmentMapper;
    @Resource
    private TenantDepartmentMapstruct tenantDepartmentMapstruct;
    @Resource
    private IAppUserClient appUserClient;

    @Transactional(readOnly = true)
    @Override
    public PageInfo<TenantDepartmentVO> page(TenantDepartmentQueryDTO queryDto) {
        QueryWrapper<TenantDepartment> queryWrapper = this.buildQuery(queryDto);
        Page<TenantDepartment> page = new Page<>(queryDto.getPageNo(), queryDto.getPageSize());
        Page<TenantDepartment> result = this.tenantDepartmentMapper.selectPage(page, queryWrapper);
        return PageInfo.of(tenantDepartmentMapstruct.toVoList(result.getRecords()));
    }

    @Transactional(readOnly = true)
    @Override
    public List<TenantDepartmentVO> list(TenantDepartmentQueryDTO queryDto) {
        QueryWrapper<TenantDepartment> queryWrapper = this.buildQuery(queryDto);
        return tenantDepartmentMapstruct.toVoList(this.tenantDepartmentMapper.selectList(queryWrapper));
    }

    @Transactional(readOnly = true)
    @Override
    public List<Integer> findParentDepartmentIds(Integer deptId) {
        AssertUtil.isNotNull(deptId, "deptId不能为空");
        TenantDepartment entity = tenantDepartmentMapper.selectById(deptId);
        AssertUtil.isNotNull(entity, "找不到id为 " + deptId + " 的记录");

        QueryWrapper<TenantDepartment> queryWrapper = this.buildQuery(null);
        List<TenantDepartment> tenantDepartments = this.tenantDepartmentMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(tenantDepartments)){
            return null;
        }

        List<Integer> parentIds = Lists.newArrayList();
        recursiveGetParentIds(deptId, tenantDepartments,parentIds);
        return parentIds;
    }

    private void recursiveGetParentIds(int deptId, List<TenantDepartment> tenantDepartments,List<Integer> parentIds) {
        // 查询当前部门的所有上级部门
        Optional<TenantDepartment> curTenantDepartment = tenantDepartments.stream().filter(a -> a.getDeptId().equals(deptId)).findFirst();
        if(curTenantDepartment.isEmpty()){
            return;
        }
        if(curTenantDepartment.get().getParentId()!=0) {
            parentIds.add(curTenantDepartment.get().getParentId());
        }
        recursiveGetParentIds(curTenantDepartment.get().getParentId(),tenantDepartments, parentIds);
    }


    @Transactional(readOnly = true)
    @Override
    public TenantDepartmentTreeVO getTreeDepartment() {
        QueryWrapper<TenantDepartment> queryWrapper = this.buildQuery(null);
        List<TenantDepartment> tenantDepartments = this.tenantDepartmentMapper.selectList(queryWrapper);
        List<TenantDepartmentTreeVO> treeVoList = tenantDepartmentMapstruct.toTreeVoList(tenantDepartments);
        if(CollectionUtils.isEmpty(treeVoList)){
            return null;
        }
        //树形结构
        List<TenantDepartmentTreeVO> resList = buildTree(treeVoList,0);
        return resList.get(0);
    }

    private List<TenantDepartmentTreeVO> buildTree(List<TenantDepartmentTreeVO> nodes, Integer rootParentId) {
        // 使用Stream分组，按parentId分组
        Map<Integer, List<TenantDepartmentTreeVO>> childrenMap = nodes.stream()
                .collect(Collectors.groupingBy(node -> Objects.requireNonNullElse(node.getParentId(), 0)));

        // 获取根节点列表
        List<TenantDepartmentTreeVO> rootNodes = childrenMap.getOrDefault(rootParentId, new ArrayList<>());

        // 为每个节点设置其子节点
        nodes.forEach(node -> {
            List<TenantDepartmentTreeVO> children = childrenMap.getOrDefault(node.getDeptId(), new ArrayList<>());
            if(CollectionUtils.isNotEmpty(children)) {
                children.sort((p1, p2) -> Integer.compare(p1.getDeptSort(), p2.getDeptSort()));
            }
            node.setChildren(children);
            if(CollectionUtils.isNotEmpty(children)) {
                node.setNums(node.getTotalDescendants());
                node.setDeptIds(node.getSubDeptIds());
            }
        });

        return rootNodes;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(TenantDepartmentDTO dto) {
        AssertUtil.isNotNull(dto.getParentId(), "部门父id不能为空");

        //校验部门名称是否重复
        checkNameNotExist(dto.getParentId(),dto.getDeptName());

        Integer maxDeptSort = 1;
        //设置部门位置
        List<TenantDepartment> tenantDepartments = tenantDepartmentMapper.queryTenantSubDepartment(UserContext.getTenantId(), dto.getParentId());
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(tenantDepartments)){
            List<Integer> listDeptSort = tenantDepartments.stream().map(TenantDepartment::getDeptSort).toList();
            maxDeptSort = Collections.max(listDeptSort) + 1;
        }

        TenantDepartment entity = tenantDepartmentMapstruct.toEntity(dto);
        entity.setDeptSort(maxDeptSort);
        entity.setTenantId(UserContext.getTenantId());
        entity.setDeptId(null);
        tenantDepartmentMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(TenantDepartmentDTO dto) {
        BusinessAssertUtil.notNull(dto.getDeptId(), "id不能为空");
        TenantDepartment entity = tenantDepartmentMapper.selectById(dto.getDeptId());
        AssertUtil.isNotNull(entity, "找不到id为 " + dto.getDeptId() + " 的记录");

        //校验部门名称是否重复
        checkNameNotExist(dto.getParentId(),dto.getDeptName());

        entity.setDeptName(dto.getDeptName());
        entity.setParentId(dto.getParentId());
        tenantDepartmentMapper.updateById(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Integer deptId) {
        AssertUtil.isNotNull(deptId, "部门id不能为空");
        TenantDepartment entity = tenantDepartmentMapper.selectById(deptId);
        AssertUtil.isNotNull(entity, "找不到id为 " + deptId + " 的记录");

        //判断是否存在下级部门
        List<TenantDepartment> tenantDepartments = tenantDepartmentMapper.queryTenantSubDepartment(UserContext.getTenantId(), entity.getDeptId());
        AssertUtil.isFalse(org.apache.commons.collections4.CollectionUtils.isNotEmpty(tenantDepartments),"存在下级部门，不能删除");

        //判断是否存在员工
        int userCount = tenantDepartmentMapper.getDepartmentUserCount(UserContext.getTenantId(), entity.getDeptId());
        AssertUtil.isFalse(userCount>0, "部门下存在员工，不能删除");

        //校验所有下级是否有员工
        QueryWrapper<TenantDepartment> queryWrapper = this.buildQuery(null);
        List<TenantDepartment> allDepartments = this.tenantDepartmentMapper.selectList(queryWrapper);
        AssertUtil.isNotEmpty(allDepartments,"没有查询到部门数据");
        List<TenantDepartmentTreeVO> treeVoList = tenantDepartmentMapstruct.toTreeVoList(allDepartments);
        //树形结构
        List<Integer> subDepartmentIds = getSubDepartmentIds(treeVoList, deptId);
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(subDepartmentIds)){
            AppUserQueryDTO appUserQueryDTO = new AppUserQueryDTO();
            appUserQueryDTO.setDeptIds(subDepartmentIds);
            ResultVo<List<AppUserVO>> listResultVo = appUserClient.list(appUserQueryDTO);
            AssertUtil.isTrue(listResultVo.isSuccess(), "查询部门员工失败");
            List<AppUserVO> listUser = listResultVo.getData();
            AssertUtil.isFalse(org.apache.commons.collections4.CollectionUtils.isNotEmpty(listUser),"部门下存在员工，不能删除");
        }

        entity.setDeptStatus(StatusEnum.STATUS_DELETE.getCode());
        tenantDepartmentMapper.updateById(entity);
    }

    private List<Integer> getSubDepartmentIds(List<TenantDepartmentTreeVO> nodes, Integer rootParentId) {
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(nodes)){
            return Lists.newArrayList();
        }

        List<TenantDepartmentTreeVO> list = nodes.stream().filter(a -> a.getParentId().equals(rootParentId)).toList();
        List<Integer> retDeptList = new ArrayList<>(list.stream().map(TenantDepartmentTreeVO::getDeptId).distinct().toList());

        // 为每个节点设置其子节点
        list.forEach(node -> {
            retDeptList.addAll(this.getSubDepartmentIds(nodes,node.getDeptId()));
        });

        return retDeptList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deptMove(Integer curDeptId, TenantDepartmentMoveDTO targetDto) {
        AssertUtil.isNotNull(curDeptId, "移动部门id不能为空");
        TenantDepartment curEntity = tenantDepartmentMapper.selectById(curDeptId);
        AssertUtil.isNotNull(curEntity, "找不到放置部门id为 " + curDeptId + " 的记录");

        //判断是否存在下级部门
        List<TenantDepartment> tenantDepartments = tenantDepartmentMapper.queryTenantSubDepartment(UserContext.getTenantId(), targetDto.getParentId());

        curEntity.setParentId(targetDto.getParentId());
        //无子部门的情况
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(tenantDepartments)){
            curEntity.setDeptSort(1);
            tenantDepartmentMapper.updateById(curEntity);
            return;
        }

        //移动到第一个位置
        if(Objects.isNull(targetDto.getPreviousSiblingId()) || targetDto.getPreviousSiblingId() <=0){
            //设置父节点&位置
            tenantDepartments.add(0,curEntity);
        }else{
            TenantDepartment targerEntity = tenantDepartmentMapper.selectById(targetDto.getPreviousSiblingId());
            AssertUtil.isNotNull(targerEntity, "找不到移动部门id为 " + targetDto.getPreviousSiblingId() + " 的记录");
            tenantDepartments.add(tenantDepartments.indexOf(targerEntity),curEntity);
        }

        //重新排序
        int idx = 0;
        for (TenantDepartment tenantDepartment : tenantDepartments) {
            tenantDepartment.setDeptSort(idx++);
            tenantDepartmentMapper.updateById(tenantDepartment);
        }
    }

    @Transactional(readOnly = true)
    @Override
    public TenantDepartmentVO get(Integer id) {
        BusinessAssertUtil.notNull(id, "id不能为空");
        TenantDepartment entity = tenantDepartmentMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到id为 " + id + " 的记录");
        return tenantDepartmentMapstruct.toVo(entity);
    }

    @Override
    public TenantDepartment getTenantRootDepartment(Integer tenantId){
        return tenantDepartmentMapper.getTenantRootDepartment(tenantId);
    }

    private boolean checkNameNotExist(Integer parentId,String name) {
        QueryWrapper<TenantDepartment> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TenantDepartment::getTenantId, UserContext.getTenantId())
        .eq(TenantDepartment::getDeptStatus, 1)
        .eq(TenantDepartment::getParentId, parentId)
        .eq(TenantDepartment::getDeptName, name);
        return tenantDepartmentMapper.selectOne(queryWrapper) == null;
    }

    private QueryWrapper<TenantDepartment> buildQuery(TenantDepartmentQueryDTO queryDto) {
        QueryWrapper<TenantDepartment> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TenantDepartment::getTenantId, UserContext.getTenantId());
        queryWrapper.lambda().eq(TenantDepartment::getDeptStatus, 1);
        return queryWrapper;
    }
}