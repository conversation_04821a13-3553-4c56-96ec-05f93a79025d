package com.ai.application.agent.base.api.mapstruct;
import com.ai.application.agent.base.api.entity.AgentRunKb;
import com.ai.application.agent.base.api.dto.AgentRunKbDTO;
import com.ai.application.agent.base.api.vo.AgentRunKbVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 智能体知识库检索记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */

@Mapper(componentModel = "spring")
public interface AgentRunKbMapstruct {

    AgentRunKb toEntity(AgentRunKbDTO dto);
    List<AgentRunKb> toEntityList(List<AgentRunKbDTO> dtolist);
    AgentRunKbVO toVo(AgentRunKb entity);
    List<AgentRunKbVO> toVoList(List<AgentRunKb> entities);
}
