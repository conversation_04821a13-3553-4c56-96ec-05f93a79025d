package com.ai.application.agent.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(name = "AgentPublishDTO")
@Data
public class AgentPublishDTO {
    @Schema(description = "Agent编码")
    private String agentSn;

    @Schema(description = "版本号")
    private String versionNumber;

    @Schema(description = "版本编号")
    private String versionSn;

    @Schema(description = "发布启用 true启用 false未启用")
    private Boolean enable = false;

    @Schema(description = "是否为模板 1是 0否")
    private int templateSwitch;
}