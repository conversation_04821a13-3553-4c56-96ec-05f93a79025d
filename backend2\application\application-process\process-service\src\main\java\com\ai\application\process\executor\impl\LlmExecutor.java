package com.ai.application.process.executor.impl;

import cn.hutool.json.JSONUtil;
import com.ai.application.agent.chat.api.dto.ProcessChatDto;
import com.ai.application.agent.chat.api.feign.IProcessChatClient;
import com.ai.application.process.enums.ProcessErrorCodeEnum;
import com.ai.application.process.executor.BaseExecutor;
import com.ai.application.process.executor.ExecutionContext;
import com.ai.application.user.api.enums.ModelFeatureType;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.vo.ResultVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Component
@AllArgsConstructor
@Slf4j
public class LlmExecutor implements BaseExecutor {

    private final IProcessChatClient agentChatClient;
    @Override
    public Map<String, Object> execute(ExecutionContext executionContext) {
        Map<String, Object> parameters = executionContext.getParameters();
        String sysPrompt = JSONUtil.toJsonStr(parameters.get("sysPrompt"));
        String prompt = JSONUtil.toJsonStr(parameters.get("prompt"));
        String temperature = (String) parameters.get("temperature");
        String model = (String) parameters.get("model");
        Long timeoutInSeconds = executionContext.getConfig().getTimeoutInSeconds();
        String reply = execute(prompt, sysPrompt, temperature, model, timeoutInSeconds, executionContext.getAuthorization());

        Map<String, Object> result = new HashMap<>();
        result.put("message", reply);

        return result;
    }

    public String execute(String prompt, String sysPrompt, String temperature, String model, Long timeoutInSeconds, String authorization){
        checkParameters(prompt, temperature, model);
        // 调用agent-chat获取LLM回复
        ProcessChatDto dto = new ProcessChatDto();
        String sysPromptValue = sysPrompt;
        if (StringUtils.isNotEmpty(sysPromptValue)) {
            sysPromptValue = JSONUtil.toJsonStr(Collections.singletonList(sysPromptValue));
        }
        dto.setSysPrompt(sysPromptValue);
        dto.setPrompt(prompt);
        dto.setTemperature(temperature);
        dto.setModel(model);
        dto.setTimeoutInSeconds(timeoutInSeconds == null ? null : timeoutInSeconds.intValue());
        ResultVo<String> result = agentChatClient.chat(dto, authorization);
        int code = result.getCode();
        if(!Objects.equals(code, 0)){
            throw new ServiceException(code, result.getMessage());
        }

        return result.getData();
    }

    private void checkParameters(String prompt, String temperature, String model) {
        if(StringUtils.isBlank(prompt)){
            throw new ServiceException(ProcessErrorCodeEnum.LLM_EXECUTOR_PROMPT_IS_BLANK);
        }
//        if(StringUtils.isBlank(temperature)){
//            throw new ServiceException(ProcessErrorCodeEnum.LLM_EXECUTOR_TEMPERATURE_IS_BLANK);
//        }
        if(StringUtils.isBlank(model)){
            throw new ServiceException(ProcessErrorCodeEnum.LLM_EXECUTOR_MODEL_IS_BLANK);
        }
    }

    @Override
    public String getId() {
        return ModelFeatureType.LLM.getKey();
    }
}
