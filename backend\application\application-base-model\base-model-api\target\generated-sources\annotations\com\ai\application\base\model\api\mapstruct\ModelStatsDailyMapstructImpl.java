package com.ai.application.base.model.api.mapstruct;

import com.ai.application.base.model.api.dto.ModelStatsDailyDTO;
import com.ai.application.base.model.api.entity.ModelStatsDaily;
import com.ai.application.base.model.api.vo.ModelStatsDailyVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-13T10:32:22+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class ModelStatsDailyMapstructImpl implements ModelStatsDailyMapstruct {

    @Override
    public ModelStatsDaily toEntity(ModelStatsDailyDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ModelStatsDaily modelStatsDaily = new ModelStatsDaily();

        modelStatsDaily.setStatsId( dto.getStatsId() );
        modelStatsDaily.setStatsDate( dto.getStatsDate() );
        modelStatsDaily.setCallCount( dto.getCallCount() );
        modelStatsDaily.setSuccessCount( dto.getSuccessCount() );
        modelStatsDaily.setFailedCount( dto.getFailedCount() );
        modelStatsDaily.setTimeoutCount( dto.getTimeoutCount() );
        modelStatsDaily.setPromptTokens( dto.getPromptTokens() );
        modelStatsDaily.setCompletionTokens( dto.getCompletionTokens() );
        modelStatsDaily.setTotalTokens( dto.getTotalTokens() );
        modelStatsDaily.setTotalDuration( dto.getTotalDuration() );
        modelStatsDaily.setAvgDuration( dto.getAvgDuration() );
        modelStatsDaily.setMaxDuration( dto.getMaxDuration() );
        modelStatsDaily.setMinDuration( dto.getMinDuration() );
        modelStatsDaily.setCostAmount( dto.getCostAmount() );
        modelStatsDaily.setUniqueUserCount( dto.getUniqueUserCount() );
        modelStatsDaily.setUniqueAgentCount( dto.getUniqueAgentCount() );
        modelStatsDaily.setModelId( dto.getModelId() );
        modelStatsDaily.setTenantId( dto.getTenantId() );
        modelStatsDaily.setCreateTime( dto.getCreateTime() );
        modelStatsDaily.setUpdateTime( dto.getUpdateTime() );

        return modelStatsDaily;
    }

    @Override
    public List<ModelStatsDaily> toEntityList(List<ModelStatsDailyDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<ModelStatsDaily> list = new ArrayList<ModelStatsDaily>( dtolist.size() );
        for ( ModelStatsDailyDTO modelStatsDailyDTO : dtolist ) {
            list.add( toEntity( modelStatsDailyDTO ) );
        }

        return list;
    }

    @Override
    public ModelStatsDailyVO toVo(ModelStatsDaily entity) {
        if ( entity == null ) {
            return null;
        }

        ModelStatsDailyVO modelStatsDailyVO = new ModelStatsDailyVO();

        modelStatsDailyVO.setStatsId( entity.getStatsId() );
        modelStatsDailyVO.setStatsDate( entity.getStatsDate() );
        modelStatsDailyVO.setCallCount( entity.getCallCount() );
        modelStatsDailyVO.setSuccessCount( entity.getSuccessCount() );
        modelStatsDailyVO.setFailedCount( entity.getFailedCount() );
        modelStatsDailyVO.setTimeoutCount( entity.getTimeoutCount() );
        modelStatsDailyVO.setPromptTokens( entity.getPromptTokens() );
        modelStatsDailyVO.setCompletionTokens( entity.getCompletionTokens() );
        modelStatsDailyVO.setTotalTokens( entity.getTotalTokens() );
        modelStatsDailyVO.setTotalDuration( entity.getTotalDuration() );
        modelStatsDailyVO.setAvgDuration( entity.getAvgDuration() );
        modelStatsDailyVO.setMaxDuration( entity.getMaxDuration() );
        modelStatsDailyVO.setMinDuration( entity.getMinDuration() );
        modelStatsDailyVO.setCostAmount( entity.getCostAmount() );
        modelStatsDailyVO.setUniqueUserCount( entity.getUniqueUserCount() );
        modelStatsDailyVO.setUniqueAgentCount( entity.getUniqueAgentCount() );
        modelStatsDailyVO.setModelId( entity.getModelId() );
        modelStatsDailyVO.setTenantId( entity.getTenantId() );
        modelStatsDailyVO.setCreateTime( entity.getCreateTime() );
        modelStatsDailyVO.setUpdateTime( entity.getUpdateTime() );

        return modelStatsDailyVO;
    }

    @Override
    public List<ModelStatsDailyVO> toVoList(List<ModelStatsDaily> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ModelStatsDailyVO> list = new ArrayList<ModelStatsDailyVO>( entities.size() );
        for ( ModelStatsDaily modelStatsDaily : entities ) {
            list.add( toVo( modelStatsDaily ) );
        }

        return list;
    }
}
