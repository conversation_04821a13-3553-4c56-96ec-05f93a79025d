package com.ai.application.agent.run.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 短期记忆存储请求DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "ShortTermMemoryStoreRequestDTO")
public class ShortTermMemoryStoreRequestDTO {

    /**
     * 智能体ID
     */
    @Schema(description = "智能体ID")
    private String agentId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 存储桶编号
     */
    @Schema(description = "存储桶编号")
    private String bucketSn;

    /**
     * 记忆内容
     */
    @Schema(description = "记忆内容")
    private String memoryContent;

    /**
     * 批量记忆内容
     */
    @Schema(description = "批量记忆内容")
    private List<MemoryContentItem> memoryContents;

    /**
     * 记忆内容项
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class MemoryContentItem {
        /**
         * 记忆内容
         */
        @Schema(description = "记忆内容")
        private String memoryContent;

        /**
         * 存储桶编号
         */
        @Schema(description = "存储桶编号")
        private String bucketSn;
    }
}
