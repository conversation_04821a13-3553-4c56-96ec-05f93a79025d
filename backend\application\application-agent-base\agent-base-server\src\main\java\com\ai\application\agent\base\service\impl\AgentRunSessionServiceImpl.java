package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.dto.SessionCreateDTO;
import com.ai.application.agent.base.api.dto.SessionHistoryDTO;
import com.ai.application.agent.base.api.entity.Agent;
import com.ai.application.agent.base.api.entity.AgentVersion;
import com.ai.application.agent.base.api.enums.SessionStatusEnum;
import com.ai.application.agent.base.api.mapstruct.AgentRunSessionMapstruct;
import com.ai.application.agent.base.api.vo.SessionHistoryVO;
import com.ai.application.agent.base.mapper.AgentMapper;
import com.ai.application.agent.base.mapper.AgentVersionMapper;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.util.validator.AssertUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ai.application.agent.base.mapper.AgentRunSessionMapper;
import com.ai.application.agent.base.api.entity.AgentRunSession;
import com.ai.application.agent.base.service.IAgentRunSessionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.annotation.Resource;
import org.springframework.util.Assert;

/**
 * 智能体运行会话表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Service
public class AgentRunSessionServiceImpl implements IAgentRunSessionService{
    @Resource
    private AgentRunSessionMapper agentRunSessionMapper;

    @Resource
    private AgentRunSessionMapstruct agentRunSessionMapstruct;

    @Resource
    private AgentMapper agentMapper;

    @Resource
    private AgentVersionMapper agentVersionMapper;

    @Transactional(readOnly = true)
    @Override
    public PageInfo<SessionHistoryVO> history(SessionHistoryDTO queryDto) {
        Agent agent = agentMapper.selectByAgentSn(queryDto.getAgentSn());
        Assert.notNull(agent, "agent is null");

        AgentVersion agentVersion = agentVersionMapper.selectByVersionSn(queryDto.getVersionSn());
        Assert.notNull(agentVersion, "agent version is null");

        LambdaQueryWrapper<AgentRunSession> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentRunSession::getVersionId, agentVersion.getVersionId());
        queryWrapper.eq(AgentRunSession::getAgentId, agent.getAgentId());
        queryWrapper.gt(AgentRunSession::getSessionStatus, 0);

        Page<AgentRunSession> page = new Page<>(queryDto.getPageNo(), queryDto.getPageSize());
        Page<AgentRunSession> result = this.agentRunSessionMapper.selectPage(page, queryWrapper);

        return PageInfo.of(agentRunSessionMapstruct.toSessionHistoryList(result.getRecords()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void create(SessionCreateDTO dto) {
        Agent agent = agentMapper.selectByAgentSn(dto.getAgentSn());
        AssertUtil.isNotNull(agent, "agent is null");
        AssertUtil.isNotNull(agent.getVersionId(), "版本不存在");

        AgentRunSession agentRunSession = new AgentRunSession();
        agentRunSession.setAgentId(agent.getAgentId());
        agentRunSession.setVersionId(agent.getVersionId());
        agentRunSession.setSessionSn(UUIDUtil.genRandomSn("session"));
        agentRunSession.setSessionStatus(SessionStatusEnum.ACTIVE.getCode());
        agentRunSession.setUserId(UserContext.getUserId());
        agentRunSession.setTenantId(UserContext.getTenantId());
        agentRunSession.setSessionTitle("智能生成标题预留");
        agentRunSessionMapper.insert(agentRunSession);
    }
}