version: '3.8'

services:
  agent-memory:
    build:
      context: .
      dockerfile: Dockerfile
    image: agent-memory:latest
    container_name: agent-memory
    ports:
      - "6023:6023"
    environment:
      - PYTHONPATH=/app
      - TZ=Asia/Shanghai
      # Docker和服务IP配置（通过环境变量动态设置）
      - DOCKER_CONTAINER=true
      - DOCKER_HOST_IP=************   # 优先使用环境变量，默认值作为备选
    # volumes:
    #   - ./config:/app/config
    #   - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "test/check_health.py"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - agentforce-net

networks:
  agentforce-net:
    driver: bridge 
