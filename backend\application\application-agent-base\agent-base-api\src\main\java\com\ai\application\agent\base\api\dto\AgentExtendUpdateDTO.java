package com.ai.application.agent.base.api.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * agent扩展信息
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Data
@Schema(name = "agent扩展信息DTO")
public class AgentExtendUpdateDTO {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer itemId;

    /**
     * 参数名称
     */
    @Schema(description = "参数名称")
    private String itemName;

    /**
     * 参数值
     */
    @Schema(description = "参数值")
    private String itemValue;

    /**
     * 状态 0失效 1有效
     */
    @Schema(description = "状态 0失效 1有效")
    private Integer itemStatus;

    /**
     * 智能体id
     */
    @Schema(description = "智能体id")
    private Integer agentId;
}