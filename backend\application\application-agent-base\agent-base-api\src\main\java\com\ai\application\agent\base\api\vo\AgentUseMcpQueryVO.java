package com.ai.application.agent.base.api.vo;

import com.ai.application.agent.base.api.enums.VersionStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class AgentUseMcpQueryVO extends AgentUseMcpListVO {
    @Schema(description = "agent名称")
    private String agentName;

    @Schema(description = "agent状态")
    private VersionStatusEnum versionStatusEnum;
}
