package com.ai.application.agent.base.api.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * agent版本扩展信息
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@Schema(name = "")
public class AgentVersionExtendVO {
    @Schema(description = "")
    private Integer itemId;

    /**
     * 参数名称
     */
    @Schema(description = "参数名称")
    private String itemName;

    /**
     * 参数值
     */
    @Schema(description = "参数值")
    private String itemValue;

    /**
     * 状态 0失效 1有效
     */
    @Schema(description = "状态 0失效 1有效")
    private Integer itemStatus;

    /**
     * 智能体id
     */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
     * 智能体版本id
     */
    @Schema(description = "智能体版本id")
    private Integer versionId;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}