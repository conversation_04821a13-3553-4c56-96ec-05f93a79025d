package com.ai.application.task.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 任务执行记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-07
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("task_run")
public class TaskRun implements Serializable {
    /**
     * 任务执行id
     */
    @Schema(description = "任务执行id")
    @TableId(type = IdType.AUTO)
    private Long taskRunId;

    /**
     * 执行类型:10-计划执行,20-手动执行,30-重试执行
     */
    @Schema(description = "执行类型:10-计划执行,20-手动执行,30-重试执行")
    private Integer runType;

    /**
     * 执行状态:1-等待,2-执行中,3-成功,4-失败,5-超时,6-中断
     */
    @Schema(description = "执行状态:1-等待,2-执行中,3-成功,4-失败,5-超时,6-中断")
    private Integer runStatus;

    /**
     * 计划执行时间
     */
    @Schema(description = "计划执行时间")
    private Date scheduledTime;

    /**
     * 实际开始时间
     */
    @Schema(description = "实际开始时间")
    private Date actualStartTime;

    /**
     * 实际结束时间
     */
    @Schema(description = "实际结束时间")
    private Date actualEndTime;

    /**
     * 执行时长(毫秒)
     */
    @Schema(description = "执行时长(毫秒)")
    private Integer duration;

    /**
     * 重试次数
     */
    @Schema(description = "重试次数")
    private Integer retryAttempt;

    /**
     * 执行输入参数
     */
    @Schema(description = "执行输入参数")
    private String runInput;

    /**
     * 执行输出结果
     */
    @Schema(description = "执行输出结果")
    private String runOutput;

    /**
     * 执行错误信息
     */
    @Schema(description = "执行错误信息")
    private String runError;

    /**
     * 执行元数据
     */
    @Schema(description = "执行元数据")
    private String runMetadata;

    /**
     * 消耗tokens
     */
    @Schema(description = "消耗tokens")
    private Integer tokensUsed;

    /**
     * 下次重试时间
     */
    @Schema(description = "下次重试时间")
    private Date nextRetryTime;

    /**
     * 任务id
     */
    @Schema(description = "任务id")
    private Integer taskId;

    /**
     * 关联的智能体运行记录id
     */
    @Schema(description = "关联的智能体运行记录id")
    private Long agentRunId;

    /**
     * 触发用户id(手动执行时)
     */
    @Schema(description = "触发用户id(手动执行时)")
    private Integer triggerUserId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}