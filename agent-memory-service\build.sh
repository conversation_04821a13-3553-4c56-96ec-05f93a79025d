#!/bin/bash

# 智能体记忆模块打包脚本
PROJECT="agent-memory"
PORT=6023
IMAGE_NAME="ai-agentforce-agent-memory-service"
IMAGE_TAG=$(date "+%Y%m%d%H%M%S")
REGISTRY="harbor.idc7x24.cn/ai"

echo "开始打包智能体记忆模块"
echo "项目名称: ${PROJECT}"
echo "端口: ${PORT}"
echo "镜像标签: ${IMAGE_TAG}"
echo "镜像仓库: ${REGISTRY}"

# 清理Python缓存
echo "清理Python缓存文件..."
find . -type f -name "*.pyc" -delete
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true

echo "清理完成"

# 生成Docker镜像
echo "开始构建Docker镜像..."
docker build -f Dockerfile -t ${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG} .

# 检查镜像构建是否成功
if [ $? -ne 0 ]; then
    echo "Docker镜像构建失败"
    exit 1
fi

echo "Docker镜像构建完成: ${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"

# 获取当前服务正在运行的容器ID
CONTAINER_ID=$(docker ps -aqf "name=${PROJECT}-service")

# 如果容器正在运行，则停止并删除它
if [ ! -z "$CONTAINER_ID" ]; then
    echo "停止并删除当前运行的容器: $CONTAINER_ID"
    docker stop $CONTAINER_ID
    docker rm $CONTAINER_ID
fi

# 获取当前服务的最新镜像ID
LATEST_IMAGE_ID=$(docker images -q "${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}")

# 获取当前服务的所有历史镜像ID（排除最新构建的镜像）
IMAGE_IDS=$(docker images -q "${REGISTRY}/${IMAGE_NAME}" | grep -v "$LATEST_IMAGE_ID" || true)

# 删除历史镜像
if [ ! -z "$IMAGE_IDS" ]; then
    echo "删除历史镜像: $IMAGE_IDS"
    docker rmi -f $IMAGE_IDS
else
    echo "没有历史镜像需要删除"
fi

echo "准备启动新容器..."
echo "执行命令: docker run -d --name ${PROJECT}-service -p ${PORT}:${PORT} --restart=always --pull=never ${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"

# 启动新容器
docker run -d --name ${PROJECT}-service \
    --network=host \
    -e PYTHONPATH=/app \
    -e PYTHONUNBUFFERED=1 \
    -e DOCKER_CONTAINER=true \
    -e DOCKER_HOST_IP=************ \
    -p ${PORT}:${PORT} \
    --restart=unless-stopped \
    --pull=never \
    ${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}

    # -v $(pwd)/config:/app/config \
    # -v $(pwd)/models:/app/models \
    # -v $(pwd)/logs:/app/logs \

# 检查容器启动是否成功
if [ $? -eq 0 ]; then
    echo "新容器已成功启动"
    echo "服务地址: http://localhost:${PORT}"
    echo "API文档: http://localhost:${PORT}/docs"
    echo "健康检查: http://localhost:${PORT}/agentmemory/v1/health"
    
    # 等待几秒钟让容器完全启动
    echo "等待服务启动..."
    sleep 5
    
    # 检查容器状态
    CONTAINER_STATUS=$(docker ps -f "name=${PROJECT}-service" --format "{{.Status}}")
    if [ ! -z "$CONTAINER_STATUS" ]; then
        echo "容器状态: $CONTAINER_STATUS"
    else
        echo "警告: 容器可能启动失败，请检查日志"
        docker logs ${PROJECT}-service
    fi
else
    echo "容器启动失败"
    exit 1
fi

echo "智能体记忆模块部署完成" 