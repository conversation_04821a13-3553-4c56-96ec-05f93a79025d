package com.ai.application.task.service;

import com.github.pagehelper.PageInfo;
import com.ai.application.task.api.dto.TaskAttachmentDTO;
import com.ai.application.task.api.dto.query.TaskAttachmentQueryDTO;
import com.ai.application.task.api.vo.TaskAttachmentVO;
import java.util.List;
import java.util.Set;

/**
 * 任务附件表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
public interface ITaskAttachmentService {

        /**
         * 分页
         *
         * @param queryDto
         * @return
         */
        PageInfo<TaskAttachmentVO> page(TaskAttachmentQueryDTO queryDto);

        /**
         * 列表
         *
         * @param sort
         * @param queryDto
         * @return
         */
        List<TaskAttachmentVO> list(TaskAttachmentQueryDTO queryDto);

        /**
         * 保存
         *
         * @param dto
         */
        void add(TaskAttachmentDTO dto);

        /**
         * 更新
         *
         * @param dto
         */
        void update(TaskAttachmentDTO dto);

        /**
         * 查看
         *
         * @param id
         * @return
         */
        TaskAttachmentVO get(Integer id);
}