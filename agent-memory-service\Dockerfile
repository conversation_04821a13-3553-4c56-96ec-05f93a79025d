# 使用私有仓库的Ubuntu 22.04 Python 3.12基础镜像
FROM harbor.idc7x24.cn/devops/python:3.12-ubuntu22.04

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 安装系统依赖 (Ubuntu使用apt包管理器)
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libc6-dev \
    linux-libc-dev \
    git \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements文件
COPY requirements.txt .

# 安装Python依赖 (使用清华源加速)
RUN pip install --no-cache-dir -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p logs

# 暴露端口（根据配置文件config.yaml中的端口6023）
EXPOSE 6023

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=60s --retries=3 \
    CMD python test/check_health.py || exit 1

# 运行应用 - 使用start.py启动，可以包含完整的启动前检查
CMD ["python", "start.py"] 