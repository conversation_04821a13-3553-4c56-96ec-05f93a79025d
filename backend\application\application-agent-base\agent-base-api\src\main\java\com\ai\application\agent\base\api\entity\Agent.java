package com.ai.application.agent.base.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 智能体表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("agent")
public class Agent implements Serializable {
        /**
    * 智能体id
    */
    @Schema(description = "智能体id")
    @TableId(type = IdType.AUTO)
    private Integer agentId;

    /**
    * 智能体sn
    */
    @Schema(description = "智能体sn")
    private String agentSn;

    /**
    * 智能体名称
    */
    @Schema(description = "智能体名称")
    private String agentName;

    /**
    * 智能体描述
    */
    @Schema(description = "智能体描述")
    private String agentDesc;

    /**
    * 智能体类型: 10:对话流, 20:工作流, 30:master
    */
    @Schema(description = "智能体类型: 10:对话流, 20:工作流, 30:master")
    private Integer agentType;

    /**
    * 智能体元信息:logo,icon,创建人名等
    */
    @Schema(description = "智能体元信息:logo,icon,创建人名等")
    private String agentMetadata;

    /**
    * 状态 0停用 1开发 5发布
    */
    @Schema(description = "状态 0停用 1开发 5发布")
    private Integer agentStatus;

    /**
    * 智能体启用版本id
    */
    @Schema(description = "智能体启用版本id")
    private Integer versionId;

    /**
    * 租户id
    */
    @Schema(description = "租户id")
    private Integer tenantId;

    /**
    * 创建人id
    */
    @Schema(description = "创建人id")
    private Integer userId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}