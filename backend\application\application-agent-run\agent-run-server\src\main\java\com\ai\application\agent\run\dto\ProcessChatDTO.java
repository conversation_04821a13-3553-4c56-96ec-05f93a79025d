package com.ai.application.agent.run.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 流程聊天 DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(name = "ProcessChatDTO")
public class ProcessChatDTO {
    
    /**
     * 系统提示词
     */
    @Schema(description = "系统提示词")
    private String sysPrompt;
    
    /**
     * 用户提示词
     */
    @Schema(description = "用户提示词")
    private String prompt;
    
    /**
     * 温度参数
     */
    @Schema(description = "温度参数")
    private String temperature;
    
    /**
     * 模型编号
     */
    @Schema(description = "模型编号")
    private String model;
    
    /**
     * 超时时间(秒) 不传则使用默认的超时时间
     */
    @Schema(description = "超时时间(秒)")
    private Integer timeoutInSeconds;
}
