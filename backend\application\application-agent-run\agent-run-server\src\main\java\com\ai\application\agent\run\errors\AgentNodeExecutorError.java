package com.ai.application.agent.run.errors;

import com.ai.framework.core.enums.IErrorCode;

public enum AgentNodeExecutorError implements IErrorCode {

    AGENT_NODE_MISSING_INPUT_PARAMETERS(51000, "Agent节点缺少inputParameters配置"),

    AGENT_NODE_EXECUTOR_ERROR (51001,"agentSn 不能为空"),
    AGENT_TYPE_IS_NULL (51002,"agentType 不能为空"),
    AGENT_MSG_CONTENT_IS_NULL (51003,"msgContent 不能为空"),
    AGENT_RESPONSE_IS_NULL (51005,"智能体返回结果为空")
    ;

    private Integer code;
    private String message;

    AgentNodeExecutorError(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getMessage() {
        return this.message;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }
}
