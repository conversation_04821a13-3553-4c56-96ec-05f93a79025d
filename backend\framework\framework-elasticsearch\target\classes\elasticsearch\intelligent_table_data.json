{"properties": {"lineSn": {"type": "keyword"}, "intelligentTableSn": {"type": "keyword"}, "creator": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 60}}}, "updater": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 60}}}, "createTime": {"type": "date"}, "updateTime": {"type": "date"}, "md5": {"type": "keyword"}, "cell": {"type": "nested", "properties": {"fieldName": {"type": "keyword"}, "dataType": {"type": "keyword"}, "filetype": {"type": "keyword"}, "inventorySn": {"type": "keyword"}, "inventoryName": {"type": "keyword"}, "fieldSn": {"type": "keyword"}, "fileName": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 60}}}, "data": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 300}}}, "inputType": {"type": "integer"}, "key": {"type": "integer"}}}}}