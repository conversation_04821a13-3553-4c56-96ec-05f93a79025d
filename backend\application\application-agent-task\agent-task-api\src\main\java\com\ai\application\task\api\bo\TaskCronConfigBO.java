package com.ai.application.task.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class TaskCronConfigBO {
    @Schema(description = "执行周期: 10：仅一次，20：每天重复，30：每周重复")
    private Integer executeCycle = 10;

    @Schema(description = "执行周(执行周期选【每周重复】时用)")
    private List<Integer> executeWeeks;

    @Schema(description = "消息模板id")
    private Integer notificationTemplateId;
}
