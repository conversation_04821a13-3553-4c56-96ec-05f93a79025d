package com.ai.application.process.executor.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.ai.application.process.entity.ShortTermMemory;
import com.ai.application.process.enums.ProcessErrorCodeEnum;
import com.ai.application.process.executor.BaseExecutor;
import com.ai.application.process.executor.ExecutionContext;
import com.ai.application.process.mapper.ShortTermMemoryBucketMapper;
import com.ai.application.process.mapper.ShortTermMemoryMapper;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @version 1.0.0
 */
@Service
@Slf4j
public class ShortTermMemorySaveExecutor implements BaseExecutor {

    @Resource
    private ShortTermMemoryMapper shortTermMemoryMapper;

    @Resource
    private ShortTermMemoryBucketMapper shortTermMemoryBucketMapper;

    @Override
    public Map<String, Object> execute(ExecutionContext executionContext) {
        // 获取参数并校验
        var processId = executionContext.getProcessId();
        var sessionSn = executionContext.getParameterAsString("sessionSn");
        var memoryContent = executionContext.getParameterAsString("memoryContent");
        var memoryContents = executionContext.getParameter("memoryContents");
        var bucketSn = executionContext.getParameterAsString("bucketSn");
        log.debug("ShortTermMemory Save sessionSn:{},memoryContent:{},memoryContents:{},bucketSn:{},processId:{}", sessionSn, memoryContent, memoryContents, bucketSn, processId);
        if (StringUtils.isBlank(sessionSn)) {
            // 兼容单组件调试可能无sessionSn问题
            return Map.of("output", "SUCCESS");
        }
        if (StringUtils.isBlank(memoryContent) && memoryContents == null) {
            log.error("ShortTermMemory Save fail sessionSn:{},memoryContent:{},memoryContents:{},bucketSn:{},processId:{}", sessionSn, memoryContent, memoryContents, bucketSn, processId);
            throw new ServiceException(ProcessErrorCodeEnum.SHORT_TERM_MEMORY_EXTRACT_FAIL);
        }

        if (StringUtils.isNotBlank(memoryContent)) {
            this.addMemory(sessionSn, processId, memoryContent, bucketSn);
        } else {
            List<MemoryContent> memoryContentList = JsonUtils.convertValue(memoryContents, new TypeReference<>() {});
            for (var memoryContentDto : memoryContentList) {
                this.addMemory(sessionSn, processId, memoryContentDto.memoryContent, memoryContentDto.bucketSn);
            }
        }
        // 调用方不需要返回结果 默认返回个SUCCESS
        return Map.of("output", "SUCCESS");
    }

    private void addMemory(String sessionSn, String processId, String memoryContent, String bucketSn) {
        if (StringUtils.isNotBlank(bucketSn) && !"default".equals(bucketSn)) {
            // 校验短期记忆存储库是否被删除
            var bucket = shortTermMemoryBucketMapper.queryBySnAndProcessId(bucketSn, processId);
            if (bucket == null || bucket.getDeleted()) {
                throw new ServiceException(ProcessErrorCodeEnum.SHORT_TERM_MEMORY_BUCKET_INVALID);
            }
        }
        var entity = new ShortTermMemory();
        entity.setMemorySn(UUID.randomUUID().toString());
        entity.setSessionSn(sessionSn);
        entity.setMemoryContent(memoryContent);
        var now = new Timestamp(System.currentTimeMillis());
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        entity.setDeleted(false);
        entity.setProcessId(processId);
        entity.setBucketSn(StringUtils.isBlank(bucketSn) ? "default" : bucketSn);
        shortTermMemoryMapper.insert(entity);
    }

    @Data
    private static class MemoryContent {
        private String memoryContent;

        private String bucketSn;
    }

    @Override
    public String getId() {
        return "SHORT_TERM_MEMORY_SAVE";
    }
}
