package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.dto.AgentStatDTO;
import com.ai.application.agent.base.api.vo.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface IAgentStatService {
    @Transactional(readOnly = true)
    List<LastSessionAgentVO> queryLastSessionAgent();

    @Transactional(rollbackFor = Exception.class)
    void removeSessionByAgentSn(String agentSn);

    AgentStatResultVO statCountMetrics(AgentStatDTO dto);

    List<AgentTokensStatisticsDetailVO> queryTokensFrequentAgent(AgentStatDTO dto);

    List<AgentTokensStatisticsDetailVO> queryLastTokensAgent(AgentStatDTO dto);

    List<AgentStatVO> queryLastCreateAgent();

    List<AgentStatVO> queryFrequentAgent();

    /**
     * 使用统计
     *
     * @return
     */
    AgentUseTotalVO agentUserTotal(Integer day);
}
