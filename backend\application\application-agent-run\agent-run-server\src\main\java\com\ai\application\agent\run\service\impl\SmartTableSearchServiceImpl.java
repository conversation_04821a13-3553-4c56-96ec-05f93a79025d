package com.ai.application.agent.run.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ai.application.agent.run.dto.SmartTableSearchRequestDTO;
import com.ai.application.agent.run.dto.SmartTableSearchResultDTO;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.service.ISmartTableSearchService;
import com.ai.application.knowledge.table.dto.TableDataListDto;
import com.ai.application.knowledge.table.feign.ITableFeignClient;
import com.ai.application.knowledge.table.vo.TableDataListVo;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 智能表格检索服务实现
 */
@Slf4j
@Service
public class SmartTableSearchServiceImpl implements ISmartTableSearchService {

    private final ITableFeignClient tableFeignClient;

    public SmartTableSearchServiceImpl(ITableFeignClient tableFeignClient) {
        this.tableFeignClient = tableFeignClient;
    }

    @Override
    public ResultVo<SmartTableSearchResultDTO> executeSmartTableSearch(SmartTableSearchRequestDTO request, String authorization) {
        log.info("SmartTableSearchService executeSmartTableSearch start, request: {}", JsonUtils.toJsonString(request));

        try {
            // 参数校验
            validateSmartTableSearchRequest(request);

            // 构建查询条件
            TableDataListDto queryDto = buildTableDataListDto(request);

            // 执行数据检索
            ResultVo<List<TableDataListVo>> result = tableFeignClient.dataQuery(queryDto);

            if (result.getCode() != 0) {
                throw new ServiceException(result.getCode(), result.getMessage());
            }

            // 转换结果格式
            List<String> outputList = convertToOutputFormat(result.getData(), request);

            // 构建返回结果
            SmartTableSearchResultDTO searchResult = SmartTableSearchResultDTO.builder()
                    .success(true)
                    .output(outputList)
                    .intelligentTableSn(request.getIntelligentTableSn())
                    .resultCount(outputList.size())
                    .build();

            log.info("SmartTableSearchService executeSmartTableSearch success, result count: {}", outputList.size());
            return ResultVo.data(searchResult);

        } catch (Exception e) {
            log.error("SmartTableSearchService executeSmartTableSearch error", e);
            SmartTableSearchResultDTO errorResult = SmartTableSearchResultDTO.builder()
                    .success(false)
                    .errorMessage(e.getMessage())
                    .intelligentTableSn(request.getIntelligentTableSn())
                    .resultCount(0)
                    .output(Collections.emptyList())
                    .build();
            return ResultVo.data(errorResult);
        }
    }

    @Override
    public boolean validateSearchConditions(SmartTableSearchRequestDTO request) {
        if (request == null) {
            return false;
        }

        // 至少需要有一种检索条件
        boolean hasRuleConditions = CollectionUtils.isNotEmpty(request.getRuleConditions());
        boolean hasSemanticConditions = CollectionUtils.isNotEmpty(request.getSemanticConditions());

        if (!hasRuleConditions && !hasSemanticConditions) {
            return false;
        }

        // 如果指定了部分输出，必须提供字段列表
        if (request.getOutputField() != null && request.getOutputField() == 1) {
            return CollectionUtils.isNotEmpty(request.getDefinitionSns());
        }

        return true;
    }

    @Override
    public Object buildQueryConditions(SmartTableSearchRequestDTO request) {
        return buildTableDataListDto(request);
    }

    /**
     * 校验智能表格检索请求
     */
    private void validateSmartTableSearchRequest(SmartTableSearchRequestDTO request) {
        if (StringUtils.isBlank(request.getIntelligentTableSn())) {
            throw new ServiceException(ExecutorError.SMART_TABLE_SN_IS_BLANK);
        }

        if (!validateSearchConditions(request)) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "检索条件无效");
        }
    }

    /**
     * 构建TableDataListDto查询对象
     */
    private TableDataListDto buildTableDataListDto(SmartTableSearchRequestDTO request) {
        TableDataListDto queryDto = new TableDataListDto();
        queryDto.setTableSn(request.getIntelligentTableSn());

        // 设置最大行数限制
        if (request.getMaxOutputRows() != null && request.getMaxOutputRows() > 0) {
            queryDto.setMaxRow(request.getMaxOutputRows());
        }

        // 构建过滤条件
        List<TableDataListDto.Filter> filters = new ArrayList<>();

        // 处理规则检索条件
        if (CollectionUtils.isNotEmpty(request.getRuleConditions())) {
            for (SmartTableSearchRequestDTO.RuleCondition ruleCondition : request.getRuleConditions()) {
                TableDataListDto.Filter filter = convertRuleConditionToFilter(ruleCondition);
                if (filter != null) {
                    filters.add(filter);
                }
            }
        }

        // 处理语义检索条件 - 注意：TableDataListDto可能不直接支持语义检索
        // 这里我们将语义条件转换为模糊匹配条件
        if (CollectionUtils.isNotEmpty(request.getSemanticConditions())) {
            for (SmartTableSearchRequestDTO.SemanticCondition semanticCondition : request.getSemanticConditions()) {
                List<TableDataListDto.Filter> semanticFilters = convertSemanticConditionToFilters(semanticCondition);
                filters.addAll(semanticFilters);
            }
        }

        queryDto.setFilter(filters);
        return queryDto;
    }

    /**
     * 转换规则条件为过滤器
     */
    private TableDataListDto.Filter convertRuleConditionToFilter(SmartTableSearchRequestDTO.RuleCondition ruleCondition) {
        if (ruleCondition == null || StringUtils.isBlank(ruleCondition.getDefinitionSn()) || ruleCondition.getValue() == null) {
            return null;
        }

        TableDataListDto.Filter filter = new TableDataListDto.Filter();
        filter.setFieldSn(ruleCondition.getDefinitionSn());
        filter.setValue(ruleCondition.getValue().toString());

        // 根据条件操作符设置表达式
        String condition = ruleCondition.getCondition();
        switch (condition) {
            case "==":
                filter.setExpression("=");
                break;
            case "!=":
                filter.setExpression("!=");
                break;
            case "contains":
            case "!contains":
                filter.setExpression("like");
                if ("!contains".equals(condition)) {
                    // 对于不包含的情况，可能需要特殊处理
                    filter.setExpression("not like");
                }
                break;
            case ">":
                filter.setExpression(">");
                break;
            case "<":
                filter.setExpression("<");
                break;
            case ">=":
                filter.setExpression(">=");
                break;
            case "<=":
                filter.setExpression("<=");
                break;
            default:
                filter.setExpression("like");
                break;
        }

        return filter;
    }

    /**
     * 转换语义条件为过滤器列表
     */
    private List<TableDataListDto.Filter> convertSemanticConditionToFilters(SmartTableSearchRequestDTO.SemanticCondition semanticCondition) {
        List<TableDataListDto.Filter> filters = new ArrayList<>();

        if (semanticCondition == null || CollectionUtils.isEmpty(semanticCondition.getDefinitionSn()) || StringUtils.isBlank(semanticCondition.getValue())) {
            return filters;
        }

        // 为每个字段创建一个模糊匹配过滤器
        for (String fieldSn : semanticCondition.getDefinitionSn()) {
            TableDataListDto.Filter filter = new TableDataListDto.Filter();
            filter.setFieldSn(fieldSn);
            filter.setExpression("like");
            filter.setValue(semanticCondition.getValue());
            filters.add(filter);
        }

        return filters;
    }

    /**
     * 转换结果为输出格式
     */
    private List<String> convertToOutputFormat(List<TableDataListVo> dataList, SmartTableSearchRequestDTO request) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }

        return dataList.stream().map(data -> {
            JSONObject entries = new JSONObject();

            // 处理数据字段
            if (data.getFieldSnMap() != null) {

                for(Map.Entry<String, TableDataListVo.Definition> entry : data.getFieldSnMap().entrySet()) {
                    String keyName = entry.getKey();
                    TableDataListVo.Definition definition = entry.getValue();

                    if (definition == null) continue;

                    // 根据字段类型决定取哪个值
                    if ("FILE".equalsIgnoreCase(definition.getFieldType())) {
                        entries.put(keyName, definition.getFileName());
                    } else {
                        entries.put(keyName, definition.getData());
                    }
                }
            }

            return JSONUtil.toJsonStr(entries);
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
