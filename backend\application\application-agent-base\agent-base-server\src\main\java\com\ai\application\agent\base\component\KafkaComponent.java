package com.ai.application.agent.base.component;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class KafkaComponent {
    @KafkaListener(topics = "topic-test", groupId = "topic-name")
    public void listen(ConsumerRecord<String, Object> record, Acknowledgment ack) {
        try {
            log.info("received: {}", record.value());
            log.info(
                    "topic ={},partition={},offset={},customer={},value={}",
                    record.topic(),
                    record.partition(),
                    record.offset(),
                    record.key(),
                    record.value());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            // 手动提交
            ack.acknowledge();
        }
    }
}