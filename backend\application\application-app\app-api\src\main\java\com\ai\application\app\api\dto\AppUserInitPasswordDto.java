package com.ai.application.app.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
@Schema(name = "应用用户表初始化DTO")
public class AppUserInitPasswordDto {

    /**
     * 密码
     */
    @NotEmpty(message="密码不能为空")
    @Length(min = 8,message = "密码长度不能小于8个字符")
    String password;

    /**
     * 确认密码
     */
    @NotEmpty(message="确认密码不能为空")
    @Length(min = 8,message = "密码长度不能小于8个字符")
    String confirmPassword;

}