package com.ai.application.agent.run.executor.agent;

import com.ai.application.agent.base.api.dto.AgentChatDTO;
import com.ai.application.agent.base.api.feign.IAgentClient;
import com.ai.application.agent.base.api.vo.AgentChatVO;
import com.ai.application.agent.run.errors.AgentNodeExecutorError;
import com.ai.application.agent.run.executor.AgentExecutionContext;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 表单型智能体执行器
 */
@Slf4j
@Component
public class FormAgentExecutor extends BaseAgentExecutor {

    @Autowired
    private IAgentClient agentClient;

    @Override
    public Map<String, Object> execute(AgentExecutionContext executionContext) {
        String processInstanceId = executionContext.getProcessInstanceId();
        String agentSn = executionContext.getParameterAsString("agentSn");
        String agentType = executionContext.getParameterAsString("agentType");
        String sessionSn = executionContext.getParameterAsString("sessionSn");
        String msgContent = executionContext.getParameterAsString("msgContent");
        String docFileSn = executionContext.getParameterAsString("doc");
        String imageFileSn = executionContext.getParameterAsString("image");
        String auth = executionContext.getAuthorization();
        
        // 会话管理
        boolean contextMemorised = Objects.equals("true", executionContext.getParameterAsString("contextMemorised"));
        if (sessionSn == null || !contextMemorised) {
            String modelSn = executionContext.getParameterAsString("model");
            String prompt = executionContext.getParameterAsString("sysPrompt");
            String temperature = executionContext.getParameterAsString("temperature");
            
            if (StringUtils.isNotBlank(prompt) || StringUtils.isNotBlank(modelSn) || StringUtils.isNotBlank(temperature)) {
                sessionSn = newDebugSessionSn(auth, agentSn, modelSn, prompt, 
                    temperature != null ? Double.parseDouble(temperature) : 0.6);
            } else {
                sessionSn = newSessionSn(auth, agentSn);
            }
        }

        // 构建智能体请求（表单智能体可能需要附件）
        AgentChatDTO chatDto = AgentChatDTO.builder()
                .agentSn(agentSn)
                .agentType(agentType)
                .msgContent(msgContent)
                .msgType("text")
                .delayInMs(20L)
                .fromCode("Workflow")
                .processId(executionContext.getProcessId())
                .sessionSn(sessionSn)
                .debug(executionContext.isDebugRun())
                .build();

        log.info("[{}] {} agent form request body {}", processInstanceId, agentType, JsonUtils.toJsonString(chatDto));

        // 调用智能体服务
        ResultVo<AgentChatVO> result = agentClient.chat(chatDto);

        if (result == null || !Objects.equals(result.getCode(), 0)) {
            String errorMsg = result != null ? result.getMessage() : "调用文档智能体失败";
            log.error("[{}] {} agent doc error {}", processInstanceId, agentType, errorMsg);
            throw new ServiceException(51004,"调用文档智能体失败: " + errorMsg);
        }

        AgentChatVO agentChatVo = result.getData();
        if (agentChatVo == null) {
            throw new ServiceException(AgentNodeExecutorError.AGENT_RESPONSE_IS_NULL);
        }
        // 解析返回结果
        String message = parseMsgContent(agentChatVo);
        
        // 表单智能体特有的逻辑：解析表单数据和收集状态
        boolean isAllCollected = parseFormCollectionStatus(agentChatVo);
        Map<String, Object> collectedData = parseFormData(agentChatVo);
        
        log.info("[{}] {} agent form response, message: {}, isAllCollected: {}", 
                processInstanceId, agentType, message, isAllCollected);

        // 构建返回结果
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("message", message);
        resultMap.put("sessionSn", sessionSn);
        resultMap.put("success", agentChatVo.getSuccess());
        resultMap.put("isAllCollected", isAllCollected);
        resultMap.put("collectedData", collectedData);
        
        // 根据表单收集状态确定下一步流向
        String target = isAllCollected ? "collected" : "collecting";
        resultMap.put("target", target);
        
        return resultMap;
    }

    /**
     * 解析消息内容
     */
    private String parseMsgContent(AgentChatVO agentChatVo) {
        if (agentChatVo.getContent() != null) {
            JsonNode answerNode = agentChatVo.getContent().get("answer");
            if (answerNode != null && answerNode.isTextual()) {
                return answerNode.asText();
            }
        }
        
        return StringUtils.defaultIfBlank(agentChatVo.getReply(), "");
    }

    /**
     * 解析表单收集状态
     */
    private boolean parseFormCollectionStatus(AgentChatVO agentChatVo) {
        if (agentChatVo.getContent() != null) {
            JsonNode statusNode = agentChatVo.getContent().get("isAllRequiredCollected");
            if (statusNode != null && statusNode.isBoolean()) {
                return statusNode.asBoolean();
            }
        }
        return false;
    }

    /**
     * 解析表单数据
     */
    private Map<String, Object> parseFormData(AgentChatVO agentChatVo) {
        Map<String, Object> formData = new HashMap<>();
        
        if (agentChatVo.getContent() != null) {
            JsonNode formNode = agentChatVo.getContent().get("form");
            if (formNode != null && formNode.isArray()) {
                for (JsonNode item : formNode) {
                    String name = item.has("name") ? item.get("name").asText() : null;
                    String value = item.has("value") ? item.get("value").asText() : null;
                    if (name != null && value != null) {
                        formData.put(name, value);
                    }
                }
            }
        }
        
        return formData;
    }

    @Override
    public String getAgentType() {
        return "form";
    }
}
