package com.ai.application.base.log.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class MailConfigDTO {
    @Schema(description = "邮件服务器地址")
    @NotBlank(message = "邮件服务器地址不能为空")
    @Length(max = 100, message = "邮件服务器地址长度不能超过100")
    private String host;

    @Schema(description = "邮件服务器端口")
    @NotNull(message = "邮件服务器端口不能为空")
    private Integer port;

    @Schema(description = "邮箱地址")
    @NotBlank(message = "邮箱地址不能为空")
    @Length(max = 50, message = "邮箱地址长度不能超过50")
    private String email;

    @Schema(description = "发件人显示名")
    @NotBlank(message = "发件人显示名不能为空")
    @Length(max = 20, message = "发件人显示名长度不能超过20")
    private String showName;

    @Schema(description = "邮件服务器用户名")
    @NotBlank(message = "邮件服务器用户名不能为空")
    @Length(max = 20, message = "邮件服务器用户名长度不能超过20")
    private String username;

    @Schema(description = "邮件服务器密码")
    @NotBlank(message = "邮件服务器密码不能为空")
    @Length(max = 20, message = "邮件服务器密码长度不能超过20")
    private String password;



}
