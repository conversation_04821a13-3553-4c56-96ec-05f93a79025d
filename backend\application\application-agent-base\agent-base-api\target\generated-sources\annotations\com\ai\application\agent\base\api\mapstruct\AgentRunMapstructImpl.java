package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.dto.AgentRunDTO;
import com.ai.application.agent.base.api.entity.AgentRun;
import com.ai.application.agent.base.api.vo.AgentRunVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-12T18:39:57+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentRunMapstructImpl implements AgentRunMapstruct {

    @Override
    public AgentRun toEntity(AgentRunDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentRun agentRun = new AgentRun();

        agentRun.setRunId( dto.getRunId() );
        agentRun.setRunType( dto.getRunType() );
        agentRun.setRunMode( dto.getRunMode() );
        agentRun.setRunStatus( dto.getRunStatus() );
        agentRun.setRunInput( dto.getRunInput() );
        agentRun.setRunOutput( dto.getRunOutput() );
        agentRun.setRunMetadata( dto.getRunMetadata() );
        agentRun.setRunDuration( dto.getRunDuration() );
        agentRun.setRunTokens( dto.getRunTokens() );
        agentRun.setRunStartTime( dto.getRunStartTime() );
        agentRun.setRunEndTime( dto.getRunEndTime() );
        agentRun.setAgentId( dto.getAgentId() );
        agentRun.setVersionId( dto.getVersionId() );
        agentRun.setTenantId( dto.getTenantId() );
        agentRun.setUserId( dto.getUserId() );
        agentRun.setTokenId( dto.getTokenId() );
        agentRun.setRequestId( dto.getRequestId() );
        agentRun.setSessionId( dto.getSessionId() );
        agentRun.setCreateTime( dto.getCreateTime() );
        agentRun.setUpdateTime( dto.getUpdateTime() );

        return agentRun;
    }

    @Override
    public List<AgentRun> toEntityList(List<AgentRunDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<AgentRun> list = new ArrayList<AgentRun>( dtolist.size() );
        for ( AgentRunDTO agentRunDTO : dtolist ) {
            list.add( toEntity( agentRunDTO ) );
        }

        return list;
    }

    @Override
    public AgentRunVO toVo(AgentRun entity) {
        if ( entity == null ) {
            return null;
        }

        AgentRunVO agentRunVO = new AgentRunVO();

        agentRunVO.setRunId( entity.getRunId() );
        agentRunVO.setRunType( entity.getRunType() );
        agentRunVO.setRunMode( entity.getRunMode() );
        agentRunVO.setRunStatus( entity.getRunStatus() );
        agentRunVO.setRunInput( entity.getRunInput() );
        agentRunVO.setRunOutput( entity.getRunOutput() );
        agentRunVO.setRunMetadata( entity.getRunMetadata() );
        agentRunVO.setRunDuration( entity.getRunDuration() );
        agentRunVO.setRunTokens( entity.getRunTokens() );
        agentRunVO.setRunStartTime( entity.getRunStartTime() );
        agentRunVO.setRunEndTime( entity.getRunEndTime() );
        agentRunVO.setAgentId( entity.getAgentId() );
        agentRunVO.setVersionId( entity.getVersionId() );
        agentRunVO.setTenantId( entity.getTenantId() );
        agentRunVO.setUserId( entity.getUserId() );
        agentRunVO.setTokenId( entity.getTokenId() );
        agentRunVO.setRequestId( entity.getRequestId() );
        agentRunVO.setSessionId( entity.getSessionId() );
        agentRunVO.setCreateTime( entity.getCreateTime() );
        agentRunVO.setUpdateTime( entity.getUpdateTime() );

        return agentRunVO;
    }

    @Override
    public List<AgentRunVO> toVoList(List<AgentRun> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AgentRunVO> list = new ArrayList<AgentRunVO>( entities.size() );
        for ( AgentRun agentRun : entities ) {
            list.add( toVo( agentRun ) );
        }

        return list;
    }
}
