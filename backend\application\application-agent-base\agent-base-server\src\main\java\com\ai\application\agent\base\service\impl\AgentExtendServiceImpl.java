package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.dto.AgentExtendAddDTO;
import com.ai.application.agent.base.api.dto.AgentVersionExtendDTO;
import com.ai.application.agent.base.api.enums.AgentExtendNameEnum;
import com.ai.application.agent.base.mapper.AgentExtendMapper;
import com.ai.application.agent.base.api.entity.AgentExtend;
import com.ai.application.agent.base.api.dto.AgentExtendUpdateDTO;
import com.ai.application.agent.base.api.mapstruct.AgentExtendMapstruct;
import com.ai.application.agent.base.service.IAgentExtendService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.Date;

/**
 * agent扩展信息-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Service
public class AgentExtendServiceImpl implements IAgentExtendService {

    @Resource
    private AgentExtendMapper agentExtendMapper;

    @Resource
    private AgentExtendMapstruct agentExtendMapstruct;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(AgentExtendAddDTO dto) {
        AgentExtend entity = agentExtendMapstruct.toEntityAdd(dto);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());
        entity.setItemStatus(1);
        agentExtendMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AgentExtendUpdateDTO dto) {
        BusinessAssertUtil.notNull(dto.getItemId(), "ItemId不能为空");

        AgentExtend entity = agentExtendMapper.selectById(dto.getItemId());
        BusinessAssertUtil.notNull(entity, "找不到ItemId为 " + dto.getItemId() + " 的记录");

        AgentExtend entityList = agentExtendMapstruct.toEntityUpdate(dto);
        entityList.setUpdateTime(new Date());
        agentExtendMapper.updateById(entityList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateByStatus(Integer agentId, AgentExtendNameEnum agentExtendNameEnum, Integer status) {
        agentExtendMapper.updateItemStatus(agentId,  agentExtendNameEnum.getCode(), status);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void setItem(Integer agentId, String itemName, String itemValue) {
        for (AgentExtend agentExtend : agentExtendMapper.findByAgentId(agentId)) {
            if (agentExtend.getItemName().equals(itemName)) {
                agentExtend.setItemValue(itemValue);
                agentExtendMapper.updateById(agentExtend);
            } else {
                AgentExtendAddDTO agentExtendAddDTO = new AgentExtendAddDTO();
                agentExtendAddDTO.setAgentId(agentId);
                agentExtendAddDTO.setItemName(itemName);
                agentExtendAddDTO.setItemValue(itemValue);
                this.add(agentExtendAddDTO);
            }
        }
    }
}