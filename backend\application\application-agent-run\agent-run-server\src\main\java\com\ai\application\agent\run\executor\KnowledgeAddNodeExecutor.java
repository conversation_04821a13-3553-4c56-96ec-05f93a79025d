package com.ai.application.agent.run.executor;

import cn.hutool.json.JSONUtil;
import com.ai.application.agent.run.dto.KnowledgeEmbeddingParamsDTO;
import com.ai.application.agent.run.dto.KnowledgeEmbeddingResultDTO;
import com.ai.application.agent.run.dto.KnowledgeFileEmbeddingDTO;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.base.file.api.dto.DocFileDto;
import com.ai.application.base.file.api.dto.FileInfoDto;
import com.ai.application.base.file.api.dto.FileUploadGtIdDto;
import com.ai.application.base.file.api.dto.FileUploadNoticeDto;
import com.ai.application.base.file.api.feign.IFileFeignClient;
import com.ai.application.base.file.api.vo.FileUpGtIdVo;
import com.ai.application.knowledge.table.dto.DeleteBySnFeignDto;
import com.ai.application.knowledge.table.dto.EmbeddingFeignDto;
import com.ai.application.knowledge.table.dto.FileByIdFeignDto;
import com.ai.application.knowledge.table.feign.DocFeignClient;
import com.ai.application.knowledge.table.vo.BaseDetailVo;
import com.ai.application.knowledge.table.vo.FileDetailVo;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import com.ai.framework.workflow.excutor.NodeExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 知识添加节点执行器
 * 用于在工作流中添加知识到知识库
 */
@Slf4j
@Component
public class KnowledgeAddNodeExecutor implements NodeExecutor {

    private DocFeignClient docFeignClient;

    private IFileFeignClient fileFeignClient;

    @Override
    public void execute(WorkflowContext context) {
        String nodeKey = context.getCurrentNodeKey();
        NodeContext nodeCtx = context.getNodeContexts().get(nodeKey);
        Map<String, Object> nodeDef = nodeCtx.getNodeDefinition();

        log.info("KnowledgeAddNodeExecutor execute start, nodeKey: {}, nodeDef: {}", nodeKey, JsonUtils.toJsonString(nodeDef));

        try {
            // 设置节点状态为运行中
            nodeCtx.setStatus(NodeStatus.RUNNING);

            // 初始化节点输出
            if (nodeCtx.getOutput() == null) {
                nodeCtx.setOutput(new HashMap<>());
            }

            // 从节点定义中获取输入参数
            Map<String, Object> inputParameters = (Map<String, Object>) nodeDef.get("inputParameters");
            if (inputParameters == null) {
                throw new ServiceException(ExecutorError.NODE_DEFINITION_IS_NULL);
            }

            // 获取参数值，支持变量替换
            String knowledgeInventory = getParameterValue(inputParameters, "knowledgeInventorySn", context);
            String continueWhenErr = getParameterValue(inputParameters, "continueWhenErr", context);
            String knowledgeType = getParameterValue(inputParameters, "knowledgeType", context);
            Object inputValue = getParameterObject(inputParameters, "input", context);
            Object deepParseParam = inputParameters.get("deepParse");
            Object separatorContent = inputParameters.get("separatorContent");

            // 参数校验
            if (StringUtils.isBlank(knowledgeInventory)) {
                throw new ServiceException(ExecutorError.KNOWLEDGE_INVENTORY_SN_IS_NULL);
            }
            if (inputValue == null) {
                throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "input 不能为空");
            }

            // 处理输入数据
            List<FileInfoDto> files = processInputData(inputValue, context);
            if (files.isEmpty()) {
                throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "没有有效的输入文件");
            }

            // 构建嵌入参数
            KnowledgeEmbeddingParamsDTO embeddingParams = buildEmbeddingParams(
                    knowledgeInventory, continueWhenErr, knowledgeType, deepParseParam, separatorContent, inputParameters, context);

            // 执行文件嵌入
            KnowledgeEmbeddingResultDTO embeddingResult = executeFileEmbedding(files, embeddingParams, context);

            // 构建输出结果
            Map<String, Object> result = buildOutputResult(embeddingResult, files.size());

            // 将结果写入输出参数
            writeOutputParameters(nodeDef, context, result);

            // 设置节点输出
            nodeCtx.getOutput().putAll(result);

            // 设置节点状态为成功
            nodeCtx.setStatus(NodeStatus.SUCCESS);
            nodeCtx.setEndTime(java.time.LocalDateTime.now());

            log.info("KnowledgeAddNodeExecutor execute success, result: {}", JsonUtils.toJsonString(result));

        } catch (Exception e) {
            log.error("KnowledgeAddNodeExecutor execute error", e);
            nodeCtx.setStatus(NodeStatus.FAILED);
            nodeCtx.setErrorMsg("知识添加执行失败: " + e.getMessage());
            nodeCtx.setEndTime(java.time.LocalDateTime.now());
            throw e;
        }
    }

    /**
     * 处理输入数据，转换为文件信息列表
     */
    private List<FileInfoDto> processInputData(Object inputValue, WorkflowContext context) {
        List<FileInfoDto> files = new ArrayList<>();
        String tenantName = getTenantName(context);
        String processInstanceId = context.getWorkflowInstanceId() != null ? 
                context.getWorkflowInstanceId().toString() : "unknown";

        if (inputValue instanceof List) {
            // 数组类型输入
            List<?> inputList = (List<?>) inputValue;
            for (Object item : inputList) {
                if (item != null) {
                    Optional<FileInfoDto> fileInfo = createFileInfoFromInput(
                            tenantName, processInstanceId, item.toString());
                    fileInfo.ifPresent(files::add);
                }
            }
        } else {
            // 单个输入
            Optional<FileInfoDto> fileInfo = createFileInfoFromInput(
                    tenantName, processInstanceId, inputValue.toString());
            fileInfo.ifPresent(files::add);
        }

        return files;
    }

    /**
     * 从输入内容创建文件信息
     */
    private Optional<FileInfoDto> createFileInfoFromInput(String tenantName, String processInstanceId, String inputContent) {
        if (StringUtils.isBlank(inputContent)) {
            log.info("KnowledgeAddNodeExecutor empty input, processInstanceId: [{}]", processInstanceId);
            return Optional.empty();
        }

        if (StringUtils.isBlank(tenantName)) {
            tenantName = "XBOT";
        }

        FileInfoDto fileInfo = new FileInfoDto();
        fileInfo.setFormatFileSn(inputContent);

        // 判断是否为文件类型（包含::分隔符）
        if (inputContent.contains("::")) {
            // 文件类型：查询已上传的文件
            String fileSn = inputContent.split("::")[0];
            try {
                ResultVo<DocFileDto> result = fileFeignClient.getDocFileByFileSn(fileSn);

                if (result.getCode() == 0 && result.getData() != null && !Objects.isNull(result.getData())) {
                    DocFileDto processFile = result.getData();
                    fileInfo.setFileName(processFile.getFileName());
                    fileInfo.setFileId(processFile.getFileId());
                    fileInfo.setFileSn(processFile.getFileSn());
                } else {
                    log.warn("Process file not found for fileSn: {}", fileSn);
                    return Optional.empty();
                }
            } catch (Exception e) {
                log.error("Error querying process file: {}", e.getMessage());
                return Optional.empty();
            }
        } else {
            // 文本类型：创建文本文件
            String filename = String.format("%s-%s.%s", RandomStringUtils.randomAlphanumeric(8), "xbot-chain-text", "txt");
            String fileType = "text/plain";
            String fileSn = String.format("%s/%s", tenantName, UUID.randomUUID().toString().replaceAll("-", ""));

            try {
                // 文件基础信息
                FileUploadGtIdDto fileUploadGtIdDto=  buildFileUploadNoticeDto(processInstanceId, inputContent, fileSn, filename, fileType);
                ResultVo<FileUpGtIdVo> uploadResult = fileFeignClient.uploadGtId(fileUploadGtIdDto);

                if (uploadResult.getCode() != 0) {
                    log.error("File upload failed: {}", uploadResult.getMessage());
                    return Optional.empty();
                }

                fileInfo.setFileId(uploadResult.getData().getFileId());
                fileInfo.setFileName(filename);
                fileInfo.setFileSn(fileSn);

            } catch (Exception e) {
                log.error("Error uploading text file: {}", e.getMessage());
                return Optional.empty();
            }
        }

        return Optional.of(fileInfo);
    }

    private  FileUploadGtIdDto buildFileUploadNoticeDto(String processInstanceId, String inputContent, String fileSn, String filename, String fileType) {
        FileUploadGtIdDto fileUploadGtIdDto = new FileUploadGtIdDto();
        FileUploadNoticeDto fileUploadNoticeDto = new FileUploadNoticeDto();
        fileUploadNoticeDto.setFileSn(fileSn);
        fileUploadNoticeDto.setFileName(filename);
        fileUploadNoticeDto.setFileType(10);
        fileUploadNoticeDto.setFileFrom("11");
        fileUploadNoticeDto.setFileStatus("0");
        fileUploadNoticeDto.setFileSource(processInstanceId);

        fileUploadGtIdDto.setFileUploadNoticeDto(fileUploadNoticeDto);
        fileUploadGtIdDto.setContent(inputContent.getBytes());
        fileUploadGtIdDto.setFileSn(fileSn);
        fileUploadGtIdDto.setContentType(fileType);
        fileUploadGtIdDto.setType("private");
        
        return fileUploadGtIdDto;
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "txt";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1) : "txt";
    }

    /**
     * 获取租户名称
     */
    private String getTenantName(WorkflowContext context) {
        // TODO 这里需要从上下文中获取租户信息
//        Object tenantName = context.getGlobalVars().get("tenantName");
//        return tenantName != null ? tenantName.toString() : "XBOT";

        return null;
    }

    /**
     * 获取参数值，支持变量替换
     */
    private String getParameterValue(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        String strValue = value.toString();
        
        // 如果是变量引用（以$开头），从全局变量中获取
        if (strValue.startsWith("$")) {
            String varName = strValue.substring(1);
            Object varValue = context.getGlobalVars().get(varName);
            return varValue != null ? varValue.toString() : null;
        }
        
        return strValue;
    }

    /**
     * 获取参数对象，支持变量替换
     */
    private Object getParameterObject(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        if (value instanceof String) {
            String strValue = value.toString();
            // 如果是变量引用（以$开头），从全局变量中获取
            if (strValue.startsWith("$")) {
                String varName = strValue.substring(1);
                return context.getGlobalVars().get(varName);
            }
        }
        
        return value;
    }

    /**
     * 构建嵌入参数
     */
    private KnowledgeEmbeddingParamsDTO buildEmbeddingParams(String knowledgeInventory, String continueWhenErr,
                                                           String knowledgeType, Object deepParseParam, Object separatorContent,
                                                           Map<String, Object> inputParameters, WorkflowContext context) {
        KnowledgeEmbeddingParamsDTO params = new KnowledgeEmbeddingParamsDTO();
        params.setKnowledgeInventory(knowledgeInventory);
        params.setContinueWhenErr(StringUtils.isNotBlank(continueWhenErr) ? Integer.parseInt(continueWhenErr) : 0);
        params.setKnowledgeType(StringUtils.isNotBlank(knowledgeType) ? knowledgeType : "sn");

        // 处理深度解析参数
        List<String> deepParse = new ArrayList<>();
        if (deepParseParam instanceof List) {
            List<?> deepParseList = (List<?>) deepParseParam;
            for (Object item : deepParseList) {
                if (item != null) {
                    deepParse.add(item.toString());
                }
            }
        }
        params.setDeepParse(deepParse);

        // 处理分段规则
        buildSplitRule(inputParameters, params, separatorContent, context);

        return params;
    }

    /**
     * 构建分段规则
     */
    private void buildSplitRule(Map<String, Object> inputParameters, KnowledgeEmbeddingParamsDTO params,
                               Object separatorObj, WorkflowContext context) {
        String splitRuleType = getParameterValue(inputParameters, "splitRuleType", context);

        if (StringUtils.isNotBlank(splitRuleType) && "1".equals(splitRuleType)) {
            // 自定义分段规则
            String splitRule = getParameterValue(inputParameters, "splitRule", context);
            String splitter = getParameterValue(inputParameters, "splitter", context);
            String wordCountLimit = getParameterValue(inputParameters, "wordCountLimit", context);
            String wordCountOverlap = getParameterValue(inputParameters, "wordCountOverlap", context);

            if (StringUtils.isBlank(splitRule)) {
                throw new ServiceException(ExecutorError.KNOWLEDGE_SPLIT_RULE_CONFIG_ERROR);
            }

            params.setSplitRuleType(splitRuleType);
            params.setSplitRule(splitRule);

            if ("1".equals(splitRule)) {
                if (StringUtils.isBlank(splitter)) {
                    throw new ServiceException(ExecutorError.KNOWLEDGE_SPLIT_RULE_CONFIG_ERROR.getCode(), "切分器不能为空");
                }
                if (StringUtils.isBlank(wordCountLimit)) {
                    throw new ServiceException(ExecutorError.KNOWLEDGE_SPLIT_RULE_CONFIG_ERROR.getCode(), "分段字数上限不能为空");
                }

                params.setSplitter(splitter);
                params.setWordCountLimit(wordCountLimit);

                if (!"1".equals(splitter) && StringUtils.isBlank(wordCountOverlap)) {
                    throw new ServiceException(ExecutorError.KNOWLEDGE_SPLIT_RULE_CONFIG_ERROR.getCode(), "重叠区域字数不能为空");
                }
                if (StringUtils.isNotBlank(wordCountOverlap)) {
                    params.setWordCountOverlap(wordCountOverlap);
                }

                if ("3".equals(splitter)) {
                    if (separatorObj == null) {
                        throw new ServiceException(ExecutorError.KNOWLEDGE_SPLIT_RULE_CONFIG_ERROR.getCode(), "分隔符设置不能为空");
                    }
                    params.setSeparatorContent((List<String>)separatorObj);
                }
            }
        } else {
            // 跟随知识库设置

            ResultVo<BaseDetailVo> resultVo = docFeignClient.detailBySn((params.getKnowledgeInventory()));
            if(resultVo.getCode() != 0 && resultVo.getData() != null) {
                BaseDetailVo baseDetailVo = resultVo.getData();
                params.setSplitRule(baseDetailVo.getSplitRule());
                params.setSplitter(baseDetailVo.getSplitter());
                params.setWordCountLimit(baseDetailVo.getWordCountLimit());
                params.setWordCountOverlap(baseDetailVo.getWordCountOverlap());
                params.setSeparatorContent(baseDetailVo.getSeparatorContent());
                params.setSplitRuleType("0");
            }
        }
    }

    /**
     * 执行文件嵌入
     */
    private KnowledgeEmbeddingResultDTO executeFileEmbedding(List<FileInfoDto> files,
                                                           KnowledgeEmbeddingParamsDTO embeddingParams,
                                                           WorkflowContext context) {
        KnowledgeEmbeddingResultDTO result = new KnowledgeEmbeddingResultDTO();
        List<FileInfoDto> successEmbedding = new ArrayList<>();
        Map<String, String> errorMsg = new HashMap<>();
        String authorization = (String) context.getGlobalVars().get("authorization");

        Integer continueWhenErr = embeddingParams.getContinueWhenErr();
        String knowledgeInventory = embeddingParams.getKnowledgeInventory();

        if (continueWhenErr == 1) {
            // 如果选择报错继续，try catch接收
            files.forEach(fileInfoDto -> {
                try {
                    ResultVo<BaseDetailVo> baseDetailVoResultVoResult =  docFeignClient.detailBySn(knowledgeInventory);
                    if (baseDetailVoResultVoResult.getCode() == 0 && baseDetailVoResultVoResult.getData() != null) {
                        BaseDetailVo baseDetailVo = baseDetailVoResultVoResult.getData();
                        if(Objects.isNull(baseDetailVo) || StringUtils.isEmpty(baseDetailVo.getKbSn())){
                            throw new ServiceException(ExecutorError.KNOWLEDGE_INVENTORY_IS_NOT_FOUND);
                        }
                    }
                    executeFileEmbeddingForSingleFile(embeddingParams, authorization, fileInfoDto, successEmbedding);
                } catch (Exception e) {
                    errorMsg.put(fileInfoDto.getFormatFileSn(), e.toString());
                    log.error("KnowledgeAddNodeExecutor, file embedding failed，err: [{}]", e.toString());
                }
            });
        } else {
            // 如果选择报错不继续，直接循环
            files.forEach(fileInfoDto -> {
                ResultVo<BaseDetailVo> baseDetailVoResultVoResult =  docFeignClient.detailBySn(knowledgeInventory);
                if (baseDetailVoResultVoResult.getCode() == 0 && baseDetailVoResultVoResult.getData() != null) {
                    BaseDetailVo baseDetailVo = baseDetailVoResultVoResult.getData();
                    if(Objects.isNull(baseDetailVo) || StringUtils.isEmpty(baseDetailVo.getKbSn())){
                        throw new ServiceException(ExecutorError.KNOWLEDGE_INVENTORY_IS_NOT_FOUND);
                    }
                }
                executeFileEmbeddingForSingleFile(embeddingParams, authorization, fileInfoDto, successEmbedding);
            });
        }

        result.setDatasetId(knowledgeInventory);
        result.setErrorMsgs(errorMsg);
        result.setFiles(successEmbedding);

        return result;
    }

    /**
     * 执行单个文件的嵌入
     */
    private void executeFileEmbeddingForSingleFile(KnowledgeEmbeddingParamsDTO embeddingParams, String authorization,
                                                  FileInfoDto fileInfoDto, List<FileInfoDto> successEmbedding) {
        String knowledgeInventory = embeddingParams.getKnowledgeInventory();

        FileByIdFeignDto fileByIdFeignDto = new FileByIdFeignDto();
        fileByIdFeignDto.setFileId(fileInfoDto.getFileId());
        fileByIdFeignDto.setKbSn(knowledgeInventory);
        ResultVo<FileDetailVo> duplicateResult = docFeignClient.fileDetailById(fileByIdFeignDto);

        if (duplicateResult.getCode() == 0 && duplicateResult.getData() != null) {
            log.info("doc file [{}] already exist in knowledge inventory [{}], return doc file",
                    fileInfoDto.getFileSn(), knowledgeInventory);
            // 如果知识库中文件说明重复了，不再embedding
            FileDetailVo existingFile = duplicateResult.getData();

            if(existingFile != null){
                if (existingFile.getFileStatus() == 4) { // 失败状态
                    // 对于重复的文件，查询状态，如果是失败的，先删除该文件，再进行embedding
                    DeleteBySnFeignDto deleteBySnFeignDto = new DeleteBySnFeignDto();
                    deleteBySnFeignDto.setKbSn(knowledgeInventory);
                    deleteBySnFeignDto.setFileId(fileInfoDto.getFileId());
                    docFeignClient.deleteById(deleteBySnFeignDto);

                    // todo 这里embddding 待提供接口在修改
                    actualExecuteEmbedding(embeddingParams, authorization, fileInfoDto, successEmbedding);
                    return;
                }
            }

            successEmbedding.add(fileInfoDto);
            return;
        }

        // todo 这里embddding 待提供接口在修改
        actualExecuteEmbedding(embeddingParams, authorization, fileInfoDto, successEmbedding);
    }

    /**
     * 实际执行嵌入
     */
    private void actualExecuteEmbedding(KnowledgeEmbeddingParamsDTO embeddingParams, String authorization,
                                       FileInfoDto fileInfoDto, List<FileInfoDto> successEmbedding) {
        KnowledgeFileEmbeddingDTO embeddingReq = prepareEmbeddingRequest(embeddingParams, fileInfoDto);
        log.info("KnowledgeAddNodeExecutor call fileEmbedding, request param: {}", JsonUtils.toJsonString(embeddingReq));

        ResultVo<FileInfoDto> result = docFeignClient.embedding();
        if (result.getCode() != 0) {
            throw new ServiceException(result.getCode(), result.getMessage());
        }

        FileInfoDto embeddedFile = result.getData();
        if (embeddedFile == null) {
            throw new ServiceException(ExecutorError.KNOWLEDGE_FILE_EMBEDDING_FAILED);
        }

        // 轮询embedding状态
        pollEmbeddingStatus(embeddingParams.getKnowledgeInventory(), fileInfoDto.getFileId());
        successEmbedding.add(embeddedFile);
    }

    /**
     * 准备embedding请求
     */
    private KnowledgeFileEmbeddingDTO prepareEmbeddingRequest(KnowledgeEmbeddingParamsDTO embeddingParams,
                                                            FileInfoDto fileInfoDto) {
        String knowledgeInventory = embeddingParams.getKnowledgeInventory();
        List<String> deepParse = embeddingParams.getDeepParse();
        ResultVo<BaseDetailVo> resultVo = docFeignClient.detailBySn(knowledgeInventory);

        KnowledgeFileEmbeddingDTO embeddingRequest = new KnowledgeFileEmbeddingDTO();

        if (resultVo.getCode() == 0 && resultVo.getData() != null) {
            String modelSn = resultVo.getData().getModelSn();
            embeddingRequest.setDatasetId(knowledgeInventory);
            embeddingRequest.setFileInfo(fileInfoDto);
            embeddingRequest.setModelSn(modelSn);
            embeddingRequest.setEmbeddingType("PROCESS");
            embeddingRequest.setUploadType(1); // PROCESS类型
            embeddingRequest.setSplitRuleType(
                    embeddingParams.getSplitRuleType() != null ? Integer.parseInt(embeddingParams.getSplitRuleType()) : null);

            // 构建嵌入配置
            KnowledgeFileEmbeddingDTO.EmbeddingConfig embeddingConfig = new KnowledgeFileEmbeddingDTO.EmbeddingConfig();
            embeddingConfig.setOcrTable(false);
            embeddingConfig.setOcrImage(false);
            embeddingConfig.setOcrWord(false);

            if (deepParse != null && !deepParse.isEmpty()) {
                for (String deep : deepParse) {
                    switch (deep) {
                        case "parseForm":
                            embeddingConfig.setOcrTable(true);
                            break;
                        case "parseImage":
                            embeddingConfig.setOcrImage(true);
                            break;
                        case "parseWord":
                            embeddingConfig.setOcrWord(true);
                            break;
                    }
                }
            }

            // 设置分段规则
            String splitRuleType = embeddingParams.getSplitRuleType();
            if ("1".equals(splitRuleType) || "0".equals(splitRuleType)) {
                embeddingConfig.setSplitRule(embeddingParams.getSplitRule());
                embeddingConfig.setSplitter(embeddingParams.getSplitter());
                embeddingConfig.setWordCountLimit(embeddingParams.getWordCountLimit());
                embeddingConfig.setWordCountOverlap(embeddingParams.getWordCountOverlap());
                embeddingConfig.setSeparators(embeddingParams.getSeparatorContent());
            }

            embeddingRequest.setEmbeddingConfig(embeddingConfig);
        }

        return embeddingRequest;
    }

    /**
     * 轮询embedding状态
     */
    private void pollEmbeddingStatus(String kbSn,Integer fileId) {
        long pollingInterval = 3000; // 3秒
        int maxPollingAttempts = 1000;

        FileByIdFeignDto fileByIdFeignDto = new FileByIdFeignDto();
        fileByIdFeignDto.setKbSn(kbSn);
        fileByIdFeignDto.setFileId(fileId);

        for (int attempt = 1; attempt <= maxPollingAttempts; attempt++) {
            ResultVo<FileDetailVo> statusResult = docFeignClient.fileDetailById(fileByIdFeignDto);

            if (statusResult.getCode() == 0 && statusResult.getData() != null) {
                FileDetailVo statusInfo = statusResult.getData();

                if (statusInfo.getKfStatus() == 5) { // 成功
                    break;
                } else if (statusInfo.getKfStatus() == 4) { // 失败
                    throw new ServiceException(ExecutorError.KNOWLEDGE_FILE_EMBEDDING_FAILED);
                }
            }

            try {
                TimeUnit.MILLISECONDS.sleep(pollingInterval);
            } catch (InterruptedException e) {
                log.error("thread sleep interrupted failed, {}", e.toString());
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    /**
     * 构建输出结果
     */
    private Map<String, Object> buildOutputResult(KnowledgeEmbeddingResultDTO embeddingResult, int totalFiles) {
        int succeededCount = embeddingResult.getFiles().size();
        int failedCount = totalFiles - succeededCount;
        String resultMessage = String.format("成功:%d，失败:%d", succeededCount, failedCount);

        Map<String, Object> result = new HashMap<>();
        result.put("embeddingFiles", embeddingResult.getFiles().stream()
                .map(fileInfo -> String.format("%s::%s::%s", fileInfo.getFileId(), fileInfo.getFileName(), "KNOWLEDGE"))
                .collect(Collectors.toList()));
        result.put("knowledgeInventorySn", embeddingResult.getDatasetId());
        result.put("errorMsg", embeddingResult.getErrorMsgs());
        result.put("resultMessage", resultMessage);
        result.put("knowledgeNames", embeddingResult.getFiles().stream()
                .map(FileInfoDto::getFileName)
                .collect(Collectors.toList()));

        return result;
    }

    /**
     * 写入输出参数
     */
    private void writeOutputParameters(Map<String, Object> nodeDef, WorkflowContext context, Map<String, Object> result) {
        Map<String, Object> outputParameters = (Map<String, Object>) nodeDef.get("outputParameters");
        if (outputParameters != null) {
            for (Map.Entry<String, Object> entry : outputParameters.entrySet()) {
                String outputKey = entry.getKey();
                String variableName = entry.getValue().toString();

                Object resultValue = result.get(outputKey);
                if (resultValue != null) {
                    context.setVar(variableName, resultValue);
                    log.info("Set variable {} = {}", variableName, resultValue);
                }
            }
        }
    }

    @Override
    public String getType() {
        return "KNOWLEDGE_ADD";
    }
}
