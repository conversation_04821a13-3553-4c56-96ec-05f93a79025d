package com.ai.application.knowledge.table.feign;

import com.ai.application.knowledge.table.dto.TableDataCreateDto;
import com.ai.application.knowledge.table.dto.TableDataListDto;
import com.ai.application.knowledge.table.vo.TableDataListVo;
import com.ai.application.knowledge.table.vo.TableDetailVo;
import com.ai.framework.core.vo.ResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = "knowledge-table-service", contextId = "ITableFeignClient")
//@FeignClient(name = "abc", url = "http://127.0.0.1:6042")
public interface ITableFeignClient {

    @GetMapping("/detailById/{tableId}")
    ResultVo<TableDetailVo> detailById(@PathVariable("tableId") Integer tableId);

    @PostMapping("/data/create")
    ResultVo<String> dataCreate(@RequestBody TableDataCreateDto dto, @RequestHeader("Authorization") String token);

    @PostMapping("/data/query")
    ResultVo<List<TableDataListVo>> dataQuery(@RequestBody TableDataListDto dto);

}
