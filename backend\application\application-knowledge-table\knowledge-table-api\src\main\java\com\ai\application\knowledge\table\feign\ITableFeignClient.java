package com.ai.application.knowledge.table.feign;

import com.ai.application.knowledge.table.dto.TableDataCreateDto;
import com.ai.application.knowledge.table.dto.TableDataListDto;
import com.ai.application.knowledge.table.vo.TableDataListVo;
import com.ai.application.knowledge.table.vo.TableDetailVo;
import com.ai.framework.core.vo.ResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "knowledge-table-service", contextId = "ITableFeignClient")
public interface ITableFeignClient {

    @GetMapping("/detailById/{tableId}")
    ResultVo<TableDetailVo> detailById(@PathVariable("tableId") Integer tableId);

    @PostMapping("/data/create")
    ResultVo<String> dataCreate(@RequestBody TableDataCreateDto dto);

    @PostMapping("/data/query")
    ResultVo<List<TableDataListVo>> dataQuery(@RequestBody TableDataListDto dto);

}
