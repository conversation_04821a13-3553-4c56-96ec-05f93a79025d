package com.ai.application.agent.run.service.impl;

import com.ai.application.agent.run.dto.*;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.feign.IMemoryFeignClient;
import com.ai.application.agent.run.service.IShortTermMemoryRetrieveService;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 短期记忆提取服务实现
 */
@Slf4j
@Service
public class ShortTermMemoryRetrieveServiceImpl implements IShortTermMemoryRetrieveService {

    private final IMemoryFeignClient memoryFeignClient;

    public ShortTermMemoryRetrieveServiceImpl(IMemoryFeignClient memoryFeignClient) {
        this.memoryFeignClient = memoryFeignClient;
    }

    @Override
    public ResultVo<ShortTermMemoryRetrieveResultDTO> executeShortTermMemoryRetrieve(ShortTermMemoryRetrieveRequestDTO request, String authorization) {
        log.info("ShortTermMemoryRetrieveService executeShortTermMemoryRetrieve start, request: {}", JsonUtils.toJsonString(request));

        try {
            // 参数校验
            validateShortTermMemoryRetrieveRequest(request);

            // 构建记忆搜索请求
            MemorySearchRequestDTO searchRequest = buildMemorySearchRequestDTO(request);

            log.info("Calling memory service to search memories: agentId={}, userId={}, category={}", 
                    request.getAgentId(), request.getUserId(), request.getCategory());

            // 调用记忆服务搜索
            MemorySearchResponseDTO searchResponse = memoryFeignClient.searchMemory(searchRequest);

            if (searchResponse == null || !searchResponse.getSuccess()) {
                String errorMsg = searchResponse != null ? searchResponse.getMessage() : "记忆搜索服务调用失败";
                throw new ServiceException(ExecutorError.MEMORY_SERVICE_CALL_FAILED.getCode(), errorMsg);
            }

            // 转换结果格式
            ShortTermMemoryRetrieveResultDTO retrieveResult = convertToRetrieveResult(searchResponse, request);

            log.info("ShortTermMemoryRetrieveService executeShortTermMemoryRetrieve success, found {} memories", 
                    retrieveResult.getReturnCount());
            return ResultVo.data(retrieveResult);

        } catch (Exception e) {
            log.error("ShortTermMemoryRetrieveService executeShortTermMemoryRetrieve error", e);
            ShortTermMemoryRetrieveResultDTO errorResult = ShortTermMemoryRetrieveResultDTO.builder()
                    .success(false)
                    .output(Collections.emptyList())
                    .errorMessage(e.getMessage())
                    .totalCount(0)
                    .returnCount(0)
                    .build();
            return ResultVo.data(errorResult);
        }
    }

    @Override
    public boolean validateMemoryRetrieveRequest(ShortTermMemoryRetrieveRequestDTO request) {
        if (request == null) {
            return false;
        }

        // 必须有智能体ID和用户ID
        if (StringUtils.isBlank(request.getAgentId()) || StringUtils.isBlank(request.getUserId())) {
            return false;
        }

        // 至少需要有一个检索条件
        boolean hasRetrieveContent = StringUtils.isNotBlank(request.getRetrieveContent());
        boolean hasCategory = StringUtils.isNotBlank(request.getCategory());
        boolean hasTimeRange = request.getStartTime() != null || request.getEndTime() != null;

        return hasRetrieveContent || hasCategory || hasTimeRange;
    }

    @Override
    public Object buildMemorySearchRequest(ShortTermMemoryRetrieveRequestDTO request) {
        return buildMemorySearchRequestDTO(request);
    }

    /**
     * 校验短期记忆提取请求
     */
    private void validateShortTermMemoryRetrieveRequest(ShortTermMemoryRetrieveRequestDTO request) {
        if (StringUtils.isBlank(request.getAgentId())) {
            throw new ServiceException(ExecutorError.AGENT_ID_IS_BLANK);
        }

        if (StringUtils.isBlank(request.getUserId())) {
            throw new ServiceException(ExecutorError.USER_ID_IS_BLANK);
        }

        if (!validateMemoryRetrieveRequest(request)) {
            throw new ServiceException(ExecutorError.SHORT_TERM_MEMORY_RETRIEVE_CONDITIONS_INVALID);
        }
    }

    /**
     * 构建记忆搜索请求DTO
     */
    private MemorySearchRequestDTO buildMemorySearchRequestDTO(ShortTermMemoryRetrieveRequestDTO request) {
        return MemorySearchRequestDTO.builder()
                .agentId(request.getAgentId())
                .userId(request.getUserId())
                .category(request.getCategory())
                .userQuestion(request.getRetrieveContent())
                .similarityThreshold(request.getSimilarityThreshold() != null ? request.getSimilarityThreshold() : 0.7)
                .startTime(request.getStartTime())
                .endTime(request.getEndTime())
                .sortBy(StringUtils.isNotBlank(request.getSortBy()) ? request.getSortBy() : "similarity_desc")
                .limit(request.getLimit() != null ? request.getLimit() : 10)
                .build();
    }

    /**
     * 转换搜索结果为提取结果
     */
    private ShortTermMemoryRetrieveResultDTO convertToRetrieveResult(MemorySearchResponseDTO searchResponse, 
                                                                    ShortTermMemoryRetrieveRequestDTO request) {
        MemorySearchResponseDTO.MemorySearchData data = searchResponse.getData();
        if (data == null) {
            return ShortTermMemoryRetrieveResultDTO.builder()
                    .success(true)
                    .output(Collections.emptyList())
                    .totalCount(0)
                    .returnCount(0)
                    .build();
        }

        List<MemorySearchResponseDTO.MemoryItemDTO> memories = data.getMemories();
        if (CollectionUtils.isEmpty(memories)) {
            return ShortTermMemoryRetrieveResultDTO.builder()
                    .success(true)
                    .output(Collections.emptyList())
                    .totalCount(data.getTotal() != null ? data.getTotal() : 0)
                    .returnCount(0)
                    .build();
        }

        // 根据输出格式转换结果
        List<String> outputList = convertMemoriesToOutput(memories, request.getOutputFormat());

        return ShortTermMemoryRetrieveResultDTO.builder()
                .success(true)
                .output(outputList)
                .totalCount(data.getTotal() != null ? data.getTotal() : memories.size())
                .returnCount(memories.size())
                .memoryDetails(memories)
                .build();
    }

    /**
     * 转换记忆列表为输出格式
     */
    private List<String> convertMemoriesToOutput(List<MemorySearchResponseDTO.MemoryItemDTO> memories, Integer outputFormat) {
        if (CollectionUtils.isEmpty(memories)) {
            return Collections.emptyList();
        }

        return memories.stream().map(memory -> {
            if (outputFormat != null && outputFormat == 1) {
                // 详细格式：包含更多信息
                return formatDetailedMemory(memory);
            } else {
                // 简化格式：只包含主要内容
                return formatSimpleMemory(memory);
            }
        }).filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    /**
     * 格式化简化记忆
     */
    private String formatSimpleMemory(MemorySearchResponseDTO.MemoryItemDTO memory) {
        if (memory == null) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotBlank(memory.getUserQuestion())) {
            sb.append(memory.getUserQuestion());
        }
        if (StringUtils.isNotBlank(memory.getQuestionReply())) {
            if (sb.length() > 0) {
                sb.append(" | ");
            }
            sb.append(memory.getQuestionReply());
        }

        return sb.toString();
    }

    /**
     * 格式化详细记忆
     */
    private String formatDetailedMemory(MemorySearchResponseDTO.MemoryItemDTO memory) {
        if (memory == null) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("记忆ID: ").append(memory.getId());
        sb.append(", 类别: ").append(memory.getCategory());
        if (StringUtils.isNotBlank(memory.getUserQuestion())) {
            sb.append(", 问题: ").append(memory.getUserQuestion());
        }
        if (StringUtils.isNotBlank(memory.getQuestionReply())) {
            sb.append(", 回复: ").append(memory.getQuestionReply());
        }
        if (memory.getSimilarityScore() != null) {
            sb.append(", 相似度: ").append(String.format("%.3f", memory.getSimilarityScore()));
        }
        if (memory.getQuestionTime() != null) {
            sb.append(", 时间: ").append(memory.getQuestionTime());
        }

        return sb.toString();
    }
}
