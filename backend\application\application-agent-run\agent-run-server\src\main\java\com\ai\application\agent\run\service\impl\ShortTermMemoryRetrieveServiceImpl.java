package com.ai.application.agent.run.service.impl;

import com.ai.application.agent.run.dto.*;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.feign.IMemoryFeignClient;
import com.ai.application.agent.run.service.IShortTermMemoryRetrieveService;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 短期记忆提取服务实现
 */
@Slf4j
@Service
public class ShortTermMemoryRetrieveServiceImpl implements IShortTermMemoryRetrieveService {

    private final IMemoryFeignClient memoryFeignClient;

    public ShortTermMemoryRetrieveServiceImpl(IMemoryFeignClient memoryFeignClient) {
        this.memoryFeignClient = memoryFeignClient;
    }

    @Override
    public ResultVo<ShortTermMemoryRetrieveResultDTO> executeShortTermMemoryRetrieve(ShortTermMemoryRetrieveRequestDTO request, String authorization) {
        log.info("ShortTermMemoryRetrieveService executeShortTermMemoryRetrieve start, request: {}", JsonUtils.toJsonString(request));

        try {
            // 参数校验
            validateShortTermMemoryRetrieveRequest(request);

            // 处理提取配置列表
            Map<String, Object> memoryMap = processExtractConfigs(request);

            // 构建返回结果
            ShortTermMemoryRetrieveResultDTO retrieveResult = ShortTermMemoryRetrieveResultDTO.builder()
                    .success(true)
                    .memoryMap(memoryMap)
                    .processedConfigCount(request.getExtractConfigs() != null ? request.getExtractConfigs().size() : 0)
                    .successConfigCount(memoryMap.size())
                    .build();

            log.info("ShortTermMemoryRetrieveService executeShortTermMemoryRetrieve success, processed {} configs, success {} configs",
                    retrieveResult.getProcessedConfigCount(), retrieveResult.getSuccessConfigCount());
            return ResultVo.data(retrieveResult);

        } catch (Exception e) {
            log.error("ShortTermMemoryRetrieveService executeShortTermMemoryRetrieve error", e);
            ShortTermMemoryRetrieveResultDTO errorResult = ShortTermMemoryRetrieveResultDTO.builder()
                    .success(false)
                    .memoryMap(new HashMap<>())
                    .errorMessage(e.getMessage())
                    .processedConfigCount(0)
                    .successConfigCount(0)
                    .build();
            return ResultVo.data(errorResult);
        }
    }

    @Override
    public boolean validateMemoryRetrieveRequest(ShortTermMemoryRetrieveRequestDTO request) {
        if (request == null) {
            return false;
        }

        // 必须有智能体ID和用户ID
        if (StringUtils.isBlank(request.getAgentId()) || StringUtils.isBlank(request.getUserId())) {
            return false;
        }

        // 必须有提取配置列表
        return CollectionUtils.isNotEmpty(request.getExtractConfigs());
    }

    /**
     * 校验短期记忆提取请求
     */
    private void validateShortTermMemoryRetrieveRequest(ShortTermMemoryRetrieveRequestDTO request) {
        if (StringUtils.isBlank(request.getAgentId())) {
            throw new ServiceException(ExecutorError.AGENT_ID_IS_BLANK);
        }

        if (StringUtils.isBlank(request.getUserId())) {
            throw new ServiceException(ExecutorError.USER_ID_IS_BLANK);
        }

        if (!validateMemoryRetrieveRequest(request)) {
            throw new ServiceException(ExecutorError.SHORT_TERM_MEMORY_RETRIEVE_CONDITIONS_INVALID);
        }
    }

    /**
     * 处理提取配置列表（完全匹配backend2逻辑）
     */
    private Map<String, Object> processExtractConfigs(ShortTermMemoryRetrieveRequestDTO request) {
        Map<String, Object> memoryMap = new HashMap<>();

        if (CollectionUtils.isEmpty(request.getExtractConfigs())) {
            return memoryMap;
        }

        for (MemoryRetrieveConfigDTO extractConfig : request.getExtractConfigs()) {
            try {
                // 获取配置参数
                String extractType = extractConfig.getExtractType();
                String extractCountStr = extractConfig.getExtractCount();
                String extractContent = extractConfig.getExtractContent();
                String index = extractConfig.getIndex();

                // 计算提取数量
                int extractCount = "ALL".equals(extractType) ? 99 : Integer.parseInt(extractCountStr);

                // 构建搜索请求
                MemorySearchRequestDTO searchRequest = buildMemorySearchRequestForConfig(request, extractConfig, extractCount);

                // 调用记忆服务搜索
                MemorySearchResponseDTO searchResponse = memoryFeignClient.searchMemory(searchRequest);

                if (searchResponse != null && searchResponse.getSuccess() && searchResponse.getData() != null) {
                    List<MemorySearchResponseDTO.MemoryItemDTO> memories = searchResponse.getData().getMemories();

                    if (CollectionUtils.isNotEmpty(memories)) {
                        // 处理结果格式
                        Object memoryResult = processMemoryResult(memories, extractCountStr, extractContent);
                        if (memoryResult != null && StringUtils.isNotBlank(index)) {
                            memoryMap.put(index, memoryResult);
                        }
                    }
                }

            } catch (Exception e) {
                log.warn("Failed to process extract config: {}, error: {}", JsonUtils.toJsonString(extractConfig), e.getMessage());
            }
        }

        return memoryMap;
    }

    /**
     * 为单个配置构建搜索请求
     */
    private MemorySearchRequestDTO buildMemorySearchRequestForConfig(ShortTermMemoryRetrieveRequestDTO request,
                                                                    MemoryRetrieveConfigDTO config,
                                                                    int extractCount) {
        return MemorySearchRequestDTO.builder()
                .agentId(request.getAgentId())
                .userId(request.getUserId())
                .category(StringUtils.isNotBlank(config.getBucketSn()) ? "short_term_memory_"+config.getBucketSn() : "short_term_memory_default")
                .userQuestion(config.getRetrieveContent())
                .similarityThreshold(config.getSimilarityThreshold() != null ? config.getSimilarityThreshold() : 0.7)
                .startTime(request.getStartTime())
                .endTime(request.getEndTime())
                .sortBy(StringUtils.isNotBlank(request.getSortBy()) ? request.getSortBy() : "time_desc")
                .limit(extractCount)
                .build();
    }

    /**
     * 处理记忆结果（完全匹配backend2逻辑）
     */
    private Object processMemoryResult(List<MemorySearchResponseDTO.MemoryItemDTO> memories,
                                     String extractCountStr,
                                     String extractContent) {
        if (CollectionUtils.isEmpty(memories)) {
            return null;
        }

        if ("1".equals(extractCountStr)) {
            // 单个记忆
            MemorySearchResponseDTO.MemoryItemDTO memory = memories.get(0);
            if ("ALL".equals(extractContent)) {
                // 返回完整记忆对象的JSON字符串
                return JsonUtils.toJsonString(convertToMemoryDTO(memory));
            } else if ("CONTENT".equals(extractContent)) {
                // 仅返回记忆内容
                return memory.getUserQuestion();
            }
        } else {
            // 多个记忆
            if ("ALL".equals(extractContent)) {
                // 返回完整记忆对象列表的JSON字符串列表
                return memories.stream()
                        .map(this::convertToMemoryDTO)
                        .map(JsonUtils::toJsonString)
                        .collect(Collectors.toList());
            } else if ("CONTENT".equals(extractContent)) {
                // 仅返回记忆内容列表
                return memories.stream()
                        .map(MemorySearchResponseDTO.MemoryItemDTO::getUserQuestion)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
            }
        }

        return null;
    }

    /**
     * 转换为记忆DTO
     */
    private Map<String, Object> convertToMemoryDTO(MemorySearchResponseDTO.MemoryItemDTO memory) {
        Map<String, Object> memoryDto = new HashMap<>();
        memoryDto.put("memoryContent", memory.getUserQuestion());
        memoryDto.put("createTime", memory.getCreatedAt());
        memoryDto.put("bucketName", memory.getCategory());
        return memoryDto;
    }


}
