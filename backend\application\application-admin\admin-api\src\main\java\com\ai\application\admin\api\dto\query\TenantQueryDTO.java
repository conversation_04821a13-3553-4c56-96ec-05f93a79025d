package com.ai.application.admin.api.dto.query;

import com.ai.framework.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 租户表 查询条件
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(name = "租户表QueryDTO")
public class TenantQueryDTO extends PageParam {
    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Integer tenantId;

    /**
     * 租户状态 0:禁用,1:启用,-1:删除
     */
    @Schema(description = "租户状态 0:禁用,1:启用,-1:删除")
    private Integer tenantStatus;
}