package com.ai.application.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 租户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("tenant")
public class Tenant implements Serializable {
        /**
    * 租户id
    */
    @Schema(description = "租户id")
    @TableId(type = IdType.AUTO)
    private Integer tenantId;

    /**
    * 租户sn
    */
    @Schema(description = "租户sn")
    private String tenantSn;

    /**
    * 租户名称
    */
    @Schema(description = "租户名称")
    private String tenantName;

    /**
    * 租户域名
    */
    @Schema(description = "租户域名")
    private String tenantDomain;

    /**
    * 租户描述
    */
    @Schema(description = "租户描述")
    private String tenantDesc;

    /**
    * 有效截止时间
    */
    @Schema(description = "有效截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tenantExpireTime;

    /**
    * 租户状态 0:禁用,1:启用,-1:删除
    */
    @Schema(description = "租户状态 0:禁用,1:启用,-1:删除")
    private Integer tenantStatus;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}