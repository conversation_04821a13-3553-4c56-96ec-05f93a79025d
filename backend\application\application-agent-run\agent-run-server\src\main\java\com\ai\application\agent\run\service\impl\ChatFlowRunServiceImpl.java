package com.ai.application.agent.run.service.impl;

import com.ai.application.agent.run.service.IChatFlowRunService;
import com.ai.framework.core.vo.ResultVo;
import dto.ChatFlowRunDTO;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import vo.ChatFlowRunVO;

@Service
public class ChatFlowRunServiceImpl implements IChatFlowRunService {
    @Override
    public Flux<ResultVo<ChatFlowRunVO>> run(ChatFlowRunDTO dto) {
        return null;
    }
}