package com.ai.application.admin.mapper;

import com.ai.application.admin.api.entity.ModelSupplier;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 模型厂商表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Mapper
public interface ModelSupplierMapper extends BaseMapper<ModelSupplier> {
    @Select("select * from model_supplier where supplier_sn = #{supplierSn}")
    ModelSupplier getModelSupplierBySn(@Param("supplierSn") String supplierSn);
}
