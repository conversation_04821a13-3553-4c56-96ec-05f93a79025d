package com.ai.application.skill.tool.api.vo;

import com.ai.application.skill.tool.api.dto.PythonCodeParamDto;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class PythonCodeExecResp {

    private int code;
    private String message;
    private ExecRespData data;


    @Data
    public static class ExecRespData {
        private List<PythonCodeParamDto> outputs;
        private String stderr;
        private String stdout;

    }


}
