#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康检查脚本

从配置文件中读取端口和主机信息，进行健康检查
用于Docker容器的HEALTHCHECK指令

Usage:
    python test/check_health.py

Project: Agent Memory Service
Author: 张小龙 <<EMAIL>>
Created: 2025-06-01
Version: 1.0.0
License: private
"""

import sys
import requests
from pathlib import Path

def main():
    """主函数"""
    try:
        # 添加项目路径
        project_root = Path(__file__).parent.parent
        sys.path.insert(0, str(project_root))
        
        # 导入配置
        from app.config.settings import settings
        
        # 构建健康检查URL
        host = "localhost"  # 容器内部检查使用localhost
        port = settings.app.port
        health_url = f"http://{host}:{port}/health"
        
        # 发送健康检查请求
        response = requests.get(health_url, timeout=10)
        
        if response.status_code == 200:
            health_data = response.json()
            status = health_data.get('status', 'unknown')
            if status in ['healthy', 'ready']:
                print(f"[SUCCESS] 健康检查通过: {status}")
                sys.exit(0)
            else:
                print(f"[ERROR] 服务状态异常: {status}")
                sys.exit(1)
        else:
            print(f"[ERROR] 健康检查失败: HTTP {response.status_code}")
            sys.exit(1)
            
    except ImportError as e:
        print(f"[ERROR] 无法导入配置模块: {e}")
        sys.exit(1)
    except requests.exceptions.RequestException as e:
        print(f"[ERROR] 健康检查请求失败: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"[ERROR] 健康检查异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 