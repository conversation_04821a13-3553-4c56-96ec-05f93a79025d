package com.ai.application.task.controller;

import com.ai.application.task.api.dto.TaskBatchAddDTO;
import com.ai.application.task.api.dto.query.TaskQueryDTO;
import com.ai.application.task.api.vo.TaskBatchDetailVO;
import com.ai.application.task.api.vo.TaskBatchPageVO;
import com.ai.application.task.api.vo.TaskDetailVO;
import com.ai.application.task.service.ITaskBatchService;
import com.ai.framework.core.vo.ResultVo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 批任务表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Tag(name = "批任务表", description = "批任务-相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1/batch")
public class TaskBatchController {

    @Resource
    private ITaskBatchService taskService;

    @Operation(summary = "批任务-分页查询", description = "查询所有计划任务表 信息")
    @PostMapping("/page")
    public ResultVo<PageInfo<TaskBatchPageVO>> page(@Validated @RequestBody TaskQueryDTO queryDto){
        return ResultVo.data(taskService.page(queryDto));
    }

    @Operation(summary = "批任务-新增")
    @PostMapping("/add")
    public ResultVo<Void> add(@Validated @RequestBody TaskBatchAddDTO dto){
        taskService.add(dto);
        return ResultVo.success("保存成功");
    }

    @Operation(summary = "批任务-删除")
    @PostMapping(value = "/delete/{taskSn}")
    public ResultVo<Void> delete(@PathVariable("taskSn") String taskSn){
        taskService.delete(taskSn);
        return ResultVo.success("删除成功");
    }

    @Operation(summary = "批任务-终止")
    @PostMapping(value = "/stop/{taskSn}")
    public ResultVo<Void> stop(@PathVariable("taskSn") String taskSn){
        taskService.stop(taskSn);
        return ResultVo.success("停用成功");
    }

    @Operation(summary = "批任务-详情")
    @PostMapping(value = "/dtail/{taskSn}")
    public ResultVo<TaskBatchDetailVO> dtail(@PathVariable("taskSn") String taskSn){
        return ResultVo.data(taskService.detail(taskSn));
    }

    @Operation(summary = "批任务-数据集模板下载")
    @GetMapping("/datasets/template/{agentSn}/{versionSn}")
    public void getDatasetTemplate(@PathVariable("agentSn") String agentSn, @PathVariable("versionSn") String versionSn,HttpServletResponse response) {
        taskService.getDataSetTemplate(agentSn,versionSn,response);
    }


}