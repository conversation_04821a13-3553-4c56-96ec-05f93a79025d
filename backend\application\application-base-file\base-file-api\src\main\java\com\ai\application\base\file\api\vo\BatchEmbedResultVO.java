package com.ai.application.base.file.api.vo;


import com.ai.application.base.file.api.dto.FileInfoDto;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchEmbedResultVO {

    private List<FileInfoDto> insertedFiles = Lists.newArrayList();
    private List<FileInfoDto> duplicateFiles = Lists.newArrayList();
}
