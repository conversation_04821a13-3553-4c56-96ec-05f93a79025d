package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.dto.AgentUseMcpAddDTO;
import com.ai.application.agent.base.api.dto.AgentUseMcpUpdateDTO;
import com.ai.application.agent.base.api.entity.AgentUseMcp;
import com.ai.application.agent.base.api.vo.AgentUseMcpListVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T09:54:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentUseMcpMapstructImpl implements AgentUseMcpMapstruct {

    @Override
    public AgentUseMcp toAddEntity(AgentUseMcpAddDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentUseMcp agentUseMcp = new AgentUseMcp();

        agentUseMcp.setAmcStatus( dto.getAmcStatus() );
        agentUseMcp.setAgentId( dto.getAgentId() );
        agentUseMcp.setVersionId( dto.getVersionId() );
        agentUseMcp.setMcpExtend( dto.getMcpExtend() );
        agentUseMcp.setMcpToolId( dto.getMcpToolId() );

        return agentUseMcp;
    }

    @Override
    public AgentUseMcp toUpdateEntity(AgentUseMcpUpdateDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentUseMcp agentUseMcp = new AgentUseMcp();

        agentUseMcp.setAmcId( dto.getAmcId() );
        agentUseMcp.setAmcStatus( dto.getAmcStatus() );
        agentUseMcp.setAgentId( dto.getAgentId() );
        agentUseMcp.setVersionId( dto.getVersionId() );
        agentUseMcp.setMcpExtend( dto.getMcpExtend() );
        agentUseMcp.setMcpToolId( dto.getMcpToolId() );
        agentUseMcp.setCreateTime( dto.getCreateTime() );
        agentUseMcp.setUpdateTime( dto.getUpdateTime() );

        return agentUseMcp;
    }

    @Override
    public AgentUseMcpListVO toVo(AgentUseMcp entity) {
        if ( entity == null ) {
            return null;
        }

        AgentUseMcpListVO agentUseMcpListVO = new AgentUseMcpListVO();

        agentUseMcpListVO.setAmcId( entity.getAmcId() );
        agentUseMcpListVO.setAmcStatus( entity.getAmcStatus() );
        agentUseMcpListVO.setAgentId( entity.getAgentId() );
        agentUseMcpListVO.setVersionId( entity.getVersionId() );
        agentUseMcpListVO.setMcpToolId( entity.getMcpToolId() );
        agentUseMcpListVO.setMcpExtend( entity.getMcpExtend() );
        agentUseMcpListVO.setCreateTime( entity.getCreateTime() );
        agentUseMcpListVO.setUpdateTime( entity.getUpdateTime() );

        return agentUseMcpListVO;
    }

    @Override
    public List<AgentUseMcpListVO> toVoList(List<AgentUseMcp> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AgentUseMcpListVO> list = new ArrayList<AgentUseMcpListVO>( entities.size() );
        for ( AgentUseMcp agentUseMcp : entities ) {
            list.add( toVo( agentUseMcp ) );
        }

        return list;
    }
}
