package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.dto.AgentUseToolUpdateDTO;
import com.ai.application.agent.base.api.entity.AgentUseTool;
import com.ai.application.agent.base.api.dto.AgentUseToolAddDTO;
import com.ai.application.agent.base.api.vo.AgentUseToolListVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 智能体使用工具表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-07
 */

@Mapper(componentModel = "spring")
public interface AgentUseToolMapstruct {

    AgentUseTool toAddEntity(AgentUseToolAddDTO dto);
    AgentUseTool toUpdateEntity(AgentUseToolUpdateDTO dto);
    AgentUseToolListVO toVo(AgentUseTool entity);
    List<AgentUseToolListVO> toVoList(List<AgentUseTool> entities);
}
