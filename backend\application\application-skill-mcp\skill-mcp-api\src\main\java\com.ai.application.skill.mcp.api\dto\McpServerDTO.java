package com.ai.application.skill.mcp.api.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

/**
 * MCP服务器表
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Data
@Schema(name = "MCP服务器表DTO")
public class McpServerDTO {
    /**
     * MCP服务器id
     */
    @Schema(description = "MCP服务器id")
    private Integer serverId;

    /**
     * MCP服务器sn
     */
    @Schema(description = "MCP服务器sn")
    private String serverSn;


    @Schema(description = "MCP服务供应商名称")
    private String supplierName ;

    /**
     * MCP服务器名称
     */
    @Schema(description = "MCP服务器名称")
    private String serverName;

    /**
     * MCP服务器描述
     */
    @Schema(description = "MCP服务器描述")
    private String serverDesc;

    /**
     * 服务器类型:10-官方,20-第三方,30-自建
     */
    @Schema(description = "服务器类型:10-官方,20-第三方,30-自建")
    private Integer serverType;




//    /**
//     * 服务器地址
//     */
//    @Schema(description = "服务器地址")
//    private String serverUrl;

//    /**
//     * 服务器端口
//     */
//    @Schema(description = "服务器端口")
//    private Integer serverPort;

    /**
     * 传输类型:10-stdio,20-sse,30-websocket
     */
//    @Schema(description = "传输类型:10-stdio,20-sse,30-websocket")
//    private Integer transportType;

    /**
     * 认证类型:10-无,20-API Key,30-OAuth,40-自定义
     */
//    @Schema(description = "认证类型:10-无,20-API Key,30-OAuth,40-自定义")
//    private Integer authType;

    /**
     * 认证配置
     */
//    @Schema(description = "认证配置")
//    private String authConfig;

    /**
     * 连接配置
     */
    @Schema(description = "连接配置")
    private String connectionConfig;

    /**
     * 服务器能力
     */
//    @Schema(description = "服务器能力")
//    private String serverCapabilities;

    /**
     * 服务器元数据
     */
    @Schema(description = "服务器元数据")
    private String serverMetadata;

    /**
     * 服务器状态:1-启用,0-禁用,-1-删除
     */
    @Schema(description = "服务器状态:1-启用,0-禁用,-1-删除")
    private Integer serverStatus;

    /**
     * 健康检查地址
     */
//    @Schema(description = "健康检查地址")
//    private String healthCheckUrl;


//    private JSONObject jsonObject;

    /**
     * 最后检查时间
     */
//    @Schema(description = "最后检查时间")
//    private Date lastCheckTime;

    /**
     * 检查状态:1-正常,0-异常
     */
//    @Schema(description = "检查状态:1-正常,0-异常")
//    private Integer checkStatus;

//    /**
//     * 租户id
//     */
//    @Schema(description = "租户id")
//    private Integer tenantId;

    /**
     * 创建用户id
     */
//    @Schema(description = "创建用户id")
//    private Integer createUserId;

    /**
     * 更新用户id
     */
//    @Schema(description = "更新用户id")
//    private Integer updateUserId;

//    @Schema(description = "")
//    private Date createTime;
//
//    @Schema(description = "")
//    private Date updateTime;



    @Schema(description = "0-更新 1-状态变更（停、启用） 2-基本信息变更")
    private Integer type;

}