package com.ai.application.agent.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * Agent Chat DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(name = "AgentChatDTO")
public class AgentChatDTO {
    /**
     * agent sn
     */
    @Schema(description = "智能体编号")
    private String agentSn;

    /**
     * agent type
     */
    @Schema(description = "智能体类型")
    private String agentType;

    /**
     * 请求内容
     */
    @Schema(description = "消息内容")
    private String msgContent;

    /**
     * 消息类型
     */
    @Schema(description = "消息类型")
    private String msgType;

    /**
     * 延迟时间
     */
    @Schema(description = "延迟时间(毫秒)")
    private Long delayInMs;

    /**
     * 来源码
     */
    @Schema(description = "来源码")
    private String fromCode;

    /**
     * 工作流名称 fromCode=Process时有值
     */
    @Schema(description = "工作流ID")
    private String processId;

    /**
     * master agent sn fromCode=Skill且是master agent调用时有值
     */
    @Schema(description = "主智能体编号")
    private String masterAgentSn;

    /**
     * 是否是调试
     */
    @Schema(description = "是否调试模式")
    private Boolean debug;

    /**
     * 会话编号
     */
    @Schema(description = "会话编号")
    private String sessionSn;
}
