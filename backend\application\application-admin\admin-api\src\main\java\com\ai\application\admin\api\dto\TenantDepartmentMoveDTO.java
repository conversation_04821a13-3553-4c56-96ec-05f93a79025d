package com.ai.application.admin.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 租户部门表
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@Schema(name = "租户部门表DTO")
public class TenantDepartmentMoveDTO {
    /**
     * 部门id
     */
    @Schema(description = "部门id")
    private Integer deptId;

    /**
     * 父级ID
     */
    @Schema(description = "父级ID")
    @NotNull(message = "父级ID不能为空")
    private Integer parentId;
}