package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.dto.AgentUseWorkflowAddDTO;
import com.ai.application.agent.base.api.dto.AgentUseWorkflowUpdateDTO;
import com.ai.application.agent.base.api.entity.AgentUseWorkflow;
import com.ai.application.agent.base.api.vo.AgentUseWorkflowListVO;
import com.ai.application.agent.base.api.vo.AgentUseWorkflowVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-13T10:32:23+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentUseWorkflowMapstructImpl implements AgentUseWorkflowMapstruct {

    @Override
    public AgentUseWorkflow toAddEntity(AgentUseWorkflowAddDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentUseWorkflow agentUseWorkflow = new AgentUseWorkflow();

        agentUseWorkflow.setFlowId( dto.getFlowId() );
        agentUseWorkflow.setFlowVariables( dto.getFlowVariables() );
        agentUseWorkflow.setFlowDefinition( dto.getFlowDefinition() );
        agentUseWorkflow.setFlowExtensions( dto.getFlowExtensions() );
        agentUseWorkflow.setFlowStartVariables( dto.getFlowStartVariables() );
        agentUseWorkflow.setFlowEndVariables( dto.getFlowEndVariables() );
        agentUseWorkflow.setFlowGuide( dto.getFlowGuide() );
        agentUseWorkflow.setFlowStatus( dto.getFlowStatus() );
        agentUseWorkflow.setAgentId( dto.getAgentId() );
        agentUseWorkflow.setVersionId( dto.getVersionId() );

        return agentUseWorkflow;
    }

    @Override
    public AgentUseWorkflow toUpdateEntity(AgentUseWorkflowUpdateDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentUseWorkflow agentUseWorkflow = new AgentUseWorkflow();

        agentUseWorkflow.setFlowId( dto.getFlowId() );
        agentUseWorkflow.setFlowVariables( dto.getFlowVariables() );
        agentUseWorkflow.setFlowDefinition( dto.getFlowDefinition() );
        agentUseWorkflow.setFlowExtensions( dto.getFlowExtensions() );
        agentUseWorkflow.setFlowStartVariables( dto.getFlowStartVariables() );
        agentUseWorkflow.setFlowEndVariables( dto.getFlowEndVariables() );
        agentUseWorkflow.setFlowGuide( dto.getFlowGuide() );
        agentUseWorkflow.setFlowStatus( dto.getFlowStatus() );
        agentUseWorkflow.setAgentId( dto.getAgentId() );
        agentUseWorkflow.setVersionId( dto.getVersionId() );

        return agentUseWorkflow;
    }

    @Override
    public List<AgentUseWorkflowListVO> toVoList(List<AgentUseWorkflow> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AgentUseWorkflowListVO> list = new ArrayList<AgentUseWorkflowListVO>( entities.size() );
        for ( AgentUseWorkflow agentUseWorkflow : entities ) {
            list.add( agentUseWorkflowToAgentUseWorkflowListVO( agentUseWorkflow ) );
        }

        return list;
    }

    @Override
    public AgentUseWorkflowVO toVo(AgentUseWorkflow dto) {
        if ( dto == null ) {
            return null;
        }

        AgentUseWorkflowVO agentUseWorkflowVO = new AgentUseWorkflowVO();

        agentUseWorkflowVO.setFlowId( dto.getFlowId() );
        agentUseWorkflowVO.setFlowVariables( dto.getFlowVariables() );
        agentUseWorkflowVO.setFlowDefinition( dto.getFlowDefinition() );
        agentUseWorkflowVO.setFlowExtensions( dto.getFlowExtensions() );
        agentUseWorkflowVO.setFlowStartVariables( dto.getFlowStartVariables() );
        agentUseWorkflowVO.setFlowEndVariables( dto.getFlowEndVariables() );
        agentUseWorkflowVO.setFlowGuide( dto.getFlowGuide() );
        agentUseWorkflowVO.setFlowStatus( dto.getFlowStatus() );
        agentUseWorkflowVO.setAgentId( dto.getAgentId() );
        agentUseWorkflowVO.setVersionId( dto.getVersionId() );
        agentUseWorkflowVO.setCreateTime( dto.getCreateTime() );
        agentUseWorkflowVO.setUpdateTime( dto.getUpdateTime() );

        return agentUseWorkflowVO;
    }

    protected AgentUseWorkflowListVO agentUseWorkflowToAgentUseWorkflowListVO(AgentUseWorkflow agentUseWorkflow) {
        if ( agentUseWorkflow == null ) {
            return null;
        }

        AgentUseWorkflowListVO agentUseWorkflowListVO = new AgentUseWorkflowListVO();

        agentUseWorkflowListVO.setFlowId( agentUseWorkflow.getFlowId() );
        agentUseWorkflowListVO.setFlowVariables( agentUseWorkflow.getFlowVariables() );
        agentUseWorkflowListVO.setFlowDefinition( agentUseWorkflow.getFlowDefinition() );
        agentUseWorkflowListVO.setFlowExtensions( agentUseWorkflow.getFlowExtensions() );
        agentUseWorkflowListVO.setFlowStartVariables( agentUseWorkflow.getFlowStartVariables() );
        agentUseWorkflowListVO.setFlowEndVariables( agentUseWorkflow.getFlowEndVariables() );
        agentUseWorkflowListVO.setFlowGuide( agentUseWorkflow.getFlowGuide() );
        agentUseWorkflowListVO.setFlowStatus( agentUseWorkflow.getFlowStatus() );
        agentUseWorkflowListVO.setAgentId( agentUseWorkflow.getAgentId() );
        agentUseWorkflowListVO.setVersionId( agentUseWorkflow.getVersionId() );
        agentUseWorkflowListVO.setCreateTime( agentUseWorkflow.getCreateTime() );
        agentUseWorkflowListVO.setUpdateTime( agentUseWorkflow.getUpdateTime() );

        return agentUseWorkflowListVO;
    }
}
