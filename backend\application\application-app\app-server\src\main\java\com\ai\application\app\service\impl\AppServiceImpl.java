package com.ai.application.app.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.github.pagehelper.PageInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ai.application.app.mapper.AppMapper;
import com.ai.application.app.api.entity.App;
import com.ai.application.app.service.IAppService;
import com.ai.application.app.api.dto.AppDTO;
import com.ai.application.app.api.dto.query.AppQueryDTO;
import com.ai.application.app.api.vo.AppVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Date;

/**
 * 应用表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Service
public class AppServiceImpl implements IAppService{

    @Resource
    private AppMapper appMapper;

    @Transactional(readOnly = true)
    @Override
    public PageInfo<AppVO> page(AppQueryDTO queryDto) {
        QueryWrapper<App> queryWrapper = this.buildQuery(queryDto);
        Page<App> page = new Page<>(queryDto.getPageNo(), queryDto.getPageSize());
        Page<App> result = this.appMapper.selectPage(page, queryWrapper);
        return PageInfo.of(BeanUtil.copyToList(result.getRecords(), AppVO.class));
    }

    @Transactional(readOnly = true)
    @Override
    public List<AppVO> list(AppQueryDTO queryDto) {
        QueryWrapper<App> queryWrapper = this.buildQuery(queryDto);
        List<App> apps = appMapper.selectList(queryWrapper);
        return BeanUtil.copyToList(apps, AppVO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void save(AppDTO dto) {
        dto.setAppId(null);
        App entity = BeanUtil.copyProperties(dto, App.class);
        entity.setCreateTime(new Date());
        appMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AppDTO dto) {
        BusinessAssertUtil.notNull(dto.getAppId(), "id不能为空");
        App entity = appMapper.selectById(dto.getAppId());
        BusinessAssertUtil.notNull(entity, "找不到ID为 " + dto.getAppId() + " 的记录");
        BeanUtil.copyProperties(dto, entity, CopyOptions.create().setIgnoreNullValue(true));
        appMapper.updateById(entity);
    }

    @Transactional(readOnly = true)
    @Override
    public AppVO get(Integer appId) {
        BusinessAssertUtil.notNull(appId, "id不能为空");
        App entity = appMapper.selectById(appId);
        BusinessAssertUtil.notNull(entity, "找不到appId为 " + appId + " 的记录");
        return BeanUtil.copyProperties(entity, AppVO.class);
    }

    private QueryWrapper<App> buildQuery(AppQueryDTO queryDto) {
        QueryWrapper<App> queryWrapper = new QueryWrapper<>();
        return queryWrapper;
    }
}