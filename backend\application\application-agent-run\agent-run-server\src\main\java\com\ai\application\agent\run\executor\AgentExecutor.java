package com.ai.application.agent.run.executor;

import com.ai.application.agent.base.api.dto.AgentChatDTO;
import com.ai.application.agent.base.api.feign.IAgentChatClient;
import com.ai.application.agent.base.api.vo.AgentChatVO;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.excutor.NodeExecutor;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Agent 执行器 - 用于工作流中调用智能体
 */
@Slf4j
@Component
@AllArgsConstructor
public class AgentExecutor implements BaseExecutor, NodeExecutor {

    private final IAgentChatClient agentChatClient;

    @Override
    public Map<String, Object> execute(ExecutionContext executionContext) {
        log.info("AgentExecutor execute start, context: {}", executionContext);

        // 获取参数
        String agentSn = executionContext.getParameterAsString("agentSn");
        String agentType = executionContext.getParameterAsString("agentType");
        String msgContent = executionContext.getParameterAsString("msgContent");
        String processId = executionContext.getProcessId();
        String authorization = executionContext.getAuthorization();
        boolean isDebug = executionContext.isDebugRun();

        // 参数校验
        if (StringUtils.isBlank(agentSn)) {
            throw new ServiceException("agentSn 不能为空");
        }
        if (StringUtils.isBlank(agentType)) {
            throw new ServiceException("agentType 不能为空");
        }
        if (StringUtils.isBlank(msgContent)) {
            throw new ServiceException("msgContent 不能为空");
        }

        // 构建请求
        AgentChatDTO chatDto = AgentChatDTO.builder()
                .agentSn(agentSn)
                .agentType(agentType)
                .msgContent(msgContent)
                .msgType("text")
                .delayInMs(20L)
                .fromCode("Process")
                .processId(processId)
                .debug(isDebug)
                .build();

        log.info("AgentExecutor request: {}", JsonUtils.toJsonString(chatDto));

        try {
            // 调用智能体
            ResultVo<AgentChatVO> result = agentChatClient.requestLLMWithAgent(chatDto, authorization);
            
            if (result == null || !Objects.equals(result.getCode(), 0)) {
                String errorMsg = result != null ? result.getMessage() : "调用智能体失败";
                throw new ServiceException("调用智能体失败: " + errorMsg);
            }

            AgentChatVO agentChatVo = result.getData();
            if (agentChatVo == null) {
                throw new ServiceException("智能体返回结果为空");
            }

            // 解析返回结果
            String reply = parseMsgContent(agentChatVo);
            JsonNode reference = null;
            if (agentChatVo.getContent() != null) {
                reference = agentChatVo.getContent().get("pages");
            }

            // 构建输出结果
            Map<String, Object> outputResult = new HashMap<>();
            outputResult.put("message", reply);
            outputResult.put("reply", reply);
            outputResult.put("sessionSn", agentChatVo.getSessionSn());
            outputResult.put("msgSn", agentChatVo.getMsgSn());
            outputResult.put("success", agentChatVo.getSuccess());
            
            if (reference != null) {
                outputResult.put("reference", reference);
            }

            log.info("AgentExecutor execute success, result: {}", JsonUtils.toJsonString(outputResult));
            return outputResult;

        } catch (Exception e) {
            log.error("AgentExecutor execute error", e);
            throw new ServiceException("智能体执行失败: " + e.getMessage());
        }
    }

    /**
     * 解析消息内容
     */
    private String parseMsgContent(AgentChatVO agentChatVo) {
        if (agentChatVo.getContent() != null) {
            JsonNode answerNode = agentChatVo.getContent().get("answer");
            if (answerNode != null && answerNode.isTextual()) {
                return answerNode.asText();
            }
        }
        
        // 如果没有content或answer，返回reply字段
        return StringUtils.defaultIfBlank(agentChatVo.getReply(), "");
    }

    @Override
    public String getId() {
        return "AGENT";
    }

    @Override
    public void preValidate(ExecutionContext executionContext) {
        String agentSn = executionContext.getParameterAsString("agentSn");
        String agentType = executionContext.getParameterAsString("agentType");
        String msgContent = executionContext.getParameterAsString("msgContent");

        if (StringUtils.isBlank(agentSn)) {
            throw new ServiceException("agentSn 参数不能为空");
        }
        if (StringUtils.isBlank(agentType)) {
            throw new ServiceException("agentType 参数不能为空");
        }
        if (StringUtils.isBlank(msgContent)) {
            throw new ServiceException("msgContent 参数不能为空");
        }
    }

    // 实现 NodeExecutor 接口，用于工作流引擎
    @Override
    public void execute(WorkflowContext context) {
        // 从工作流上下文中提取参数
        Map<String, Object> globalVars = context.getGlobalVars();
        
        // 创建执行上下文
        DefaultExecutionContext executionContext = new DefaultExecutionContext(
                (String) globalVars.get("processId"),
                context.getWorkflowInstanceId() != null ? context.getWorkflowInstanceId().toString() : null,
                context.getCurrentNodeKey(),
                context.getCurrentNodeKey(),
                globalVars,
                null, // 用户信息需要从上下文中获取
                (String) globalVars.get("authorization"),
                false
        );

        // 执行并将结果写回工作流上下文
        Map<String, Object> result = execute(executionContext);
        result.forEach(context::setVar);
    }

    @Override
    public String getType() {
        return "AGENT";
    }
}
