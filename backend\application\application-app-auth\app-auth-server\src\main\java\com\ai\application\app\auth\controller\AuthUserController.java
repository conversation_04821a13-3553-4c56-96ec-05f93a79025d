package com.ai.application.app.auth.controller;

import com.ai.application.app.auth.api.dto.UserLoginDTO;
import com.ai.application.app.auth.api.vo.GrantVO;
import com.ai.application.app.auth.api.vo.UserInfoVO;
import com.ai.application.app.auth.granter.PasswordTokenGranter;
import com.ai.application.app.auth.granter.TokenGranterBuilder;
import com.ai.application.app.auth.granter.TokenParameter;
import com.ai.application.app.auth.service.IAuthUserService;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Tag(name = "用户授权登录", description = "用户授权相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1/user")
public class AuthUserController {
    @Resource
    private IAuthUserService userService;

    @Operation(summary = "用户登录")
    @PostMapping(value = "/login")
    public ResultVo<GrantVO> token(@Validated @RequestBody UserLoginDTO dto) {
        TokenParameter tokenParameter = new TokenParameter();
        tokenParameter.getArgs()
                .set("appId", dto.getAppId())
                .set("loginName", dto.getLoginName())
                .set("password", dto.getPassword())
                .set("tenantSn", dto.getTenantSn());
        return ResultVo.data(TokenGranterBuilder.getGranter(PasswordTokenGranter.GRANT_TYPE).grant(tokenParameter));
    }

    @Operation(summary = "退出登录")
    @PostMapping(value = "/logout")
    public ResultVo<Boolean> logout() {
        userService.logout();
        return ResultVo.data(Boolean.TRUE);
    }

    /**
     * 获取用户登录后的信息
     * @return
     */
    @Operation(summary = "用户信息")
    @GetMapping(value = "/info")
    public ResultVo<UserInfoVO> info() {
        return ResultVo.data(userService.getUserInfo());
    }
}
