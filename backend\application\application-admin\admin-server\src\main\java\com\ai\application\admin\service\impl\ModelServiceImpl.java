package com.ai.application.admin.service.impl;

import com.ai.application.admin.api.dto.ModelAddDTO;
import com.ai.application.admin.api.dto.ModelUpdateDTO;
import com.ai.application.admin.api.dto.ResourceAddReqDTO;
import com.ai.application.admin.api.dto.query.ModelQueryDTO;
import com.ai.application.admin.api.entity.ModelInfo;
import com.ai.application.admin.api.entity.ModelSupplier;
import com.ai.application.admin.api.entity.ModelTest;
import com.ai.application.admin.api.enums.ResourceTypeEnum;
import com.ai.application.admin.api.mapstruct.ModelMapstruct;
import com.ai.application.admin.api.vo.ModelBaseVO;
import com.ai.application.admin.api.vo.ModelInfoDetailVO;
import com.ai.application.admin.api.vo.ModelSupplierGroupVO;
import com.ai.application.admin.mapper.ModelInfoMapper;
import com.ai.application.admin.mapper.ModelSupplierMapper;
import com.ai.application.admin.mapper.ModelTestMapper;
import com.ai.application.admin.service.IModelService;
import com.ai.application.admin.service.IResourceService;
import com.ai.application.base.model.api.dto.ModelLLMChatDTO;
import com.ai.application.base.model.api.dto.ModelLLMChatSupplierDTO;
import com.ai.application.base.model.api.feign.ILLMClient;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.reactive.ReactiveFeignClientFactory;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.util.validator.AssertUtil;
import com.ai.framework.core.vo.ResultVo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 模型信息表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Service
@Slf4j
@AllArgsConstructor
public class ModelServiceImpl implements IModelService {
    private final ModelInfoMapper modelInfoMapper;
    private final ModelMapstruct modelMapstruct;
    private final ModelTestMapper modelTestMapper;
    private final ModelSupplierMapper modelSupplierMapper;
    private final ReactiveFeignClientFactory factory;
    private final IResourceService resourceService;

    @Transactional(readOnly = true)
    @Override
    public List<ModelSupplierGroupVO> list(ModelQueryDTO queryDto) {
        log.info("模型列表,参数={}", JsonUtils.toJsonString(queryDto));
        QueryWrapper<ModelSupplier> querySupplierWrapper = new QueryWrapper<>();
        querySupplierWrapper.lambda().eq(ModelSupplier::getSupplierStatus, 1);
        List<ModelSupplier> modelSuppliers = modelSupplierMapper.selectList(querySupplierWrapper);
        if (CollectionUtils.isEmpty(modelSuppliers)) {
            return Lists.newArrayList();
        }

        List<ModelSupplierGroupVO> resList = modelMapstruct.toSupplierList(modelSuppliers);

        //模型查询
        QueryWrapper<ModelInfo> queryWrapper = this.buildQuery(queryDto);
        List<ModelInfo> modelInfos = this.modelInfoMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(modelInfos)) {
            return resList;
        }

        List<Integer> modelIds = modelInfos.stream().map(ModelInfo::getModelId).toList();
        List<ModelBaseVO> baseVoList = modelMapstruct.toBaseVoList(modelInfos);

        //设置测试状态
        if(CollectionUtils.isNotEmpty(modelIds)) {
            List<Integer> ids = modelTestMapper.queryMaxTestIdList(UserContext.getTenantId(), modelIds);
            if (CollectionUtils.isNotEmpty(ids)) {
                List<ModelTest> modelTests = modelTestMapper.selectBatchIds(ids);
                baseVoList.forEach(a -> {
                    modelTests.stream().filter(b -> b.getModelId().equals(b.getModelId())).findFirst().ifPresent(b -> {
                        a.setTestStatus(b.getTestStatus());
                    });
                });
            }
        }
        Map<Integer, List<ModelBaseVO>> mapSupplier = baseVoList.stream().collect(Collectors.groupingBy(ModelBaseVO::getSupplierId));
        resList.forEach(supplier -> {
            supplier.setListModel(mapSupplier.get(supplier.getSupplierId()));
            supplier.setSupplierId(null);
        });
        return resList;
    }

    @Override
    public void add(ModelAddDTO dto) {
        log.info("模型新增,参数={}", JsonUtils.toJsonString(dto));
        ModelSupplier modelSupplier = modelSupplierMapper.getModelSupplierBySn(dto.getSupplierSn());
        AssertUtil.isNotNull(modelSupplier, "供应商不存在");

        ModelInfo model = modelInfoMapper.getModelInfoByModelEngine(modelSupplier.getSupplierId(), dto.getModelEngine());
        AssertUtil.isFalse(Objects.nonNull(model), "模型已存在");

        //调用模型测试
        ILLMClient modelClient = factory.createClient(ILLMClient.class,
                ServiceConstant.BASE_MODEL);
        ModelLLMChatSupplierDTO chatDTO = new ModelLLMChatSupplierDTO();
        chatDTO.setSupplierSn(modelSupplier.getSupplierSn());
        chatDTO.setMessage("Hi");
        ResultVo<String> chat = modelClient.chatSupplier(chatDTO);
        log.info("模型测试结果={}", JsonUtils.toJsonString(chat));
        AssertUtil.isTrue(chat.isSuccess(), "模型测试失败,请检查配置");

        ModelInfo entity = modelMapstruct.toEntity(dto);
        String modelSn = UUIDUtil.genRandomSn("model");
        entity.setModelSn(modelSn);
        if (StringUtils.isBlank(entity.getModelName())) {
            entity.setModelName(entity.getModelEngine());
        }
        entity.setSupplierId(modelSupplier.getSupplierId());
        entity.setModelStatus(1);
        modelInfoMapper.insert(entity);

        //保存测试结果
        ModelTest modelTest = new ModelTest();
        modelTest.setModelId(entity.getModelId());
        modelTest.setTestStatus(chat.isSuccess() ? 1 : 0);
        modelTest.setTestConfig(JsonUtils.toJsonString(chatDTO));
        modelTest.setTestResult(JsonUtils.toJsonString(chat));
        modelTest.setTenantId(0);
        modelTestMapper.insert(modelTest);

        //添加资源
        ResourceAddReqDTO resourceAddReqDTO = new ResourceAddReqDTO();
        resourceAddReqDTO.setResourceTenantId(0);
        resourceAddReqDTO.setResourceType(ResourceTypeEnum.MODEL.getCode());
        resourceAddReqDTO.setResourceObjectId(entity.getModelId());
        resourceService.addResource(resourceAddReqDTO);
    }

    @Override
    public ModelInfoDetailVO detail(String modelSn) {
        log.info("模型详情,参数={}", modelSn);
        ModelInfo model = modelInfoMapper.selectByModelSn(modelSn);
        AssertUtil.isNotNull(model, "模型不存在");
        ModelInfoDetailVO detailVo = modelMapstruct.toDetailVo(model);

        //设置测试状态
        List<Integer> ids = modelTestMapper.queryMaxTestIdList(0, Collections.singletonList(model.getModelId()));
        if(CollectionUtils.isNotEmpty(ids)) {
            List<ModelTest> modelTests = modelTestMapper.selectBatchIds(ids);
            if (CollectionUtils.isNotEmpty(modelTests)) {
                detailVo.setTestStatus(modelTests.get(0).getTestStatus());
            }
        }
        return detailVo;
    }

    @Override
    public void update(ModelUpdateDTO dto) {
        log.info("模型修改,参数={}", JsonUtils.toJsonString(dto));
        ModelInfo model = modelInfoMapper.selectByModelSn(dto.getModelSn());
        AssertUtil.isNotNull(model, "模型不存在");

        //调用模型测试
        modelTest(dto.getModelSn());

        model.setModelDesc(dto.getModelDesc());
        model.setModelConfig(dto.getModelConfig());
        model.setModelToolcall(dto.getModelToolcall());
        model.setModelName(dto.getModelName());
        if (StringUtils.isBlank(model.getModelName())) {
            model.setModelName(model.getModelEngine());
        }
        model.setModelType(dto.getModelType());
        model.setModelStatus(1);
        model.setUpdateTime(null);
        modelInfoMapper.updateById(model);
    }

    @Override
    public void modelTest(String modelSn) {
        log.info("模型测试,参数={}", modelSn);
        ModelInfo model = modelInfoMapper.selectByModelSn(modelSn);
        AssertUtil.isNotNull(model, "模型不存在");

        //调用模型测试
        ILLMClient modelClient = factory.createClient(ILLMClient.class,
                ServiceConstant.BASE_MODEL);
        ModelLLMChatDTO chatDTO = new ModelLLMChatDTO();
        chatDTO.setModelSn(modelSn);
        chatDTO.setMessage("Hi");
        ResultVo<String> chat = modelClient.chat(chatDTO);
        log.info("模型测试结果={}", JsonUtils.toJsonString(chat));
        //保存测试结果
        ModelTest modelTest = new ModelTest();
        modelTest.setModelId(model.getModelId());
        modelTest.setTestStatus(chat.isSuccess() ? 1 : 0);
        modelTest.setTestConfig(JsonUtils.toJsonString(chatDTO));
        modelTest.setTestResult(JsonUtils.toJsonString(chat));
        modelTest.setTenantId(0);
        modelTestMapper.insert(modelTest);
        AssertUtil.isTrue(chat.isSuccess(), "模型测试失败");
    }

    private QueryWrapper<ModelInfo> buildQuery(ModelQueryDTO queryDto) {
        QueryWrapper<ModelInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ModelInfo::getModelStatus, 1);
        return queryWrapper;
    }
}