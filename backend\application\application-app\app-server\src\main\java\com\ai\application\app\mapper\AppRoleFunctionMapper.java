package com.ai.application.app.mapper;

import com.ai.application.app.api.entity.AppRoleFunction;
import com.ai.application.app.api.vo.AppUserVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.application.app.api.vo.AppRoleFunctionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 应用角色功能表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Mapper
public interface AppRoleFunctionMapper extends BaseMapper<AppRoleFunction> {
    /**
     * 查询应用角色功能表
     *
     * @return
     */
    List<AppRoleFunctionVO> selectAppRoleFunctionList();

    @Select("select * from app_role_function where role_id = #{roleId} and rf_status >= 0")
    List<AppRoleFunction> queryFunctionListByRoleId(@Param("roleId") Integer roleId);

    List<AppRoleFunctionVO> queryFunctionListByRoleIds(@Param("roleIds") List<Integer> roleIds);
}
