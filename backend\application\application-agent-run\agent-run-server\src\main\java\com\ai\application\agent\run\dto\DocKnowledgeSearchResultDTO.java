package com.ai.application.agent.run.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 文档知识检索结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocKnowledgeSearchResultDTO {

    /**
     * 文档列表
     */
    private List<String> docFile;

    /**
     * 下个节点
     */
    private String target;

    /**
     * 匹配结果：True/False
     */
    private String matchResult;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 文档信息DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DocFileInfoDTO {
        /**
         * 文件ID
         */
        private Long fileId;

        /**
         * 文件名
         */
        private String fileName;

        /**
         * 文件编号
         */
        private String fileSn;

        /**
         * 数据类型
         */
        private String dataType;

        /**
         * 文档标签
         */
        private String documentTag;

        /**
         * 文档摘要
         */
        private String documentSummary;

        /**
         * 文档后缀
         */
        private String documentSuffix;

        /**
         * 相似度分数
         */
        private Double score;

        /**
         * 创建时间
         */
        private Long createTime;
    }
}
