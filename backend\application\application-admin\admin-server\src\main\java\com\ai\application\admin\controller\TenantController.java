package com.ai.application.admin.controller;

import com.ai.application.admin.api.dto.*;
import com.ai.application.admin.api.dto.query.TenantQueryDTO;
import com.ai.application.admin.api.dto.query.TenantQueryPageDTO;
import com.ai.application.admin.api.vo.AdminResourceVO;
import com.ai.application.admin.api.vo.TenantDetailVO;
import com.ai.application.admin.api.vo.TenantSimpleVO;
import com.ai.application.admin.api.vo.TenantVO;
import com.ai.application.admin.service.IAdminTenantService;
import com.ai.framework.core.vo.ResultVo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * 租户表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Tag(name = "租户表", description = "租户表相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1/tenant")
@AllArgsConstructor
public class TenantController {

    private final IAdminTenantService tenantService;

    @Operation(summary = "租户表 分页查询", description = "查询所有租户表 信息")
    @ApiResponse(responseCode = "0", description = "成功",
            content = @Content(schema = @Schema(implementation = TenantVO.class)))
    @RequestMapping(value = "/page", method = {RequestMethod.POST})
    public ResultVo<PageInfo<TenantVO>> page(@Validated @RequestBody TenantQueryPageDTO queryDto){
        return ResultVo.data(tenantService.page(queryDto));
    }

    @Operation(summary = "有效租户列表", description = "查询所有有效的租户列表")
    @ApiResponse(responseCode = "0", description = "成功",
            content = @Content(schema = @Schema(implementation = TenantSimpleVO.class)))
    @RequestMapping(value = "/valids", method = {RequestMethod.GET})
    public ResultVo<List<TenantSimpleVO>> valids(){
        return ResultVo.data(tenantService.valids());
    }

    @Operation(summary = "租户表新增")
    @PostMapping("/create")
    public ResultVo<Void> save(@Validated @RequestBody TenantAddDTO dto){
        tenantService.save(dto);
        return ResultVo.success("保存成功");
    }

    @Operation(summary = "租户表修改")
    @PostMapping(value = "/update")
    public ResultVo<Void> update(@Validated @RequestBody TenantUpdateDTO dto){
        tenantService.update(dto);
        return ResultVo.success("修改成功");
    }

    @Operation(summary = "租户表 查询详情", description = "根据ID租户表 信息")
    @ApiResponse(responseCode = "0", description = "成功",
            content = @Content(schema = @Schema(implementation = TenantVO.class)))
    @GetMapping("/get")
    public ResultVo<TenantVO> get(@RequestParam Long id){
        return ResultVo.data(tenantService.get(id));
    }

    @Operation(summary = "租户表-启用/禁用", description = "根据租户编码设置启用/禁用")
    @PostMapping("/{tenantSn}/enable")
    public ResultVo<Boolean> updateStatus(@PathVariable("tenantSn") String tenantSn, @RequestBody @Validated TenantStatusDTO dto) {
        tenantService.saveTenantStatus(tenantSn, dto.getEnable());
        return ResultVo.success("状态设置成功");
    }

    @Operation(summary = "租户表-查询详情", description = "根据租户表编号应用用户表 信息")
    @ApiResponse(responseCode = "0", description = "成功",
            content = @Content(schema = @Schema(implementation = TenantDetailVO.class)))
    @GetMapping("/{tenantSn}/detail")
    public ResultVo<TenantDetailVO> detail(@PathVariable("tenantSn") String tenantSn) {
        return ResultVo.data(tenantService.detail(tenantSn));
    }

    @Operation(summary = "租户表-修改密码", description = "根据用户编码修改密码")
    @PostMapping("/{tenantSn}/updatePassword")
    public ResultVo<Void> updatePassword(@PathVariable("tenantSn") String tenantSn, @RequestBody @Validated TenantUpdatePasswordDTO dto) {
        tenantService.updatePassword(tenantSn, dto);
        return ResultVo.success("修改密码成功");
    }

    @Operation(summary = "租户表-智能体查询", description = "租户表-智能体查询")
    @GetMapping("/queryAgentList")
    public ResultVo<List<AdminResourceVO>> queryAgentList() {
        return ResultVo.data(tenantService.queryAgentList());
    }

    @Operation(summary = "租户表-模型查询", description = "租户表-模型查询")
    @GetMapping("/queryModelList")
    public ResultVo<List<AdminResourceVO>> queryModelList() {
        return ResultVo.data(tenantService.queryModelList());
    }
}