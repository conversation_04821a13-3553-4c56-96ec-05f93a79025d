package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.dto.AgentRunNodeListDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ai.application.agent.base.mapper.AgentRunNodeMapper;
import com.ai.application.agent.base.api.entity.AgentRunNode;
import com.ai.application.agent.base.service.IAgentRunNodeService;
import com.ai.application.agent.base.api.dto.AgentRunNodeDTO;
import com.ai.application.agent.base.api.vo.AgentRunNodeVO;
import com.ai.application.agent.base.api.mapstruct.AgentRunNodeMapstruct;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Date;

/**
 * 工作流节点运行记录表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
public class AgentRunNodeServiceImpl implements IAgentRunNodeService{

    @Resource
    private AgentRunNodeMapper agentRunNodeMapper;

    @Resource
    private AgentRunNodeMapstruct agentRunNodeMapstruct;

    @Transactional(readOnly = true)
    @Override
    public List<AgentRunNodeVO> list(AgentRunNodeListDTO queryDto) {
        LambdaQueryWrapper<AgentRunNode> queryWrapper = this.buildQuery(queryDto);
        return agentRunNodeMapstruct.toVoList(this.agentRunNodeMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(AgentRunNodeDTO dto) {
        dto.setRunId(null);
        AgentRunNode entity = agentRunNodeMapstruct.toEntity(dto);
        entity.setCreateTime(new Date());

        agentRunNodeMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AgentRunNodeDTO dto) {
        BusinessAssertUtil.notNull(dto.getNodeRunId(), "RunId不能为空");

        AgentRunNode entity = agentRunNodeMapper.selectById(dto.getNodeRunId());
        BusinessAssertUtil.notNull(entity, "找不到ID为 " + dto.getNodeRunId() + " 的记录");

        AgentRunNode entityList = agentRunNodeMapstruct.toEntity(dto);
        entityList.setUpdateTime(new Date());
        agentRunNodeMapper.updateById(entityList);
    }

    @Transactional(readOnly = true)
    @Override
    public AgentRunNodeVO get(Integer id) {
        BusinessAssertUtil.notNull(id, "RunId不能为空");

        AgentRunNode entity = agentRunNodeMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到RunId为 " + id + " 的记录");

        return agentRunNodeMapstruct.toVo(entity);
    }

    private LambdaQueryWrapper<AgentRunNode> buildQuery(AgentRunNodeListDTO queryDto) {
        LambdaQueryWrapper<AgentRunNode> queryWrapper = new LambdaQueryWrapper<>();
        return queryWrapper;
    }
}