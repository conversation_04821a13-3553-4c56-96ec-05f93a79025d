package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.enums.AgentPageSourceEnum;
import com.ai.application.agent.base.service.IAgentCommService;
import com.ai.application.tenant.authorize.api.dto.ResourceGrantReqDTO;
import com.ai.application.tenant.authorize.api.enums.GrantObjectTypeEnum;
import com.ai.application.tenant.authorize.api.enums.GrantTypeEnum;
import com.ai.application.tenant.authorize.api.enums.ResourceTypeEnum;
import com.ai.application.tenant.authorize.api.feign.ITenantAuthorizeClient;
import com.ai.application.tenant.authorize.api.vo.ResourceVO;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.util.validator.AssertUtil;
import com.ai.framework.core.vo.ResultVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
@Slf4j
public class AgentCommServiceImpl implements IAgentCommService {
    private final ITenantAuthorizeClient tenantAuthorizeClient;

    /**
     * 根据页面来源获取授权的智能体id集合
     * @param pageSource 智能体页面来源:10-管理,20-市场,30-工作区
     * @return
     */
    @Override
    public List<Integer> queryGrantAgentIdList(Integer pageSource){
        log.info("根据页面来源获取授权的智能体id集合,pageSource={}",pageSource);
        Integer grantObjectType = GrantObjectTypeEnum.USER.getCode();
        Integer grantObjectId = UserContext.getUserId();
        List<Integer> grantTypes = Lists.newArrayList();
        if(AgentPageSourceEnum.MANAGE.getCode().equals(pageSource)){
            grantObjectType = GrantObjectTypeEnum.TENANT.getCode();
            grantObjectId = UserContext.getTenantId();
        }

        //市场-智能体列表：公共市场：管理端统一分发给租户的智能体，再由租户进行使用权限授权，有使用权限的用户可在公共市场查看到该智能体
        if(AgentPageSourceEnum.MARKET.getCode().equals(pageSource)){
            grantTypes.add(GrantTypeEnum.USE.getCode());
        }

        //工作区-智能体列表：可查看有协作/管理/所有权的全部智能体
        if(AgentPageSourceEnum.WORKSPACE.getCode().equals(pageSource)){
            grantTypes.add(GrantTypeEnum.OWNER.getCode());
            grantTypes.add(GrantTypeEnum.MANAGE.getCode());
            grantTypes.add(GrantTypeEnum.COORDINATION.getCode());
        }

        //获取授权给当前用户的智能体id集合
        ResourceGrantReqDTO resourceGrantReqDTO = new ResourceGrantReqDTO();
        resourceGrantReqDTO.setResourceType(ResourceTypeEnum.AGENT.getCode());
        resourceGrantReqDTO.setGrantObjectType(grantObjectType);
        resourceGrantReqDTO.setGrantObjectId(grantObjectId);
        resourceGrantReqDTO.setGrantTypes(grantTypes);
        ResultVo<List<ResourceVO>> listResultVo = tenantAuthorizeClient.queryGrantResourceList(resourceGrantReqDTO);
        AssertUtil.isTrue(listResultVo.isSuccess(),"获取智能体授权资源失败");
        List<ResourceVO> listResource = listResultVo.getData();
        if (CollectionUtils.isEmpty(listResource)) {
            return Lists.newArrayList();
        }
        return listResource.stream().map(ResourceVO::getResourceObjectId).toList();
    }

}
