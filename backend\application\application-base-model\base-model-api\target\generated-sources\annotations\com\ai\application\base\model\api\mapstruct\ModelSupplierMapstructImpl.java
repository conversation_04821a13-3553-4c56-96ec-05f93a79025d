package com.ai.application.base.model.api.mapstruct;

import com.ai.application.base.model.api.dto.ModelSupplierDTO;
import com.ai.application.base.model.api.entity.ModelSupplier;
import com.ai.application.base.model.api.vo.ModelSupplierVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T09:54:03+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class ModelSupplierMapstructImpl implements ModelSupplierMapstruct {

    @Override
    public ModelSupplier toEntity(ModelSupplierDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ModelSupplier modelSupplier = new ModelSupplier();

        modelSupplier.setSupplierId( dto.getSupplierId() );
        modelSupplier.setSupplierSn( dto.getSupplierSn() );
        modelSupplier.setSupplierName( dto.getSupplierName() );
        modelSupplier.setSupplierLogo( dto.getSupplierLogo() );
        modelSupplier.setSupplierDesc( dto.getSupplierDesc() );
        modelSupplier.setSupplierWebUrl( dto.getSupplierWebUrl() );
        modelSupplier.setSupplierApiUrl( dto.getSupplierApiUrl() );
        modelSupplier.setSupplierStatus( dto.getSupplierStatus() );
        modelSupplier.setSupplierWeight( dto.getSupplierWeight() );
        modelSupplier.setCreateTime( dto.getCreateTime() );
        modelSupplier.setUpdateTime( dto.getUpdateTime() );

        return modelSupplier;
    }

    @Override
    public List<ModelSupplier> toEntityList(List<ModelSupplierDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<ModelSupplier> list = new ArrayList<ModelSupplier>( dtolist.size() );
        for ( ModelSupplierDTO modelSupplierDTO : dtolist ) {
            list.add( toEntity( modelSupplierDTO ) );
        }

        return list;
    }

    @Override
    public ModelSupplierVO toVo(ModelSupplier entity) {
        if ( entity == null ) {
            return null;
        }

        ModelSupplierVO modelSupplierVO = new ModelSupplierVO();

        modelSupplierVO.setSupplierId( entity.getSupplierId() );
        modelSupplierVO.setSupplierSn( entity.getSupplierSn() );
        modelSupplierVO.setSupplierName( entity.getSupplierName() );
        modelSupplierVO.setSupplierLogo( entity.getSupplierLogo() );
        modelSupplierVO.setSupplierDesc( entity.getSupplierDesc() );
        modelSupplierVO.setSupplierWebUrl( entity.getSupplierWebUrl() );
        modelSupplierVO.setSupplierApiUrl( entity.getSupplierApiUrl() );
        modelSupplierVO.setSupplierStatus( entity.getSupplierStatus() );
        modelSupplierVO.setSupplierWeight( entity.getSupplierWeight() );
        modelSupplierVO.setCreateTime( entity.getCreateTime() );
        modelSupplierVO.setUpdateTime( entity.getUpdateTime() );

        return modelSupplierVO;
    }

    @Override
    public List<ModelSupplierVO> toVoList(List<ModelSupplier> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ModelSupplierVO> list = new ArrayList<ModelSupplierVO>( entities.size() );
        for ( ModelSupplier modelSupplier : entities ) {
            list.add( toVo( modelSupplier ) );
        }

        return list;
    }
}
