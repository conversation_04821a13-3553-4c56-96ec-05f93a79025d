package com.ai.application.admin.api.dto.query;

import com.ai.framework.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 租户扩展配置表 查询条件
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@Schema(name = "租户扩展配置表QueryDTO")
public class TenantExtendQueryDTO extends PageParam {

    /**
     * 参数name
     */
    @Schema(description = "参数name")
    private String itemName;

}