package com.ai.application.agent.base.api.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 智能体表
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@Schema(name = "")
public class AgentVO {
    /**
     * 智能体id
     */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
     * 智能体sn
     */
    @Schema(description = "智能体sn")
    private String agentSn;

    /**
     * 智能体名称
     */
    @Schema(description = "智能体名称")
    private String agentName;

    /**
     * 智能体描述
     */
    @Schema(description = "智能体描述")
    private String agentDesc;

    /**
     * 智能体类型: 10:对话流, 20:工作流, 30:master
     */
    @Schema(description = "智能体类型: 10:对话流, 20:工作流, 30:master")
    private Integer agentType;

    /**
     * 智能体元信息:logo,icon,创建人名等
     */
    @Schema(description = "智能体元信息:logo,icon,创建人名等")
    private String agentMetadata;

    /**
     * 状态 0停用 1开发 5发布
     */
    @Schema(description = "状态 0停用 1开发 5发布")
    private Integer agentStatus;

    /**
     * 智能体启用版本id
     */
    @Schema(description = "智能体启用版本id")
    private Integer versionId;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Integer tenantId;

    /**
     * 创建人id
     */
    @Schema(description = "创建人id")
    private Integer userId;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}