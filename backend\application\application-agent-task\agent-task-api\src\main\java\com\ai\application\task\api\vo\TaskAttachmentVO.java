package com.ai.application.task.api.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 任务附件表
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Data
@Schema(name = "")
public class TaskAttachmentVO {
    /**
     * 附件id
     */
    @Schema(description = "附件id")
    private Long attachId;

    /**
     * 处理状态:1-待处理,2-处理中,3-处理完成,4-处理失败,5-跳过
     */
    @Schema(description = "处理状态:1-待处理,2-处理中,3-处理完成,4-处理失败,5-跳过")
    private Integer attachStatus;

    /**
     * 开始处理时间
     */
    @Schema(description = "开始处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processStartTime;

    /**
     * 处理完成时间
     */
    @Schema(description = "处理完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processEndTime;

    /**
     * 处理时长(毫秒)
     */
    @Schema(description = "处理时长(毫秒)")
    private Integer processDuration;

    /**
     * 处理输入参数
     */
    @Schema(description = "处理输入参数")
    private String processInput;

    /**
     * 处理输出结果
     */
    @Schema(description = "处理输出结果")
    private String processOutput;

    /**
     * 当前处理标识
     */
    @Schema(description = "当前处理标识")
    private String processFlag;

    /**
     * 处理错误信息
     */
    @Schema(description = "处理错误信息")
    private String processError;

    /**
     * 处理元数据
     */
    @Schema(description = "处理元数据")
    private String processMetadata;

    /**
     * 重试次数
     */
    @Schema(description = "重试次数")
    private Integer retryCount;

    /**
     * 消耗tokens
     */
    @Schema(description = "消耗tokens")
    private Integer tokensUsed;

    /**
     * 结果文件路径
     */
    @Schema(description = "结果文件路径")
    private String fileResultPath;

    /**
     * 任务id
     */
    @Schema(description = "任务id")
    private Integer taskId;

    /**
     * 任务执行记录id
     */
    @Schema(description = "任务执行记录id")
    private Long taskRunId;

    /**
     * 文件id
     */
    @Schema(description = "文件id")
    private Integer fileId;

    /**
     * 关联的智能体运行记录id
     */
    @Schema(description = "关联的智能体运行记录id")
    private Long agentRunId;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}