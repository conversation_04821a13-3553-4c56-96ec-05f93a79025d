package com.ai.application.agent.base.controller;

import com.ai.application.agent.base.api.dto.SessionCreateDTO;
import com.ai.application.agent.base.api.dto.SessionHistoryDTO;
import com.ai.application.agent.base.api.vo.AgentSessionPageVO;
import com.ai.application.agent.base.api.vo.SessionHistoryVO;
import com.ai.application.agent.base.service.IAgentRunSessionService;
import com.ai.framework.core.vo.ResultVo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 智能体会话
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Tag(name = "智能体会话", description = "智能体会话-相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1/session")
public class AgentSessionController {

    @Resource
    private IAgentRunSessionService agentRunSessionService;

    /**
     * 历史分话
     * @param queryDto
     * @return
     */
    @Operation(summary = "历史会话-分页查询", description = "查询历史会话信息")
    @PostMapping("/history")
    public ResultVo<PageInfo<SessionHistoryVO>> history(@Validated @RequestBody SessionHistoryDTO queryDto){
        return ResultVo.data(agentRunSessionService.history(queryDto));
    }
    
    /**
     * 创建新会话
     *
     * @param dto
     * @return
     */
    @Operation(summary = "创建新会话")
    @PostMapping("/create")
    public ResultVo<Void> add(@Validated @RequestBody SessionCreateDTO dto){
        agentRunSessionService.create(dto);
        return ResultVo.success("创建成功");
    }

    @Operation(summary = "会话分表")
    @PostMapping("/page")
    public ResultVo<Void> page(@Validated @RequestBody AgentSessionPageVO dto){
        return ResultVo.success("会话分表");
    }
}