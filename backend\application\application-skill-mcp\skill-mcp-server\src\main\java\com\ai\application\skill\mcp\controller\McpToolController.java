package com.ai.application.skill.mcp.controller;

import com.ai.application.skill.mcp.api.dto.McpToolDTO;
import com.ai.application.skill.mcp.api.dto.query.McpToolQueryDTO;
import com.ai.application.skill.mcp.api.vo.McpToolVO;
import com.ai.application.skill.mcp.service.IMcpToolService;
import com.github.pagehelper.PageInfo;

import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

/**
 * MCP工具表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Tag(name = "MCP工具表", description = "MCP工具表-相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/mcpTool")
public class McpToolController {

    @Resource
    private IMcpToolService mcpToolService;

    /**
     * 分页查询
     *
     * @param queryDto
     * @return
     */
    @Operation(summary = "MCP工具表-分页查询", description = "查询所有MCP工具表 信息")
    @PostMapping("/tools")
    public ResultVo<List<McpToolVO>> tools(@Validated @RequestBody McpToolQueryDTO queryDto){
        return ResultVo.data(mcpToolService.tools(queryDto));
    }


}