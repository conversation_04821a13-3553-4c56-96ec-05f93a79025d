package com.ai.application.tenant.api.mapstruct;

import com.ai.application.tenant.api.dto.TenantDTO;
import com.ai.application.tenant.api.entity.Tenant;
import com.ai.application.tenant.api.vo.TenantSimpleVO;
import com.ai.application.tenant.api.vo.TenantVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-14T11:00:29+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class TenantMapstructImpl implements TenantMapstruct {

    @Override
    public Tenant toEntity(TenantDTO dto) {
        if ( dto == null ) {
            return null;
        }

        Tenant tenant = new Tenant();

        tenant.setTenantId( dto.getTenantId() );
        tenant.setTenantSn( dto.getTenantSn() );
        tenant.setTenantName( dto.getTenantName() );
        tenant.setTenantDomain( dto.getTenantDomain() );
        tenant.setTenantDesc( dto.getTenantDesc() );
        tenant.setTenantExpireTime( dto.getTenantExpireTime() );
        tenant.setTenantStatus( dto.getTenantStatus() );
        tenant.setCreateTime( dto.getCreateTime() );
        tenant.setUpdateTime( dto.getUpdateTime() );

        return tenant;
    }

    @Override
    public List<Tenant> toEntityList(List<TenantDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<Tenant> list = new ArrayList<Tenant>( dtolist.size() );
        for ( TenantDTO tenantDTO : dtolist ) {
            list.add( toEntity( tenantDTO ) );
        }

        return list;
    }

    @Override
    public TenantVO toVo(Tenant entity) {
        if ( entity == null ) {
            return null;
        }

        TenantVO tenantVO = new TenantVO();

        tenantVO.setTenantId( entity.getTenantId() );
        tenantVO.setTenantSn( entity.getTenantSn() );
        tenantVO.setTenantName( entity.getTenantName() );
        tenantVO.setTenantDomain( entity.getTenantDomain() );
        tenantVO.setTenantDesc( entity.getTenantDesc() );
        tenantVO.setTenantExpireTime( entity.getTenantExpireTime() );
        tenantVO.setTenantStatus( entity.getTenantStatus() );
        tenantVO.setCreateTime( entity.getCreateTime() );
        tenantVO.setUpdateTime( entity.getUpdateTime() );

        return tenantVO;
    }

    @Override
    public List<TenantVO> toVoList(List<Tenant> entities) {
        if ( entities == null ) {
            return null;
        }

        List<TenantVO> list = new ArrayList<TenantVO>( entities.size() );
        for ( Tenant tenant : entities ) {
            list.add( toVo( tenant ) );
        }

        return list;
    }

    @Override
    public List<TenantSimpleVO> toVoSimpleList(List<Tenant> entities) {
        if ( entities == null ) {
            return null;
        }

        List<TenantSimpleVO> list = new ArrayList<TenantSimpleVO>( entities.size() );
        for ( Tenant tenant : entities ) {
            list.add( tenantToTenantSimpleVO( tenant ) );
        }

        return list;
    }

    protected TenantSimpleVO tenantToTenantSimpleVO(Tenant tenant) {
        if ( tenant == null ) {
            return null;
        }

        TenantSimpleVO tenantSimpleVO = new TenantSimpleVO();

        tenantSimpleVO.setTenantSn( tenant.getTenantSn() );
        tenantSimpleVO.setTenantName( tenant.getTenantName() );

        return tenantSimpleVO;
    }
}
