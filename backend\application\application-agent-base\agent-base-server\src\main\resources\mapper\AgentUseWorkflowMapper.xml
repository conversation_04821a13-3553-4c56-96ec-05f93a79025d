<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.agent.base.mapper.AgentUseWorkflowMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.agent.base.api.entity.AgentUseWorkflow">
                    <id column="flow_id" property="flowId" />
                    <result column="flow_variables" property="flowVariables" />
                    <result column="flow_definition" property="flowDefinition" />
                    <result column="flow_extensions" property="flowExtensions" />
                    <result column="flow_start_variables" property="flowStartVariables" />
                    <result column="flow_end_variables" property="flowEndVariables" />
                    <result column="flow_guide" property="flowGuide" />
                    <result column="flow_status" property="flowStatus" />
                    <result column="agent_id" property="agentId" />
                    <result column="version_id" property="versionId" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        flow_id, flow_variables, flow_definition, flow_extensions, flow_start_variables, flow_end_variables, flow_guide, flow_status, agent_id, version_id, create_time, update_time
    </sql>

    <select id="selectAgentUseWorkflowList" resultType="com.ai.application.agent.base.api.vo.AgentUseWorkflowVO">
        select
        <include refid="com.ai.application.agent.base.mapper.AgentUseWorkflowMapper.Base_Column_List"></include>
        from agent_use_workflow
        order by create_time desc limit 10;
    </select>
</mapper>