package ${cfg.queryDtoPackage};

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import com.ai.framework.core.vo.PageParam;

/**
 * $!{table.comment}-查询条件
 *
 * <AUTHOR>
 * @since ${date}
 */
@Data
@Schema(name = "$!{table.comment}QueryDTO")
public class ${entity}QueryDTO extends PageParam {
    #foreach($field in ${table.fields})
        #if("$field.propertyName" == "id")
            #if(${field.keyFlag})
                #set($keyPropertyName=${field.propertyName})
            #end
            #if("$!field.comment" != "")
    /**
     * ${field.comment}
     */
            #end
    @Schema(description = "$!{field.comment}")
    private ${field.propertyType} ${field.propertyName};

        #end
    #end
}