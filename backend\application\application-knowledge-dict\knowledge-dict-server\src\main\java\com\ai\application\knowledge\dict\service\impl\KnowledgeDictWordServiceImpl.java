package com.ai.application.knowledge.dict.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ai.application.app.api.dto.query.AppUserQueryDTO;
import com.ai.application.app.api.feign.IAppUserClient;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.application.base.file.api.feign.IFileFeignClient;
import com.ai.application.knowledge.dict.bo.WordQueryData;
import com.ai.application.knowledge.dict.bo.WordTemplateData;
import com.ai.application.knowledge.dict.bo.WordWarnData;
import com.ai.application.knowledge.dict.dto.DictWordCreateDto;
import com.ai.application.knowledge.dict.dto.DictWordListDto;
import com.ai.application.knowledge.dict.dto.DictWordUpdateDto;
import com.ai.application.knowledge.dict.entity.KnowledgeDict;
import com.ai.application.knowledge.dict.entity.KnowledgeDictWord;
import com.ai.application.knowledge.dict.enums.DictWordStatusEnum;
import com.ai.application.knowledge.dict.enums.DocTypeEnum;
import com.ai.application.knowledge.dict.error.AgentDocError;
import com.ai.application.knowledge.dict.handler.WordReportExportCellWriteHandler;
import com.ai.application.knowledge.dict.mapper.KnowledgeDictWordMapper;
import com.ai.application.knowledge.dict.service.IKnowledgeDictService;
import com.ai.application.knowledge.dict.service.IKnowledgeDictWordService;
import com.ai.application.knowledge.dict.vo.DictWordDataImportVo;
import com.ai.application.knowledge.dict.vo.DictWordExportVo;
import com.ai.application.knowledge.dict.vo.DictWordListVo;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.date.DateUtil;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.list.CollectionUtils;
import com.ai.framework.core.util.string.StringUtil;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.vo.ResultVo;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.ai.application.knowledge.dict.error.AgentDocError.FILE_READ_ERROR;

/**
 * <p>
 * 词条表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
@Slf4j
public class KnowledgeDictWordServiceImpl extends ServiceImpl<KnowledgeDictWordMapper, KnowledgeDictWord> implements IKnowledgeDictWordService {

    @Resource
    private IKnowledgeDictService knowledgeDictService;

    @Autowired
    private ResourceLoader resourceLoader;

    @Autowired
    private IAppUserClient userClient;

    @Resource
    private IFileFeignClient fileFeignClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultVo<String> create(DictWordCreateDto dto) {
        // 检验词库是否存在
        KnowledgeDict knowledgeDict = knowledgeDictService.checkDictExist(dto.getDictSn());
        dto.setDictId(knowledgeDict.getDictId());

        //数据是否已存在相同词条名称或别名
        checkWordNameAndAlias(dto, knowledgeDict);

        //保存
        List<KnowledgeDictWord> dictWordSaveList = dto.getWords().stream().map(word -> {
            KnowledgeDictWord dictWord = new KnowledgeDictWord();
            BeanUtils.copyProperties(word, dictWord);
            // 别名去重保存
            dictWord.setWordAlias(JSONObject.toJSONString(word.getWordAlias().stream().distinct().toList()));
            dictWord.setDictId(knowledgeDict.getDictId());
            dictWord.setCreateUserId(UserContext.getUserId());
            dictWord.setUpdateUserId(UserContext.getUserId());
            return dictWord;
        }).toList();

        saveBatch(dictWordSaveList);

        return ResultVo.success("词条创建成功");
    }


    @Override
    public ResultVo<String> update(DictWordUpdateDto dto) {
        // 检验词库是否存在
        KnowledgeDict knowledgeDict = knowledgeDictService.checkDictExist(dto.getDictSn());

        //检查词是否存在
        KnowledgeDictWord knowledgeDictWord = this.getOne(Wrappers.lambdaQuery(KnowledgeDictWord.class)
                .eq(KnowledgeDictWord::getWordId, dto.getWordId()));

        if (ObjectUtils.isEmpty(knowledgeDictWord)) {
            throw new ServiceException(AgentDocError.DICT_WORD_IS_NOT_EXISTS.getCode(), AgentDocError.DICT_WORD_IS_NOT_EXISTS.getMessage());
        }

        // 获取当前词库下所有的词信息
        List<KnowledgeDictWord> dictWordList = this.list(Wrappers.lambdaQuery(KnowledgeDictWord.class)
                .eq(KnowledgeDictWord::getDictId, knowledgeDict.getDictId())
                .ne(KnowledgeDictWord::getWordId, dto.getWordId()));

        //数据是否已存在相同词条名称或别名
        dictWordList.forEach(f -> {
            if (f.getWordName().equals(dto.getWordName())) {
                throw new ServiceException(AgentDocError.DICT_WORD_EXISTS.getCode(), AgentDocError.DICT_WORD_EXISTS.getMessage());
            }
            List<String> stringList = JsonUtils.parseArray(f.getWordAlias(), String.class);
            stringList.forEach(alias -> {
                if (dto.getWordAlias().contains(alias)) {
                    throw new ServiceException(AgentDocError.DICT_WORD_EXISTS.getCode(), AgentDocError.DICT_WORD_EXISTS.getMessage());
                }
            });
        });

        BeanUtils.copyProperties(dto, knowledgeDictWord);
        if (!ObjectUtils.isEmpty(dto.getWordAlias())) {
            knowledgeDictWord.setWordAlias(JsonUtils.toJsonString(dto.getWordAlias()));
        }
        knowledgeDictWord.setCreateUserId(UserContext.getUserId());
        knowledgeDictWord.setUpdateTime(new Date());
        updateById(knowledgeDictWord);
        //更新词库信息
        updateKnowledgeDict(knowledgeDict);

        return ResultVo.data("操作成功");
    }

    @Override
    public ResultVo<String> delete(String dictSn, String wordId) {
        // 检验词库是否存在
        KnowledgeDict knowledgeDict = knowledgeDictService.checkDictExist(dictSn);

        //检查词是否存在
        KnowledgeDictWord knowledgeDictWord = this.getOne(Wrappers.lambdaQuery(KnowledgeDictWord.class)
                .eq(KnowledgeDictWord::getWordId, wordId));

        if (ObjectUtils.isEmpty(knowledgeDictWord)) {
            throw new ServiceException(AgentDocError.DICT_WORD_IS_NOT_EXISTS.getCode(), AgentDocError.DICT_WORD_IS_NOT_EXISTS.getMessage());
        }

        //删除
        update(Wrappers.lambdaUpdate(KnowledgeDictWord.class)
                .set(KnowledgeDictWord::getWordStatus, DictWordStatusEnum.DELETED.getCode())
                .eq(KnowledgeDictWord::getWordId, wordId));

        //更新词库信息
        updateKnowledgeDict(knowledgeDict);
        return ResultVo.data("操作成功");
    }

    @Override
    public ResultVo<PageInfo<DictWordListVo>> list(DictWordListDto dto) {

        // 租户赋值
        dto.setTenantId(UserContext.getTenantId());

        // 检验词库是否存在
        KnowledgeDict knowledgeDict = knowledgeDictService.checkDictExist(dto.getDictSn());
        dto.setDictId(knowledgeDict.getDictId());

        //询满足姓名模糊查询的用户
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(dto.getKeyword())) {
            AppUserQueryDTO appUserQuery = new AppUserQueryDTO();
            appUserQuery.setUserName(dto.getKeyword());
            log.info("查询满足姓名模糊查询的用户 入参：{}", JSON.toJSONString((appUserQuery)));
            List<AppUserVO> list = userClient.list(appUserQuery).getData();
            log.info("查询满足姓名模糊查询的用户 出参：{}", JSON.toJSONString(list));
            dto.setUserIdsByName(list.stream().map(AppUserVO::getUserId).toList());
        }


        // 设置分页参数
        PageHelper.startPage(dto.getPageNo(), dto.getPageSize());

        // 查询数据源列表
        List<DictWordListVo> result = this.getBaseMapper().list(dto);


        // 获取所有的创建人姓名和更新人姓名
        List<Integer> creators = result.stream().map(DictWordListVo::getCreateUserId).distinct().toList();

        List<Integer> updaters = result.stream().map(DictWordListVo::getUpdateUserId).distinct().toList();

        List<Integer> updaterAndCreator = new ArrayList<>();
        updaterAndCreator.addAll(creators);
        updaterAndCreator.addAll(updaters);

        // 获取创建人名字
        AppUserQueryDTO appUserQuery = new AppUserQueryDTO();
        appUserQuery.setUserIds(updaterAndCreator);
        log.info("查询姓名模糊查询的用户 入参：{}", JSON.toJSONString(appUserQuery));
        List<AppUserVO> list = userClient.list(appUserQuery).getData();
        log.info("查询姓名模糊查询的用户 出参：{}", JSON.toJSONString((list)));

        Map<Integer, AppUserVO> wordMap = list.stream().collect(Collectors.toMap(AppUserVO::getUserId, vo -> vo, (o1, o2) -> o1));

        result.forEach(f -> {
            f.setCreator(wordMap.get(f.getCreateUserId()).getUserName());
            f.setUpdater(wordMap.get(f.getUpdateUserId()).getUserName());
        });

        // 返回分页结果
        return ResultVo.data(new PageInfo<>(result));
    }

    @Override
    public ResponseEntity<byte[]> wordTemplate(HttpServletResponse response) throws IOException {
        // 使用类加载器获取模板文件的输入流
        org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:词批量导入模版.xlsx");
        InputStream inputStream = resource.getInputStream();

        // 读取模板文件的内容
        byte[] fileContent = inputStream.readAllBytes();

        // 设置响应头信息
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", URLEncoder.encode("词批量导入模版.xlsx", StandardCharsets.UTF_8));

        // 返回响应实体
        return ResponseEntity.ok()
                .headers(headers)
                .body(fileContent);
    }

    @Override
    public ResponseEntity<byte[]> wordExport(HttpServletResponse response, DictWordListDto dto) throws IOException {
        // 处理导出数据
        List<WordQueryData> data = wordExportDataHandle(dto).getData();

        // 获取模板文件资源
        org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:词批量导入模版.xlsx");
        InputStream templateInputStream = resource.getInputStream();

        // 使用Apache POI读取Excel模板
        Workbook workbook = new XSSFWorkbook(templateInputStream);
        Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表

        // 从第三行开始插入数据（索引从0开始，第2行为第三行）
        int rowNum = 2;
        for (WordQueryData item : data) {
            Row row = sheet.createRow(rowNum++);

            Cell cell0 = row.createCell(0);
            cell0.setCellValue(item.getWordName());

            Cell cell1 = row.createCell(1);
            cell1.setCellValue(item.getAliasStr());

            Cell cell2 = row.createCell(2);
            cell2.setCellValue(item.getWordDesc());

            Cell cell3 = row.createCell(3);
            cell3.setCellValue(item.getCreator());

            Cell cell4 = row.createCell(4);
            cell4.setCellValue(item.getCreateTimeStr());

            Cell cell5 = row.createCell(5);
            cell5.setCellValue(item.getUpdater());

            Cell cell6 = row.createCell(6);
            cell6.setCellValue(item.getUpdateTimeStr());
        }

        // 将生成的Excel写入字节数组
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        workbook.write(byteArrayOutputStream);
        workbook.close();

        // 设置响应头信息
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        String encodedFileName = URLEncoder.encode("词批量导出.xlsx", StandardCharsets.UTF_8);
        headers.setContentDispositionFormData("attachment", encodedFileName);

        return ResponseEntity.ok()
                .headers(headers)
                .body(byteArrayOutputStream.toByteArray());
    }

    @Override
    public ResultVo<DictWordDataImportVo> wordImport(MultipartFile file, String dictSn) {
        // 检验词库是否存在
        KnowledgeDict knowledgeDict = knowledgeDictService.checkDictExist(dictSn);

        // 检查文件是否是Excel文件
        ServiceException.throwIf(!Objects.requireNonNull(Objects.requireNonNull(file.getOriginalFilename())).endsWith(".xlsx"), AgentDocError.DICT_WORD_FIELD_IMPORT_ILLEGALITY_ERROR);

        //检查文件模版是否匹配
        checkTemplate(file);

        // 读取文件
        List<WordTemplateData> vocabularyExcelWords = this.readExcel(file);

        // 查询词数据，以便后续比对和处理重复项
        List<KnowledgeDictWord> wordList = getBaseMapper().selectList(Wrappers.lambdaQuery(KnowledgeDictWord.class).eq(KnowledgeDictWord::getDictId, knowledgeDict.getDictId()));

        // 将查询到的词数据转换为Map，便于快速查找
        Map<String, KnowledgeDictWord> wordMap = wordList.stream().collect(Collectors.toMap(KnowledgeDictWord::getWordName, vo -> vo, (o1, o2) -> o1));

        // 获取数据库中的词列表，用于后续的数据校验
        List<String> dbWords = this.dbWords(wordList);

        // 统计失败的条数
        List<WordWarnData> warnList = new ArrayList<>();
        Set<String> words = new HashSet<>();
        List<WordTemplateData> insertList = new ArrayList<>();
        List<WordTemplateData> updateList = new ArrayList<>();
        AtomicInteger failTotal = new AtomicInteger(0);

        // 遍历Excel中的单词数据，进行数据构建和校验
        vocabularyExcelWords.forEach(vo -> this.wordsDataBuild(vo, warnList, words, wordMap, updateList, insertList, failTotal));

        // 插入数据
        if (CollectionUtils.isNotEmpty(insertList)) {
            this.importCheck(insertList, dbWords, warnList, failTotal);
            this.insertBatchWords(insertList, knowledgeDict.getDictId());
        }

        // 更新数据
        if (CollectionUtils.isNotEmpty(updateList)) {
            List<String> removeWords = updateList.stream().map(WordTemplateData::getWordName).collect(Collectors.toList());
            updateList.forEach(update -> {
                KnowledgeDictWord word = wordMap.get(update.getWordName());
                if (StringUtils.isNotBlank(word.getWordAlias())) {
                    removeWords.addAll(JSONArray.parseArray(word.getWordAlias(), String.class));
                }
            });
            dbWords.removeAll(removeWords);
            this.importCheck(updateList, dbWords, warnList, failTotal);
            this.updateBatchWords(updateList, wordMap);
        }

        // 生成警告excel文件
        String failureFileName = "";
        String failureFileUrl = "";
        if (CollectionUtils.isNotEmpty(warnList)) {
            failureFileName = "词批量导入异常信息.xlsx";
            failureFileUrl = this.writeUserToExcelUploadThenGetDownloadUrl(warnList, failureFileName);
        }

        // 总数
        Integer total = vocabularyExcelWords.size();
        DictWordDataImportVo dictWordDataImportVo = new DictWordDataImportVo();
        dictWordDataImportVo.setTotal(total);
        dictWordDataImportVo.setCreatedTotal(insertList.size());
        dictWordDataImportVo.setUpdatedTotal(updateList.size());
        dictWordDataImportVo.setFailedTotal(failTotal.get());
        dictWordDataImportVo.setFailureFileUrl(failureFileUrl);
        dictWordDataImportVo.setFailureFileName(failureFileName);

        return ResultVo.data(dictWordDataImportVo);
    }

    private void checkTemplate(MultipartFile file) {
        try (InputStream templateInputStream = getClass().getResourceAsStream("/词批量导入模版.xlsx")) {
            assert templateInputStream != null;
            Workbook templateWorkbook = new XSSFWorkbook(templateInputStream);
            Workbook uploadedWorkbook = new XSSFWorkbook(file.getInputStream());

            for (int i = 0; i < 2; i++) { // 检查前两行
                Row templateRow = templateWorkbook.getSheetAt(0).getRow(i);
                Row uploadedRow = uploadedWorkbook.getSheetAt(0).getRow(i);

                if (templateRow == null || uploadedRow == null) {
                    throw new ServiceException(AgentDocError.DICT_WORD_FIELD_IMPORT_FORMAT_ERROR);
                }

                for (int j = 0; j < templateRow.getLastCellNum(); j++) {
                    Cell templateCell = templateRow.getCell(j);
                    Cell uploadedCell = uploadedRow.getCell(j);

                    if (templateCell == null || uploadedCell == null ||
                        !templateCell.toString().trim().equals(uploadedCell.toString().trim())) {
                        throw new ServiceException(AgentDocError.DICT_WORD_FIELD_IMPORT_FORMAT_ERROR);
                    }
                }
            }
        } catch (IOException e) {
            throw new ServiceException(FILE_READ_ERROR);
        }
    }
    private DictWordExportVo wordExportDataHandle(DictWordListDto dto) {
        // 检验词库是否存在
        KnowledgeDict knowledgeDict = knowledgeDictService.checkDictExist(dto.getDictSn());
        List<DictWordListVo> dictWordListVos = list(dto).getData().getList();
        List<WordQueryData> wordQueryDataList = new ArrayList<>();
        dictWordListVos.forEach(word -> {
            WordQueryData wordQueryData = new WordQueryData();
            BeanUtil.copyProperties(word, wordQueryData);
            if (StringUtils.isNotBlank(word.getWordAlias())) {
                wordQueryData.setAliasStr(String.join(",", JsonUtils.parseArray(word.getWordAlias(), String.class)));
            }
            wordQueryData.setUpdater(word.getUpdater());
            wordQueryData.setCreator(word.getCreator());
            wordQueryData.setCreateTimeStr(DateUtil.format(word.getCreateTime()));
            wordQueryData.setUpdateTimeStr(DateUtil.format(word.getUpdateTime()));
            wordQueryDataList.add(wordQueryData);
        });
        DictWordExportVo vocabularyImportVo = new DictWordExportVo();
        vocabularyImportVo.setData(wordQueryDataList);
        vocabularyImportVo.setName(knowledgeDict.getDictName());
        return vocabularyImportVo;
    }

    private void checkWordNameAndAlias(DictWordCreateDto dto, KnowledgeDict knowledgeDict) {
        //检查请求的数据是否已存在相同词条名称或别名
        List<String> allName = dto.getWords().stream().map(DictWordCreateDto.Word::getWordName).toList();
        List<String> allAlias = dto.getWords().stream().flatMap(word -> word.getWordAlias().stream()).toList();

        //检查词名和别名是否重复
        if (new HashSet<>(allAlias).containsAll(allName)) {
            throw new ServiceException(AgentDocError.DICT_WORD_EXISTS.getCode(), AgentDocError.DICT_WORD_EXISTS.getMessage());
        }

        //检查allName中是否有重复元素
        if (allName.size() != allName.stream().distinct().count() || allAlias.size() != allAlias.stream().distinct().count()) {
            throw new ServiceException(AgentDocError.DICT_WORD_EXISTS.getCode(), AgentDocError.DICT_WORD_EXISTS.getMessage());
        }

        // 获取当前词库下所有的词信息
        List<KnowledgeDictWord> dictWordList = this.list(Wrappers.lambdaQuery(KnowledgeDictWord.class)
                .eq(KnowledgeDictWord::getDictId, knowledgeDict.getDictId()));

        // 校验数据库是否已存在相同词条名称或别名
        dictWordList.forEach(word -> {
            if (allName.contains(word.getWordName())) {
                throw new ServiceException(AgentDocError.DICT_WORD_EXISTS.getCode(), AgentDocError.DICT_WORD_EXISTS.getMessage());
            }
            List<String> stringList = JsonUtils.parseArray(word.getWordAlias(), String.class);
            stringList.forEach(alias -> {
                if (allAlias.contains(alias)) {
                    throw new ServiceException(AgentDocError.DICT_WORD_EXISTS.getCode(), AgentDocError.DICT_WORD_EXISTS.getMessage());
                }
            });
        });
    }

    private void updateKnowledgeDict(KnowledgeDict knowledgeDict) {
        knowledgeDict.setUpdateTime(new Date());
        knowledgeDict.setUpdateUserId(UserContext.getUserId());
        knowledgeDictService.updateById(knowledgeDict);
    }


    private List<WordTemplateData> readExcel(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        ExcelTypeEnum excelType;
        if (StringUtils.endsWith(fileName, DocTypeEnum.XLSX.getFileSuffix())) {
            excelType = ExcelTypeEnum.XLSX;
        } else if (StringUtils.endsWith(fileName, DocTypeEnum.XLS.getFileSuffix())) {
            excelType = ExcelTypeEnum.XLS;
        } else {
            excelType = ExcelTypeEnum.CSV;
        }
        InputStream fis;
        try {
            fis = file.getInputStream();
        } catch (IOException e) {
            throw new ServiceException(FILE_READ_ERROR);
        }
        List<WordTemplateData> WordTemplateDataList;
        try {
            WordTemplateDataList = EasyExcelFactory
                    .read(fis, WordTemplateData.class, new ReadListener<WordTemplateData>() {
                        @Override
                        public void invoke(WordTemplateData data, AnalysisContext context) {

                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext context) {
                        }
                    })
                    .headRowNumber(2)
                    .registerConverter(new NullConverter())
                    .excelType(excelType)
                    .doReadAllSync();
        } catch (Exception e) {
            throw new ServiceException(FILE_READ_ERROR);
        }
        return WordTemplateDataList;
    }

    private static class NullConverter implements Converter<String> {
        @Override
        public Class<?> supportJavaTypeKey() {
            return String.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public String convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
            if (StringUtils.isNotBlank(cellData.getStringValue())) {
                return cellData.getStringValue().trim();
            }
            return cellData.getStringValue();
        }
    }


    @NotNull
    private List<String> dbWords(List<KnowledgeDictWord> vocabularyWords) {
        List<String> words = new ArrayList<>(vocabularyWords.stream().map(KnowledgeDictWord::getWordName).toList());
        vocabularyWords.forEach(word -> {
            if (StringUtils.isNotBlank(word.getWordAlias())) {
                words.addAll(JSONArray.parseArray(word.getWordAlias(), String.class));
            }
        });
        return words;
    }


    private void wordsDataBuild(WordTemplateData vo,
                                List<WordWarnData> warnList,
                                Set<String> words, Map<String,
                    KnowledgeDictWord> wordMap,
                                List<WordTemplateData> updateList,
                                List<WordTemplateData> insertList,
                                AtomicInteger failTotal) {
        if (StringUtils.isBlank(vo.getWordName())) {
            this.addWarningList(vo, warnList, "词不能为空，添加失败");
            failTotal.getAndIncrement();
            return;
        }
        if (!words.add(vo.getWordName())) {
            this.addWarningList(vo, warnList, "词/别名重复，添加失败");
            failTotal.getAndIncrement();
            return;
        }
        if (vo.getWordName().length() > 50) {
            this.addWarningList(vo, warnList, "词不得超过50字符，添加失败");
            failTotal.getAndIncrement();
            return;
        }
        if (StringUtils.isNotBlank(vo.getAlias())) {
            String[] alias = vo.getAlias().split(",");
            boolean a = Arrays.stream(alias).anyMatch(e -> vo.getWordName().equals(e));
            if (a) {
                String failReason = "词/别名重复，添加失败";
                this.addWarningList(vo, warnList, failReason);
                failTotal.getAndIncrement();
                return;
            }
            boolean b = Arrays.stream(alias).anyMatch(e -> e.length() > 50);
            if (b) {
                String failReason = "别称超过50字符已被忽略";
                this.addWarningList(vo, warnList, failReason);
                vo.setAlias(null);
            }
        }
        if (StringUtils.isNotBlank(vo.getDefinition()) && vo.getDefinition().length() > 1000) {
            String failReason = "释义超过1000字符已被忽略";
            this.addWarningList(vo, warnList, failReason);
            vo.setDefinition(null);
        }
        if (!wordMap.isEmpty() && wordMap.containsKey(vo.getWordName())) {
            this.updateWords(vo, wordMap, warnList, updateList, failTotal);
        } else {
            insertList.add(vo);
        }
    }


    private void addWarningList(WordTemplateData vo, List<WordWarnData> warnList, String failReason) {
        WordWarnData warnData = new WordWarnData();
        BeanUtil.copyProperties(vo, warnData);
        warnData.setFailReason(failReason);
        warnList.add(warnData);
    }

    private void updateWords(WordTemplateData vo, Map<String, KnowledgeDictWord> wordMap, List<WordWarnData> warnList, List<WordTemplateData> updateList, AtomicInteger failTotal) {
        String excelAlias = vo.getAlias();
        KnowledgeDictWord word = wordMap.get(vo.getWordName());
        List<String> alias = JSONArray.parseArray(word.getWordAlias(), String.class);
        if (StringUtils.isBlank(excelAlias) && com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(alias)
            && StringUtils.isBlank(vo.getDefinition()) && StringUtils.isBlank(word.getWordDesc())) {
            this.addWarningList(vo, warnList, "词信息重复，添加失败");
            failTotal.getAndIncrement();
            return;
        } else {
            List<String> sortedAlias = com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(alias) ? new ArrayList<>() : alias.stream().sorted().toList();
            if (StringUtils.isNotBlank(excelAlias) || StringUtils.isNotBlank(vo.getDefinition())) {
                String[] aliasArray = StringUtils.isBlank(excelAlias) ? null : excelAlias.split(",");
                List<String> sortedExcelAlias = aliasArray == null ? new ArrayList<>() : Arrays.stream(aliasArray).sorted().toList();
                if (Objects.equals(sortedExcelAlias, sortedAlias)
                    && StringUtil.equals(vo.getDefinition(), word.getWordDesc())) {
                    this.addWarningList(vo, warnList, "词信息重复，添加失败");
                    failTotal.getAndIncrement();
                    return;
                } else {
                    this.addWarningList(vo, warnList, "词重复，别称/释义已更新");
                }
            }
        }
        updateList.add(vo);
    }

    private void importCheck(List<WordTemplateData> updateList, List<String> dbWords, List<WordWarnData> warnList, AtomicInteger failTotal) {
        Set<String> saveWords = updateList.stream().map(WordTemplateData::getWordName).collect(Collectors.toSet());
        updateList.removeIf(e -> {
            if (dbWords.contains(e.getWordName())) {
                this.addWarningList(e, warnList, "词/别名重复，添加失败");
                failTotal.getAndIncrement();
                return true;
            }
            if (StringUtils.isNotBlank(e.getAlias())) {
                List<String> aliasList = Arrays.asList(e.getAlias().split(","));
                boolean b = aliasList.stream().anyMatch(saveWords::contains) || aliasList.stream().anyMatch(dbWords::contains);
                if (b) {
                    this.addWarningList(e, warnList, "词/别名重复，添加失败");
                    failTotal.getAndIncrement();
                    return true;
                }
            }
            return false;
        });
    }


    private void insertBatchWords(List<WordTemplateData> insertList, Integer dictId) {
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(insertList)) {
            return;
        }
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        List<KnowledgeDictWord> KnowledgeDictWords = insertList.stream().map(word -> {
            KnowledgeDictWord KnowledgeDictWord = new KnowledgeDictWord();
            String alias = word.getAlias();
            if (StringUtils.isNotBlank(alias)) {
                List<String> list = Arrays.stream(alias.split(","))
                        .map(String::trim) // 去除前后空格
                        .filter(StringUtils::isNotBlank) // 剔除空字符串
                        .toList();
                alias = JSONObject.toJSONString(list);
            }
            KnowledgeDictWord.setWordName(word.getWordName());
            KnowledgeDictWord.setDictId(dictId);
            KnowledgeDictWord.setWordAlias(alias);
            KnowledgeDictWord.setWordDesc(word.getDefinition());
            KnowledgeDictWord.setCreateTime(timestamp);
            KnowledgeDictWord.setUpdateTime(timestamp);
            KnowledgeDictWord.setUpdateUserId(UserContext.getUserId());
            KnowledgeDictWord.setCreateUserId(UserContext.getUserId());
            return KnowledgeDictWord;
        }).toList();
        saveBatch(KnowledgeDictWords);
    }


    private void updateBatchWords(List<WordTemplateData> updateList, Map<String, KnowledgeDictWord> wordMap) {
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        updateList.forEach(vo -> {
            KnowledgeDictWord vocabularyWord = wordMap.get(vo.getWordName());
            vocabularyWord.setUpdateTime(timestamp);
            if (StringUtils.isNotBlank(vo.getAlias())) {
                List<String> list = Arrays.stream(vo.getAlias().split(","))
                        .map(String::trim) // 去除前后空格
                        .filter(StringUtils::isNotBlank) // 剔除空字符串
                        .toList();
                vocabularyWord.setWordAlias(JSONObject.toJSONString(list));
            }
            if (StringUtils.isNotBlank(vo.getDefinition())) {
                vocabularyWord.setWordDesc(vo.getDefinition());
            }
            updateById(vocabularyWord);
        });
    }


    private String writeUserToExcelUploadThenGetDownloadUrl(List<WordWarnData> users, String fileName) {
        try {
            ByteArrayOutputStream byteArrayOutputStream = writeUsersToExcel(users);
            // 失败文件返回URL
            return fileFeignClient.processFileUploadCompa(byteArrayOutputStream.toByteArray(), UUIDUtil.genRandomSn("file"), "text/csv", fileName, null, null).getData();
        } catch (Exception e) {
            throw new RuntimeException("writeUserToExcelUploadThenGetDownloadUrl 失败文件生成失败", e);
        }
    }

    private ByteArrayOutputStream writeUsersToExcel(List<WordWarnData> users) {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        EasyExcelFactory.write(byteArrayOutputStream, WordWarnData.class)
                .registerWriteHandler(new WordReportExportCellWriteHandler("注意：\n请在第一列，从A3开始填写词 \n单个词最多50个字符，超出部分将会被忽略 \n词、别称均不允许重复"))
                .sheet(0)
                .doWrite(new ArrayList<>(users));
        return byteArrayOutputStream;
    }
}
