package com.ai.application.knowledge.table.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class TableDataCreateDto {

    @Schema(description = "描述")
    private String tableSn;

    @Schema(description = "行Sn 编辑使用")
    private String rowSn;

    @Schema(description = "是否覆盖现有数据的标志 1-覆盖，2-跳过")
    private Integer overwrite;

    private List<TableData> datas;

    @Data
    public static class TableData {
        @Schema(description = "字段编号")
        private String fieldSn;

        @Schema(description = "数据")
        private String data;
    }

}
