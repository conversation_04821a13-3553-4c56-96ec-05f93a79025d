package com.ai.application.app.service;

import com.ai.application.app.api.vo.AppRoleFunctionTreeVO;
import com.github.pagehelper.PageInfo;
import com.ai.application.app.api.dto.AppFunctionDTO;
import com.ai.application.app.api.dto.query.AppFunctionQueryDTO;
import com.ai.application.app.api.vo.AppFunctionVO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * 应用功能表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
public interface IAppFunctionService {

    List<AppRoleFunctionTreeVO> queryRoleFunctionTree();

    /**
     * 分页
     *
     * @param queryDto
     * @return
     */
    PageInfo<AppFunctionVO> page(AppFunctionQueryDTO queryDto);

    /**
     * 列表
     *
     * @param queryDto
     * @return
     */
    List<AppFunctionVO> list(AppFunctionQueryDTO queryDto);

    /**
     * 保存
     *
     * @param dto
     */
    void save(AppFunctionDTO dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(AppFunctionDTO dto);

    /**
     * 查看
     *
     * @param id
     * @return
     */
    AppFunctionVO get(Long id);

    /**
     * 删除
     *
     * @param ids
     */
    void delete(Set<Long> ids);
}