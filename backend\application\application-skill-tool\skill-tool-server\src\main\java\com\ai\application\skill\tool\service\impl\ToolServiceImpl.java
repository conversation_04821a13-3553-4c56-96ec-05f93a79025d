package com.ai.application.skill.tool.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.ai.application.app.api.feign.IAppUserClient;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.application.base.file.api.dto.DocFileDto;
import com.ai.application.base.file.api.feign.IFileFeignClient;
import com.ai.application.skill.tool.api.constant.ToolConstant;
import com.ai.application.skill.tool.api.dto.*;
import com.ai.application.skill.tool.api.dto.query.ToolQueryDTO;
import com.ai.application.skill.tool.api.entity.Tool;
import com.ai.application.skill.tool.api.entity.ToolApi;
import com.ai.application.skill.tool.api.entity.ToolCode;
import com.ai.application.skill.tool.api.enums.*;
import com.ai.application.skill.tool.api.mapstruct.ToolMapstruct;
import com.ai.application.skill.tool.api.vo.*;
import com.ai.application.skill.tool.mapper.ToolApiMapper;
import com.ai.application.skill.tool.mapper.ToolCodeMapper;
import com.ai.application.skill.tool.mapper.ToolMapper;
import com.ai.application.skill.tool.service.ISkillHandler;
import com.ai.application.skill.tool.service.IToolService;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.date.DateUtil;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.string.StringUtil;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.vo.ResultVo;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;

import java.sql.Timestamp;
import java.util.*;

/**
 * 工具资源表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Service
@Slf4j
public class ToolServiceImpl implements IToolService {

    @Resource
    private ToolMapper toolMapper;

    @Resource
    private ToolApiMapper toolApiMapper;

    @Resource
    private ToolCodeMapper toolCodeMapper;

    @Resource
    private ToolMapstruct toolMapstruct;
    @Resource
    private IAppUserClient appUserClient;


    @Resource
    private IFileFeignClient fileFeignClient;

    /**
     * 技能的处理器注册
     */
    private final Map<String, ISkillHandler> skillHandlerRegistry = new HashMap<>() {
        @Override
        public ISkillHandler get(Object key) {
            ISkillHandler skillHandler = super.get(key);
            ServiceException.throwIf(skillHandler == null, SkillErrorCode.SKILL_HANDLER_NOT_DEFINED);
            return super.get(key);
        }
    };
    @Autowired
    private UserContext userContext;

    @PostConstruct
    public void init() {
        SpringUtil.getBeansOfType(ISkillHandler.class).values().forEach(skillHandler -> skillHandlerRegistry.put(skillHandler.getType(), skillHandler));
    }


    @Transactional(readOnly = true)
    @Override
    public PageInfo<ToolVO> page(ToolQueryDTO queryDto) {
        QueryWrapper<Tool> queryWrapper = this.buildQuery(queryDto);
        Page<Tool> page = new Page<>(queryDto.getPageNo(), queryDto.getPageSize());
        Page<Tool> result = this.toolMapper.selectPage(page, queryWrapper);
        log.info("page result:{}", JsonUtils.toJsonString(result));
        List<ToolVO> voList = toolMapstruct.toVoList(result.getRecords());
        //处理工具的创建人信息
        voList.forEach(tool -> {
            tool.setToolTypeName(SkillType.ofType(tool.getToolType()).getName());
            if (tool.getToolType() == 20) {
                ToolApi toolApi = toolApiMapper.selectOne(new QueryWrapper<ToolApi>().eq("tool_id", tool.getToolId()));
                if (toolApi != null) {
                    SkillUserDto skillUserDto = JSONObject.parseObject(toolApi.getApiMetadata(), SkillUserDto.class);
                    tool.setUserName(
                            Optional.ofNullable(appUserClient.getUserById(skillUserDto.getUserId()))
                                    .map(ResultVo::getData)
                                    .map(AppUserVO::getUserName)
                                    .orElse(null)
                    );

                }
            } else if (tool.getToolType() == 30) {
                ToolCode toolCode = toolCodeMapper.selectOne(new QueryWrapper<ToolCode>().eq("tool_id", tool.getToolSn()));
                if (toolCode != null) {
                    SkillUserDto skillUserDto = JSONObject.parseObject(toolCode.getCodeMetadata(), SkillUserDto.class);
                    tool.setUserName(
                            Optional.ofNullable(appUserClient.getUserById(skillUserDto.getUserId()))
                                    .map(ResultVo::getData)
                                    .map(AppUserVO::getUserName)
                                    .orElse(null));
                }
            }
        });
        PageInfo<ToolVO> toolVOPageInfo = PageInfo.of(voList);
        toolVOPageInfo.setTotal(result.getTotal());
        return toolVOPageInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(String skillSn) {

        BusinessAssertUtil.notNull(skillSn, "skillSn不能为空");
        var skillQuery = new LambdaQueryWrapper<Tool>();
        skillQuery.eq(Tool::getToolSn, skillSn);
        Tool entity = toolMapper.selectOne(skillQuery);


        entity.setToolStatus(-1); // 设置为删除状态
        entity.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        toolMapper.updateById(entity);
    }


    private QueryWrapper<Tool> buildQuery(ToolQueryDTO queryDto) {
        QueryWrapper<Tool> queryWrapper = new QueryWrapper<>();
        log.info("queryDto:{}", JsonUtils.toJsonString(queryDto));

        if (Objects.nonNull(queryDto.getToolType())) {
            int type = SkillType.ofName(
                    queryDto.getToolType()).getType();
            queryWrapper.lambda().eq(Tool::getToolType, type)
                    .ne(Tool::getToolStatus, -1)
            ;
        }

        if (Objects.nonNull(queryDto.getKeyword())) {

            queryWrapper.lambda().like(Tool::getToolName, queryDto.getKeyword())
            ;
        }
        return queryWrapper;
    }


    @Override
    public ToolDetailVo getSkillDetail(String skillSn) {
        var skillQuery = new LambdaQueryWrapper<Tool>();
        skillQuery.eq(Tool::getToolSn, skillSn);
        Tool skillInfo = toolMapper.selectOne(skillQuery);
        if (skillInfo == null) {
            throw new ServiceException(SkillErrorCode.SKILL_NOT_EXIST.getCode(), SkillErrorCode.SKILL_NOT_EXIST.getMessage());
        }
        ToolDetailVo skillDetailVo = skillHandlerRegistry.get(SkillType.ofType(skillInfo.getToolType()).getName()).getSkillDetail(skillInfo, null);
        skillDetailVo.setToolLogo(skillInfo.getToolLogo());
        return skillDetailVo;
    }

    @Override
    public ToolDetailVo getSkillDetail(Integer toolId) {
        var skillQuery = new LambdaQueryWrapper<Tool>();
        skillQuery.eq(Tool::getToolId, toolId);
        Tool skillInfo = toolMapper.selectOne(skillQuery);
        if (skillInfo == null) {
            throw new ServiceException(SkillErrorCode.SKILL_NOT_EXIST.getCode(), SkillErrorCode.SKILL_NOT_EXIST.getMessage());
        }
        ToolDetailVo skillDetailVo = skillHandlerRegistry.get(SkillType.ofType(skillInfo.getToolType()).getName()).getSkillDetail(skillInfo, null);
        skillDetailVo.setToolLogo(skillInfo.getToolLogo());
        return skillDetailVo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public SkillSaveVo saveSkill(SkillSaveDto skillSaveDto) {
        return updateOneSkill(skillSaveDto, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SkillSaveVo publish(SkillSaveDto skillSaveDto) {
        return updateOneSkill(skillSaveDto, true);
    }

    /**
     * 测试技能
     *
     * @param skillSn
     * @param executeDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SkillResultVo testSkill(String skillSn, SkillExecuteDto executeDto) {
        var skillQuery = new LambdaQueryWrapper<Tool>();
        skillQuery.eq(Tool::getToolSn, skillSn);
        Tool skillInfo = toolMapper.selectOne(skillQuery);
        if (skillInfo == null) {
            throw new ServiceException(SkillErrorCode.SKILL_NOT_EXIST.getCode(), SkillErrorCode.SKILL_NOT_EXIST.getMessage());
        }
        return skillExecute(skillInfo, executeDto);
    }


    /**
     * 技能执行
     *
     * @param skillInfo
     * @param executeDto
     * @return
     */
    public SkillResultVo skillExecute(Tool skillInfo, SkillExecuteDto executeDto) {
//        Tool skillResult = saveSkillResult(skillInfo, executeDto, SkillResultStatus.RUNNING); // 保存技能执行结果
        try {
            SkillResultVo resultVo = skillHandlerRegistry.get(SkillType.ofType(skillInfo.getToolType()).getName()).runSkill(skillInfo, executeDto);
//            updateSkillResult(skillResult, resultVo.getCode() == 0 ? SkillResultStatus.COMPLETED : SkillResultStatus.ERROR, resultVo); // 更新技能执行结果
            return resultVo;
        } catch (Exception exception) {
            log.error("执行技能失败", exception);
            SkillResultVo resultVo = SkillResultVo.builder()
                    .code(exception instanceof ServiceException ? ((ServiceException) exception).getCode() : 500)
                    .success(false)
                    .message(exception.getMessage())
                    .build();
//            updateSkillResult(skillResult, SkillResultStatus.ERROR, resultVo); // 更新技能执行结果
            return resultVo;
        }
    }

    @Override
    public SkillResultVo apiTest(ApiSkillTestDto dto) {
        ApiSkillHandler apiSkillHandler = (ApiSkillHandler) skillHandlerRegistry.get(SkillType.API.getName());
        return apiSkillHandler.apiTest(dto);
    }


    private SkillSaveVo updateOneSkill(SkillSaveDto skillSaveDto, boolean toPublish) {
        Integer toolId = 0;
        if (skillSaveDto == null) {
            return null;
        }
        SkillSaveVo skillSaveVo = new SkillSaveVo();
        Long tenantId = getTenantId();
        if (toPublish) {
            var skillQuery = new LambdaQueryWrapper<Tool>();
            skillQuery.eq(Tool::getToolSn, skillSaveDto.getToolSn());
            Tool updateSkill = toolMapper.selectOne(skillQuery);
            updateSkill.setToolStatus(1);
            toolMapper.updateById(updateSkill);
            skillSaveVo.setSuccess(true);
            return skillSaveVo;
        } else {
            skillSaveVo = checkSkillSaveDto(skillSaveDto, tenantId, toPublish);
        }
        // 执行保存
        boolean success = skillSaveVo.getSuccess();
        skillSaveDto.setToolStatus(toPublish ? SkillStatus.PUBLISHED.getStatus() : SkillStatus.UNPUBLISHED.getStatus());
        Integer userId = UserContext.getUserId();
        String userSn = UserContext.getUserSn();
        String skillSn = StringUtil.isEmpty(skillSaveDto.getToolSn()) ? UUIDUtil.genRandomSn("skill", 20) : skillSaveDto.getToolSn();
        Timestamp now = new Timestamp(System.currentTimeMillis());
        Tool skillInfo = null;
        if (success) {
            int skillType = SkillType.ofName(skillSaveDto.getToolType()).getType();
            skillSaveVo.setUpdateTime(DateUtil.dataLongToString(now.getTime(), "yyyy-MM-dd HH:mm:ss"));
            // 更新技能数据
            if (StringUtil.isNotEmpty(skillSaveDto.getToolSn())) {
                var skillQuery = new LambdaQueryWrapper<Tool>();
                skillQuery.eq(Tool::getToolSn, skillSaveDto.getToolSn());
                Tool updateSkill = toolMapper.selectOne(skillQuery);
                toolId = updateSkill.getToolId();
                if (updateSkill != null) {
//                    if (toPublish || updateSkill.getToolStatus() == SkillStatus.UNPUBLISHED.getStatus()) {// 还没有发布的技能，所有字段都是其草稿
//                        updateSkill.setToolName(skillSaveDto.getToolName());
//                        updateSkill.setToolDesc(skillSaveDto.getToolDesc());
//                        updateSkill.setToolType(skillType);
//                        updateSkill.setToolStatus(skillSaveDto.getToolStatus());
//                    }
                    updateSkill.setUpdateTime(now);
                    try {
                        if (toPublish) {
                            updateSkill.setToolStatus(1);
                            skillHandlerRegistry.get(skillSaveDto.getToolType()).savePublishAfterHandler(skillSaveDto);
                            success = toolMapper.updateById(updateSkill) > 0;
                            return skillSaveVo;
                        }
                        updateSkill.setToolDesc(skillSaveDto.getToolDesc());
                        updateSkill.setToolSn(updateSkill.getToolSn());
                        updateSkill.setToolName(skillSaveDto.getToolName());
                        updateSkill.setToolLogo(skillSaveDto.getToolLogo());
                        updateSkill.setToolType(skillType);
                        updateSkill.setUpdateTime(now);
                        success = toolMapper.updateById(updateSkill) > 0;
                        skillSaveVo.setSuccess(success);

                        if (!success) {
                            skillSaveVo.setCode(SkillErrorCode.SKILL_SAVE_FAILED.getCode());
                            skillSaveVo.setMessage("数据库保存失败，请编辑后重试");
                        }
                    } catch (Exception e) {
                        log.error("保存技能失败", e);
                        skillSaveVo.setCode(SkillErrorCode.SKILL_SAVE_FAILED.getCode());
                        skillSaveVo.setSuccess(false);
                        skillSaveVo.setMessage("数据库保存失败，请编辑后重试" + e.getMessage());
                    }
//                    return skillSaveVo;
                }
            } else {
                // 插入新数据
                skillInfo = Tool.builder()
                        .toolSn(skillSn)
                        .toolName(skillSaveDto.getToolName())
                        .toolDesc(skillSaveDto.getToolDesc())
                        .toolType(skillType)
                        .toolLogo(skillSaveDto.getToolLogo())
                        .toolStatus(skillSaveDto.getToolStatus())
                        .createTime(now)
                        .updateTime(now)
                        .build();
                if (toPublish) {
                    skillSaveDto.setToolSn(skillSn);
                    skillHandlerRegistry.get(skillSaveDto.getToolType()).savePublishAfterHandler(skillSaveDto);
                }

                try {
                    //授权逻辑处理
//                dataAuthClient.authDataAdminToSingleUser(userSn, "skill", skillSn);
                    success = toolMapper.insert(skillInfo) > 0;
                    log.info("保存技能成功，技能编号：{}", skillInfo.getToolId());
                    skillSaveVo.setSuccess(success);
                    skillSaveVo.setToolSn(skillInfo.getToolSn());
                    if (!success) {
                        skillSaveVo.setCode(SkillErrorCode.SKILL_SAVE_FAILED.getCode());
                        skillSaveVo.setMessage("数据库保存失败，请编辑后重试");
                    }
                } catch (Exception e) {
                    log.error("保存技能失败", e);
                    skillSaveVo.setCode(SkillErrorCode.SKILL_SAVE_FAILED.getCode());
                    skillSaveVo.setSuccess(false);
                    skillSaveVo.setMessage("数据库保存失败，请编辑后重试");
                }
            }
            //处理api和自定义代码的扩展项
            if (success && SkillType.API.getName().equals(skillSaveDto.getToolType())) {
                // 保存API技能
                if (StringUtil.isNotEmpty(skillSaveDto.getToolSn())) {
                    //更新api工具操作
                    var skillQuery = new LambdaQueryWrapper<ToolApi>();
                    skillQuery.eq(ToolApi::getToolId, toolId);
                    ToolApi updateSkill = toolApiMapper.selectOne(skillQuery);
                    if (updateSkill != null) {
                        updateSkill.setApiUrl(skillSaveDto.getExtendedData().getString(ToolConstant.URL));
                        updateSkill.setApiMethod(skillSaveDto.getExtendedData().getString(ToolConstant.METHOD));
                        updateSkill.setApiHeaders(skillSaveDto.getExtendedData().getString(ToolConstant.HEADER));
                        updateSkill.setApiResponse(skillSaveDto.getExtendedData().getString(ToolConstant.RESPONSE));
                        updateSkill.setApiRequest(skillSaveDto.getExtendedData().getString(ToolConstant.REQUEST));
                        updateSkill.setUpdateTime(now);
                        toolApiMapper.updateById(updateSkill);
                    }
                } else {
                    // 新增API工具扩展
                    log.info("新增API工具扩展{}", skillSaveDto.getExtendedData());
                    SkillUserDto skillUserDto = new SkillUserDto();
                    if (Objects.nonNull(userId)) {
                        skillUserDto.setUserId(userId);
                    } else {
                        skillUserDto.setUserId(100002);
                    }
                    skillUserDto.setTimeout(skillSaveDto.getTimeout());
                    ToolApi saveApi = ToolApi.builder().apiUrl(skillSaveDto.getExtendedData().getString(ToolConstant.URL))
                            .apiMethod(skillSaveDto.getExtendedData().getString(ToolConstant.METHOD))
                            .apiHeaders(skillSaveDto.getExtendedData().getString(ToolConstant.HEADER))
                            .apiResponse(skillSaveDto.getExtendedData().getString(ToolConstant.RESPONSE))
                            .apiRequest(skillSaveDto.getExtendedData().getString(ToolConstant.REQUEST))
                            .apiProvider(skillSaveDto.getExtendedData().getString(ToolConstant.PROVIDER))
                            .apiMetadata(JsonUtils.toJsonString(skillUserDto))
                            .toolId(skillInfo.getToolId())
                            .createTime(now)
                            .updateTime(now)
                            .build();
                    toolApiMapper.insert(saveApi);
                }
            } else if (success && SkillType.CUSTOM.getName().equals(skillSaveDto.getToolType())) {

                // 保存自定义代码技能
                if (StringUtil.isNotEmpty(skillSaveDto.getToolSn())) {
                    //更新自定义工具操作
                    var skillQuery = new LambdaQueryWrapper<ToolCode>();
                    skillQuery.eq(ToolCode::getToolId, toolId);
                    ToolCode updateSkill = toolCodeMapper.selectOne(skillQuery);
                    SkillUserDto skillUserDto = JsonUtils.parseObject(updateSkill.getCodeMetadata(), SkillUserDto.class);
                    skillUserDto.setTimeout(skillSaveDto.getTimeout());

                    if (updateSkill != null) {
                        updateSkill.setCodeType(10);
                        updateSkill.setCodeInput(skillSaveDto.getExtendedData().getString(ToolConstant.CODEINPUT));
                        updateSkill.setCodeSource(skillSaveDto.getExtendedData().getString(ToolConstant.CODESOURCE));
                        updateSkill.setCodeOutput(skillSaveDto.getExtendedData().getString(ToolConstant.CODEOUTPUT));
                        updateSkill.setUpdateTime(now);
                        updateSkill.setCodeMetadata(JsonUtils.toJsonString(skillUserDto));
                        toolCodeMapper.updateById(updateSkill);
                    }
                } else {
                    SkillUserDto skillUserDto = new SkillUserDto();
                    if (Objects.nonNull(userId)) {
                        skillUserDto.setUserId(userId);
                    } else {
                        skillUserDto.setUserId(100002);
                    }
                    skillUserDto.setTimeout(skillSaveDto.getTimeout());
                    // 新增自定义工具扩展
                    log.info("新增code工具扩展{}", skillSaveDto.getExtendedData());
                    ToolCode saveCode = ToolCode.builder().codeSource(skillSaveDto.getExtendedData().getString(ToolConstant.CODESOURCE))
                            .codeInput(skillSaveDto.getExtendedData().getString(ToolConstant.CODEINPUT))
                            .codeOutput(skillSaveDto.getExtendedData().getString(ToolConstant.CODEOUTPUT))
                            .codeType(10)
                            .codeMetadata(JsonUtils.toJsonString(skillUserDto))
                            .toolId(skillInfo.getToolId())
                            .createTime(now)
                            .updateTime(now)
                            .build();
                    toolCodeMapper.insert(saveCode);
                }
            }


        }

        return skillSaveVo;
    }

    private Long getTenantId() {
        Integer tenantId = UserContext.getTenantId();
        return null;
    }


    private SkillSaveVo checkSkillSaveDto(SkillSaveDto skillSaveDto, Long tenantId, boolean toPublish) {
        SkillSaveVo skillSaveVo = SkillSaveVo.builder()
                .toolName(skillSaveDto.getToolName())
                .fileName(skillSaveDto.getFileName())
                .success(true)
                .message("操作成功")
                .code(0)
                .build();

        if (toPublish) {
            var skillQuery = new LambdaQueryWrapper<Tool>();
            skillQuery.eq(Tool::getToolName, skillSaveVo.getToolName());
            if (StringUtil.isNotEmpty(skillSaveDto.getToolSn())) { // 排除自己
                skillQuery.ne(Tool::getToolSn, skillSaveDto.getToolSn());
            }

            List<Tool> skillList = toolMapper.selectList(skillQuery);
            if (!com.ai.framework.core.util.list.CollectionUtils.isEmpty(skillList)) {
                skillSaveVo.setCode(SkillErrorCode.SKILL_SAVE_FAILED.getCode());
                skillSaveVo.setSuccess(false);
                skillSaveVo.setMessage("已存在同名技能，请修改名称后重试");
                return skillSaveVo;
            }
//            // 校验同一租户内是否存在同名自定义组件
//            if (tenantId == null) {
//                skillSaveVo.setCode(SkillErrorCode.SKILL_SAVE_FAILED.getCode());
//                skillSaveVo.setSuccess(false);
//                skillSaveVo.setMessage("租户不存在，请联系管理员");
//                return skillSaveVo;
//            }

            // 校验名称长度
            skillSaveDto.setToolName(StringUtil.trim(skillSaveDto.getToolName()));
            if (StringUtil.isEmpty(skillSaveDto.getToolName())) {
                skillSaveVo.setCode(SkillErrorCode.SKILL_SAVE_FAILED.getCode());
                skillSaveVo.setSuccess(false);
                skillSaveVo.setMessage("名称长度必须在1-30字符之间");
                return skillSaveVo;
            } else if (skillSaveDto.getToolName().length() > 30) {
                skillSaveVo.setCode(SkillErrorCode.SKILL_SAVE_FAILED.getCode());
                skillSaveVo.setSuccess(false);
                skillSaveVo.setMessage("名称长度必须在1-30字符之间");
                return skillSaveVo;
            }
        }
        SkillSaveVo saveVo = skillHandlerRegistry.get(skillSaveDto.getToolType()).checkSkillSaveDto(skillSaveDto);
        if (saveVo != null) {
            return saveVo;
        }

        return skillSaveVo;
    }


}