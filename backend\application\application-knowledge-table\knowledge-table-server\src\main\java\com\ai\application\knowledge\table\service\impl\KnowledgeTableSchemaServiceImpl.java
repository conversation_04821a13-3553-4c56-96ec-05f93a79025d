package com.ai.application.knowledge.table.service.impl;

import com.ai.application.app.api.dto.query.AppUserQueryDTO;
import com.ai.application.app.api.feign.IAppUserClient;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.application.knowledge.table.dto.TableManageCreateDto;
import com.ai.application.knowledge.table.dto.TableManageListDto;
import com.ai.application.knowledge.table.entity.KnowledgeTable;
import com.ai.application.knowledge.table.entity.KnowledgeTableRow;
import com.ai.application.knowledge.table.entity.KnowledgeTableSchema;
import com.ai.application.knowledge.table.enums.RowStatusEnum;
import com.ai.application.knowledge.table.enums.SchemaKeyEnum;
import com.ai.application.knowledge.table.enums.SchemaStatusEnum;
import com.ai.application.knowledge.table.errors.AgentDocError;
import com.ai.application.knowledge.table.mapper.KnowledgeTableRowMapper;
import com.ai.application.knowledge.table.mapper.KnowledgeTableSchemaMapper;
import com.ai.application.knowledge.table.service.IKnowledgeTableSchemaService;
import com.ai.application.knowledge.table.service.IKnowledgeTableService;
import com.ai.application.knowledge.table.vo.TableManageDetailVo;
import com.ai.application.knowledge.table.vo.TableManageImportVo;
import com.ai.application.knowledge.table.vo.TableManageListVo;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.vo.ResultVo;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.ai.application.knowledge.table.enums.SchemaTypeEnum.getDataTypeByName;

/**
 * <p>
 * 智能表格定义表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
@Slf4j
public class KnowledgeTableSchemaServiceImpl extends ServiceImpl<KnowledgeTableSchemaMapper, KnowledgeTableSchema> implements IKnowledgeTableSchemaService {

    @Autowired
    private IKnowledgeTableService knowledgeTableService;

    @Autowired
    private KnowledgeTableRowMapper knowledgeTableRowMapper;

    @Autowired
    private ResourceLoader resourceLoader;

    @Autowired
    private IAppUserClient userClient;

    @Override
    public ResultVo<String> create(TableManageCreateDto dto) {
        log.info("创建智能表格字段数据开始, 入参: {}", JsonUtils.toJsonString(dto));
        KnowledgeTableSchema schema = new KnowledgeTableSchema();

        // 检查智能表格是否存在
        KnowledgeTable knowledgeTable = knowledgeTableService.checkTable(dto.getTableSn());

        // 检查字段是否存在
        checkFiledName(dto.getFieldName(), knowledgeTable.getTableId());

        if (dto.getFieldKey().equals(SchemaKeyEnum.IS_KEY.getCode())) {
            //  批量文件不允许设为key字段
            ServiceException.throwIf(StringUtils.equals(dto.getFieldType(), "FILE_ARRAY"), AgentDocError.INTELLIGENT_TABLE_DEFINITION_FILE_ARRAY_ERROR);
            //  当且仅当表格内容数据为空时 才可更改key字段
            Long count = knowledgeTableRowMapper.selectCount(new LambdaQueryWrapper<KnowledgeTableRow>().eq(KnowledgeTableRow::getTableId, knowledgeTable.getTableId()).eq(KnowledgeTableRow::getRowStatus, SchemaStatusEnum.VALID.getCode()));
            ServiceException.throwIf(count > 0, AgentDocError.INTELLIGENT_TABLE_DEFINITION_FIELD_KEY_CONFIG_ERROR);
        }
        // 字段排序序号
        LambdaQueryWrapper<KnowledgeTableSchema> sortWrapper = new LambdaQueryWrapper<>();
        sortWrapper.select(KnowledgeTableSchema::getFieldSort).eq(KnowledgeTableSchema::getTableId, knowledgeTable.getTableId()).eq(KnowledgeTableSchema::getFieldStatus, SchemaStatusEnum.VALID.getCode()).last("limit 1").orderByDesc(KnowledgeTableSchema::getFieldSort);
        KnowledgeTableSchema definition = getBaseMapper().selectOne(sortWrapper);
        Integer sort = 0;
        if (definition != null) {
            sort = definition.getFieldSort();
        }
        // 插入数据
        BeanUtils.copyProperties(dto, schema);
        schema.setCreateUserId(UserContext.getUserId());
        schema.setUpdateUserId(UserContext.getUserId());
        schema.setFieldSort(sort + 1);
        schema.setFieldSn(UUIDUtil.genRandomSn("schema"));
        schema.setTableId(knowledgeTable.getTableId());
        schema.setFieldStatus(SchemaStatusEnum.VALID.getCode());
        // 更新智能表格更新人信息
        boolean saved = this.save(schema);
        // 更新操作信息
        updateBaseByUpdateUser(knowledgeTable.getTableSn());
        log.info("创建智能表格字段数据结束, 出参: {}");
        return saved ? ResultVo.data(schema.getFieldSn(), "智能表格字段创建成功") : ResultVo.fail("智能表格字段创建失败");
    }

    @Override
    public ResultVo<String> update(TableManageCreateDto dto) {
        log.info("更新智能表格字段数据开始, 入参: {}", JsonUtils.toJsonString(dto));
        // 检查智能表格是否存在
        KnowledgeTable knowledgeTable = knowledgeTableService.checkTable(dto.getTableSn());

        // 查询字段是否存在且有效
        KnowledgeTableSchema schema = checkSchema(dto.getFieldSn());

        // 检查字段是否重名
        KnowledgeTableSchema knowledgeTableSchema = this.baseMapper.selectOne(Wrappers.lambdaQuery(KnowledgeTableSchema.class).eq(KnowledgeTableSchema::getTableId, knowledgeTable.getTableId()).eq(KnowledgeTableSchema::getFieldStatus, SchemaStatusEnum.VALID.getCode()).eq(KnowledgeTableSchema::getFieldName, dto.getFieldName()).ne(KnowledgeTableSchema::getFieldSn, dto.getFieldSn()).last(" limit 1 "));
        if (ObjectUtils.isNotEmpty(knowledgeTableSchema)) {
            throw new ServiceException(AgentDocError.INTELLIGENT_TABLE_FIELD_NAME_HAS_EXISTS.getCode(), AgentDocError.INTELLIGENT_TABLE_FIELD_NAME_HAS_EXISTS.getMessage());
        }
        // 检查关键字段标志是否更改，如果更改，确保没有数据行存在
        if (ObjectUtils.isNotEmpty(dto.getFieldKey())) {
            //  当且仅当表格内容数据为空时 才可更改key字段
            Long count = knowledgeTableRowMapper.selectCount(new LambdaQueryWrapper<KnowledgeTableRow>().eq(KnowledgeTableRow::getTableId, knowledgeTable.getTableId()).eq(KnowledgeTableRow::getRowStatus, SchemaStatusEnum.VALID.getCode()));
            ServiceException.throwIf(count > 0, AgentDocError.INTELLIGENT_TABLE_DEFINITION_FIELD_KEY_CONFIG_ERROR);
        }
        // 属性拷贝
        BeanUtils.copyProperties(dto, schema);
        // 更新智能表格更新人信息
        boolean update = this.updateById(schema);
        // 更新操作信息
        updateBaseByUpdateUser(knowledgeTable.getTableSn());
        log.info("更新智能表格字段数据结束, 出参: {}");
        return update ? ResultVo.data(schema.getFieldSn(), "智能表格字段编辑成功") : ResultVo.fail("智能表格字段编辑失败");
    }

    @Override
    public ResultVo<TableManageDetailVo> detail(String fieldSn) {
        log.info("查询智能表格字段详情开始, 入参: {}", fieldSn);
        // 查询字段详情
        KnowledgeTableSchema schema = checkSchema(fieldSn);

        if (schema == null) {
            return ResultVo.fail("字段不存在或已失效");
        }

        // 转换为VO对象返回
        TableManageDetailVo vo = new TableManageDetailVo();
        BeanUtils.copyProperties(schema, vo);

        //创建人 更新人
        AppUserVO createUser = userClient.getUserById(vo.getCreateUserId()).getData();
        vo.setCreator(createUser.getUserName());
        AppUserVO updateUser = userClient.getUserById(vo.getUpdateUserId()).getData();
        vo.setUpdater(updateUser.getUserName());

        log.info("查询智能表格字段详情结束, 出参: {}", JSON.toJSONString(vo));
        return ResultVo.data(vo, "详情获取成功");
    }

    @Override
    public ResultVo<String> delete(String tableSn, String fieldSn) {
        log.info("删除智能表格字段开始, 入参: {}, {}", tableSn, fieldSn);
        // 检查智能表格是否存在
        knowledgeTableService.checkTable(tableSn);
        // 查询字段是否存在且有效
        KnowledgeTableSchema schema = checkSchema(fieldSn);

        // 检查表格中是否有数据行，存在数据行时不能删除字段
        Long count = knowledgeTableRowMapper.selectCount(new LambdaQueryWrapper<KnowledgeTableRow>().eq(KnowledgeTableRow::getTableId, schema.getTableId()).eq(KnowledgeTableRow::getRowStatus, RowStatusEnum.VALID.getCode()));
        ServiceException.throwIf(count > 0, AgentDocError.INTELLIGENT_TABLE_DEFINITION_FIELD_KEY_ERROR);

        // 软删除字段：更新状态为无效
        schema.setFieldStatus(SchemaStatusEnum.INVALID.getCode());
        boolean delete = this.updateById(schema);
        log.info("删除智能表格字段结束");
        return delete ? ResultVo.data(schema.getFieldSn(), "字段删除成功") : ResultVo.fail("字段删除失败");
    }

    @Override
    public ResultVo<PageInfo<TableManageListVo>> list(TableManageListDto dto) {
        log.info("分页查询智能表格字段列表开始, 入参: {}", JsonUtils.toJsonString(dto));
        // 租户赋值
        dto.setTenantId(UserContext.getTenantId());

        // 检查智能表格字段是否存在
        KnowledgeTable knowledgeTable = knowledgeTableService.checkTable(dto.getTableSn());
        dto.setTableId(knowledgeTable.getTableId());

        //询满足姓名模糊查询的用户
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(dto.getKeyword())) {
            AppUserQueryDTO appUserQuery = new AppUserQueryDTO();
            appUserQuery.setUserName(dto.getKeyword());
            log.info("查询满足姓名模糊查询的用户 入参：{}", JSON.toJSONString((appUserQuery)));
            List<AppUserVO> list = userClient.list(appUserQuery).getData();
            log.info("查询满足姓名模糊查询的用户 出参：{}", JSON.toJSONString(list));
            dto.setUserIdsByName(list.stream().map(AppUserVO::getUserId).toList());
        }


        // 使用 PageHelper 进行分页查询
        PageHelper.startPage(dto.getPageNo(), dto.getPageSize());

        //查询列表
        List<TableManageListVo> result = getBaseMapper().list(dto);

        // 获取所有的创建人姓名和更新人姓名
        List<Integer> creators = result.stream().map(TableManageListVo::getCreateUserId).distinct().toList();

        List<Integer> updaters = result.stream().map(TableManageListVo::getUpdateUserId).distinct().toList();

        List<Integer> updaterAndCreator = new ArrayList<>();
        updaterAndCreator.addAll(creators);
        updaterAndCreator.addAll(updaters);

        // 获取创建人名字
        AppUserQueryDTO appUserQuery = new AppUserQueryDTO();
        appUserQuery.setUserIds(updaterAndCreator);
        log.info("查询姓名模糊查询的用户 入参：{}", JSON.toJSONString(appUserQuery));
        List<AppUserVO> list = userClient.list(appUserQuery).getData();
        log.info("查询姓名模糊查询的用户 出参：{}", JSON.toJSONString((list)));

        Map<Integer, AppUserVO> wordMap = list.stream().collect(Collectors.toMap(AppUserVO::getUserId, vo -> vo, (o1, o2) -> o1));

        result.forEach(f -> {
            f.setCreator(wordMap.get(f.getCreateUserId()).getUserName());
            f.setUpdater(wordMap.get(f.getUpdateUserId()).getUserName());
        });



        log.info("分页查询智能表格字段列表结束");
        return ResultVo.data(new PageInfo<>(result));
    }

    @Override
    public ResponseEntity<byte[]> downloadTemplate() throws IOException {
        log.info("下载智能表格字段模板开始");
        // 使用类加载器获取模板文件的输入流
        Resource resource = resourceLoader.getResource("classpath:智能表格字段批量导入模版.xlsx");
        InputStream inputStream = resource.getInputStream();

        // 读取模板文件的内容
        byte[] fileContent = inputStream.readAllBytes();

        // 设置响应头信息
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", URLEncoder.encode("智能表格字段批量导入模版.xlsx", StandardCharsets.UTF_8));

        // 返回响应实体
        log.info("下载智能表格字段模板结束");
        return ResponseEntity.ok()
                .headers(headers)
                .body(fileContent);
    }

    @Override
    /**
     * 批量导入智能表格字段定义
     *
     * @param file 上传的Excel文件，包含字段定义数据
     * @param tableSn 智能表格的唯一标识符
     * @return 返回导入结果的封装对象，包含成功、失败及总数等信息
     */
    public ResultVo<TableManageImportVo> batchImport(MultipartFile file, String tableSn) {
        log.info("批量导入智能表格字段定义开始, 文件名: {}, 表格SN: {}", file.getOriginalFilename(), tableSn);
        int maxCol = 20; // 定义最大列数限制为20

        // 校验并获取智能表格实体
        KnowledgeTable knowledgeTable = knowledgeTableService.checkTable(tableSn);

        try {
            // 获取上传文件的原始文件名，并提取后缀
            String originalFilename = file.getOriginalFilename();
            String suffix = StringUtils.substringAfterLast(originalFilename, ".");
            // 校验文件类型是否为xlsx格式
            ServiceException.throwIf(!StringUtils.equals(suffix, "xlsx"), AgentDocError.INTELLIGENT_TABLE_FIELD_IMPORT_ERROR1);

            // 创建Excel工作簿对象并获取第一个sheet
            Workbook workbook = new XSSFWorkbook(file.getInputStream());
            Sheet sheetAt = workbook.getSheetAt(0);

            // 获取字段总数（基于第一行的实际单元格数量）
            int total = sheetAt.getRow(1).getPhysicalNumberOfCells() - 1;
            if (total > maxCol) {
                total = maxCol; // 超过最大列数则截断
            }

            // 初始化字段相关集合用于存储解析后的数据
            List<KnowledgeTableSchema> schemaList = new ArrayList<>();
            Map<Integer, String> fieldNameMap = new HashMap<>();
            Map<Integer, String> dataTypeMap = new HashMap<>();
            Map<Integer, String> descMap = new HashMap<>();
            Map<Integer, String> keyMap = new HashMap<>();

            // 遍历每一行进行处理
            sheetAt.forEach(row -> processRow(row, maxCol, fieldNameMap, dataTypeMap, descMap, keyMap));

            // 准备存储key字段和普通字段名称列表
            List<String> keyFieldNames = new ArrayList<>();
            List<String> fieldNames = new ArrayList<>();

            // 构建每个字段的定义
            for (int i = 1; i <= total; i++) {
                String fieldName = fieldNameMap.get(i);
                // 忽略空或超长字段名
                if (StringUtils.isBlank(fieldName) || fieldName.length() > 20) {
                    continue;
                }
                // 忽略重复字段名
                if (fieldNames.contains(fieldName)) {
                    continue;
                }

                fieldNames.add(fieldName); // 添加到普通字段名列表
                String keyDesc = keyMap.get(i);
                // 如果标记为“是”，则加入key字段列表
                if (StringUtils.equals(keyDesc, SchemaKeyEnum.IS_KEY.getDescription())) {
                    keyFieldNames.add(fieldName);
                }

                // 获取数据类型并处理描述信息
                String dataType = getDataTypeByName(dataTypeMap.get(i));
                String description = processDescription(descMap.get(i));

                // 构建字段定义对象并添加至列表
                KnowledgeTableSchema schema = buildKnowledgeTableSchema(knowledgeTable, i, fieldName, dataType, description, keyDesc);
                schemaList.add(schema);
            }

            // 如果无有效字段定义，则返回空结果
            if (schemaList.isEmpty()) {
                return buildEmptyResult(tableSn, total);
            }

            // 处理key字段校验（已有数据时不可修改）
            handleKeyFieldValidation(knowledgeTable, keyFieldNames, schemaList);
            // 处理重复字段校验
            handleDuplicateFields(knowledgeTable, fieldNames, schemaList);
            // 更新排序并保存字段定义
            updateSortAndSave(knowledgeTable, schemaList);

            // 返回最终导入结果
            log.info("批量导入智能表格字段定义结束");
            return buildImportResult(schemaList, total, knowledgeTable.getTableSn());
        } catch (Exception e) {
            // 异常统一处理
            handleException(e);
            return ResultVo.fail("导入失败");
        }
    }

    private void processRow(org.apache.poi.ss.usermodel.Row row, int maxCol,
                            Map<Integer, String> fieldNameMap,
                            Map<Integer, String> dataTypeMap,
                            Map<Integer, String> descMap,
                            Map<Integer, String> keyMap) {
        if (row.getRowNum() == 0) return;

        DecimalFormat decimalFormat = new DecimalFormat("#.#");
        row.forEach(cell -> {
            int columnIndex = cell.getColumnIndex();
            if (columnIndex == 0) {
                this.checkImportTemplate(row.getRowNum(), getCellValue(cell, decimalFormat));
                return;
            }
            if (columnIndex > maxCol) return;

            String value = getCellValue(cell, decimalFormat);
            switch (row.getRowNum()) {
                case 1:
                    fieldNameMap.put(columnIndex, value);
                    break;
                case 2:
                    dataTypeMap.put(columnIndex, value);
                    break;
                case 3:
                    descMap.put(columnIndex, value);
                    break;
                case 4:
                    String dataType = dataTypeMap.get(columnIndex);
                    keyMap.put(columnIndex, StringUtils.equals("批量文件", dataType) ? "否" : value);
                    break;
            }
        });
    }

    private String getCellValue(org.apache.poi.ss.usermodel.Cell cell, DecimalFormat decimalFormat) {
        if (cell.getCellType() == CellType.STRING) {
            return cell.getStringCellValue();
        } else if (cell.getCellType() == CellType.NUMERIC) {
            return decimalFormat.format(cell.getNumericCellValue());
        } else {
            return cell.getStringCellValue();
        }
    }

    private String processDescription(String description) {
        if (StringUtils.isBlank(description)) {
            return null;
        }
        return description.length() > 50 ? null : description;
    }

    private KnowledgeTableSchema buildKnowledgeTableSchema(KnowledgeTable knowledgeTable, int index,
                                                           String fieldName, String fieldType,
                                                           String description, String keyDesc) {
        return KnowledgeTableSchema.builder()
                .tableId(knowledgeTable.getTableId())
                .fieldSn(UUIDUtil.genRandomSn("schema"))
                .fieldName(fieldName)
                .fieldType(fieldType)
                .fieldDesc(description)
                .fieldKey(SchemaKeyEnum.getCodeByDesc(keyDesc))
                .fieldSort(index)
                .fieldStatus(SchemaStatusEnum.VALID.getCode())
                .createTime(new Timestamp(System.currentTimeMillis()))
                .updateTime(new Timestamp(System.currentTimeMillis()))
                .build();
    }

    private void handleKeyFieldValidation(KnowledgeTable knowledgeTable, List<String> keyFieldNames,
                                          List<KnowledgeTableSchema> schemaList) {
        if (!keyFieldNames.isEmpty()) {
            Long count = knowledgeTableRowMapper.selectCount(new LambdaQueryWrapper<KnowledgeTableRow>()
                    .eq(KnowledgeTableRow::getTableId, knowledgeTable.getTableId())
                    .eq(KnowledgeTableRow::getRowStatus, SchemaStatusEnum.VALID.getCode()));

            if (count > 0) {
                schemaList.removeIf(item -> keyFieldNames.contains(item.getFieldName()));
            }
        }
    }

    private void handleDuplicateFields(KnowledgeTable knowledgeTable, List<String> fieldNames,
                                       List<KnowledgeTableSchema> schemaList) {
        if (fieldNames.isEmpty()) return;

        LambdaQueryWrapper<KnowledgeTableSchema> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(KnowledgeTableSchema::getFieldName)
                .eq(KnowledgeTableSchema::getTableId, knowledgeTable.getTableId())
                .in(KnowledgeTableSchema::getFieldName, fieldNames)
                .eq(KnowledgeTableSchema::getFieldStatus, SchemaStatusEnum.VALID.getCode());

        List<KnowledgeTableSchema> repeatFields = getBaseMapper().selectList(queryWrapper);
        if (!repeatFields.isEmpty()) {
            List<String> list = repeatFields.stream()
                    .map(KnowledgeTableSchema::getFieldName)
                    .toList();
            schemaList.removeIf(c -> list.contains(c.getFieldName()));
        }
    }

    private void updateSortAndSave(KnowledgeTable knowledgeTable, List<KnowledgeTableSchema> schemaList) {
        LambdaQueryWrapper<KnowledgeTableSchema> sortWrapper = new LambdaQueryWrapper<>();
        sortWrapper.select(KnowledgeTableSchema::getFieldSort)
                .eq(KnowledgeTableSchema::getTableId, knowledgeTable.getTableId())
                .eq(KnowledgeTableSchema::getFieldStatus, SchemaStatusEnum.VALID.getCode())
                .orderByDesc(KnowledgeTableSchema::getFieldSort)
                .last("LIMIT 1");

        KnowledgeTableSchema definition = getBaseMapper().selectOne(sortWrapper);
        int baseSort = definition != null ? definition.getFieldSort() : 0;

        schemaList.forEach(item -> {
                    item.setFieldSort(baseSort + item.getFieldSort());
                    item.setCreateUserId(UserContext.getUserId());
                    item.setUpdateUserId(UserContext.getUserId());
                }
        );

        if (!schemaList.isEmpty()) {
            saveBatch(schemaList);
            knowledgeTable.setUpdateTime(new Date());
            knowledgeTable.setUpdateUserId(UserContext.getUserId());
            knowledgeTableService.updateById(knowledgeTable);
        }
    }

    private ResultVo<TableManageImportVo> buildEmptyResult(String tableSn, int total) {
        return ResultVo.data(TableManageImportVo.builder()
                .successful(0)
                .fail(total)
                .totalCount(total)
                .tableSn(tableSn)
                .build());
    }

    private ResultVo<TableManageImportVo> buildImportResult(List<KnowledgeTableSchema> schemaList, int total, String tableSn) {
        return ResultVo.data(TableManageImportVo.builder()
                .successful(schemaList.size())
                .fail(total - schemaList.size())
                .totalCount(total)
                .tableSn(tableSn)
                .build());
    }

    private void handleException(Exception e) {
        if (e instanceof ServiceException ex) {
            throw new ServiceException(ex.getCode(), ex.getMessage());
        }
        throw new ServiceException(AgentDocError.INTELLIGENT_TABLE_FIELD_IMPORT_ERROR2);
    }

    public void checkFiledName(String fieldName, Integer tableId) {
        KnowledgeTableSchema knowledgeTableSchema = this.baseMapper.selectOne(Wrappers.lambdaQuery(KnowledgeTableSchema.class).eq(KnowledgeTableSchema::getTableId, tableId).eq(KnowledgeTableSchema::getFieldStatus, SchemaStatusEnum.VALID.getCode()).eq(KnowledgeTableSchema::getFieldName, fieldName).last(" limit 1 "));
        if (ObjectUtils.isNotEmpty(knowledgeTableSchema)) {
            throw new ServiceException(AgentDocError.INTELLIGENT_TABLE_FIELD_NAME_HAS_EXISTS.getCode(), AgentDocError.INTELLIGENT_TABLE_FIELD_NAME_HAS_EXISTS.getMessage());
        }
    }

    public KnowledgeTableSchema checkSchema(String fieldSn) {
        KnowledgeTableSchema schema = this.baseMapper.selectOne(Wrappers.lambdaQuery(KnowledgeTableSchema.class).eq(KnowledgeTableSchema::getFieldSn, fieldSn).eq(KnowledgeTableSchema::getFieldStatus, SchemaStatusEnum.VALID.getCode()));
        if (ObjectUtils.isEmpty(schema)) {
            throw new ServiceException(AgentDocError.INTELLIGENT_TABLE_DATA_FIELD_IS_NOT_EXISTS.getCode(), AgentDocError.INTELLIGENT_TABLE_DATA_FIELD_IS_NOT_EXISTS.getMessage());
        }
        return schema;
    }

    /**
     * 更新智能表格更新人信息
     */
    private void updateBaseByUpdateUser(String tableSn) {
        knowledgeTableService.update(Wrappers.lambdaUpdate(KnowledgeTable.class).set(KnowledgeTable::getUpdateTime, new Date()).set(KnowledgeTable::getUpdateUserId, UserContext.getUserId()).eq(KnowledgeTable::getTableSn, tableSn));
    }

    private void checkImportTemplate(int rowIndex, String value) {
        ServiceException.throwIf(rowIndex == 1 && !StringUtils.equals(value.trim(), "字段名称"), AgentDocError.INTELLIGENT_TABLE_FIELD_IMPORT_TEMPLATE_ERROR);
        ServiceException.throwIf(rowIndex == 2 && !StringUtils.equals(value.trim(), "数据类型"), AgentDocError.INTELLIGENT_TABLE_FIELD_IMPORT_TEMPLATE_ERROR);
        ServiceException.throwIf(rowIndex == 3 && !StringUtils.equals(value.trim(), "字段描述"), AgentDocError.INTELLIGENT_TABLE_FIELD_IMPORT_TEMPLATE_ERROR);
        ServiceException.throwIf(rowIndex == 4 && !StringUtils.equals(value.trim(), "设置为key"), AgentDocError.INTELLIGENT_TABLE_FIELD_IMPORT_TEMPLATE_ERROR);
    }

}
