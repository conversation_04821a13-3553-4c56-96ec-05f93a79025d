package com.ai.application.app.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 应用用户表
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(name = "应用用户表修改密码DTO")
public class UserUpdatePasswordDTO {
    /**
     * 登录密码
     */
    @Schema(description = "登录密码")
    @Length(min = 8,message = "密码长度不能小于8个字符")
    private String userPassword;

    @Schema(description = "登录密码")
    @Length(min = 8,message = "密码长度不能小于8个字符")
    private String confirmPassword;
}