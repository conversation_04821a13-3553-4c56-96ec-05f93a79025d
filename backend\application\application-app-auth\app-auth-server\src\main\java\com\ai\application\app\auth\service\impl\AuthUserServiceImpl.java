package com.ai.application.app.auth.service.impl;

import com.ai.application.app.api.feign.IAppUserClient;
import com.ai.application.app.api.vo.AppUserDetailVO;
import com.ai.application.app.auth.api.constants.TokenConstant;
import com.ai.application.app.auth.api.vo.UserInfoVO;
import com.ai.application.app.auth.service.IAuthUserService;
import com.ai.application.tenant.api.dto.query.TenantQueryDTO;
import com.ai.application.tenant.api.entity.Tenant;
import com.ai.application.tenant.api.feign.ITenantClient;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.string.StringUtil;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.util.validator.AssertUtil;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.redis.utils.RedisUtils;
import com.ai.framework.web.component.JwtComponent;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.User;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

@Slf4j
@Service
public class AuthUserServiceImpl implements IAuthUserService {
    @Resource
    private IAppUserClient appUserClient;

    @Resource
    private ITenantClient tenantClient;

    /**
     * 退出登录功能实现
     * 
     * 本方法主要完成用户退出登录时的相关操作，包括清除用户的相关缓存信息
     * 具体步骤如下：
     * 1. 检查用户编号是否存在，若不存在则抛出异常
     * 2. 根据用户编号从Redis中获取用户信息的缓存，若不存在则直接返回
     * 3. 删除用户的访问令牌（accessToken）和刷新令牌（refreshToken）相关的缓存信息
     */
    @Override
    public void logout() {
        // 获取当前用户编号
        String userSn = UserContext.getUserSn();
        // 校验用户编号是否存在，若不存在则抛出异常
        AssertUtil.isNotEmpty(userSn, "用户编号不存在");
        // 构造Redis中用户信息的键
        String userKey = TokenConstant.USER_KEY + userSn;

        // 从Redis中获取用户信息的缓存
        String userKeyJsonStr = RedisUtils.getCacheObject(userKey);
        // 日志记录用户信息缓存内容
        log.info("userKeyJsonStr:{}", userKeyJsonStr);
        // 若用户信息缓存为空，则直接返回
        if (StringUtil.isEmpty(userKeyJsonStr)) {
            return;
        }

        // 删除用户信息的缓存，并记录删除结果
        boolean userKeyStatus = RedisUtils.deleteObject(userKey);
        log.info("userKey delete:{}", userKeyStatus);

        // 解析用户信息缓存为Map，以便获取accessToken和refreshToken
        Map<String, String> userKeyJsonMap = JsonUtils.parseMapStr(userKeyJsonStr);
        String accessToken = userKeyJsonMap.get("accessToken");
        String refreshToken = userKeyJsonMap.get("refreshToken");
        log.info("accessToken:{},refreshToken:{}", accessToken, refreshToken);

        // 若refreshToken存在，则删除对应的缓存
        if (!StringUtil.isBlank(refreshToken)) {
            String refreshTokenKey = TokenConstant.USER_REFRESH_TOKEN.concat(refreshToken);
            boolean refreshTokenKeyStatus = RedisUtils.deleteObject(refreshTokenKey);
            log.info("refreshTokenKey delete:{}", refreshTokenKeyStatus);
        }

        // 若accessToken存在，则删除对应的缓存
        if (!StringUtil.isBlank(accessToken)) {
            String accessTokenKey = TokenConstant.USER_ACCESS_TOKEN.concat(accessToken);
            boolean refreshTokenKeyStatus = RedisUtils.deleteObject(accessTokenKey);
            log.info("accessTokenKey delete:{}", refreshTokenKeyStatus);
        }
    }

    /**
     * 获取用户信息
     * @return
     */
    @Override
    public UserInfoVO getUserInfo() {
        String userSn = UserContext.getUserSn();
        log.info("userSn:{}", userSn);
        ResultVo<AppUserDetailVO> userBySnRes = appUserClient.getUserBySn(userSn);
        AssertUtil.isTrue(userBySnRes.isSuccess(), "获取用户信息异常");
        if (Objects.isNull(userBySnRes.getData())) {
            return null;
        }

        AppUserDetailVO userData = userBySnRes.getData();
        UserInfoVO userInfoVO = new UserInfoVO();
        userInfoVO.setUserName(userData.getUserAccount());
        userInfoVO.setEmail(userData.getUserEmail());
        userInfoVO.setNickname(userData.getUserName());
        userInfoVO.setUserSn(userData.getUserSn());

        ResultVo<Tenant> tenantResultVo = tenantClient.getTenantById(userData.getTenantId());
        if (Objects.nonNull(tenantResultVo.getData())) {
            userInfoVO.setTenant(tenantResultVo.getData());
        }

        userInfoVO.setResourceCodes(List.of("index:login", "index:chat"));
        UserInfoVO.Role role = new UserInfoVO.Role();
        role.setRoleName("admin");
        role.setCode(UUIDUtil.genRandomSn("role"));
        userInfoVO.setRoles(List.of(role));

        return userInfoVO;
    }
}
