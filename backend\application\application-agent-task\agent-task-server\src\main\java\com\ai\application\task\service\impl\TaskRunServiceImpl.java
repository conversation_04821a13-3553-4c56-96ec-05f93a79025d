package com.ai.application.task.service.impl;

import com.github.pagehelper.PageInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ai.application.task.mapper.TaskRunMapper;
import com.ai.application.task.api.entity.TaskRun;
import com.ai.application.task.service.ITaskRunService;
import com.ai.application.task.api.dto.TaskRunDTO;
import com.ai.application.task.api.dto.query.TaskRunQueryDTO;
import com.ai.application.task.api.vo.TaskRunVO;
import com.ai.application.task.api.mapstruct.TaskRunMapstruct;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Date;

/**
 * 任务执行记录表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Service
public class TaskRunServiceImpl implements ITaskRunService{

    @Resource
    private TaskRunMapper taskRunMapper;

    @Resource
    private TaskRunMapstruct taskRunMapstruct;

    @Transactional(readOnly = true)
    @Override
    public PageInfo<TaskRunVO> page(TaskRunQueryDTO queryDto) {
        QueryWrapper<TaskRun> queryWrapper = this.buildQuery(queryDto);
        Page<TaskRun> page = new Page<>(queryDto.getPageNo(), queryDto.getPageSize());
        Page<TaskRun> result = this.taskRunMapper.selectPage(page, queryWrapper);
        return PageInfo.of(taskRunMapstruct.toVoList(result.getRecords()));
    }

    @Transactional(readOnly = true)
    @Override
    public List<TaskRunVO> list(TaskRunQueryDTO queryDto) {
        QueryWrapper<TaskRun> queryWrapper = this.buildQuery(queryDto);
        return taskRunMapstruct.toVoList(this.taskRunMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(TaskRunDTO dto) {
        dto.setTaskId(null);
        TaskRun entity = taskRunMapstruct.toEntity(dto);
        entity.setCreateTime(new Date());

        taskRunMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(TaskRunDTO dto) {
        BusinessAssertUtil.notNull(dto.getTaskId(), "TaskId不能为空");

        // TODO 唯一性字段校验
        TaskRun entity = taskRunMapper.selectById(dto.getTaskId());
        BusinessAssertUtil.notNull(entity, "找不到TaskId为 " + dto.getTaskId() + " 的记录");

        TaskRun entityList = taskRunMapstruct.toEntity(dto);
        entityList.setUpdateTime(new Date());
        taskRunMapper.updateById(entityList);
    }

    @Transactional(readOnly = true)
    @Override
    public TaskRunVO get(Integer id) {
        BusinessAssertUtil.notNull(id, "TaskId不能为空");

        TaskRun entity = taskRunMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到TaskId为 " + id + " 的记录");

        return taskRunMapstruct.toVo(entity);
    }

    private QueryWrapper<TaskRun> buildQuery(TaskRunQueryDTO queryDto) {
        QueryWrapper<TaskRun> queryWrapper = new QueryWrapper<>();
        return queryWrapper;
    }
}