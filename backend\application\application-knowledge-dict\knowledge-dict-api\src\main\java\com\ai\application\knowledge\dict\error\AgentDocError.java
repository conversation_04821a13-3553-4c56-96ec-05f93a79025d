package com.ai.application.knowledge.dict.error;


import com.ai.framework.core.enums.IErrorCode;

public enum AgentDocError implements IErrorCode {

    DICT_IS_NOT_EXISTS(31300, "词库不存在"),

    DICT_NAME_EXISTS(31301, "词库名称重复"),

    DICT_WORD_EXISTS(31302, "词/别名重复，请检查后重新输入"),

    DICT_WORD_IS_NOT_EXISTS(31303, "词不存在"),

    FILE_READ_ERROR(31304, "文件读取错误"),

    DICT_WORD_FIELD_IMPORT_ILLEGALITY_ERROR(31305, "仅支持xlsx格式文件"),

    DICT_WORD_FIELD_IMPORT_FORMAT_ERROR(31306, "上传格式错误文件"),

    DICT_WORD_RELATIVE_AGENT(31334,"词库已关联启用的智能体，请先解除关联");
    ;

    private Integer code;
    private String message;

    AgentDocError(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getMessage() {
        return this.message;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }
}
