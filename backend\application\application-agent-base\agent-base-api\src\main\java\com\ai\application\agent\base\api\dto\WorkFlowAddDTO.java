package com.ai.application.agent.base.api.dto;

import com.ai.application.agent.base.api.bo.*;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Schema(name = "WorkFlowAddDTO")
@Data
public class WorkFlowAddDTO {
    @Schema(description = "Agent编码")
    private String agentSn;

    @Schema(description = "版本名称")
    private String versionNumber;

    @Schema(description = "版本状态")
    private Integer versionStatus;

    @Schema(description = "元素")
    private JSONObject graph;

    @Schema(description = "定量")
    private List<FlowVariableBO> variables;

    @Schema(description = "流程")
    private List<FlowElementBO> steps;

    @Schema(description = "敏感词")
    public List<MasterDictBO> sensitives;

    @Schema(description = "短期记忆")
    private List<ShortMemoryBO> shortMemory;

    @Schema(description = "长期记忆")
    private LongMemoryBO longMemory;
}
