package com.ai.application.agent.base.api.dto;

import com.ai.application.agent.base.api.bo.FlowElementBO;
import com.ai.application.agent.base.api.bo.FlowVariableBO;
import com.ai.application.agent.base.api.bo.LongMemoryBO;
import com.ai.application.agent.base.api.bo.ShortMemoryBO;
import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Schema(name = "智能体版本添加")
@Data
public class WorkFlowAddDTO {
    @Schema(description = "Agent编码")
    private String agentSn;

    @Schema(description = "版本名称")
    private String versionNumber;

    @Schema(description = "版本状态")
    private Integer versionStatus;

    @Schema(description = "元素定义")
    private JSONObject extensions;

    @Schema(description = "定量")
    private List<FlowVariableBO> variables;

    @Schema(description = "开始变量")
    private List<FlowVariableBO> startVariables;

    @Schema(description = "结束变量")
    private List<FlowVariableBO> endVariables;

    @Schema(description = "流程元素")
    private List<FlowElementBO> definition;

    @Schema(description = "短期记忆")
    private List<ShortMemoryBO> shortMemory;

    @Schema(description = "长期记忆")
    private LongMemoryBO longMemory;
}
