<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;30fd1d7e-7960-43d6-af0a-835cfc4506de&quot;,&quot;conversations&quot;:{&quot;7bddfd3a-8749-49df-9dce-9433aa7608b8&quot;:{&quot;id&quot;:&quot;7bddfd3a-8749-49df-9dce-9433aa7608b8&quot;,&quot;createdAtIso&quot;:&quot;2025-06-13T01:39:37.018Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-13T01:39:37.018Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0},&quot;30fd1d7e-7960-43d6-af0a-835cfc4506de&quot;:{&quot;id&quot;:&quot;30fd1d7e-7960-43d6-af0a-835cfc4506de&quot;,&quot;createdAtIso&quot;:&quot;2025-06-13T01:39:37.173Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-13T01:39:37.173Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;332ec715-ff37-41f7-af68-45733660daaf&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;sortConversationsBy&quot;:&quot;lastMessageTimestamp&quot;,&quot;sendMode&quot;:&quot;send&quot;}" />
      </map>
    </option>
  </component>
</project>