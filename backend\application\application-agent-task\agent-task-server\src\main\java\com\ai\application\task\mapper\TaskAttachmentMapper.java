package com.ai.application.task.mapper;

import com.ai.application.task.api.entity.TaskAttachment;
import com.ai.application.task.api.entity.TaskRun;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.application.task.api.vo.TaskAttachmentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 任务附件表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Mapper
public interface TaskAttachmentMapper extends BaseMapper<TaskAttachment> {
    /**
     * 查询任务附件表
     *
     * @return
     */
    List<TaskAttachmentVO> selectTaskAttachmentList();

    @Select("select * from task_attachment where task_id = #{taskId}")
    List<TaskRun> queryTaskAttachmentByTaskId(@Param("taskId") Integer taskId);
}
