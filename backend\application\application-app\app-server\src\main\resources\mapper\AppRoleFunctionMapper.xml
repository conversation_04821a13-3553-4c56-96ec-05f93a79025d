<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.app.mapper.AppRoleFunctionMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.app.api.entity.AppRoleFunction">
                    <id column="rf_id" property="rfId" />
                    <result column="role_id" property="roleId" />
                    <result column="fun_id" property="funId" />
                    <result column="rf_status" property="rfStatus" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        rf_id, role_id, fun_id, rf_status, create_time, update_time
    </sql>

    <select id="selectAppRoleFunctionList" resultType="com.ai.application.app.api.vo.AppRoleFunctionVO">
        select
        <include refid="com.ai.application.app.mapper.AppRoleFunctionMapper.Base_Column_List"></include>
        from app_role_function
        order by create_time desc limit 10;
    </select>

    <select id="queryFunctionListByRoleIds" resultType="com.ai.application.app.api.vo.AppRoleFunctionVO">
        select
        <include refid="com.ai.application.app.mapper.AppRoleFunctionMapper.Base_Column_List"></include>
        from app_role_function
        <where>
            rf_status = 1
        <if test="roleIds != null and roleIds.size &gt; 0">
            and role_id in
            <foreach close=")" collection="roleIds" item="roleId" open="(" separator=",">
                #{roleId}
            </foreach>
        </if>
        </where>
    </select>

</mapper>