package com.ai.application.app.api.mapstruct;
import com.ai.application.app.api.entity.AppFunction;
import com.ai.application.app.api.dto.AppFunctionDTO;
import com.ai.application.app.api.vo.AppFunctionVO;
import com.ai.application.app.api.vo.AppRoleFunctionTreeVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 应用功能表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */

@Mapper(componentModel = "spring")
public interface AppFunctionMapstruct {

    AppFunction toEntity(AppFunctionDTO dto);
    List<AppFunction> toEntityList(List<AppFunctionDTO> dtolist);
    AppFunctionVO toVo(AppFunction entity);
    List<AppFunctionVO> toVoList(List<AppFunction> entities);
    List<AppRoleFunctionTreeVO> toTreeVoList(List<AppFunction> entities);
}
