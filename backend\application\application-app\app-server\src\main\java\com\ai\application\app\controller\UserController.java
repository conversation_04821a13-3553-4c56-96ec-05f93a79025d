package com.ai.application.app.controller;

import com.ai.application.app.api.dto.UserLoginDTO;
import com.ai.application.app.api.vo.GetUserInfoVO;
import com.ai.application.app.api.vo.UserLoginVO;
import com.ai.application.app.service.IUserService;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 应用表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Tag(name = "用户", description = "用户相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1/user")
public class UserController {

    @Resource
    private IUserService userService;

    /**
     * 用户登录
     * @param dto
     * @return
     */
    @Operation(summary = "用户登录")
    @PostMapping(value = "/login")
    public ResultVo<UserLoginVO> login(@Validated @RequestBody UserLoginDTO dto) {
        return ResultVo.data(userService.login(dto));
    }

    @Operation(summary = "退出登录")
    @PostMapping(value = "/logout")
    public ResultVo<Boolean> logout() {
        userService.logout();
        return ResultVo.data(Boolean.TRUE);
    }

    /**
     * 获取用户登录后的信息
     * @return
     */
    @Operation(summary = "用户信息")
    @GetMapping(value = "/info")
    public ResultVo<GetUserInfoVO> info() {
        return ResultVo.data(userService.getUserInfo());
    }
}