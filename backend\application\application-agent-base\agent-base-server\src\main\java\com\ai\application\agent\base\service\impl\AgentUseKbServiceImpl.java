package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.dto.AgentUseKbUpdateDTO;
import com.ai.application.agent.base.service.IAgentUseKbService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ai.application.agent.base.mapper.AgentUseKbMapper;
import com.ai.application.agent.base.api.entity.AgentUseKb;
import com.ai.application.agent.base.api.dto.AgentUseKbAddDTO;
import com.ai.application.agent.base.api.dto.AgentUseKbListDTO;
import com.ai.application.agent.base.api.vo.AgentUseKbListVO;
import com.ai.application.agent.base.api.mapstruct.AgentUseKbMapstruct;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Date;
import java.util.Objects;

/**
 * 智能体关联知识库表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Service
public class AgentUseKbServiceImpl implements IAgentUseKbService {

    @Resource
    private AgentUseKbMapper agentUseKbMapper;

    @Resource
    private AgentUseKbMapstruct agentUseKbMapstruct;

    @Transactional(readOnly = true)
    @Override
    public List<AgentUseKbListVO> list(AgentUseKbListDTO queryDto) {
        LambdaQueryWrapper<AgentUseKb> queryWrapper = this.buildQuery(queryDto);
        return agentUseKbMapstruct.toVoList(this.agentUseKbMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(AgentUseKbAddDTO dto) {
        AgentUseKb entity = agentUseKbMapstruct.toAddEntity(dto);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());

        agentUseKbMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AgentUseKbUpdateDTO dto) {
        BusinessAssertUtil.notNull(dto.getKbId(), "AdId不能为空");

        AgentUseKb entity = agentUseKbMapper.selectById(dto.getKbId());
        BusinessAssertUtil.notNull(entity, "找不到AdId为 " + dto.getKbId() + " 的记录");

        AgentUseKb entityList = agentUseKbMapstruct.toUpdateEntity(dto);
        entityList.setUpdateTime(new Date());
        agentUseKbMapper.updateById(entityList);
    }

    @Transactional(readOnly = true)
    @Override
    public List<Integer> toDetail(Integer versionId) {
        AgentUseKbListDTO agentUseKbListDTO = new AgentUseKbListDTO();
        agentUseKbListDTO.setVersionId(versionId);
        List<AgentUseKbListVO> list = this.list(agentUseKbListDTO);
        return list.stream().map(AgentUseKbListVO::getKbId).toList();
    }

    private LambdaQueryWrapper<AgentUseKb> buildQuery(AgentUseKbListDTO queryDto) {
        LambdaQueryWrapper<AgentUseKb> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(queryDto.getVersionId()), AgentUseKb::getVersionId, queryDto.getVersionId());
        return queryWrapper;
    }
}