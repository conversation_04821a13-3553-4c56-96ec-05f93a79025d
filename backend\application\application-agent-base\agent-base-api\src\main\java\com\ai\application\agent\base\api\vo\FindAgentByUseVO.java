package com.ai.application.agent.base.api.vo;

import com.ai.application.agent.base.api.enums.VersionStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class FindAgentByUseVO {
    @Schema(description = "agentID")
    private Integer agentId;

    @Schema(description = "agent编码")
    private String agentSn;

    @Schema(description = "agent名称")
    private String agentName;

    @Schema(description = "agent状态")
    private VersionStatusEnum versionStatusEnum;
}
