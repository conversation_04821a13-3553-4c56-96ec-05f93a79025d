package com.ai.application.tenant.authorize.api.feign;

import com.ai.application.tenant.authorize.api.dto.ResourceAddReqDTO;
import com.ai.application.tenant.authorize.api.dto.ResourceGrantReqDTO;
import com.ai.application.tenant.authorize.api.feign.fallback.ITenantAuthorizeClientFallback;
import com.ai.application.tenant.authorize.api.vo.ResourceVO;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Tag(name = "租户授权Feign接口", description = "租户授权基本操作")
@FeignClient(
        value = ServiceConstant.TENANT_AUTHORIZE,
        fallback = ITenantAuthorizeClientFallback.class,
        contextId = "ITenantAuthorizeClient"
)
@Component
public interface ITenantAuthorizeClient {
    String API_PREFIX = "/v1/feign/tenant/authorize";

    @PostMapping(API_PREFIX + "/queryGrantResourceList")
    ResultVo<List<ResourceVO>> queryGrantResourceList(@RequestBody @Validated ResourceGrantReqDTO req);

    @PostMapping(API_PREFIX + "/addResource")
    ResultVo<Void> addResource(@RequestBody @Validated ResourceAddReqDTO dto);
}
