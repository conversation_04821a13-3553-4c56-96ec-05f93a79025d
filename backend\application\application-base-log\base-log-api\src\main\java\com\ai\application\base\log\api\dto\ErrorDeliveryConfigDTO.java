package com.ai.application.base.log.api.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

/**
 * 错误通知配置表
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Data
@Schema(name = "错误通知配置表DTO")
public class ErrorDeliveryConfigDTO {
    /**
     * 配置id
     */
    @Schema(description = "配置id")
    private Integer configId;

    /**
     * 配置状态:1-启用,0-禁用,-1-删除
     */
    @Schema(description = "配置状态:1-启用,0-禁用,-1-删除")
    private Integer configStatus;

    /**
     * 告警错误级别:1-致命,2-严重,3-警告,4-信息,0-全部
     */
    @Schema(description = "告警错误级别:1-致命,2-严重,3-警告,4-信息,0-全部")
    private Integer deliveryLevel;

    /**
     * 通知方式:10-邮件,20-短信,30-钉钉,40-企微,50-飞书,60-<PERSON>lack,70-Webhook,80-站内消息
     */
    @Schema(description = "通知方式:10-邮件,20-短信,30-钉钉,40-企微,50-飞书,60-Slack,70-Webhook,80-站内消息")
    private Integer deliveryType;

    /**
     * 是否启用告警:0-禁用,1-启用
     */
    @Schema(description = "是否启用告警:0-禁用,1-启用")
    private Integer deliveryEnabled;

    /**
     * 接收人配置
     */
    @Schema(description = "接收人配置")
    private String deliveryRecipient;

    /**
     * 通知模板
     */
    @Schema(description = "通知模板")
    private String deliveryTemplate;

    /**
     * 是否启用重试:0-禁用,1-启用
     */
    @Schema(description = "是否启用重试:0-禁用,1-启用")
    private Integer retryEnabled;

    /**
     * 最大重试次数
     */
    @Schema(description = "最大重试次数")
    private Integer maxRetryCount;

    /**
     * 重试间隔(秒)
     */
    @Schema(description = "重试间隔(秒)")
    private Integer retryInterval;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Integer tenantId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}