package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.dto.AgentRunKbListDTO;
import com.ai.application.agent.base.api.dto.AgentRunKbDTO;
import com.ai.application.agent.base.api.vo.AgentRunKbVO;
import java.util.List;

/**
 * 智能体知识库检索记录表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public interface IAgentRunKbService {
    /**
     * 列表
     *
     * @param queryDto
     * @return
     */
    List<AgentRunKbVO> list(AgentRunKbListDTO queryDto);

    /**
     * 保存
     *
     * @param dto
     */
    void add(AgentRunKbDTO dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(AgentRunKbDTO dto);

    /**
     * 查看
     *
     * @param id
     * @return
     */
    AgentRunKbVO get(Integer id);
}