package com.ai.application.base.log.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 错误通知配置表
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Data
@Schema(name = "错误通知配置表DTO")
public class ErrorDeliveryConfigSaveDTO {
    @Schema(description = "告警错误级别:1-致命,2-严重,3-警告,4-信息,0-全部")
    private Integer deliveryLevel;

    /**
     * 通知方式:10-邮件,20-短信,30-钉钉,40-企微,50-飞书,60-Slack,70-Webhook,80-站内消息
     */
    @Schema(description = "通知方式:10-邮件,20-短信,30-钉钉,40-企微,50-飞书,60-Slack,70-Webhook,80-站内消息")
    private Integer deliveryType;

    /**
     * 接收人配置
     */
    @Schema(description = "接收人配置")
    private String deliveryRecipient;

    /**
     * 通知模板
     */
    @Schema(description = "通知模板")
    private String deliveryTemplate;

}