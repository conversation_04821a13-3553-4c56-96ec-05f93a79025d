package com.ai.application.agent.base.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 智能体MCP工具关联表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("agent_use_mcp")
public class AgentUseMcp implements Serializable {
    /**
    * 关联id
    */
    @Schema(description = "关联id")
    @TableId(type = IdType.AUTO)
    private Integer amcId;

    /**
    * 关联状态:1-启用,0-禁用,-1-删除
    */
    @Schema(description = "关联状态:1-启用,0-禁用,-1-删除")
    private Integer amcStatus;

    /**
    * 智能体id
    */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
    * 智能体版本id
    */
    @Schema(description = "智能体版本id")
    private Integer versionId;

    @Schema(description = "mcp扩展信息")
    private String mcpExtend;

    /**
    * MCP工具id
    */
    @Schema(description = "MCP工具id")
    private Integer mcpToolId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}