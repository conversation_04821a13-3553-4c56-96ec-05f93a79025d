package com.ai.application.admin.api.mapstruct;

import com.ai.application.admin.api.dto.TenantDepartmentDTO;
import com.ai.application.admin.api.entity.TenantDepartment;
import com.ai.application.admin.api.vo.TenantDepartmentTreeVO;
import com.ai.application.admin.api.vo.TenantDepartmentVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 * 租户部门表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */

@Mapper(componentModel = "spring")
public interface TenantDepartmentMapstruct {

    TenantDepartment toEntity(TenantDepartmentDTO dto);
    List<TenantDepartment> toEntityList(List<TenantDepartmentDTO> dtolist);
    TenantDepartmentVO toVo(TenantDepartment entity);
    List<TenantDepartmentVO> toVoList(List<TenantDepartment> entities);
    List<TenantDepartmentTreeVO> toTreeVoList(List<TenantDepartment> entities);
}
