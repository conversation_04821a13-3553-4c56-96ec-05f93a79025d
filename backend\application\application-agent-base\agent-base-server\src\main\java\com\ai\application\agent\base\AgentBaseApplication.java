package com.ai.application.agent.base;

import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.launch.AiApplication;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

@EnableDiscoveryClient
@ComponentScan(basePackages = {"com.ai"})
@SpringBootApplication(exclude= {DataSourceAutoConfiguration.class})
@EnableConfigurationProperties(DynamicDataSourceProperties.class)
@MapperScan(basePackages = {"com.ai.application.agent.base.mapper"})
@EnableFeignClients("com.ai")
public class AgentBaseApplication {
    public static void main(String[] args) {
        AiApplication.run(ServiceConstant.AGENT_BASE, AgentBaseApplication.class, args);
    }
}