package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.dto.AgentRunLlmListDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ai.application.agent.base.mapper.AgentRunLlmMapper;
import com.ai.application.agent.base.api.entity.AgentRunLlm;
import com.ai.application.agent.base.service.IAgentRunLlmService;
import com.ai.application.agent.base.api.dto.AgentRunLlmDTO;
import com.ai.application.agent.base.api.vo.AgentRunLlmVO;
import com.ai.application.agent.base.api.mapstruct.AgentRunLlmMapstruct;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Date;

/**
 * 智能体LLM调用记录表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
public class AgentRunLlmServiceImpl implements IAgentRunLlmService{

    @Resource
    private AgentRunLlmMapper agentRunLlmMapper;

    @Resource
    private AgentRunLlmMapstruct agentRunLlmMapstruct;

    @Transactional(readOnly = true)
    @Override
    public List<AgentRunLlmVO> list(AgentRunLlmListDTO queryDto) {
        LambdaQueryWrapper<AgentRunLlm> queryWrapper = this.buildQuery(queryDto);
        return agentRunLlmMapstruct.toVoList(this.agentRunLlmMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(AgentRunLlmDTO dto) {
        dto.setRunId(null);
        AgentRunLlm entity = agentRunLlmMapstruct.toEntity(dto);
        entity.setCreateTime(new Date());

        agentRunLlmMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AgentRunLlmDTO dto) {
        BusinessAssertUtil.notNull(dto.getLlmRunId(), "ID不能为空");

        AgentRunLlm entity = agentRunLlmMapper.selectById(dto.getLlmRunId());
        BusinessAssertUtil.notNull(entity, "找不到ID为 " + dto.getLlmRunId() + " 的记录");

        AgentRunLlm entityList = agentRunLlmMapstruct.toEntity(dto);
        entityList.setUpdateTime(new Date());
        agentRunLlmMapper.updateById(entityList);
    }

    @Transactional(readOnly = true)
    @Override
    public AgentRunLlmVO get(Integer id) {
        BusinessAssertUtil.notNull(id, "ID不能为空");

        AgentRunLlm entity = agentRunLlmMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到ID为 " + id + " 的记录");

        return agentRunLlmMapstruct.toVo(entity);
    }

    private LambdaQueryWrapper<AgentRunLlm> buildQuery(AgentRunLlmListDTO queryDto) {
        LambdaQueryWrapper<AgentRunLlm> queryWrapper = new LambdaQueryWrapper<>();
        return queryWrapper;
    }
}