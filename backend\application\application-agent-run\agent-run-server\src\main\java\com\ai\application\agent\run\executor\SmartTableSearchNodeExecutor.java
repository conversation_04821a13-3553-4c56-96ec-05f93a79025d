package com.ai.application.agent.run.executor;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ai.application.agent.run.dto.SmartTableSearchRequestDTO;
import com.ai.application.agent.run.dto.SmartTableSearchResultDTO;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.service.ISmartTableSearchService;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import com.ai.framework.workflow.excutor.NodeExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 智能表格检索节点执行器
 * 用于在工作流中检索智能表格数据
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SmartTableSearchNodeExecutor implements NodeExecutor {

    private final ISmartTableSearchService smartTableSearchService;


    @Override
    public void execute(WorkflowContext context) {
        String nodeKey = context.getCurrentNodeKey();
        NodeContext nodeCtx = context.getNodeContexts().get(nodeKey);
        Map<String, Object> nodeDef = nodeCtx.getNodeDefinition();

        log.info("SmartTableSearchNodeExecutor execute start, nodeKey: {}, nodeDef: {}", nodeKey, JsonUtils.toJsonString(nodeDef));

        try {
            // 设置节点状态为运行中
            nodeCtx.setStatus(NodeStatus.RUNNING);

            // 初始化节点输出
            if (nodeCtx.getOutput() == null) {
                nodeCtx.setOutput(new HashMap<>());
            }

            // 从节点定义中获取输入参数
            Map<String, Object> inputParameters = (Map<String, Object>) nodeDef.get("inputParameters");
            if (inputParameters == null) {
                throw new ServiceException(ExecutorError.NODE_DEFINITION_IS_NULL);
            }

            // 构建智能表格检索请求
            SmartTableSearchRequestDTO request = buildSmartTableSearchRequest(inputParameters, context);

            // 获取授权信息
            String authorization = (String) context.getGlobalVars().get("authorization");

            // 执行智能表格检索
            ResultVo<SmartTableSearchResultDTO> result = smartTableSearchService.executeSmartTableSearch(request, authorization);

            if (result.getCode() != 0 || result.getData() == null || !result.getData().getSuccess()) {
                String errorMsg = result.getData() != null ? result.getData().getErrorMessage() : result.getMessage();
                throw new ServiceException(result.getCode(), errorMsg);
            }

            SmartTableSearchResultDTO searchResult = result.getData();

            // 构建输出结果
            Map<String, Object> outputResult = buildOutputResult(searchResult);

            // 将结果写入输出参数
            writeOutputParameters(nodeDef, context, outputResult);

            // 设置节点输出
            nodeCtx.getOutput().putAll(outputResult);

            // 设置节点状态为成功
            nodeCtx.setStatus(NodeStatus.SUCCESS);
            nodeCtx.setEndTime(java.time.LocalDateTime.now());

            log.info("SmartTableSearchNodeExecutor execute success, result count: {}", searchResult.getResultCount());

        } catch (Exception e) {
            log.error("SmartTableSearchNodeExecutor execute error", e);
            nodeCtx.setStatus(NodeStatus.FAILED);
            nodeCtx.setErrorMsg("智能表格检索执行失败: " + e.getMessage());
            nodeCtx.setEndTime(java.time.LocalDateTime.now());
            throw e;
        }
    }

    /**
     * 构建智能表格检索请求
     */
    private SmartTableSearchRequestDTO buildSmartTableSearchRequest(Map<String, Object> inputParameters, WorkflowContext context) {
        // 获取参数值，支持变量替换
        String intelligentTableSn = getParameterValue(inputParameters, "intelligentTableSn", context);
        Object ruleConditions = getParameterObject(inputParameters, "ruleConditions", context);
        Object semanticConditions = getParameterObject(inputParameters, "semanticConditions", context);
        String outputFieldStr = getParameterValue(inputParameters, "outputField", context);
        Object definitionSns = getParameterObject(inputParameters, "definitionSns", context);
        String maxOutputRowsStr = getParameterValue(inputParameters, "maxOutputRows", context);

        // 参数校验
        if (StringUtils.isBlank(intelligentTableSn)) {
            throw new ServiceException(ExecutorError.SMART_TABLE_SN_IS_BLANK);
        }

        // 转换规则条件
        List<SmartTableSearchRequestDTO.RuleCondition> ruleConditionList = null;
        if (ruleConditions instanceof List<?> rules) {
            ruleConditionList = new ArrayList<>();
            for (Object rule : rules) {
                SmartTableSearchRequestDTO.RuleCondition ruleCondition = convertToRuleCondition(rule);
                if (ruleCondition != null) {
                    ruleConditionList.add(ruleCondition);
                }
            }
        }

        // 转换语义条件
        List<SmartTableSearchRequestDTO.SemanticCondition> semanticConditionList = null;
        if (semanticConditions instanceof List<?> semantics) {
            semanticConditionList = new ArrayList<>();
            for (Object semantic : semantics) {
                SmartTableSearchRequestDTO.SemanticCondition semanticCondition = convertToSemanticCondition(semantic);
                if (semanticCondition != null) {
                    semanticConditionList.add(semanticCondition);
                }
            }
        }

        // 转换字段编号列表
        List<String> definitionSnList = null;
        if (definitionSns instanceof List<?> sns) {
            definitionSnList = new ArrayList<>();
            for (Object sn : sns) {
                if (sn != null) {
                    definitionSnList.add(sn.toString());
                }
            }
        }

        // 构建请求
        return SmartTableSearchRequestDTO.builder()
                .intelligentTableSn(intelligentTableSn)
                .ruleConditions(ruleConditionList)
                .semanticConditions(semanticConditionList)
                .outputField(StringUtils.isNotBlank(outputFieldStr) ? Integer.parseInt(outputFieldStr) : 0)
                .definitionSns(definitionSnList)
                .maxOutputRows(StringUtils.isNotBlank(maxOutputRowsStr) ? Integer.parseInt(maxOutputRowsStr) : null)
                .build();
    }

    /**
     * 转换为规则条件
     */
    private SmartTableSearchRequestDTO.RuleCondition convertToRuleCondition(Object rule) {
        if (rule == null) {
            return null;
        }

        try {
            JSONObject jsonRule = JSONUtil.parseObj(JsonUtils.toJsonString(rule));
            return SmartTableSearchRequestDTO.RuleCondition.builder()
                    .definitionSn(jsonRule.getStr("definitionSn"))
                    .condition(jsonRule.getStr("condition"))
                    .value(jsonRule.get("value"))
                    .filterType(jsonRule.getStr("filterType"))
                    .build();
        } catch (Exception e) {
            log.warn("Failed to convert rule condition: {}", rule, e);
            return null;
        }
    }

    /**
     * 转换为语义条件
     */
    private SmartTableSearchRequestDTO.SemanticCondition convertToSemanticCondition(Object semantic) {
        if (semantic == null) {
            return null;
        }

        try {
            JSONObject jsonSemantic = JSONUtil.parseObj(JsonUtils.toJsonString(semantic));
            List<String> definitionSnList = new ArrayList<>();
            Object definitionSn = jsonSemantic.get("definitionSn");
            if (definitionSn instanceof List<?> sns) {
                for (Object sn : sns) {
                    if (sn != null) {
                        definitionSnList.add(sn.toString());
                    }
                }
            }

            return SmartTableSearchRequestDTO.SemanticCondition.builder()
                    .definitionSn(definitionSnList)
                    .value(jsonSemantic.getStr("value"))
                    .similarity(jsonSemantic.getStr("similarity"))
                    .build();
        } catch (Exception e) {
            log.warn("Failed to convert semantic condition: {}", semantic, e);
            return null;
        }
    }

    /**
     * 构建输出结果
     */
    private Map<String, Object> buildOutputResult(SmartTableSearchResultDTO searchResult) {
        Map<String, Object> result = new HashMap<>();
        result.put("output", CollectionUtils.isNotEmpty(searchResult.getOutput()) ? searchResult.getOutput() : new ArrayList<>());
        return result;
    }

    /**
     * 获取参数值，支持变量替换
     */
    private String getParameterValue(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        String strValue = value.toString();
        
        // 如果是变量引用（以$开头），从全局变量中获取
        if (strValue.startsWith("$")) {
            String varName = strValue.substring(1);
            Object varValue = context.getGlobalVars().get(varName);
            return varValue != null ? varValue.toString() : null;
        }
        
        return strValue;
    }

    /**
     * 获取参数对象，支持变量替换
     */
    private Object getParameterObject(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        if (value instanceof String) {
            String strValue = value.toString();
            // 如果是变量引用（以$开头），从全局变量中获取
            if (strValue.startsWith("$")) {
                String varName = strValue.substring(1);
                return context.getGlobalVars().get(varName);
            }
        }
        
        return value;
    }

    /**
     * 写入输出参数
     */
    private void writeOutputParameters(Map<String, Object> nodeDef, WorkflowContext context, Map<String, Object> result) {
        Map<String, Object> outputParameters = (Map<String, Object>) nodeDef.get("outputParameters");
        if (outputParameters != null) {
            for (Map.Entry<String, Object> entry : outputParameters.entrySet()) {
                String outputKey = entry.getKey();
                String variableName = entry.getValue().toString();

                Object resultValue = result.get(outputKey);
                if (resultValue != null) {
                    context.setVar(variableName, resultValue);
                    log.info("Set variable {} = {}", variableName, resultValue);
                }
            }
        }
    }

    @Override
    public String getType() {
        return "SMART_TABLE_SEARCH";
    }
}
