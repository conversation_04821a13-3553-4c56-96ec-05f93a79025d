package com.ai.application.tenant.audit.feign;


import com.ai.application.tenant.audit.api.dto.TenantAuditDTO;
import com.ai.application.tenant.audit.api.dto.query.TenantAddDTO;
import com.ai.application.tenant.audit.api.feign.IAuditFeignClient;
import com.ai.application.tenant.audit.service.ITenantAuditService;
import com.ai.framework.core.vo.ResultVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 文件服务
 */
@RestController
@Slf4j
public class AuditFeignClient implements IAuditFeignClient {
    @Resource
    private ITenantAuditService tenantAuditService;

public ResultVo<Void> update( TenantAuditDTO dto){
    tenantAuditService.update(dto);
    return ResultVo.success("修改成功");
}


    public ResultVo<Void> add(TenantAddDTO dto) {
        tenantAuditService.add(dto);
        return ResultVo.success("保存成功");
    }




}
