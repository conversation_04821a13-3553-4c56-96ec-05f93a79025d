package com.ai.application.app.service;

import com.ai.application.app.api.dto.*;
import com.ai.application.app.api.dto.query.AppUserQueryDTO;
import com.ai.application.app.api.dto.query.AppUserQueryPageDTO;
import com.ai.application.app.api.dto.query.AppUserVerifyPasswordDTO;
import com.ai.application.app.api.vo.AppUserBatchImportResultVO;
import com.ai.application.app.api.vo.AppUserDetailVO;
import com.ai.application.app.api.vo.AppUserVO;
import com.github.pagehelper.PageInfo;
import lombok.SneakyThrows;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

/**
 * 应用用户表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public interface IAppUserService {

    /**
     * 分页
     *
     * @param queryDto
     * @return
     */
    PageInfo<AppUserVO> page(AppUserQueryPageDTO queryDto);

    /**
     * 列表
     *
     * @param queryDto
     * @return
     */
    List<AppUserVO> list(AppUserQueryDTO queryDto);

    void saveUserStatus(String userSn, Boolean enable);

    /**
     * 保存
     *
     * @param dto
     */
    void save(AppUserCreateDTO dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(AppUserUpdateDTO dto);

    /**
     * 查看
     *
     * @param id
     * @return
     */
    AppUserVO get(Integer id);

    void updatePassword(String userSn, UserUpdatePasswordDTO dto);

    /**
     * 删除
     *
     * @param ids
     */
    void delete(Set<Long> ids);

    AppUserDetailVO detail(String userSn);

    void initPassword(AppUserInitPasswordDto dto);

    void resetPassword(String userSn);

    ResponseEntity<byte[]> downloadImportTemplage();

    AppUserBatchImportResultVO batchImport(MultipartFile file);

    ResponseEntity<byte[]> exportFailData(ImportFailUserDTO dto);

    void moveUsers(Integer deptId, UserMoveDto dto);

    void setAdmin(String userSn);

    void deleteAdmin(String userSn);

    void deleteUser(String userSn);

    AppUserVO verifyPassword(AppUserVerifyPasswordDTO dto);
}