package com.ai.application.admin.mapper;

import com.ai.application.admin.api.entity.Resource;
import com.ai.application.admin.api.vo.AdminResourceVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 资源定义表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface ResourceMapper extends BaseMapper<Resource> {
    List<AdminResourceVO> queryAgentList();
    List<AdminResourceVO> queryGrantAgentList(@Param("tenantId") Integer tenantId);
    List<AdminResourceVO> queryModelList();
    List<AdminResourceVO> queryGrantModelList(@Param("tenantId") Integer tenantId);

    @Select("select * from resource where resource_object_id=#{resourceObjectId} and resource_type = #{resourceType} limit 1")
    Resource getResourceByResourceType(@Param("resourceObjectId") Integer resourceObjectId,@Param("resourceType") Integer resourceType);
}
