package com.ai.application.agent.run.errors;


import com.ai.framework.core.enums.IErrorCode;

/**
 * 执行器错误枚举
 * 统一管理 Agent 和 LLM 节点执行器的错误码
 */
public enum ExecutorError implements IErrorCode {

    // ==================== Agent 节点执行器错误 (51001-51020) ====================
    
    /**
     * Agent节点缺少输入参数配置
     */
    AGENT_NODE_MISSING_INPUT_PARAMETERS(51001, "Agent节点缺少inputParameters配置"),

    /**
     * Agent节点执行错误
     */
    AGENT_NODE_EXECUTOR_ERROR(51002, "Agent节点执行错误"),

    /**
     * Agent类型为空
     */
    AGENT_TYPE_IS_NULL(51003, "agentType 不能为空"),

    /**
     * Agent消息内容为空
     */
    AGENT_MSG_CONTENT_IS_NULL(51004, "msgContent 不能为空"),

    /**
     * Agent返回结果为空
     */
    AGENT_RESPONSE_IS_NULL(51005, "Agent返回结果为空"),



    // ==================== LLM 节点执行器错误 (51021-51040) ====================

    /**
     * LLM节点缺少输入参数配置
     */
    LLM_NODE_MISSING_INPUT_PARAMETERS(51021, "LLM节点缺少inputParameters配置"),

    /**
     * prompt 不能为空
     */
    LLM_PROMPT_IS_BLANK(51022, "prompt 不能为空"),

    /**
     * model 不能为空
     */
    LLM_MODEL_IS_BLANK(51023, "model 不能为空"),

    /**
     * 调用大模型失败
     */
    LLM_CALL_FAILED(51024, "调用大模型失败"),

    /**
     * 大模型返回结果为空
     */
    LLM_RESPONSE_IS_NULL(51025, "大模型返回结果为空"),

    // ==================== 知识添加执行器错误 (51061-51080) ====================

    /**
     * 知识库编号为空
     */
    KNOWLEDGE_INVENTORY_SN_IS_NULL(51061, "知识库编号不能为空"),

    /**
     * 知识库不存在
     */
    KNOWLEDGE_INVENTORY_NOT_FOUND(51062, "知识库不存在"),

    /**
     * 文件嵌入失败
     */
    KNOWLEDGE_FILE_EMBEDDING_FAILED(51063, "文件embedding失败"),

    /**
     * 文件嵌入状态查询失败
     */
    KNOWLEDGE_FILE_STATUS_QUERY_FAILED(51064, "文件embedding状态查询失败"),

    /**
     * 文件上传失败
     */
    KNOWLEDGE_FILE_UPLOAD_FAILED(51065, "文件上传失败"),

    /**
     * 分段规则配置错误
     */
    KNOWLEDGE_SPLIT_RULE_CONFIG_ERROR(51066, "分段规则配置错误"),

    /**
     * 深度解析参数错误
     */
    KNOWLEDGE_DEEP_PARSE_PARAM_ERROR(51067, "深度解析参数错误"),

    /**
     * 文件重复检查失败
     */
    KNOWLEDGE_FILE_DUPLICATE_CHECK_FAILED(51068, "文件重复检查失败"),

    /**
     * 文件删除失败
     */
    KNOWLEDGE_FILE_DELETE_FAILED(51069, "文件删除失败"),

    /**
     * 嵌入轮询超时
     */
    KNOWLEDGE_EMBEDDING_POLLING_TIMEOUT(51070, "嵌入轮询超时"),

    // ==================== 知识问答执行器错误 (51081-51100) ====================

    /**
     * 知识问答内容为空
     */
    KNOWLEDGE_QA_CONTENT_IS_BLANK(51081, "知识问答内容不能为空"),

    /**
     * 知识问答模型为空
     */
    KNOWLEDGE_QA_MODEL_IS_BLANK(51082, "知识问答模型不能为空"),

    /**
     * 知识问答总结提示词为空
     */
    KNOWLEDGE_QA_SUMMARY_PROMPT_IS_BLANK(51083, "知识问答总结提示词不能为空"),

    /**
     * 知识编号为空
     */
    KNOWLEDGE_SN_IS_NULL(51084, "知识编号不能为空"),

    /**
     * 知识问答结果为空
     */
    KNOWLEDGE_QA_RESULT_IS_NULL(51085, "知识问答结果为空"),

    /**
     * 知识片段为空
     */
    KNOWLEDGE_FRAGMENT_IS_BLANK(51086, "知识片段为空"),

    /**
     * 知识问答执行失败
     */
    KNOWLEDGE_QA_EXECUTION_FAILED(51087, "知识问答执行失败"),

    /**
     * 知识检索失败
     */
    KNOWLEDGE_SEARCH_FAILED(51088, "知识检索失败"),

    /**
     * 扩展内容解析失败
     */
    EXPAND_CONTENT_PARSE_FAILED(51089, "扩展内容解析失败"),

    /**
     * 知识问答模式不支持
     */
    KNOWLEDGE_QA_MODE_NOT_SUPPORTED(51090, "知识问答模式不支持"),

    // ==================== 文档知识检索执行器错误 (51101-51120) ====================

    /**
     * 文档检索内容为空
     */
    DOCUMENT_SEARCH_CONTENT_IS_BLANK(51101, "文档检索内容不能为空"),

    /**
     * 文档检索结果为空
     */
    DOCUMENT_SEARCH_RESULT_IS_NULL(51102, "文档检索结果为空"),

    /**
     * 文档检索类型不支持
     */
    DOCUMENT_SEARCH_TYPE_NOT_SUPPORTED(51103, "文档检索类型不支持"),

    /**
     * 多检索器请求失败
     */
    MULTI_RETRIEVER_REQUEST_FAILED(51104, "多检索器请求失败"),

    /**
     * 扩展检索参数错误
     */
    EXPAND_SEARCH_PARAMS_ERROR(51105, "扩展检索参数错误"),

    /**
     * 手动检索知识为空
     */
    MANUAL_SEARCH_KNOWLEDGE_IS_NULL(51106, "手动检索知识不能为空"),

    /**
     * 检索条件解析失败
     */
    SEARCH_CONDITIONS_PARSE_FAILED(51107, "检索条件解析失败"),

    /**
     * 文档片段获取失败
     */
    DOCUMENT_FRAGMENT_GET_FAILED(51108, "文档片段获取失败"),

    /**
     * 知识树获取失败
     */
    KNOWLEDGE_TREE_GET_FAILED(51109, "知识树获取失败"),

    /**
     * 文件编号获取失败
     */
    FILE_SN_GET_FAILED(51110, "文件编号获取失败"),

    KNOWLEDGE_INVENTORY_IS_NOT_FOUND(51071,"知识库未找到"),
    // ==================== 通用执行器错误 (51041-51060) ====================

    /**
     * 节点定义为空
     */
    NODE_DEFINITION_IS_NULL(51041, "节点定义为空"),

    /**
     * 节点类型不支持
     */
    NODE_TYPE_NOT_SUPPORTED(51042, "节点类型不支持"),

    /**
     * 参数解析错误
     */
    PARAMETER_PARSE_ERROR(51043, "参数解析错误"),

    /**
     * 变量替换错误
     */
    VARIABLE_REPLACEMENT_ERROR(51044, "变量替换错误"),

    /**
     * 输出参数映射错误
     */
    OUTPUT_PARAMETER_MAPPING_ERROR(51045, "输出参数映射错误"),

    /**
     * 节点执行超时
     */
    NODE_EXECUTION_TIMEOUT(51046, "节点执行超时"),

    /**
     * 节点执行被中断
     */
    NODE_EXECUTION_INTERRUPTED(51047, "节点执行被中断"),

    /**
     * 工作流上下文为空
     */
    WORKFLOW_CONTEXT_IS_NULL(51048, "工作流上下文为空"),

    /**
     * 节点上下文为空
     */
    NODE_CONTEXT_IS_NULL(51049, "节点上下文为空"),

    /**
     * 执行器初始化失败
     */
    EXECUTOR_INITIALIZATION_FAILED(51050, "执行器初始化失败")

    ;

    private final int code;
    private final String message;

    ExecutorError(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
