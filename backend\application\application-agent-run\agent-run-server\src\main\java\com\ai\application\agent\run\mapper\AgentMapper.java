package com.ai.application.agent.run.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import entity.Agent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 智能体表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface AgentMapper extends BaseMapper<Agent> {
    @Select("select * from agent where agent_sn = #{agentSn}")
    Agent selectByAgentSn(@Param("agentSn") String agentSn);
}
