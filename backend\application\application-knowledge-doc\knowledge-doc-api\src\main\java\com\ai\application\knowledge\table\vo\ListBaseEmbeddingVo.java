package com.ai.application.knowledge.table.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class ListBaseEmbeddingVo {

    private int code;
    private DataItem[] data;

    @Data
    public static class DataItem {
        private String avatar;
        private int chunk_count;
        private String chunk_method;
        private String create_date;
        private long create_time;
        private String created_by;
        private String description;
        private int document_count;
        private String embedding_model;
        private String id;
        private String language;
        private String name;
        private int pagerank;
        private ParserConfig parser_config;
        private String permission;
        private double similarity_threshold;
        private String status;
        private String tenant_id;
        private int token_num;
        private String update_date;
        private long update_time;
        private double vector_similarity_weight;
    }

    @Data
    public static class ParserConfig {
        private int chunk_token_num;
        private String delimiter;
        private boolean html4excel;
        private String layout_recognize;
        private Raptor raptor;
    }

    @Data
    public static class Raptor {
        private boolean use_raptor;
    }


}
