package com.ai.application.agent.run.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 多检索器请求 DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(name = "MultiRetrieverRequestDTO")
public class MultiRetrieverRequestDTO {

    /**
     * LLM 模型编号
     */
    @Schema(description = "LLM 模型编号")
    private String llmSn;

    /**
     * 原始查询
     */
    @Schema(description = "原始查询")
    private String originQuery;

    /**
     * 数据集ID列表
     */
    @Schema(description = "数据集ID列表")
    private List<String> datasetIds;

    /**
     * 文件ID列表
     */
    @Schema(description = "文件ID列表")
    private List<String> fileIds;

    /**
     * 检索计划列表
     */
    @Schema(description = "检索计划列表")
    private List<RetrievalPlanDTO> retrievalPlan;

    /**
     * 检索计划 DTO
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @SuperBuilder
    @Schema(name = "RetrievalPlanDTO")
    public static class RetrievalPlanDTO {

        /**
         * 检索方法 embedding/keywords/text
         */
        @Schema(description = "检索方法")
        private String method;

        /**
         * TopK 数量
         */
        @Schema(description = "TopK 数量")
        private Integer topK;

        /**
         * 权重
         */
        @Schema(description = "权重")
        private Double weight;

        /**
         * 阈值
         */
        @Schema(description = "阈值")
        private Double threshold;
    }
}
