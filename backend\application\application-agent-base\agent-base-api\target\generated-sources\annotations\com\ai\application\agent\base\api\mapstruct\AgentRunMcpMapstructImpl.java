package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.dto.AgentRunMcpDTO;
import com.ai.application.agent.base.api.entity.AgentRunMcp;
import com.ai.application.agent.base.api.vo.AgentRunMcpVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-12T18:39:57+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentRunMcpMapstructImpl implements AgentRunMcpMapstruct {

    @Override
    public AgentRunMcp toEntity(AgentRunMcpDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentRunMcp agentRunMcp = new AgentRunMcp();

        agentRunMcp.setMcpRunId( dto.getMcpRunId() );
        agentRunMcp.setMcpName( dto.getMcpName() );
        agentRunMcp.setMcpInput( dto.getMcpInput() );
        agentRunMcp.setMcpOutput( dto.getMcpOutput() );
        agentRunMcp.setMcpStatus( dto.getMcpStatus() );
        agentRunMcp.setMcpError( dto.getMcpError() );
        agentRunMcp.setMcpDuration( dto.getMcpDuration() );
        agentRunMcp.setMcpSnapshot( dto.getMcpSnapshot() );
        agentRunMcp.setMcpStartTime( dto.getMcpStartTime() );
        agentRunMcp.setMcpEndTime( dto.getMcpEndTime() );
        agentRunMcp.setRunId( dto.getRunId() );
        agentRunMcp.setStepId( dto.getStepId() );
        agentRunMcp.setMcpId( dto.getMcpId() );
        agentRunMcp.setCreateTime( dto.getCreateTime() );
        agentRunMcp.setUpdateTime( dto.getUpdateTime() );

        return agentRunMcp;
    }

    @Override
    public List<AgentRunMcp> toEntityList(List<AgentRunMcpDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<AgentRunMcp> list = new ArrayList<AgentRunMcp>( dtolist.size() );
        for ( AgentRunMcpDTO agentRunMcpDTO : dtolist ) {
            list.add( toEntity( agentRunMcpDTO ) );
        }

        return list;
    }

    @Override
    public AgentRunMcpVO toVo(AgentRunMcp entity) {
        if ( entity == null ) {
            return null;
        }

        AgentRunMcpVO agentRunMcpVO = new AgentRunMcpVO();

        agentRunMcpVO.setMcpRunId( entity.getMcpRunId() );
        agentRunMcpVO.setMcpName( entity.getMcpName() );
        agentRunMcpVO.setMcpInput( entity.getMcpInput() );
        agentRunMcpVO.setMcpOutput( entity.getMcpOutput() );
        agentRunMcpVO.setMcpStatus( entity.getMcpStatus() );
        agentRunMcpVO.setMcpError( entity.getMcpError() );
        agentRunMcpVO.setMcpDuration( entity.getMcpDuration() );
        agentRunMcpVO.setMcpSnapshot( entity.getMcpSnapshot() );
        agentRunMcpVO.setMcpStartTime( entity.getMcpStartTime() );
        agentRunMcpVO.setMcpEndTime( entity.getMcpEndTime() );
        agentRunMcpVO.setRunId( entity.getRunId() );
        agentRunMcpVO.setStepId( entity.getStepId() );
        agentRunMcpVO.setMcpId( entity.getMcpId() );
        agentRunMcpVO.setCreateTime( entity.getCreateTime() );
        agentRunMcpVO.setUpdateTime( entity.getUpdateTime() );

        return agentRunMcpVO;
    }

    @Override
    public List<AgentRunMcpVO> toVoList(List<AgentRunMcp> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AgentRunMcpVO> list = new ArrayList<AgentRunMcpVO>( entities.size() );
        for ( AgentRunMcp agentRunMcp : entities ) {
            list.add( toVo( agentRunMcp ) );
        }

        return list;
    }
}
