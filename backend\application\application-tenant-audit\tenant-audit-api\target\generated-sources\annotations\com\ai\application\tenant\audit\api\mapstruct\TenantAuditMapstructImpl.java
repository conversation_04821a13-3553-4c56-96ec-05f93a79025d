package com.ai.application.tenant.audit.api.mapstruct;

import com.ai.application.tenant.audit.api.dto.TenantAuditDTO;
import com.ai.application.tenant.audit.api.dto.query.TenantAddDTO;
import com.ai.application.tenant.audit.api.entity.TenantAudit;
import com.ai.application.tenant.audit.api.vo.TenantAuditVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T09:54:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class TenantAuditMapstructImpl implements TenantAuditMapstruct {

    @Override
    public TenantAudit toEntity(TenantAddDTO dto) {
        if ( dto == null ) {
            return null;
        }

        TenantAudit tenantAudit = new TenantAudit();

        tenantAudit.setAuditId( dto.getAuditId() );
        tenantAudit.setAuditSn( dto.getAuditSn() );
        tenantAudit.setAuditType( dto.getAuditType() );
        tenantAudit.setAuditTitle( dto.getAuditTitle() );
        tenantAudit.setAuditDesc( dto.getAuditDesc() );
        tenantAudit.setAuditStatus( dto.getAuditStatus() );
        tenantAudit.setAuditResult( dto.getAuditResult() );
        tenantAudit.setAuditPriority( dto.getAuditPriority() );
        tenantAudit.setObjectType( dto.getObjectType() );
        tenantAudit.setObjectId( dto.getObjectId() );
        tenantAudit.setObjectSnapshot( dto.getObjectSnapshot() );
        tenantAudit.setApplyUserId( dto.getApplyUserId() );
        tenantAudit.setApplyTime( dto.getApplyTime() );
        tenantAudit.setAuditUserId( dto.getAuditUserId() );
        tenantAudit.setAuditTime( dto.getAuditTime() );
        tenantAudit.setTenantId( dto.getTenantId() );
        tenantAudit.setCreateTime( dto.getCreateTime() );
        tenantAudit.setUpdateTime( dto.getUpdateTime() );

        return tenantAudit;
    }

    @Override
    public TenantAudit toEntity(TenantAuditDTO dto) {
        if ( dto == null ) {
            return null;
        }

        TenantAudit tenantAudit = new TenantAudit();

        tenantAudit.setAuditSn( dto.getAuditSn() );
        tenantAudit.setAuditStatus( dto.getAuditStatus() );

        return tenantAudit;
    }

    @Override
    public List<TenantAudit> toEntityList(List<TenantAuditDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<TenantAudit> list = new ArrayList<TenantAudit>( dtolist.size() );
        for ( TenantAuditDTO tenantAuditDTO : dtolist ) {
            list.add( toEntity( tenantAuditDTO ) );
        }

        return list;
    }

    @Override
    public TenantAuditVO toVo(TenantAudit entity) {
        if ( entity == null ) {
            return null;
        }

        TenantAuditVO tenantAuditVO = new TenantAuditVO();

        tenantAuditVO.setAuditId( entity.getAuditId() );
        tenantAuditVO.setAuditSn( entity.getAuditSn() );
        tenantAuditVO.setAuditType( entity.getAuditType() );
        tenantAuditVO.setAuditTitle( entity.getAuditTitle() );
        tenantAuditVO.setAuditDesc( entity.getAuditDesc() );
        tenantAuditVO.setAuditStatus( entity.getAuditStatus() );
        tenantAuditVO.setAuditResult( entity.getAuditResult() );
        tenantAuditVO.setAuditPriority( entity.getAuditPriority() );
        tenantAuditVO.setObjectType( entity.getObjectType() );
        tenantAuditVO.setObjectId( entity.getObjectId() );
        tenantAuditVO.setObjectSnapshot( entity.getObjectSnapshot() );
        tenantAuditVO.setApplyUserId( entity.getApplyUserId() );
        tenantAuditVO.setApplyTime( entity.getApplyTime() );
        tenantAuditVO.setAuditUserId( entity.getAuditUserId() );
        tenantAuditVO.setAuditTime( entity.getAuditTime() );
        tenantAuditVO.setTenantId( entity.getTenantId() );
        tenantAuditVO.setCreateTime( entity.getCreateTime() );
        tenantAuditVO.setUpdateTime( entity.getUpdateTime() );

        return tenantAuditVO;
    }

    @Override
    public List<TenantAuditVO> toVoList(List<TenantAudit> entities) {
        if ( entities == null ) {
            return null;
        }

        List<TenantAuditVO> list = new ArrayList<TenantAuditVO>( entities.size() );
        for ( TenantAudit tenantAudit : entities ) {
            list.add( toVo( tenantAudit ) );
        }

        return list;
    }
}
