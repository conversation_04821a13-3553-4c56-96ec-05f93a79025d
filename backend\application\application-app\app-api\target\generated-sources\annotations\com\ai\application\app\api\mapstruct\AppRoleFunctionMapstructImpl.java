package com.ai.application.app.api.mapstruct;

import com.ai.application.app.api.dto.AppRoleFunctionDTO;
import com.ai.application.app.api.entity.AppRoleFunction;
import com.ai.application.app.api.vo.AppRoleFunctionVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T09:54:01+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AppRoleFunctionMapstructImpl implements AppRoleFunctionMapstruct {

    @Override
    public AppRoleFunction toEntity(AppRoleFunctionDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AppRoleFunction appRoleFunction = new AppRoleFunction();

        appRoleFunction.setRfId( dto.getRfId() );
        appRoleFunction.setRoleId( dto.getRoleId() );
        appRoleFunction.setFunId( dto.getFunId() );
        appRoleFunction.setRfStatus( dto.getRfStatus() );
        appRoleFunction.setCreateTime( dto.getCreateTime() );
        appRoleFunction.setUpdateTime( dto.getUpdateTime() );

        return appRoleFunction;
    }

    @Override
    public List<AppRoleFunction> toEntityList(List<AppRoleFunctionDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<AppRoleFunction> list = new ArrayList<AppRoleFunction>( dtolist.size() );
        for ( AppRoleFunctionDTO appRoleFunctionDTO : dtolist ) {
            list.add( toEntity( appRoleFunctionDTO ) );
        }

        return list;
    }

    @Override
    public AppRoleFunctionVO toVo(AppRoleFunction entity) {
        if ( entity == null ) {
            return null;
        }

        AppRoleFunctionVO appRoleFunctionVO = new AppRoleFunctionVO();

        appRoleFunctionVO.setRfId( entity.getRfId() );
        appRoleFunctionVO.setRoleId( entity.getRoleId() );
        appRoleFunctionVO.setFunId( entity.getFunId() );
        appRoleFunctionVO.setRfStatus( entity.getRfStatus() );
        appRoleFunctionVO.setCreateTime( entity.getCreateTime() );
        appRoleFunctionVO.setUpdateTime( entity.getUpdateTime() );

        return appRoleFunctionVO;
    }

    @Override
    public List<AppRoleFunctionVO> toVoList(List<AppRoleFunction> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AppRoleFunctionVO> list = new ArrayList<AppRoleFunctionVO>( entities.size() );
        for ( AppRoleFunction appRoleFunction : entities ) {
            list.add( toVo( appRoleFunction ) );
        }

        return list;
    }
}
