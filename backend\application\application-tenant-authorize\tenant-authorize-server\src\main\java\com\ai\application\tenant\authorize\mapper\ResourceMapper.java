package com.ai.application.tenant.authorize.mapper;

import com.ai.application.tenant.authorize.api.entity.Resource;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.application.tenant.authorize.api.vo.ResourceVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 资源定义表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface ResourceMapper extends BaseMapper<Resource> {
    /**
     * 查询资源定义表
     *
     * @return
     */
    List<ResourceVO> selectResourceList();

    @Select("select * from resource where resource_object_id=#{resourceObjectId} and resource_type = #{resourceType} liimit 1")
    Resource getResourceByResourceType(@Param("resourceObjectId") Integer resourceObjectId,@Param("resourceType") Integer resourceType);
}
