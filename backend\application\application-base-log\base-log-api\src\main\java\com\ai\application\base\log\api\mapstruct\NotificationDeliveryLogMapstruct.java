package com.ai.application.base.log.api.mapstruct;
import com.ai.application.base.log.api.entity.NotificationDeliveryLog;
import com.ai.application.base.log.api.dto.NotificationDeliveryLogDTO;
import com.ai.application.base.log.api.vo.NotificationDeliveryLogVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 通知发送日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */

@Mapper(componentModel = "spring")
public interface NotificationDeliveryLogMapstruct {

    NotificationDeliveryLog toEntity(NotificationDeliveryLogDTO dto);
    List<NotificationDeliveryLog> toEntityList(List<NotificationDeliveryLogDTO> dtolist);
    NotificationDeliveryLogVO toVo(NotificationDeliveryLog entity);
    List<NotificationDeliveryLogVO> toVoList(List<NotificationDeliveryLog> entities);
}
