package com.ai.application.skill.mcp.api.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * MCP服务器表
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Data
@Schema(name = "")
public class McpServerVO {
    /**
     * MCP服务器id
     */
    @Schema(description = "MCP服务器id")
    private Integer serverId;

    /**
     * MCP服务器sn
     */
    @Schema(description = "MCP服务器sn")
    private String serverSn;

    @Schema(description = "MCP服务供应商名字")
    private String supplierName;

    /**
     * MCP服务器名称
     */
    @Schema(description = "MCP服务器名称")
    private String serverName;

    /**
     * MCP服务器描述
     */
    @Schema(description = "MCP服务器描述")
    private String serverDesc;

    /**
     * 服务器类型:10-官方,20-第三方,30-自建
     */
    @Schema(description = "服务器类型:10-官方,20-第三方,30-自建")
    private Integer serverType;

    /**
     * MCP协议版本
     */
    @Schema(description = "MCP协议版本")
    private String serverVersion;

    /**
     * 服务器地址
     */
    @Schema(description = "服务器地址")
    private String serverUrl;

    /**
     * 服务器端口
     */
    @Schema(description = "服务器端口")
    private Integer serverPort;

    /**
     * 传输类型:10-stdio,20-sse,30-websocket
     */
    @Schema(description = "传输类型:10-stdio,20-sse,30-websocket")
    private Integer transportType;

    /**
     * 认证类型:10-无,20-API Key,30-OAuth,40-自定义
     */
    @Schema(description = "认证类型:10-无,20-API Key,30-OAuth,40-自定义")
    private Integer authType;

    /**
     * 认证配置
     */
    @Schema(description = "认证配置")
    private String authConfig;

    /**
     * 连接配置
     */
    @Schema(description = "连接配置")
    private String connectionConfig;

    /**
     * 服务器能力
     */
    @Schema(description = "服务器能力")
    private String serverCapabilities;

    /**
     * 服务器元数据
     */
    @Schema(description = "服务器元数据")
    private String serverMetadata;

    /**
     * 服务器状态:1-启用,0-禁用,-1-删除
     */
    @Schema(description = "服务器状态:1-启用,0-禁用,-1-删除")
    private Integer serverStatus;

    /**
     * 健康检查地址
     */
    @Schema(description = "健康检查地址")
    private String healthCheckUrl;

    /**
     * 最后检查时间
     */
    @Schema(description = "最后检查时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastCheckTime;

    /**
     * 检查状态:1-正常,0-异常
     */
    @Schema(description = "检查状态:1-正常,0-异常")
    private Integer checkStatus;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Integer tenantId;

    /**
     * 创建用户id
     */
    @Schema(description = "创建用户id")
    private Integer createUserId;

    /**
     * 更新用户id
     */
    @Schema(description = "更新用户id")
    private Integer updateUserId;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Schema(description = "供应商id")
    private Integer supplierId;
}