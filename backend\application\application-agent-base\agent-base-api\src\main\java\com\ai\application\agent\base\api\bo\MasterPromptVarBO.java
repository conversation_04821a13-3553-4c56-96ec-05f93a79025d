package com.ai.application.agent.base.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class MasterPromptVarBO {
    @Schema(description = "radio类型")
    private static final String TYPE_RADIO = "radio";

    @Schema(description = "名称")
    private String name;

    @Schema(description = "类型 radio checkbox 单选or多选")
    private String type;

    @Schema(description = "默认值")
    private String defaultValue;

    @Schema(description = "是否可修改")
    private Boolean editable;

    @Schema(description = "知识库选择权限")
    private Boolean isFilterKnowledge;

    @Schema(description = "单选列表")
    private List<ParamRadioBO> radioList;

    @Data
    public static class ParamRadioBO {
        @Schema(description = "名称")
        private Integer id;

        @Schema(description = "值")
        private String value;
    }
}
