package com.ai.application.agent.base.api.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 智能体运行会话表
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@Schema(name = "")
public class AgentRunSessionVO {
    /**
     * 会话id
     */
    @Schema(description = "会话id")
    private Integer sessionId;

    /**
     * 会话session
     */
    @Schema(description = "会话session")
    private String sessionSn;

    /**
     * 会话标题(智能生成)
     */
    @Schema(description = "会话标题(智能生成)")
    private String sessionTitle;

    /**
     * 预留类型
     */
    @Schema(description = "预留类型")
    private Integer sessionType;

    /**
     * 会话状态:1-活跃,3-结束,-1-删除
     */
    @Schema(description = "会话状态:1-活跃,3-结束,-1-删除")
    private Integer sessionStatus;

    /**
     * 会话元数据
     */
    @Schema(description = "会话元数据")
    private String sessionMetadata;

    /**
     * 智能体id
     */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
     * 智能体版本id
     */
    @Schema(description = "智能体版本id")
    private Integer versionId;

    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private Integer userId;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Integer tenantId;

    /**
     * 运行次数
     */
    @Schema(description = "运行次数")
    private Integer runCount;

    /**
     * 首次运行id
     */
    @Schema(description = "首次运行id")
    private Integer firstRunId;

    /**
     * 最后运行id
     */
    @Schema(description = "最后运行id")
    private Integer lastRunId;

    /**
     * 最后运行时间
     */
    @Schema(description = "最后运行时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastRunTime;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}