package com.ai.application.base.model.api.mapstruct;

import com.ai.application.base.model.api.dto.ModelStatsMonthlyDTO;
import com.ai.application.base.model.api.entity.ModelStatsMonthly;
import com.ai.application.base.model.api.vo.ModelStatsMonthlyVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-13T10:32:22+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class ModelStatsMonthlyMapstructImpl implements ModelStatsMonthlyMapstruct {

    @Override
    public ModelStatsMonthly toEntity(ModelStatsMonthlyDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ModelStatsMonthly modelStatsMonthly = new ModelStatsMonthly();

        modelStatsMonthly.setStatsId( dto.getStatsId() );
        modelStatsMonthly.setStatsMonth( dto.getStatsMonth() );
        modelStatsMonthly.setCallCount( dto.getCallCount() );
        modelStatsMonthly.setSuccessCount( dto.getSuccessCount() );
        modelStatsMonthly.setFailedCount( dto.getFailedCount() );
        modelStatsMonthly.setTimeoutCount( dto.getTimeoutCount() );
        modelStatsMonthly.setPromptTokens( dto.getPromptTokens() );
        modelStatsMonthly.setCompletionTokens( dto.getCompletionTokens() );
        modelStatsMonthly.setTotalTokens( dto.getTotalTokens() );
        modelStatsMonthly.setTotalDuration( dto.getTotalDuration() );
        modelStatsMonthly.setAvgDuration( dto.getAvgDuration() );
        modelStatsMonthly.setMaxDuration( dto.getMaxDuration() );
        modelStatsMonthly.setMinDuration( dto.getMinDuration() );
        modelStatsMonthly.setCostAmount( dto.getCostAmount() );
        modelStatsMonthly.setUniqueUserCount( dto.getUniqueUserCount() );
        modelStatsMonthly.setUniqueAgentCount( dto.getUniqueAgentCount() );
        modelStatsMonthly.setModelId( dto.getModelId() );
        modelStatsMonthly.setTenantId( dto.getTenantId() );
        modelStatsMonthly.setCreateTime( dto.getCreateTime() );
        modelStatsMonthly.setUpdateTime( dto.getUpdateTime() );

        return modelStatsMonthly;
    }

    @Override
    public List<ModelStatsMonthly> toEntityList(List<ModelStatsMonthlyDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<ModelStatsMonthly> list = new ArrayList<ModelStatsMonthly>( dtolist.size() );
        for ( ModelStatsMonthlyDTO modelStatsMonthlyDTO : dtolist ) {
            list.add( toEntity( modelStatsMonthlyDTO ) );
        }

        return list;
    }

    @Override
    public ModelStatsMonthlyVO toVo(ModelStatsMonthly entity) {
        if ( entity == null ) {
            return null;
        }

        ModelStatsMonthlyVO modelStatsMonthlyVO = new ModelStatsMonthlyVO();

        modelStatsMonthlyVO.setStatsId( entity.getStatsId() );
        modelStatsMonthlyVO.setStatsMonth( entity.getStatsMonth() );
        modelStatsMonthlyVO.setCallCount( entity.getCallCount() );
        modelStatsMonthlyVO.setSuccessCount( entity.getSuccessCount() );
        modelStatsMonthlyVO.setFailedCount( entity.getFailedCount() );
        modelStatsMonthlyVO.setTimeoutCount( entity.getTimeoutCount() );
        modelStatsMonthlyVO.setPromptTokens( entity.getPromptTokens() );
        modelStatsMonthlyVO.setCompletionTokens( entity.getCompletionTokens() );
        modelStatsMonthlyVO.setTotalTokens( entity.getTotalTokens() );
        modelStatsMonthlyVO.setTotalDuration( entity.getTotalDuration() );
        modelStatsMonthlyVO.setAvgDuration( entity.getAvgDuration() );
        modelStatsMonthlyVO.setMaxDuration( entity.getMaxDuration() );
        modelStatsMonthlyVO.setMinDuration( entity.getMinDuration() );
        modelStatsMonthlyVO.setCostAmount( entity.getCostAmount() );
        modelStatsMonthlyVO.setUniqueUserCount( entity.getUniqueUserCount() );
        modelStatsMonthlyVO.setUniqueAgentCount( entity.getUniqueAgentCount() );
        modelStatsMonthlyVO.setModelId( entity.getModelId() );
        modelStatsMonthlyVO.setTenantId( entity.getTenantId() );
        modelStatsMonthlyVO.setCreateTime( entity.getCreateTime() );
        modelStatsMonthlyVO.setUpdateTime( entity.getUpdateTime() );

        return modelStatsMonthlyVO;
    }

    @Override
    public List<ModelStatsMonthlyVO> toVoList(List<ModelStatsMonthly> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ModelStatsMonthlyVO> list = new ArrayList<ModelStatsMonthlyVO>( entities.size() );
        for ( ModelStatsMonthly modelStatsMonthly : entities ) {
            list.add( toVo( modelStatsMonthly ) );
        }

        return list;
    }
}
