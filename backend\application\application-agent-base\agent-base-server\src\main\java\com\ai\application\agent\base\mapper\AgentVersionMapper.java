package com.ai.application.agent.base.mapper;

import com.ai.application.agent.base.api.entity.AgentVersion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 智能体版本表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface AgentVersionMapper extends BaseMapper<AgentVersion> {
    @Select("select * from agent_version where version_sn = #{versionSn}")
    AgentVersion selectByVersionSn(@Param("versionSn") String versionSn);

    @Select("select * from agent_version where agent_id = #{agentId} and version_status = #{version_status}")
    AgentVersion findByAgentIdAndVersionStatus(@Param("agentId") Integer agentId, @Param("versionStatus") Integer versionStatus);
}
