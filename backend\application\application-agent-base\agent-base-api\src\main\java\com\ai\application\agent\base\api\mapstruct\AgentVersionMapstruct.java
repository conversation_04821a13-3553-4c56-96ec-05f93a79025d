package com.ai.application.agent.base.api.mapstruct;
import com.ai.application.agent.base.api.entity.AgentVersion;
import com.ai.application.agent.base.api.vo.AgentVO;
import com.ai.application.agent.base.api.vo.AgentVersionListVO;
import com.ai.application.agent.base.api.vo.AgentVersionVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 智能体版本表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */

@Mapper(componentModel = "spring")
public interface AgentVersionMapstruct {
    AgentVersionListVO toListVo(AgentVersion agentVersion);
    AgentVersionVO toVo(AgentVersion agentVersion);
}
