package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.dto.ApiGenDTO;
import com.ai.application.agent.base.api.vo.ApiGenVO;
import com.ai.application.agent.base.mapper.AgentMapper;
import com.ai.application.agent.base.mapper.AgentVersionMapper;
import com.ai.application.agent.base.properties.AgentProperties;
import com.ai.application.agent.base.service.IAgentApiService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import java.util.ArrayList;

@Service
public class AgentApiServiceImpl implements IAgentApiService {
    @Resource
    private AgentProperties agentProperties;

    @Resource
    private AgentVersionMapper agentVersionMapper;

    @Resource
    private AgentMapper agentMapper;

    private static final String path = "/open/api/";

    @Override
    public ApiGenVO gen(ApiGenDTO dto) {
        var apiInfoList = new ArrayList<>();
        ApiGenVO apiGenVO = new ApiGenVO();

        apiGenVO.setDomain(agentProperties.getDomain());
        apiGenVO.setAgentSn(dto.getAgentSn());
        apiGenVO.setVersionSn(dto.getVersionSn());

        ArrayList<ApiGenVO.ApiInfo> apiInfos = new ArrayList<>();
        if (CollectionUtils.isEmpty(apiInfoList)) {
            return null;
        }

        return apiGenVO;
    }
}
