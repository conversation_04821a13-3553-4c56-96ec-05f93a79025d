package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.bo.MasterSkillBO;
import com.ai.application.agent.base.api.dto.AgentUseWorkflowUpdateDTO;
import com.ai.application.agent.base.api.enums.SkillTypeEnum;
import com.ai.application.agent.base.api.enums.ToolExtendNameEnum;
import com.ai.application.agent.base.api.vo.AgentUseWorkflowVO;
import com.ai.application.agent.base.service.IAgentUseWorkflowService;
import com.ai.framework.core.util.json.JsonUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ai.application.agent.base.mapper.AgentUseWorkflowMapper;
import com.ai.application.agent.base.api.entity.AgentUseWorkflow;
import com.ai.application.agent.base.api.dto.AgentUseWorkflowAddDTO;
import com.ai.application.agent.base.api.dto.AgentUseWorkflowListDTO;
import com.ai.application.agent.base.api.vo.AgentUseWorkflowListVO;
import com.ai.application.agent.base.api.mapstruct.AgentUseWorkflowMapstruct;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 智能体工作流表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Service
public class AgentUseWorkflowService1Impl implements IAgentUseWorkflowService {

    @Resource
    private AgentUseWorkflowMapper agentUseWorkflowMapper;

    @Resource
    private AgentUseWorkflowMapstruct agentUseWorkflowMapstruct;

    @Transactional(readOnly = true)
    @Override
    public List<AgentUseWorkflowListVO> list(AgentUseWorkflowListDTO queryDto) {
        LambdaQueryWrapper<AgentUseWorkflow> queryWrapper = this.buildQuery(queryDto);
        return agentUseWorkflowMapstruct.toVoList(this.agentUseWorkflowMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(AgentUseWorkflowAddDTO dto) {
        AgentUseWorkflow entity = agentUseWorkflowMapstruct.toAddEntity(dto);
        entity.setCreateTime(new Date());
        agentUseWorkflowMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AgentUseWorkflowUpdateDTO dto) {
        BusinessAssertUtil.notNull(dto.getFlowId(), "不能为空");

        AgentUseWorkflow entity = agentUseWorkflowMapper.selectById(dto.getFlowId());
        BusinessAssertUtil.notNull(entity, "找不到AdId为 " + dto.getFlowId() + " 的记录");

        AgentUseWorkflow entityList = agentUseWorkflowMapstruct.toUpdateEntity(dto);
        entityList.setUpdateTime(new Date());
        agentUseWorkflowMapper.updateById(entityList);
    }

    @Override
    public AgentUseWorkflowVO selectByVersion(Integer versionId) {
        AgentUseWorkflowListDTO agentUseWorkflowListDTO = new AgentUseWorkflowListDTO();
        LambdaQueryWrapper<AgentUseWorkflow> agentUseWorkflowLambdaQueryWrapper = this.buildQuery(agentUseWorkflowListDTO);
        AgentUseWorkflow agentUseWorkflow = agentUseWorkflowMapper.selectOne(agentUseWorkflowLambdaQueryWrapper);
        return agentUseWorkflowMapstruct.toVo(agentUseWorkflow);
    }

    @Transactional(readOnly = true)
    @Override
    public List<MasterSkillBO> toDetail(Integer versionId) {
        AgentUseWorkflowListDTO agentUseWorkflowListDTO = new AgentUseWorkflowListDTO();
        List<AgentUseWorkflowListVO> list = this.list(agentUseWorkflowListDTO);
        return list.stream().map(work->{
            MasterSkillBO skill = new MasterSkillBO();
            skill.setSkillId(work.getFlowId());
            skill.setType(SkillTypeEnum.WORK_FLOW.getCode());
            String mcpExtend = work.getFlowExtensions();
            Map<String, String> mapStr = JsonUtils.parseMapStr(mcpExtend);
            skill.setReplayType(mapStr.get(ToolExtendNameEnum.REPLY_TYPE.getCode()));
            return skill;
        }).toList();
    }

    private LambdaQueryWrapper<AgentUseWorkflow> buildQuery(AgentUseWorkflowListDTO queryDto) {
        LambdaQueryWrapper<AgentUseWorkflow> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(queryDto.getVersionId()), AgentUseWorkflow::getVersionId, queryDto.getVersionId());
        return queryWrapper;
    }
}