package com.ai.application.base.log.api.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 影响对象类型:10-智能体,20-工具,30-知识库,40-用户,50-租户
 */
@Getter
public enum AffectedObjectTypeEnum {
    AGENT(10, "智能体"),
    TOOLS(20, "工具"),
    KNOWLEDGE(30, "知识库"),
    USER(40, "用户"),
    TENANT(50, "租户"),
    ;

    private Integer code;
    private String title;

    AffectedObjectTypeEnum(Integer code, String title) {
        this.code = code;
        this.title = title;
    }

    public static String getTitle(Integer code) {
        for(AffectedObjectTypeEnum vo :values() ) {
            if (Objects.equals(vo.code, code)) {
                return vo.title;
            }
        }
        return null;
    }
}
