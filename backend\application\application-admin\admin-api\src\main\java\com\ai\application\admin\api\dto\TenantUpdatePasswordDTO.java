package com.ai.application.admin.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 租户
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(name = "租户修改密码DTO")
public class TenantUpdatePasswordDTO {
    /**
     * 登录密码
     */
    @Schema(description = "登录密码")
    @Length(min = 8,message = "密码长度不能小于8个字符")
    private String password;

    @Schema(description = "登录密码")
    @Length(min = 8,message = "密码长度不能小于8个字符")
    private String confirmPassword;
}