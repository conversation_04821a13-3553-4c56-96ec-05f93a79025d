package com.ai.application.knowledge.table.controller;

import com.ai.application.knowledge.table.dto.TableDataCreateDto;
import com.ai.application.knowledge.table.dto.TableDataListDto;
import com.ai.application.knowledge.table.service.IKnowledgeTableDataService;
import com.ai.application.knowledge.table.vo.TableDataImportVo;
import com.ai.application.knowledge.table.vo.TableDataListVo;
import com.ai.framework.core.vo.ResultVo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;

@Slf4j
@Validated
@RestController
@RequestMapping("/v1/data")
@Tag(name = "智能表格内容", description = "智能表格内容控制器")
public class KnowledgeTableDataController {

    @Autowired
    private IKnowledgeTableDataService knowledgeTableDataService;

    @Operation(summary = "列表")
    @PostMapping("/list")
    public ResultVo<PageInfo<TableDataListVo>> list(@Valid @RequestBody TableDataListDto dto) {
        return knowledgeTableDataService.list(dto);
    }

    @Operation(summary = "删除")
    @DeleteMapping("/delete/{tableSn}/{rowId}")
    public ResultVo<String> delete(@PathVariable("tableSn") String tableSn, @PathVariable("rowId") Integer rowId) {
        return knowledgeTableDataService.delete(tableSn, rowId);
    }

    @Operation(summary = "编辑")
    @PostMapping("/update")
    public ResultVo<String> update(@Valid @RequestBody TableDataCreateDto dto) {
        return knowledgeTableDataService.update(dto);
    }

    @Operation(summary = "新增")
    @PostMapping("/create")
    public ResultVo<Integer> create(@Valid @RequestBody TableDataCreateDto dto) {
        return knowledgeTableDataService.create(dto);
    }

    @Operation(summary = "模板下载")
    @GetMapping("/template/{tableSn}")
    public ResponseEntity<byte[]> downloadExcelTemplate(@PathVariable("tableSn") String tableSn) {
        return knowledgeTableDataService.downloadTemplate(tableSn);
    }

    @Operation(summary = "导出")
    @PostMapping("/export")
    public ResponseEntity<byte[]> export(@RequestBody TableDataListDto dto) {
        return knowledgeTableDataService.export(dto);
    }

    @Operation(summary = "导入")
    @PostMapping("/import")
    public ResultVo<TableDataImportVo> importExcel(@RequestParam("file") MultipartFile file
            , @RequestParam("tableSn") String tableSn
            , @RequestParam("overwrite") Integer overwrite) {
        return knowledgeTableDataService.importExcel(file, tableSn, overwrite);
    }

}
