package com.ai.application.agent.base.controller;

import com.ai.application.agent.base.api.dto.AgentCreateDTO;
import com.ai.application.agent.base.api.dto.AgentEnableDTO;
import com.ai.application.agent.base.api.dto.query.AgentPageDTO;
import com.ai.application.agent.base.api.vo.AgentDetailVO;
import com.ai.application.agent.base.api.vo.AgentPageVO;
import com.ai.application.agent.base.api.vo.AgentSimpleVO;
import com.ai.application.agent.base.api.vo.LastSessionAgentVO;
import com.ai.application.agent.base.service.IAgentService;
import com.ai.application.base.model.api.vo.ModelSimpleVO;
import com.github.pagehelper.PageInfo;
import com.ai.application.agent.base.api.dto.AgentUpdateDTO;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;

/**
 * 智能体
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Tag(name = "智能体表", description = "智能体表-相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1")
public class AgentController {

    @Resource
    private IAgentService agentService;

    /**
     * 智能体分页列表
     * @param queryDto
     * @return
     */
    @Operation(summary = "智能体分页列表", description = "查询所有智能体信息")
    @PostMapping("/page")
    public ResultVo<PageInfo<AgentPageVO>> page(@Validated @RequestBody AgentPageDTO queryDto){
        return ResultVo.data(agentService.page(queryDto));
    }

    @Operation(summary = "简单智能体基本查询-下拉用", description = "查询所有智能体信息表信息")
    @PostMapping("/simpleList")
    public ResultVo<List<AgentSimpleVO>> simpleList(){
        return ResultVo.data(agentService.simpleList());
    }

    /**
     * 创建智能体
     * @param dto
     * @return
     */
    @Operation(summary = "创建智能体")
    @PostMapping("/create")
    public ResultVo<String> create(@Validated @RequestBody AgentCreateDTO dto){
        return ResultVo.success(agentService.create(dto));
    }

    /**
     * 智能体修改
     * @param dto
     * @return
     */
    @Operation(summary = "智能体修改")
    @PostMapping(value = "/update")
    public ResultVo<Void> update(@Validated @RequestBody AgentUpdateDTO dto){
        agentService.update(dto);
        return ResultVo.success("修改成功");
    }

    /**
     * 最近会话智能体表
     * @return
     */
    @Operation(summary = "最近会话智能体表")
    @GetMapping(value = "/session/list")
    public ResultVo<List<LastSessionAgentVO>> lastSessionAgent(){
        return ResultVo.data(agentService.queryLastSessionAgent());
    }

    /**
     * 获取智能体详情
     * @param agentSn
     * @return
     */
    @Operation(summary = "获取智能体详情")
    @GetMapping("/detail/{agentSn}")
    public ResultVo<AgentDetailVO> detail(@PathVariable("agentSn") String agentSn){
        return ResultVo.data(agentService.detail(agentSn));
    }

    /**
     * 智能体删除
     * @param agentSn
     * @return
     */
    @Operation(summary = "智能体删除")
    @DeleteMapping("/delete/{agentSn}")
    public ResultVo<Void> delete(@PathVariable("agentSn") String agentSn){
        agentService.delete(agentSn);
        return ResultVo.success("删除成功");
    }

    @Operation(summary = "智能体停用与启用")
    @PostMapping("/enable")
    public ResultVo<Void> enable(@Validated @RequestBody AgentEnableDTO dto){
        agentService.enable(dto);
        return ResultVo.success("删除成功");
    }
}