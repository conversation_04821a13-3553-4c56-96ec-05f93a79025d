package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.dto.AgentExtendAddDTO;
import com.ai.application.agent.base.api.dto.AgentExtendUpdateDTO;
import com.ai.application.agent.base.api.enums.AgentExtendNameEnum;

public interface IAgentExtendService {
    /**
     * 添加
     * @param dto
     */
    void add(AgentExtendAddDTO dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(AgentExtendUpdateDTO dto);

    /**
     * 根据agentId与itemName更新状态
     * @param agentId
     * @param agentExtendNameEnum
     * @param status
     */
    void updateByStatus(Integer agentId, AgentExtendNameEnum agentExtendNameEnum, Integer status);

    /**
     * 设置参数
     * @param agentId
     * @param itemName
     * @param itemValue
     */
    void setItem(Integer agentId, String itemName, String itemValue);
}
