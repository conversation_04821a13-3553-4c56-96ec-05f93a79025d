package com.ai.application.task.api.mapstruct;
import com.ai.application.task.api.dto.TaskAddDTO;
import com.ai.application.task.api.dto.TaskBatchAddDTO;
import com.ai.application.task.api.dto.TaskCronAddDTO;
import com.ai.application.task.api.entity.Task;
import com.ai.application.task.api.dto.TaskDTO;
import com.ai.application.task.api.vo.*;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 计划任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-07
 */

@Mapper(componentModel = "spring")
public interface TaskMapstruct {

    Task toEntity(TaskDTO dto);
    Task toEntity(TaskAddDTO dto);
    Task toEntity(TaskBatchAddDTO dto);
    Task toEntity(TaskCronAddDTO dto);
    List<Task> toEntityList(List<TaskDTO> dtolist);
    TaskVO toVo(Task entity);
    TaskBatchDetailVO toBatchDetailVo(Task entity);
    TaskCronDetailVO toCronDetailVo(Task entity);
    List<TaskVO> toVoList(List<Task> entities);
    List<TaskBatchPageVO> toBatchPageVoList(List<Task> entities);
    List<TaskCronPageVO> toCronPageVoList(List<Task> entities);
}
