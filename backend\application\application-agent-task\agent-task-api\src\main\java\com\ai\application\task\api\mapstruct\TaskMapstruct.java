package com.ai.application.task.api.mapstruct;
import com.ai.application.task.api.dto.TaskAddDTO;
import com.ai.application.task.api.entity.Task;
import com.ai.application.task.api.dto.TaskDTO;
import com.ai.application.task.api.vo.TaskDetailVO;
import com.ai.application.task.api.vo.TaskVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 计划任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-07
 */

@Mapper(componentModel = "spring")
public interface TaskMapstruct {

    Task toEntity(TaskDTO dto);
    Task toEntity(TaskAddDTO dto);
    List<Task> toEntityList(List<TaskDTO> dtolist);
    TaskVO toVo(Task entity);
    TaskDetailVO toDetailVo(Task entity);
    List<TaskVO> toVoList(List<Task> entities);
}
