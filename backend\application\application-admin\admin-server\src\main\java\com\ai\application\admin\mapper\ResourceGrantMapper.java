package com.ai.application.admin.mapper;

import com.ai.application.admin.api.entity.ResourceGrant;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 资源授权表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface ResourceGrantMapper extends BaseMapper<ResourceGrant> {
    @Select("SELECT * FROM `resource_grant` WHERE grant_status=1 AND resource_type=#{resourceType} and A.grant_object_type=40 and A.grant_object_id=#{tenantId}")
    List<ResourceGrant> queryResourceGrantByTenantId(@Param("tenantId") Integer tenantId, @Param("resourceType") Integer resourceType);
}
