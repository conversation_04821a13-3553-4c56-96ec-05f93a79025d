package com.ai.application.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 资源授权表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("resource_grant")
public class ResourceGrant implements Serializable {
    /**
     * 授权id
     */
    @Schema(description = "授权id")
    @TableId(type = IdType.AUTO)
    private Integer grantId;

    /**
     * 授权类型:10-使用,20-协作,30-管理,50-所有者
     */
    @Schema(description = "授权类型:10-使用,20-协作,30-管理,50-所有者")
    private Integer grantType;

    /**
     * 授权状态:1-有效,0-失效,-1-删除
     */
    @Schema(description = "授权状态:1-有效,0-失效,-1-删除")
    private Integer grantStatus;

    /**
     * 授权对象类型:10-用户,20-角色,30-部门,40-租户,50-平台
     */
    @Schema(description = "授权对象类型:10-用户,20-角色,30-部门,40-租户,50-平台")
    private Integer grantObjectType;

    /**
     * 授权对象id,为0表示平台级授权
     */
    @Schema(description = "授权对象id,为0表示平台级授权")
    private Integer grantObjectId;

    /**
     * 授权资源配置信息
     */
    @Schema(description = "授权资源配置信息")
    private String grantConfig;

    /**
     * 资源id
     */
    @Schema(description = "资源id")
    private Integer resourceId;

    /**
     * 资源类型:10-智能体,20-知识库,30-智能表格,40-字典,50-工具,60-MCP,70-模型
     */
    @Schema(description = "资源类型:10-智能体,20-知识库,30-智能表格,40-字典,50-工具,60-MCP,70-模型")
    private Integer resourceType;

    /**
     * 资源对象id
     */
    @Schema(description = "资源对象id")
    private Integer resourceObjectId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}