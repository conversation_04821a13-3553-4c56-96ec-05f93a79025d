package com.ai.application.app.service;

import com.github.pagehelper.PageInfo;
import com.ai.application.app.api.dto.AppRoleFunctionDTO;
import com.ai.application.app.api.dto.query.AppRoleFunctionQueryDTO;
import com.ai.application.app.api.vo.AppRoleFunctionVO;
import java.util.List;
import java.util.Set;

/**
 * 应用角色功能表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
public interface IAppRoleFunctionService {

        /**
         * 分页
         *
         * @param queryDto
         * @return
         */
        PageInfo<AppRoleFunctionVO> page(AppRoleFunctionQueryDTO queryDto);

        /**
         * 列表
         *
         * @param sort
         * @param queryDto
         * @return
         */
        List<AppRoleFunctionVO> list(AppRoleFunctionQueryDTO queryDto);

        /**
         * 保存
         *
         * @param dto
         */
        void save(AppRoleFunctionDTO dto);

        /**
         * 更新
         *
         * @param dto
         */
        void update(AppRoleFunctionDTO dto);

        /**
         * 查看
         *
         * @param id
         * @return
         */
        AppRoleFunctionVO get(Long id);

        /**
         * 删除
         *
         * @param ids
         */
        void delete(Set<Long> ids);
}