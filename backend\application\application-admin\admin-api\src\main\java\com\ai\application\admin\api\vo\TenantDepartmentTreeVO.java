package com.ai.application.admin.api.vo;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 租户部门表
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@Schema(name = "")
public class TenantDepartmentTreeVO {
    /**
     * 部门id
     */
    @Schema(description = "部门id")
    private Integer deptId;
    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String deptName;

    /**
     * 排序值
     */
    @Schema(description = "排序值")
    private Integer deptSort;

    /**
     * 人数
     */
    @Schema(description = "人数")
    private Integer nums = 0;

    @Schema(description = "下级部门id集合")
    private List<Integer> deptIds;

    /**
     * 父级ID
     */
    @Schema(description = "父级ID")
    private Integer parentId;

    @Schema(description="子部门列表")
    private List<TenantDepartmentTreeVO> children;

    // 获取所有下级员工数量（包括子节点的子节点）
    public int getTotalDescendants() {
        int count = 0;
        for (TenantDepartmentTreeVO child : children) {
            // 加上直接子节点
            count++;
            // 加上子节点的所有下级节点
            if(CollectionUtils.isNotEmpty(child.getChildren())) {
                count += child.getTotalDescendants();
            }
        }
        return count;
    }

    // 获取所有下级部门id集合
    public List<Integer> getSubDeptIds() {
        List<Integer> subDeptIds = new ArrayList<>();
        for (TenantDepartmentTreeVO child : children) {
            // 加上直接子节点
            subDeptIds.add(child.deptId);
            // 加上子节点的所有下级节点
            if(CollectionUtils.isNotEmpty(child.getChildren())) {
                subDeptIds.addAll(child.getSubDeptIds());
            }
        }
        return subDeptIds;
    }
}