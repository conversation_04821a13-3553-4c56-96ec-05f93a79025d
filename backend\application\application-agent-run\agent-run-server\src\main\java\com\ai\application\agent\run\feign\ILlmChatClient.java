package com.ai.application.agent.run.feign;

import com.ai.application.agent.run.dto.ProcessChatDTO;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.vo.ResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * LLM 聊天客户端接口
 */
@FeignClient(
        value = ServiceConstant.AGENT_RUN,
        contextId = "ILlmChatClient"
)
public interface ILlmChatClient {
    String API_PREFIX = "/v1/feign/llm";

    /**
     * 调用大模型进行对话
     *
     * @param dto 对话请求
     * @param authorization 授权头
     * @return 对话结果
     */
    @PostMapping(value = API_PREFIX + "/chat")
    ResultVo<String> chat(@RequestBody ProcessChatDTO dto, 
                         @RequestHeader(name = "Authorization") String authorization);
}
