package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.dto.AgentRunToolDTO;
import com.ai.application.agent.base.api.entity.AgentRunTool;
import com.ai.application.agent.base.api.vo.AgentRunToolVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T09:54:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentRunToolMapstructImpl implements AgentRunToolMapstruct {

    @Override
    public AgentRunTool toEntity(AgentRunToolDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentRunTool agentRunTool = new AgentRunTool();

        agentRunTool.setToolRunId( dto.getToolRunId() );
        agentRunTool.setToolCallType( dto.getToolCallType() );
        agentRunTool.setToolName( dto.getToolName() );
        agentRunTool.setToolInput( dto.getToolInput() );
        agentRunTool.setToolOutput( dto.getToolOutput() );
        agentRunTool.setToolStatus( dto.getToolStatus() );
        agentRunTool.setToolError( dto.getToolError() );
        agentRunTool.setToolDuration( dto.getToolDuration() );
        agentRunTool.setToolSnapshot( dto.getToolSnapshot() );
        agentRunTool.setToolStartTime( dto.getToolStartTime() );
        agentRunTool.setToolEndTime( dto.getToolEndTime() );
        agentRunTool.setRunId( dto.getRunId() );
        agentRunTool.setStepId( dto.getStepId() );
        agentRunTool.setToolId( dto.getToolId() );
        agentRunTool.setCreateTime( dto.getCreateTime() );
        agentRunTool.setUpdateTime( dto.getUpdateTime() );

        return agentRunTool;
    }

    @Override
    public List<AgentRunTool> toEntityList(List<AgentRunToolDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<AgentRunTool> list = new ArrayList<AgentRunTool>( dtolist.size() );
        for ( AgentRunToolDTO agentRunToolDTO : dtolist ) {
            list.add( toEntity( agentRunToolDTO ) );
        }

        return list;
    }

    @Override
    public AgentRunToolVO toVo(AgentRunTool entity) {
        if ( entity == null ) {
            return null;
        }

        AgentRunToolVO agentRunToolVO = new AgentRunToolVO();

        agentRunToolVO.setToolRunId( entity.getToolRunId() );
        agentRunToolVO.setToolCallType( entity.getToolCallType() );
        agentRunToolVO.setToolName( entity.getToolName() );
        agentRunToolVO.setToolInput( entity.getToolInput() );
        agentRunToolVO.setToolOutput( entity.getToolOutput() );
        agentRunToolVO.setToolStatus( entity.getToolStatus() );
        agentRunToolVO.setToolError( entity.getToolError() );
        agentRunToolVO.setToolDuration( entity.getToolDuration() );
        agentRunToolVO.setToolSnapshot( entity.getToolSnapshot() );
        agentRunToolVO.setToolStartTime( entity.getToolStartTime() );
        agentRunToolVO.setToolEndTime( entity.getToolEndTime() );
        agentRunToolVO.setRunId( entity.getRunId() );
        agentRunToolVO.setStepId( entity.getStepId() );
        agentRunToolVO.setToolId( entity.getToolId() );
        agentRunToolVO.setCreateTime( entity.getCreateTime() );
        agentRunToolVO.setUpdateTime( entity.getUpdateTime() );

        return agentRunToolVO;
    }

    @Override
    public List<AgentRunToolVO> toVoList(List<AgentRunTool> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AgentRunToolVO> list = new ArrayList<AgentRunToolVO>( entities.size() );
        for ( AgentRunTool agentRunTool : entities ) {
            list.add( toVo( agentRunTool ) );
        }

        return list;
    }
}
