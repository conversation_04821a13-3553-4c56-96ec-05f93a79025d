package com.ai.application.tenant.audit.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.sql.Timestamp;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 租户审核表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("tenant_audit")
public class TenantAudit implements Serializable {
        /**
    * 审核id
    */
    @Schema(description = "审核id")
    @TableId(type = IdType.AUTO)
private Integer auditId;

    /**
    * 审核单号
    */
    @Schema(description = "审核单号")
    private String auditSn;

    /**
    * 审核类型:10-智能体审核,20-工具审核
    */
    @Schema(description = "审核类型:10-智能体审核,20-工具审核")
    private Integer auditType;

    /**
    * 审核标题
    */
    @Schema(description = "审核标题")
    private String auditTitle;

    /**
    * 申请说明
    */
    @Schema(description = "申请说明")
    private String auditDesc;

    /**
    * 审核状态:1-待审核,2-审核中,3-审核通过,4-审核拒绝,0-已撤销
    */
    @Schema(description = "审核状态:1-待审核,2-审核中,3-审核通过,4-审核拒绝,0-已撤销")
    private Integer auditStatus;

    /**
    * 审核意见
    */
    @Schema(description = "审核意见")
    private String auditResult;

    /**
    * 优先级:1-紧急,2-普通,3-低
    */
    @Schema(description = "优先级:1-紧急,2-普通,3-低")
    private Integer auditPriority;

    /**
    * 审核对象类型:10-智能体,20-工具
    */
    @Schema(description = "审核对象类型:10-智能体,20-工具")
    private Integer objectType;

    /**
    * 审核对象id
    */
    @Schema(description = "审核对象id")
    private Integer objectId;

    /**
    * 审核对象快照
    */
    @Schema(description = "审核对象快照")
    private String objectSnapshot;

    /**
    * 申请人id
    */
    @Schema(description = "申请人id")
    private Integer applyUserId;

    /**
    * 申请时间
    */
    @Schema(description = "申请时间")
    private Timestamp applyTime;

    /**
    * 审核人id
    */
    @Schema(description = "审核人id")
    private Integer auditUserId;

    /**
    * 审核时间
    */
    @Schema(description = "审核时间")
    private Timestamp auditTime;

    /**
    * 租户id
    */
    @Schema(description = "租户id")
    private Integer tenantId;

    @Schema(description = "")
    private Timestamp createTime;

    @Schema(description = "")
    private Timestamp updateTime;

}