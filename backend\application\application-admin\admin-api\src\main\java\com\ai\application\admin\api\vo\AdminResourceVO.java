package com.ai.application.admin.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 资源定义表
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@Schema(name = "资源vo")
public class AdminResourceVO {
    /**
     * 资源对象id
     */
    @Schema(description = "资源id",hidden = true)
    private Integer resourceId;
    @Schema(description = "授权id",hidden = true)
    private Integer grantId;

    /**
     * 资源对象id
     */
    @Schema(description = "资源对象id")
    private Integer objectId;

    /**
     * 资源对象id
     */
    @Schema(description = "资源对象名称")
    private Integer objectName;

}