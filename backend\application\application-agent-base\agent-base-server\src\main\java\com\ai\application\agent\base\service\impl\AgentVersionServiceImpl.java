package com.ai.application.agent.base.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ai.application.agent.base.api.bo.MasterDictBO;
import com.ai.application.agent.base.api.bo.MasterModelBO;
import com.ai.application.agent.base.api.bo.MasterPromptVarBO;
import com.ai.application.agent.base.api.bo.MasterSkillBO;
import com.ai.application.agent.base.api.dto.*;
import com.ai.application.agent.base.api.entity.*;
import com.ai.application.agent.base.api.enums.*;
import com.ai.application.agent.base.api.mapstruct.AgentVersionMapstruct;
import com.ai.application.agent.base.api.vo.AgentDetailVO;
import com.ai.application.agent.base.api.vo.AgentVersionListVO;
import com.ai.application.agent.base.api.vo.AgentVersionVO;
import com.ai.application.agent.base.mapper.AgentMapper;
import com.ai.application.agent.base.mapper.AgentRunSessionMapper;
import com.ai.application.agent.base.mapper.AgentVersionExtendMapper;
import com.ai.application.agent.base.mapper.AgentVersionMapper;
import com.ai.application.agent.base.service.*;
import com.ai.application.app.api.feign.IAppUserClient;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.application.market.api.dto.MarketAgentAddDTO;
import com.ai.application.market.api.feign.IMarketClient;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.list.CollectionUtils;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.util.validator.AssertUtil;
import com.ai.framework.core.vo.ResultVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.*;

/**
 * 智能体版本服务
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Service
public class AgentVersionServiceImpl implements IAgentVersionService {

    @Resource
    private AgentMapper agentMapper;

    @Resource
    private AgentRunSessionMapper agentRunSessionMapper;

    @Resource
    private IAgentService agentService;

    @Resource
    private AgentVersionMapper agentVersionMapper;

    @Resource
    private AgentVersionMapstruct agentVersionMapstruct;

    @Resource
    private IAppUserClient appUserClient;

    @Resource
    private AgentVersionExtendMapper agentVersionExtendMapper;

    @Resource
    private IAgentVersionExtendService agentVersionExtendService;

    @Resource
    private IMarketClient marketClient;

    @Resource
    private IAgentUseMcpService agentUseMcpService;

    @Resource
    private IAgentUseToolService agentUseToolService;

    @Resource
    private IAgentUseWorkflowService agentUseWorkflowService;

    @Resource
    private IAgentUseKbService agentUseKbService;

    @Resource
    private IAgentUseDictService agentUseDictService;

    @Override
    public AgentDetailVO detail(String sn) {
        AgentRunSession agentRunSession = agentRunSessionMapper.selectBySessionSn(sn);
        AssertUtil.isNotNull(agentRunSession, "没有找到对应的版本");
        return agentService.detailById(agentRunSession.getAgentId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void publish(AgentPublishDTO dto) {
        Agent agent = agentMapper.selectByAgentSn(dto.getAgentSn());
        AssertUtil.isNotNull(agent, "没有找到智能体");

        AgentVersion agentVersion = agentVersionMapper.selectById(dto.getVersionSn());
        AssertUtil.isNotNull(agentVersion, "没有找到对应的版本");

        agent.setAgentStatus(AgentStatusEnum.PUBLISH.getCode());
        agentMapper.updateById(agent);

        if (dto.getEnable()) {
            agentVersion.setVersionStatus(VersionStatusEnum.ENABLE.getCode());
            agentVersionMapper.updateById(agentVersion);
        }

        // 发布到应用市场
        MarketAgentAddDTO marketAgentAddDTO = new MarketAgentAddDTO();
        marketAgentAddDTO.setAgentId(agent.getAgentId());
        marketAgentAddDTO.setVersionId(agentVersion.getVersionId());
        marketAgentAddDTO.setAppStatus(1);
        marketClient.addMarketAgent(marketAgentAddDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String masterDraft(MasterDraftDTO dto) {
        this.draftBefore(dto.getAgentSn());
        MasterAddDTO masterAddDTO = BeanUtil.copyProperties(dto, MasterAddDTO.class);
        masterAddDTO.setVersionNumber("0.0.0");
        return this.masterAdd(masterAddDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String workFlowDraft(WorkFlowDraftDTO dto) {
        this.draftBefore(dto.getAgentSn());
        WorkFlowAddDTO workFlowAddDTO = BeanUtil.copyProperties(dto, WorkFlowAddDTO.class);
        workFlowAddDTO.setVersionNumber("0.0.0");
        return this.workFlowAdd(workFlowAddDTO);
    }

    public void draftBefore(String agentSn) {
        Agent agent = agentMapper.selectByAgentSn(agentSn);
        AssertUtil.isNotNull(agent, "没有找到智能体");

        // 当询当前agent的草稿,将有效的草稿变更为停用
        AgentVersion agentVersion = agentVersionMapper.findByAgentIdAndVersionStatus(agent.getAgentId(), VersionStatusEnum.DRAFT.getCode());
        if (Objects.nonNull(agentVersion)) {
            agentVersion.setVersionStatus(VersionStatusEnum.STOP.getCode());
            agentVersionMapper.updateById(agentVersion);

            // 将之前配置版本扩展设置失效
            LambdaUpdateWrapper<AgentVersionExtend> agentVersionExtendLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            agentVersionExtendLambdaUpdateWrapper.eq(AgentVersionExtend::getVersionId, agentVersion.getVersionId());
            agentVersionExtendLambdaUpdateWrapper.set(AgentVersionExtend::getItemStatus, 0);
            agentVersionExtendMapper.update(agentVersionExtendLambdaUpdateWrapper);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String masterAdd(MasterAddDTO dto) {
        Agent agent = agentMapper.selectByAgentSn(dto.getAgentSn());
        AssertUtil.isNotNull(agent, "没有找到智能体");
        AgentVersion agentVersion = insert(dto.getVersionNumber(), VersionStatusEnum.STOP.getCode(), agent.getAgentId());

        // 设置模型
        this.setModel(agentVersion, dto.getModel());
        // 设置变量
        this.setVar(agentVersion, dto.getPromptVars());
        // 设置词库
        this.setDicts(agentVersion, dto.getDicts());
        // 设置敏感词
        this.setSensitives(agentVersion, dto.getSensitives());
        // 设置技能
        this.setSkills(agentVersion, dto.getSkills());
        // 设置知识库
        this.setKnowledge(agentVersion, dto.getKnowledgeIds());

        return agentVersion.getVersionSn();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String workFlowAdd(WorkFlowAddDTO dto) {
        Agent agent = agentMapper.selectByAgentSn(dto.getAgentSn());
        AssertUtil.isNotNull(agent, "没有找到智能体");
        AgentVersion agentVersion = insert(dto.getVersionNumber(), VersionStatusEnum.STOP.getCode(), agent.getAgentId());

        this.setWorkFlow(agentVersion, dto);
        if (AgentTypeEnum.CHAT_AGENT.getCode().equals(agent.getAgentType())) {
            this.setMemory(agentVersion, dto);
        }

        return agentVersion.getVersionSn();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void enable(VersionEnableDTO dto) {
        AgentVersion agentVersion = agentVersionMapper.selectByVersionSn(dto.getVersionSn());
        AssertUtil.isNotNull(agentVersion, "没有找到对应的版本");

        Agent agent = agentMapper.selectById(agentVersion.getAgentId());
        AssertUtil.isNotNull(agent, "没有找到智能体");

        // 更新Agent对应的版本
        agent.setVersionId(agentVersion.getVersionId());
        agentMapper.updateById(agent);

        this.setEnable(agentVersion);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sale(VersionSaleDTO dto) {
        AgentVersion agentVersion = agentVersionMapper.selectByVersionSn(dto.getVersionSn());
        AssertUtil.isNotNull(agentVersion, "没有找到对应的版本");

        // 将上架的设置为下架
        LambdaUpdateWrapper<AgentVersion> agentVersionLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        agentVersionLambdaUpdateWrapper.eq(AgentVersion::getAgentId, agentVersion.getAgentId());
        agentVersionLambdaUpdateWrapper.set(AgentVersion::getVersionOnsale, 0);
        agentVersionMapper.update(agentVersionLambdaUpdateWrapper);

        if (dto.getEnable()) {
            agentVersion.setVersionOnsale(1);
            marketClient.upAgent(agentVersion.getAgentId(), agentVersion.getVersionId());
        } else {
            agentVersion.setVersionOnsale(0);
            marketClient.downAgent(agentVersion.getAgentId(), agentVersion.getVersionId());
        }

        agentVersionMapper.updateById(agentVersion);
    }

    @Override
    public AgentVersionVO get(Integer versionId) {
        AgentVersion agentVersion = agentVersionMapper.selectById(versionId);
        return agentVersionMapstruct.toVo(agentVersion);
    }

    @Override
    public AgentVersionVO getBySn(String versionSn) {
        AgentVersion agentVersion = agentVersionMapper.selectByVersionSn(versionSn);
        AssertUtil.isNotNull(agentVersion, "没有找到版本");
        return agentVersionMapstruct.toVo(agentVersion);
    }

    // 启用版本
    public void setEnable(AgentVersion agentVersion) {
        // 启用版本
        agentVersion.setVersionStatus(VersionStatusEnum.ENABLE.getCode());
        agentVersionMapper.updateById(agentVersion);

        // 将之前启用的版本设置为停用
        LambdaUpdateWrapper<AgentVersion> agentVersionLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        agentVersionLambdaUpdateWrapper.eq(AgentVersion::getAgentId, agentVersion.getAgentId());
        agentVersionLambdaUpdateWrapper.eq(AgentVersion::getVersionStatus, VersionStatusEnum.ENABLE.getCode());
        agentVersionLambdaUpdateWrapper.set(AgentVersion::getVersionStatus, VersionStatusEnum.STOP.getCode());
        agentVersionMapper.update(agentVersionLambdaUpdateWrapper);
    }

    public AgentVersion insert(String versionNumber, Integer versionStaus, Integer agentId) {
        AgentVersion agentVersion = new AgentVersion();
        agentVersion.setVersionNumber(versionNumber);
        agentVersion.setAgentId(agentId);
        agentVersion.setVersionStatus(versionStaus);
        agentVersion.setCreateTime(new Date());
        agentVersion.setUpdateTime(new Date());
        agentVersion.setUpdateUserId(UserContext.getUserId());
        agentVersion.setUpdateUserId(UserContext.getUserId());
        agentVersion.setVersionSn(UUIDUtil.genRandomSn("version-"));
        agentVersionMapper.insert(agentVersion);
        return agentVersion;
    }

    // 设置记忆
    public void setMemory(AgentVersion agentVersion, WorkFlowAddDTO flowDto) {
        if (CollectionUtils.isNotEmpty(flowDto.getShortMemory())) {
            agentVersionExtendService.setItem(agentVersion.getAgentId(),
                    agentVersion.getVersionId(), AgentVersionExtendNameEnum.SHORT_MEMORY.getCode(),
                    JsonUtils.toJsonString(flowDto.getShortMemory()));
        }

        if (Objects.nonNull(flowDto.getLongMemory())) {
            agentVersionExtendService.setItem(agentVersion.getAgentId(),
                    agentVersion.getVersionId(), AgentVersionExtendNameEnum.LONG_MEMORY.getCode(),
                    JsonUtils.toJsonString(flowDto.getLongMemory()));
        }
    }

    // 设置工作流
    public void setWorkFlow(AgentVersion agentVersion, WorkFlowAddDTO flowDto) {
        AgentUseWorkflowAddDTO agentUseWorkflowAddDTO = new AgentUseWorkflowAddDTO();
        agentUseWorkflowAddDTO.setAgentId(agentVersion.getAgentId());
        agentUseWorkflowAddDTO.setVersionId(agentVersion.getVersionId());
        agentUseWorkflowAddDTO.setFlowDefinition(JsonUtils.toJsonString(flowDto.getSteps()));
        agentUseWorkflowAddDTO.setFlowVariables(JsonUtils.toJsonString(flowDto.getVariables()));
        agentUseWorkflowAddDTO.setFlowExtensions(JsonUtils.toJsonString(flowDto.getGraph()));
        agentUseWorkflowService.add(agentUseWorkflowAddDTO);
    }

    // 设置知识库
    public void setKnowledge(AgentVersion agentVersion, List<Integer> knowledgeIds) {
        if (CollectionUtils.isEmpty(knowledgeIds)) {
            return;
        }
        knowledgeIds.forEach(knowledgeId -> {
            AgentUseKbAddDTO agentUseKbAddDTO = new AgentUseKbAddDTO();
            agentUseKbAddDTO.setAgentId(agentVersion.getAgentId());
            agentUseKbAddDTO.setVersionId(agentVersion.getVersionId());
            agentUseKbAddDTO.setKbId(knowledgeId);
            agentUseKbService.add(agentUseKbAddDTO);
        });
    }

    // 设置词库
    public void setDicts(AgentVersion agentVersion, List<MasterDictBO> words) {
        if (CollectionUtils.isEmpty(words)) {
            return;
        }
        words.forEach(word -> {
            AgentUseDictAddDTO agentUseDictAddDTO = new AgentUseDictAddDTO();
            agentUseDictAddDTO.setAgentId(agentVersion.getAgentId());
            agentUseDictAddDTO.setVersionId(agentVersion.getVersionId());
            agentUseDictAddDTO.setDictId(word.getDictId());
            agentUseDictService.add(agentUseDictAddDTO);
        });
    }

    // 设置敏感词
    public void setSensitives(AgentVersion agentVersion, List<MasterDictBO> sensitives) {
        this.setDicts(agentVersion, sensitives);
    }

    // 设置工具
    public void setSkills(AgentVersion agentVersion, List<MasterSkillBO> skills) {
        if (CollectionUtils.isEmpty(skills)) {
            return;
        }

        skills.forEach(skill -> {
            Map<String, String> extend = new HashMap<>();
            extend.put(ToolExtendNameEnum.REPLY_TYPE.getCode(), skill.getReplayType().toString());

            if (SkillTypeEnum.MCP.getCode().equals(skill.getType())) {
                AgentUseMcpAddDTO agentUseMcpAddDTO = new AgentUseMcpAddDTO();
                agentUseMcpAddDTO.setMcpToolId(skill.getSkillId());
                agentUseMcpAddDTO.setAgentId(agentVersion.getAgentId());
                agentUseMcpAddDTO.setVersionId(agentVersion.getVersionId());
                agentUseMcpAddDTO.setMcpExtend(JsonUtils.toJsonString(extend));
                agentUseMcpService.add(agentUseMcpAddDTO);
            }

            if (SkillTypeEnum.TOOL.getCode().equals(skill.getType())) {
                AgentUseToolAddDTO agentUseToolAddDTO = new AgentUseToolAddDTO();
                agentUseToolAddDTO.setToolId(skill.getSkillId());
                agentUseToolAddDTO.setAgentId(agentVersion.getAgentId());
                agentUseToolAddDTO.setVersionId(agentVersion.getVersionId());
                agentUseToolAddDTO.setToolExtend(JsonUtils.toJsonString(extend));
                agentUseToolService.add(agentUseToolAddDTO);
            }
        });
    }

    /**
     * 设置变量
     * @param agentVersion
     * @param promptVar
     */
    public void setVar(AgentVersion agentVersion, List<MasterPromptVarBO> promptVar) {
        if (CollectionUtils.isEmpty(promptVar)) {
            return;
        }
        agentVersionExtendService.setItem(agentVersion.getAgentId(),
                agentVersion.getVersionId(), AgentVersionExtendNameEnum.VAR.getCode(),
                JsonUtils.toJsonString(promptVar));
    }

    /**
     * 设置模型
     * @param agentVersion
     * @param model
     */
    public void setModel(AgentVersion agentVersion, MasterModelBO model) {
        if (Objects.isNull(model)) {
            return;
        }
        agentVersionExtendService.setItem(agentVersion.getAgentId(),
                agentVersion.getVersionId(), AgentVersionExtendNameEnum.MODEL.getCode(),
                model.getModelSn());

        agentVersionExtendService.setItem(agentVersion.getAgentId(),
                agentVersion.getVersionId(), AgentVersionExtendNameEnum.TEMPERATURE.getCode(),
                new BigDecimal(model.getTemperature()).toString());

        agentVersionExtendService.setItem(agentVersion.getAgentId(),
                agentVersion.getVersionId(), AgentVersionExtendNameEnum.PROMPT.getCode(),
                model.getPrompt()
                );
    }

    @Override
    public List<AgentVersionListVO> list(AgentVersionListDTO dto) {
        Agent agent = agentMapper.selectByAgentSn(dto.getAgentSn());
        AssertUtil.isNotNull(agent, "没有找到智能体");

        LambdaQueryWrapper<AgentVersion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentVersion::getAgentId, agent.getAgentId());
        List<AgentVersion> agentVersions = agentVersionMapper.selectList(queryWrapper);
        return agentVersions.stream().map(item -> {
            AgentVersionListVO agentVersionListVO = agentVersionMapstruct.toListVo(item);
            ResultVo<AppUserVO> appUserVOResultVo = appUserClient.getUserById(item.getCreateUserId());
            if (Objects.nonNull(appUserVOResultVo.getData())) {
                agentVersionListVO.setCreateBy(appUserVOResultVo.getData().getUserName());
                agentVersionListVO.setUpdateBy(appUserVOResultVo.getData().getUserName());
            }
            return agentVersionListVO;
        }).toList();
    }

    @Override
    public void update(MasterUpdateDTO dto) {
        Agent agent = agentMapper.selectByAgentSn(dto.getAgentSn());
        AssertUtil.isNotNull(agent, "没有找到智能体");
    }
}