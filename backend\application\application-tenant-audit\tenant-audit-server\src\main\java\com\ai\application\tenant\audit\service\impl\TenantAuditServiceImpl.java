package com.ai.application.tenant.audit.service.impl;

import com.ai.application.app.api.dto.query.AppUserQueryDTO;
import com.ai.application.app.api.feign.IAppUserClient;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.application.base.notice.dto.MessageCreateDto;
import com.ai.application.base.notice.enums.MessageTypeEnum;
import com.ai.application.base.notice.feign.IMessageFeignClient;
import com.ai.application.tenant.audit.api.dto.TenantAuditDTO;
import com.ai.application.tenant.audit.api.dto.query.AuditNoticeBaseVO;
import com.ai.application.tenant.audit.api.dto.query.TenantAddDTO;
import com.ai.application.tenant.audit.api.dto.query.TenantAuditQueryDTO;
import com.ai.application.tenant.audit.api.entity.TenantAudit;
import com.ai.application.tenant.audit.api.enums.AgentTypeEnum;
import com.ai.application.tenant.audit.api.mapstruct.TenantAuditMapstruct;
import com.ai.application.tenant.audit.api.vo.AuditNoticeMsgVO;
import com.ai.application.tenant.audit.api.vo.AuditNoticeResultMsgVO;
import com.ai.application.tenant.audit.api.vo.AuditNoticeStopMsgVO;
import com.ai.application.tenant.audit.api.vo.TenantAuditVO;
import com.ai.application.tenant.audit.mapper.TenantAuditMapper;
import com.ai.application.tenant.audit.service.ITenantAuditService;
import com.ai.framework.core.util.BusinessAssertUtil;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.list.CollectionUtils;
import com.ai.framework.core.util.string.StringUtil;
import com.ai.framework.core.vo.ResultVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.util.*;

import static com.ai.application.base.notice.enums.MessageTypeEnum.AGENT_APPROVAL;

/**
 * 租户审核表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Service
@Slf4j
public class TenantAuditServiceImpl implements ITenantAuditService {
    public static final String AGENT_RESULT_REFUSE = "拒绝";

    public static final String AGENT_RESULT_PASS = "通过";
    @Resource
    private TenantAuditMapper tenantAuditMapper;

    @Resource
    private TenantAuditMapstruct tenantAuditMapstruct;

    @Resource
    private IMessageFeignClient messageFeignClient;

    @Resource
    private IAppUserClient appUserClient;


    @Transactional(readOnly = true)
    @Override
    public PageInfo<TenantAuditVO> page(TenantAuditQueryDTO queryDto) {
        //根据关键字模糊查询用户信息
        List<Integer> userIds = new ArrayList<>();
        if (StringUtil.isNotEmpty(queryDto.getKeyWord())) {
            AppUserQueryDTO dto = new AppUserQueryDTO();
            dto.setUserName(queryDto.getKeyWord());
            ResultVo<List<AppUserVO>> list = appUserClient.list(dto);
            list.getData().forEach(appUserVO -> {
                userIds.add(appUserVO.getUserId());
            });
        }
        queryDto.setUserIds(userIds);
        LambdaQueryWrapper<TenantAudit> queryWrapper = this.buildQuery(queryDto);
        Page<TenantAudit> page = new Page<>(queryDto.getPageNo(), queryDto.getPageSize());
        Page<TenantAudit> result = this.tenantAuditMapper.selectPage(page, queryWrapper);
        PageInfo<TenantAuditVO> tenantAuditVOPageInfo = PageInfo.of(tenantAuditMapstruct.toVoList(result.getRecords()));
        //处理用户名字信息
        tenantAuditVOPageInfo.getList().forEach(audit -> {
            audit.setApplyUserName(
                    Optional.ofNullable(appUserClient.getUserById(audit.getApplyUserId()))
                            .map(ResultVo::getData)
                            .map(AppUserVO::getUserName)
                            .orElse(null)
            );

            audit.setAuditUserName(
                    Optional.ofNullable(appUserClient.getUserById(audit.getAuditUserId()))
                            .map(ResultVo::getData)
                            .map(AppUserVO::getUserName)
                            .orElse(null));
        });

        return tenantAuditVOPageInfo;
    }


    @Transactional(readOnly = true)
    public void notice(AuditNoticeBaseVO queryDto, MessageTypeEnum messageType) {
        // 根据传参类型选择不同的消息构建策略
        switch (messageType) {
            case AGENT_APPROVAL:
                sendApprovalNotice(queryDto);
                break;
            case SCHEDULED_TASK_RUNNING:
                sendApprovalResultNotice(queryDto);
                break;
            case AGENT_APPROVAL_RESULT:
                sendStopNotice(queryDto);
                break;
            default:
                throw new IllegalArgumentException("不支持的消息类型: " + messageType.getDescription());
        }
    }

    private void sendApprovalNotice(AuditNoticeBaseVO queryDto) {
        MessageCreateDto message = new MessageCreateDto();
        AuditNoticeMsgVO noticeMsg = AuditNoticeMsgVO.builder()
                .agentType(queryDto.getAgentType())
                .agentName(queryDto.getAgentName())
                .agentVersion(queryDto.getAgentVersion())
                .activationTime(new Date())
                .auditUserId(queryDto.getAuditUserId())
                .applyUserId(queryDto.getApplyUserId())
                .applyUserName(queryDto.getApplyUserName())
                .auditUserName(queryDto.getAuditUserName())
                .build();

        message.setMsgType(AGENT_APPROVAL.getCode());
        message.setMsgTitle(queryDto.getAgentName());
        message.setMsgContent(JsonUtils.toJsonString(noticeMsg));
        message.setMsgLink(queryDto.getAuditId());
        message.setUserId(queryDto.getAuditUserId());
        message.setAppId(Integer.valueOf(queryDto.getAuditId()));
        log.info("发送通知消息AGENT_APPROVAL{}", JsonUtils.toJsonString(message));
        messageFeignClient.create(message);
    }

    private void sendApprovalResultNotice(AuditNoticeBaseVO queryDto) {
        MessageCreateDto message = new MessageCreateDto();
        AuditNoticeResultMsgVO resultMsg = AuditNoticeResultMsgVO.builder()
                .agentName(queryDto.getAgentName())
                .auditDesc(queryDto.getAuditDesc())
                .agentResult(queryDto.getAgentResult())
                .auditUserId(queryDto.getAuditUserId())
                .auditUserName(queryDto.getAuditUserName())
                .build();

        message.setMsgType(MessageTypeEnum.SCHEDULED_TASK_RUNNING.getCode());
        message.setMsgTitle(queryDto.getAgentName());
        message.setMsgContent(JsonUtils.toJsonString(resultMsg));
        message.setUserId(queryDto.getApplyUserId());
        message.setMsgLink(queryDto.getAuditId());
        message.setAppId(Integer.valueOf(queryDto.getAuditId()));
        messageFeignClient.create(message);
    }

    private void sendStopNotice(AuditNoticeBaseVO queryDto) {
        MessageCreateDto message = new MessageCreateDto();
        AuditNoticeStopMsgVO stopMsg = AuditNoticeStopMsgVO.builder()
                .agentName(queryDto.getAgentName())
                .agentVersion(queryDto.getAgentVersion())
                .stopTime(new Date())
                .auditUserId(queryDto.getAuditUserId())
                .applyUserId(queryDto.getApplyUserId())
                .applyUserName(queryDto.getApplyUserName())
                .auditUserName(queryDto.getAuditUserName())
                .build();

        message.setMsgType(MessageTypeEnum.AGENT_APPROVAL_RESULT.getCode());
        message.setMsgTitle(queryDto.getAgentName());
        message.setMsgContent(JsonUtils.toJsonString(stopMsg));
        message.setUserId(queryDto.getAuditUserId());
        message.setMsgLink(queryDto.getAuditId());
        message.setAppId(Integer.valueOf(queryDto.getAuditId()));
        messageFeignClient.create(message);
    }

    @Transactional(readOnly = true)
    @Override
    public List<TenantAuditVO> list(TenantAuditQueryDTO queryDto) {
        LambdaQueryWrapper<TenantAudit> queryWrapper = this.buildQuery(queryDto);
        return tenantAuditMapstruct.toVoList(this.tenantAuditMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(TenantAddDTO dto) {
        Timestamp now = new Timestamp(System.currentTimeMillis());
        TenantAudit entity = tenantAuditMapstruct.toEntity(dto);
        entity.setCreateTime(now);
        entity.setUpdateTime(now);
        entity.setApplyTime(now);
        tenantAuditMapper.insert(entity);


        ResultVo<AppUserVO> appUserAuditResultVo = appUserClient.getUserById(dto.getAuditUserId());
        ResultVo<AppUserVO> appUserApplyResultVo = appUserClient.getUserById(dto.getApplyUserId());
        if(Objects.isNull(appUserAuditResultVo) || Objects.isNull(appUserApplyResultVo)){
            throw new RuntimeException("找不到申请人或审核人信息");
        }
        // 发送通知消息 智能体启用审批
        if (Objects.nonNull(dto.getMsgNoticeType()) && dto.getMsgNoticeType() == MessageTypeEnum.AGENT_APPROVAL.getCode()) {
            AuditNoticeBaseVO queryDto = AuditNoticeBaseVO.builder()
                    .agentType(AgentTypeEnum.ofType(dto.getObjectType()).getDescription())
                    .agentName(dto.getAuditTitle())
                    .agentVersion(dto.getObjectSnapshot())
                    .applyUserId(dto.getApplyUserId())
                    .auditUserId(dto.getAuditUserId())
                    .applyUserName(appUserApplyResultVo.getData().getUserName())
                    .auditUserName(appUserAuditResultVo.getData().getUserName())
                    .auditId(String.valueOf(entity.getAuditId()))
                    .build();
            log.info("发送通知消息AGENT_APPROVAL{}", JsonUtils.toJsonString(queryDto));
            notice(queryDto, MessageTypeEnum.AGENT_APPROVAL);
        }

        // 发送通知消息 智能体停用审批
        if (Objects.nonNull(dto.getMsgNoticeType()) && dto.getMsgNoticeType() == MessageTypeEnum.AGENT_APPROVAL_RESULT.getCode()) {
            AuditNoticeBaseVO queryDto = AuditNoticeBaseVO.builder()
                    .agentType(AgentTypeEnum.ofType(dto.getObjectType()).getDescription())
                    .agentName(dto.getAuditTitle())
                    .agentVersion(dto.getObjectSnapshot())
                    .applyUserId(dto.getApplyUserId())
                    .auditUserId(dto.getAuditUserId())
                    .auditId(String.valueOf(entity.getAuditId()))
                    .applyUserName(appUserApplyResultVo.getData().getUserName())
                    .auditUserName(appUserAuditResultVo.getData().getUserName())
                    .build();
            log.info("发送通知消息AGENT_APPROVAL_RESULT{}", JsonUtils.toJsonString(queryDto));
            notice(queryDto, MessageTypeEnum.AGENT_APPROVAL_RESULT);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(TenantAuditDTO dto) {
        BusinessAssertUtil.notNull(dto.getAuditSn(), "AuditSn不能为空");
        LambdaQueryWrapper<TenantAudit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantAudit::getAuditSn, dto.getAuditSn());
        TenantAudit entity = tenantAuditMapper.selectOne(queryWrapper);


        if(dto.getAuditStatus()==3){
            //查询某个智能体的审核记录
            // 查询某个智能体的所有待审批记录
            LambdaQueryWrapper<TenantAudit> pendingQuery = new LambdaQueryWrapper<>();
            pendingQuery.eq(TenantAudit::getObjectType, entity.getObjectType())
                    .eq(TenantAudit::getAuditStatus, 1)
                    .eq(TenantAudit::getObjectId, entity.getObjectId())
                    .ne(TenantAudit::getAuditSn, dto.getAuditSn());
            // 1表示待审批状态;
            List<TenantAudit> pendingAudits = tenantAuditMapper.selectList(pendingQuery);
            if (CollectionUtils.isNotEmpty(pendingAudits)) {
                pendingAudits.forEach(audit -> {
                    audit.setAuditStatus(0);
                    audit.setAuditUserId(null);
                    tenantAuditMapper.updateById(audit);
                });
            }
        }



        // TODO 唯一性字段校验
        BusinessAssertUtil.notNull(entity, "找不到AuditId为 " + dto.getAuditSn() + " 的记录");
        entity.setAuditStatus(dto.getAuditStatus());
        entity.setUpdateTime(null);
        tenantAuditMapper.updateById(entity);


        ResultVo<AppUserVO> appUserAuditResultVo = appUserClient.getUserById(entity.getAuditUserId());
        ResultVo<AppUserVO> appUserApplyResultVo = appUserClient.getUserById(entity.getApplyUserId());
        if(Objects.isNull(appUserAuditResultVo) || Objects.isNull(appUserApplyResultVo)){
            throw new RuntimeException("找不到申请人或审核人信息");
        }
        // 发送通知消息 智能体审批结果通知 审核通过
        if (Objects.nonNull(dto.getAuditStatus()) && dto.getAuditStatus() == 3) {
            AuditNoticeBaseVO queryDto = AuditNoticeBaseVO.builder()
                    .agentType(AgentTypeEnum.ofType(entity.getObjectType()).getDescription())
                    .agentName(entity.getAuditTitle())
                    .agentVersion(entity.getObjectSnapshot())
                    .applyUserId(entity.getApplyUserId())
                    .auditUserId(entity.getAuditUserId())
                    .auditDesc(entity.getAuditDesc())
                    .applyUserName(appUserApplyResultVo.getData().getUserName())
                    .auditUserName(appUserAuditResultVo.getData().getUserName())
                    .agentResult(AGENT_RESULT_PASS)
                    .auditId(String.valueOf(entity.getAuditId()))
                    .build();
            notice(queryDto, MessageTypeEnum.SCHEDULED_TASK_RUNNING);
            //
        }

        // 发送通知消息 智能体审批结果通知 审核拒绝
        if (Objects.nonNull(dto.getAuditStatus()) && dto.getAuditStatus() == 4) {
            AuditNoticeBaseVO queryDto = AuditNoticeBaseVO.builder()
                    .agentType(AgentTypeEnum.ofType(entity.getObjectType()).getDescription())
                    .agentName(entity.getAuditTitle())
                    .agentVersion(entity.getObjectSnapshot())
                    .applyUserId(entity.getApplyUserId())
                    .auditUserId(entity.getAuditUserId())
                    .auditUserId(entity.getAuditUserId())
                    .applyUserName(appUserApplyResultVo.getData().getUserName())
                    .auditUserName(appUserAuditResultVo.getData().getUserName())
                    .auditDesc(entity.getAuditDesc())
                    .agentResult(AGENT_RESULT_REFUSE)
                    .auditId(String.valueOf(entity.getAuditId()))
                    .build();
            notice(queryDto, MessageTypeEnum.SCHEDULED_TASK_RUNNING);


        }



    }

    @Transactional(readOnly = true)
    @Override
    public TenantAuditVO get(Integer id) {
        BusinessAssertUtil.notNull(id, "AuditId不能为空");

        TenantAudit entity = tenantAuditMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到AuditId为 " + id + " 的记录");

        return tenantAuditMapstruct.toVo(entity);
    }

    private LambdaQueryWrapper<TenantAudit> buildQuery(TenantAuditQueryDTO queryDto) {
        LambdaQueryWrapper<TenantAudit> queryWrapper = new LambdaQueryWrapper<TenantAudit>();
        //待审批条件查询
        if (queryDto.getAuditType() != null && queryDto.getAuditType() == 1) {
            queryWrapper.eq(TenantAudit::getAuditType, 1);
            queryWrapper.eq(Objects.nonNull(queryDto.getObjectType()), TenantAudit::getObjectType, queryDto.getObjectType());
            //模糊搜索：名称、发起人
            //支持筛选子类型、发起时间范围筛选
            if (CollectionUtils.isNotEmpty(queryDto.getUserIds())) {
                queryWrapper.and(wrapper ->
                        wrapper.like(TenantAudit::getAuditTitle, queryDto.getKeyWord())
                                .or()
                                .in(TenantAudit::getApplyUserId, queryDto.getUserIds())
                );
            } else {
                queryWrapper.like(TenantAudit::getAuditTitle, queryDto.getKeyWord());
            }
            if (queryDto.getApplyTimeStart() != null && queryDto.getApplyTimeEnd() != null) {
                queryWrapper.between(TenantAudit::getApplyTime, queryDto.getApplyTimeStart(), queryDto.getApplyTimeEnd());
            }

            //已审批条件查询
            if (queryDto.getAuditType() != null && queryDto.getAuditType() == 3) {
                queryWrapper.in(TenantAudit::getAuditType, List.of(3, 4));
                queryWrapper.eq(Objects.nonNull(queryDto.getObjectType()), TenantAudit::getObjectType, queryDto.getObjectType());
//            模糊搜索：名称、发起人、审批人
//            支持筛选子类型、发起时间范围、审批时间、审批结果筛选
                if (CollectionUtils.isNotEmpty(queryDto.getUserIds())) {
                    queryWrapper.and(wrapper ->
                            wrapper.like(TenantAudit::getAuditTitle, queryDto.getKeyWord())
                                    .or()
                                    .in(TenantAudit::getApplyUserId, queryDto.getUserIds())
                                    .or()
                                    .in(TenantAudit::getAuditUserId, queryDto.getUserIds())
                    );
                } else {
                    queryWrapper.like(TenantAudit::getAuditTitle, queryDto.getKeyWord());
                }
                if (queryDto.getApplyTimeStart() != null && queryDto.getApplyTimeEnd() != null) {
                    queryWrapper.between(TenantAudit::getApplyTime, queryDto.getApplyTimeStart(), queryDto.getApplyTimeEnd());
                }

                if (queryDto.getAuditTimeStart() != null && queryDto.getAuditTimeEnd() != null) {
                    queryWrapper.between(TenantAudit::getAuditTime, queryDto.getAuditTimeStart(), queryDto.getAuditTimeEnd());
                }

            }


            //我发起的审批条件查询
            if (queryDto.getAuditType() != null && queryDto.getAuditType() == 2) {
                queryWrapper.in(Objects.nonNull(queryDto.getAuditResult()), TenantAudit::getAuditType, queryDto.getAuditResult());

                queryWrapper.eq(Objects.nonNull(queryDto.getObjectType()), TenantAudit::getObjectType, queryDto.getObjectType());
//                模糊搜索：名称、审批人
//                支持筛选子类型、发起时间范围、审批时间、审批结果筛选
                if (CollectionUtils.isNotEmpty(queryDto.getUserIds())) {
                    queryWrapper.and(wrapper ->
                            wrapper.like(TenantAudit::getAuditTitle, queryDto.getKeyWord())
                                    .or()
                                    .in(TenantAudit::getAuditUserId, queryDto.getUserIds())
                    );
                } else {
                    queryWrapper.like(TenantAudit::getAuditTitle, queryDto.getKeyWord());
                }

                if (queryDto.getAuditTimeStart() != null && queryDto.getAuditTimeEnd() != null) {
                    queryWrapper.between(TenantAudit::getAuditTime, queryDto.getAuditTimeStart(), queryDto.getAuditTimeEnd());
                }

            }

        }
        return queryWrapper;
    }

};