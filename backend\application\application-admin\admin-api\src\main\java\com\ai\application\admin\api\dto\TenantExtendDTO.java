package com.ai.application.admin.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 租户扩展配置表
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@Schema(name = "租户扩展配置表DTO")
public class TenantExtendDTO {
    @Schema(description = "")
    private Integer itemId;
    /**
     * 参数name
     */
    @Schema(description = "参数name")
    @NotBlank(message = "参数name不能为空")
    private String itemName;
    /**
     * 参数值
     */
    @Schema(description = "参数值")
    @NotBlank(message = "参数值不能为空")
    private String itemValue;
    /**
     * 记录状态0:失效,1:生效
     */
    @Schema(description = "记录状态0:失效,1:生效")
    private Integer itemStatus;
    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    @NotNull(message = "租户ID不能为空")
    private Integer tenantId;
    @Schema(description = "")
    private Date createTime;
    @Schema(description = "")
    private Date updateTime;
}