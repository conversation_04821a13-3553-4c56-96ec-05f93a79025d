package com.ai.application.agent.run.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 短期记忆提取结果DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "ShortTermMemoryRetrieveResultDTO")
public class ShortTermMemoryRetrieveResultDTO {

    /**
     * 是否成功
     */
    @Schema(description = "是否成功")
    private Boolean success;

    /**
     * 记忆映射结果
     * key: index（配置中的索引键）
     * value: 提取的记忆内容（可能是字符串、对象或列表）
     */
    @Schema(description = "记忆映射结果")
    private Map<String, Object> memoryMap;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 处理的配置数量
     */
    @Schema(description = "处理的配置数量")
    private Integer processedConfigCount;

    /**
     * 成功提取的配置数量
     */
    @Schema(description = "成功提取的配置数量")
    private Integer successConfigCount;
}
