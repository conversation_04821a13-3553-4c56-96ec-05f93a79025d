package com.ai.application.agent.run.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 短期记忆提取结果DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "ShortTermMemoryRetrieveResultDTO")
public class ShortTermMemoryRetrieveResultDTO {

    /**
     * 是否成功
     */
    @Schema(description = "是否成功")
    private Boolean success;

    /**
     * 输出结果
     */
    @Schema(description = "输出结果")
    private List<String> output;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 记忆总数
     */
    @Schema(description = "记忆总数")
    private Integer totalCount;

    /**
     * 返回的记忆数量
     */
    @Schema(description = "返回的记忆数量")
    private Integer returnCount;

    /**
     * 详细记忆列表（当需要详细信息时）
     */
    @Schema(description = "详细记忆列表")
    private List<MemorySearchResponseDTO.MemoryItemDTO> memoryDetails;
}
