package com.ai.application.task.api.dto.query;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import com.ai.framework.core.vo.PageParam;

/**
 * 计划任务表-查询条件
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Data
@Schema(name = "计划任务表QueryDTO")
public class TaskQueryDTO extends PageParam {
    @Schema(description = "任务类型:11-单次,13-单次批量,21-周期性")
    private Integer taskType;

    @Schema(description = "搜索关键词")
    private String keyword;
}