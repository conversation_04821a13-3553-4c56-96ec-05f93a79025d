package com.ai.application.agent.base.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 智能体运行月统计表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("agent_stats_monthly")
public class AgentStatsMonthly implements Serializable {
        /**
    * 统计id
    */
    @Schema(description = "统计id")
    @TableId(type = IdType.AUTO)
private Integer statsId;

    /**
    * 统计月份(YYYY-MM)
    */
    @Schema(description = "统计月份(YYYY-MM)")
    private String statsMonth;

    /**
    * 运行次数
    */
    @Schema(description = "运行次数")
    private Integer runCount;

    /**
    * 成功次数
    */
    @Schema(description = "成功次数")
    private Integer successCount;

    /**
    * 失败次数
    */
    @Schema(description = "失败次数")
    private Integer failedCount;

    /**
    * 超时次数
    */
    @Schema(description = "超时次数")
    private Integer timeoutCount;

    /**
    * 中断次数
    */
    @Schema(description = "中断次数")
    private Integer interruptCount;

    /**
    * 总执行时长(毫秒)
    */
    @Schema(description = "总执行时长(毫秒)")
    private Long totalDuration;

    /**
    * 平均执行时长(毫秒)
    */
    @Schema(description = "平均执行时长(毫秒)")
    private Integer avgDuration;

    /**
    * 最大执行时长(毫秒)
    */
    @Schema(description = "最大执行时长(毫秒)")
    private Integer maxDuration;

    /**
    * 最小执行时长(毫秒)
    */
    @Schema(description = "最小执行时长(毫秒)")
    private Integer minDuration;

    /**
    * 总消耗tokens
    */
    @Schema(description = "总消耗tokens")
    private Long totalTokens;

    /**
    * 提示词tokens
    */
    @Schema(description = "提示词tokens")
    private Long promptTokens;

    /**
    * 生成tokens
    */
    @Schema(description = "生成tokens")
    private Long completionTokens;

    /**
    * 智能体id
    */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
    * 用户id(0表示所有用户)
    */
    @Schema(description = "用户id(0表示所有用户)")
    private Integer userId;

    /**
    * 租户id
    */
    @Schema(description = "租户id")
    private Integer tenantId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}