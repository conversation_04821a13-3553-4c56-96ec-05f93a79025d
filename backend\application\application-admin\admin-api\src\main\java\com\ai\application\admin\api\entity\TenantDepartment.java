package com.ai.application.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 租户部门表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("tenant_department")
public class TenantDepartment implements Serializable {
    /**
     * 部门id
     */
    @Schema(description = "部门id")
    @TableId(type = IdType.AUTO)
    private Integer deptId;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String deptName;

    /**
     * 所有父节点名称
     */
    @Schema(description = "所有父节点名称")
    private String deptUpName;

    /**
     * 所有父节点路径
     */
    @Schema(description = "所有父节点路径")
    private String deptUpPath;

    /**
     * 排序值
     */
    @Schema(description = "排序值")
    private Integer deptSort;

    /**
     * 状态 1启用 0禁用 -1删除
     */
    @Schema(description = "状态 1启用 0禁用 -1删除")
    private Integer deptStatus;

    /**
     * 父级ID
     */
    @Schema(description = "父级ID")
    private Integer parentId;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Integer tenantId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}