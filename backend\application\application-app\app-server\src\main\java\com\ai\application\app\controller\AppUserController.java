package com.ai.application.app.controller;

import com.ai.application.app.api.dto.*;
import com.ai.application.app.api.dto.query.AppUserQueryDTO;
import com.ai.application.app.api.dto.query.AppUserQueryPageDTO;
import com.ai.application.app.api.vo.AppUserBatchImportResultVO;
import com.ai.application.app.api.vo.AppUserDetailVO;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.application.app.service.IAppUserService;
import com.ai.framework.core.vo.ResultVo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 应用用户表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Tag(name = "应用用户表", description = "应用用户表 相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1/user")
public class AppUserController {

    @Autowired
    private IAppUserService appUserService;

    @Operation(summary = "应用用户表-分页查询", description = "查询所有应用用户表 信息")
    @ApiResponse(responseCode = "0", description = "成功",
            content = @Content(schema = @Schema(implementation = AppUserVO.class)))
    @RequestMapping(value = "/page", method = {RequestMethod.POST})
    public ResultVo<PageInfo<AppUserVO>> page(@Validated @RequestBody AppUserQueryPageDTO queryDto) {
        return ResultVo.data(appUserService.page(queryDto));
    }

    @Operation(summary = "应用用户表-新增")
    @PostMapping("/create")
    public ResultVo<Void> create(@Validated @RequestBody AppUserCreateDTO dto) {
        appUserService.save(dto);
        return ResultVo.success("保存成功");
    }

    @Operation(summary = "应用用户表-修改")
    @PostMapping(value = "/update")
    public ResultVo<Void> update(@Validated @RequestBody AppUserUpdateDTO dto) {
        appUserService.update(dto);
        return ResultVo.success("修改成功");
    }

    @Operation(summary = "应用用户表-启用/禁用", description = "根据用户编码设置启用/禁用")
    @PostMapping("/{userSn}/enable")
    public ResultVo<Boolean> updateStatus(@PathVariable("userSn") String userSn, @RequestBody @Validated AppUserStatusDTO dto) {
        appUserService.saveUserStatus(userSn, dto.getEnable());
        return ResultVo.success("状态设置成功");
    }

    @Operation(summary = "应用用户表-查询详情", description = "根据用户编号应用用户表 信息")
    @ApiResponse(responseCode = "0", description = "成功",
            content = @Content(schema = @Schema(implementation = AppUserDetailVO.class)))
    @GetMapping("/{userSn}/detail")
    public ResultVo<AppUserDetailVO> detail(@PathVariable("userSn") String userSn) {
        return ResultVo.data(appUserService.detail(userSn));
    }

    @Operation(summary = "下载导入模板", description = "下载导入模板")
    @GetMapping("/downloadTemplage")
    public ResponseEntity<byte[]> downloadImportTemplage() {
        return appUserService.downloadImportTemplage();
    }

    @Operation(summary = "用户批量导入", description = "用户批量导入")
    @PostMapping("/import")
    public ResultVo<AppUserBatchImportResultVO> batchImport(@RequestParam("file") MultipartFile file) {
        return ResultVo.data(appUserService.batchImport(file));
    }

    @Operation(summary = "用户批量导入失败用户数据导出", description = "用户批量导入失败用户数据导出")
    @PostMapping("/fail/export")
    public ResponseEntity<byte[]> exportFailData(@RequestBody ImportFailUserDTO dto) {
        return appUserService.exportFailData(dto);
    }

    @Operation(summary = "用户转移", description = "用户转移")
    @PostMapping("/{deptId}/move")
    public ResultVo<Void> moveUsers(@PathVariable("deptId") Integer deptId, @RequestBody @Valid UserMoveDto dto){
        appUserService.moveUsers(deptId,dto);
        return ResultVo.success("转移成功");
    }

    @Operation(summary = "设置管理员", description = "设置管理员")
    @GetMapping("/{userSn}/admin")
    public ResultVo<Void> setUserAdmin(@PathVariable("userSn") String userSn){
        appUserService.setAdmin(userSn);
        return ResultVo.success("设置管理员成功");
    }

    @Operation(summary = "取消管理员", description = "取消管理员")
    @GetMapping("/{userSn}/delete/admin")
    public ResultVo<Void> deleteUserAdmin(@PathVariable("userSn") String userSn){
        appUserService.deleteAdmin(userSn);
        return ResultVo.success("取消成功");
    }

    @Operation(summary = "移除用户", description = "移除用户")
    @GetMapping("/{userSn}/delete/user")
    public ResultVo<Void> deleteUser(@PathVariable("userSn") String userSn){
        appUserService.deleteUser(userSn);
        return ResultVo.success("移除成功");
    }

    @Operation(summary = "应用用户表-初始化密码", description = "根据用户编码初始化密码")
    @PostMapping("/initPassword")
    public ResultVo<Void> initPassword(@RequestBody @Validated AppUserInitPasswordDto dto) {
        appUserService.initPassword(dto);
        return ResultVo.success("初始化密码成功");
    }

    @Operation(summary = "应用用户表-重置密码", description = "根据用户编码重置密码")
    @PostMapping("/{userSn}/resetPassword")
    public ResultVo<Void> resetPassword(@PathVariable("userSn") String userSn) {
        appUserService.resetPassword(userSn);
        return ResultVo.success("重置密码成功");
    }

    @Operation(summary = "应用用户表-修改密码", description = "根据用户编码修改密码")
    @PostMapping("/{userSn}/updatePassword")
    public ResultVo<Void> updatePassword(@PathVariable("userSn") String userSn, @RequestBody @Validated UserUpdatePasswordDTO dto) {
        appUserService.updatePassword(userSn, dto);
        return ResultVo.success("修改密码成功");
    }

}