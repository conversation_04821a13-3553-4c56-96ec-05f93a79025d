package com.ai.application.base.log.api.mapstruct;

import com.ai.application.base.log.api.dto.ErrorLogDTO;
import com.ai.application.base.log.api.entity.ErrorLog;
import com.ai.application.base.log.api.vo.ErrorLogDetailVO;
import com.ai.application.base.log.api.vo.ErrorLogPageVO;
import com.ai.application.base.log.api.vo.ErrorLogVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-12T18:43:53+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class ErrorLogMapstructImpl implements ErrorLogMapstruct {

    @Override
    public ErrorLog toEntity(ErrorLogDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ErrorLog errorLog = new ErrorLog();

        errorLog.setLogId( dto.getLogId() );
        errorLog.setErrorType( dto.getErrorType() );
        errorLog.setErrorLevel( dto.getErrorLevel() );
        errorLog.setErrorModule( dto.getErrorModule() );
        errorLog.setErrorCode( dto.getErrorCode() );
        errorLog.setErrorMessage( dto.getErrorMessage() );
        errorLog.setErrorStack( dto.getErrorStack() );
        errorLog.setErrorTrace( dto.getErrorTrace() );
        errorLog.setErrorMetadata( dto.getErrorMetadata() );
        errorLog.setAffectedObjectType( dto.getAffectedObjectType() );
        errorLog.setAffectedObjectId( dto.getAffectedObjectId() );
        errorLog.setTenantId( dto.getTenantId() );
        errorLog.setCreateTime( dto.getCreateTime() );
        errorLog.setUpdateTime( dto.getUpdateTime() );

        return errorLog;
    }

    @Override
    public List<ErrorLog> toEntityList(List<ErrorLogDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<ErrorLog> list = new ArrayList<ErrorLog>( dtolist.size() );
        for ( ErrorLogDTO errorLogDTO : dtolist ) {
            list.add( toEntity( errorLogDTO ) );
        }

        return list;
    }

    @Override
    public ErrorLogVO toVo(ErrorLog entity) {
        if ( entity == null ) {
            return null;
        }

        ErrorLogVO errorLogVO = new ErrorLogVO();

        errorLogVO.setLogId( entity.getLogId() );
        errorLogVO.setErrorType( entity.getErrorType() );
        errorLogVO.setErrorLevel( entity.getErrorLevel() );
        errorLogVO.setErrorModule( entity.getErrorModule() );
        errorLogVO.setErrorCode( entity.getErrorCode() );
        errorLogVO.setErrorMessage( entity.getErrorMessage() );
        errorLogVO.setErrorStack( entity.getErrorStack() );
        errorLogVO.setErrorTrace( entity.getErrorTrace() );
        errorLogVO.setErrorMetadata( entity.getErrorMetadata() );
        errorLogVO.setAffectedObjectType( entity.getAffectedObjectType() );
        errorLogVO.setAffectedObjectId( entity.getAffectedObjectId() );
        errorLogVO.setTenantId( entity.getTenantId() );
        errorLogVO.setCreateTime( entity.getCreateTime() );
        errorLogVO.setUpdateTime( entity.getUpdateTime() );

        return errorLogVO;
    }

    @Override
    public ErrorLogDetailVO toDetailVo(ErrorLog entity) {
        if ( entity == null ) {
            return null;
        }

        ErrorLogDetailVO errorLogDetailVO = new ErrorLogDetailVO();

        errorLogDetailVO.setLogId( entity.getLogId() );
        errorLogDetailVO.setErrorType( entity.getErrorType() );
        errorLogDetailVO.setErrorLevel( entity.getErrorLevel() );
        errorLogDetailVO.setErrorModule( entity.getErrorModule() );
        errorLogDetailVO.setErrorCode( entity.getErrorCode() );
        errorLogDetailVO.setErrorMessage( entity.getErrorMessage() );
        errorLogDetailVO.setErrorStack( entity.getErrorStack() );
        errorLogDetailVO.setErrorTrace( entity.getErrorTrace() );
        errorLogDetailVO.setErrorMetadata( entity.getErrorMetadata() );
        errorLogDetailVO.setAffectedObjectType( entity.getAffectedObjectType() );
        errorLogDetailVO.setAffectedObjectId( entity.getAffectedObjectId() );
        errorLogDetailVO.setTenantId( entity.getTenantId() );
        errorLogDetailVO.setCreateTime( entity.getCreateTime() );
        errorLogDetailVO.setUpdateTime( entity.getUpdateTime() );

        return errorLogDetailVO;
    }

    @Override
    public List<ErrorLogVO> toVoList(List<ErrorLog> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ErrorLogVO> list = new ArrayList<ErrorLogVO>( entities.size() );
        for ( ErrorLog errorLog : entities ) {
            list.add( toVo( errorLog ) );
        }

        return list;
    }

    @Override
    public List<ErrorLogPageVO> toVoPageList(List<ErrorLog> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ErrorLogPageVO> list = new ArrayList<ErrorLogPageVO>( entities.size() );
        for ( ErrorLog errorLog : entities ) {
            list.add( errorLogToErrorLogPageVO( errorLog ) );
        }

        return list;
    }

    protected ErrorLogPageVO errorLogToErrorLogPageVO(ErrorLog errorLog) {
        if ( errorLog == null ) {
            return null;
        }

        ErrorLogPageVO errorLogPageVO = new ErrorLogPageVO();

        errorLogPageVO.setLogId( errorLog.getLogId() );
        errorLogPageVO.setErrorType( errorLog.getErrorType() );
        errorLogPageVO.setErrorLevel( errorLog.getErrorLevel() );
        errorLogPageVO.setErrorModule( errorLog.getErrorModule() );
        errorLogPageVO.setErrorCode( errorLog.getErrorCode() );
        errorLogPageVO.setErrorMessage( errorLog.getErrorMessage() );
        errorLogPageVO.setErrorStack( errorLog.getErrorStack() );
        errorLogPageVO.setErrorTrace( errorLog.getErrorTrace() );
        errorLogPageVO.setErrorMetadata( errorLog.getErrorMetadata() );
        errorLogPageVO.setAffectedObjectType( errorLog.getAffectedObjectType() );
        errorLogPageVO.setAffectedObjectId( errorLog.getAffectedObjectId() );
        errorLogPageVO.setTenantId( errorLog.getTenantId() );
        errorLogPageVO.setCreateTime( errorLog.getCreateTime() );
        errorLogPageVO.setUpdateTime( errorLog.getUpdateTime() );

        return errorLogPageVO;
    }
}
