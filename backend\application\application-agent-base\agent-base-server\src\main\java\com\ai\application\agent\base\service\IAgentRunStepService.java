package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.dto.AgentRunStepListDTO;
import com.ai.application.agent.base.api.dto.AgentRunStepDTO;
import com.ai.application.agent.base.api.vo.AgentRunStepVO;
import java.util.List;

/**
 * 智能体运行步骤表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public interface IAgentRunStepService {

    /**
     * 列表
     *
     * @param queryDto
     * @return
     */
    List<AgentRunStepVO> list(AgentRunStepListDTO queryDto);

    /**
     * 保存
     *
     * @param dto
     */
    void add(AgentRunStepDTO dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(AgentRunStepDTO dto);

    /**
     * 查看
     *
     * @param id
     * @return
     */
    AgentRunStepVO get(Integer id);
}