package com.ai.application.app.api.mapstruct;

import com.ai.application.app.api.dto.AppUserBatchImportData;
import com.ai.application.app.api.dto.AppUserCreateDTO;
import com.ai.application.app.api.dto.AppUserDTO;
import com.ai.application.app.api.dto.AppUserUpdateDTO;
import com.ai.application.app.api.entity.AppUser;
import com.ai.application.app.api.vo.AppUserDetailVO;
import com.ai.application.app.api.vo.AppUserVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <p>
 * 应用用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */

@Mapper(componentModel = "spring")
public interface AppUserMapstruct {
    AppUserMapstruct INSTANCE = Mappers.getMapper(AppUserMapstruct.class);

    AppUser toEntity(AppUserDTO dto);
    AppUser toEntity(AppUserCreateDTO dto);
    AppUser toEntity(AppUserUpdateDTO dto);
    AppUser toEntity(AppUserVO dto);
    List<AppUser> toEntityList(List<AppUserDTO> dtolist);
    AppUserVO toVo(AppUser entity);
    AppUserDetailVO toDetailVo(AppUser entity);
    AppUserDTO toDto(AppUserBatchImportData data);
    List<AppUserVO> toVoList(List<AppUser> entities);
}
