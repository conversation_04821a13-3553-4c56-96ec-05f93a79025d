package com.ai.framework.core.launch;

import com.ai.framework.core.constants.LauncherConstant;
import com.ai.framework.core.util.spring.PropsUtil;
import com.ai.framework.core.util.string.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.builder.SpringApplicationBuilder;

import java.util.Properties;

/**
 * 启动参数拓展
 *
 * <AUTHOR>
 */
public class LauncherServiceImpl implements LauncherService {
	@Override
	public void launcher(SpringApplicationBuilder builder, String appName, String profile) {
		Properties props = System.getProperties();
		String ak = System.getenv("NACOS_AK");
		String sk = System.getenv("NACOS_SK");
		String addr = System.getenv("NACOS_ADDR");
		String registerIp = System.getenv("SERVER_IP");
		String registerPort = System.getenv("SERVER_PORT");

		if (StringUtil.isEmpty(ak)) ak = "nacos";
		if (StringUtil.isEmpty(sk)) sk = "DUg1Yjewwu";
		if (StringUtil.isEmpty(addr)) addr = "************:7148";

		System.out.println("nacos host:" + addr);
		if(StringUtils.isNotBlank(registerIp)){
			PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.ip", registerIp);
		}
		if(StringUtils.isNotBlank(registerPort)){
			PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.port", registerPort);
		}

		PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.accessKey", ak);
		PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.secretKey", sk);
		PropsUtil.setProperty(props, "spring.cloud.nacos.config.accessKey", ak);
		PropsUtil.setProperty(props, "spring.cloud.nacos.config.secretKey", sk);
		PropsUtil.setProperty(props, "spring.cloud.nacos.username", ak);
		PropsUtil.setProperty(props, "spring.cloud.nacos.password", sk);

		PropsUtil.setProperty(props, "spring.cloud.nacos.server-addr", addr);
		PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.namespace", profile);
		PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.group", profile);
		PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.timeout", "3000");

		PropsUtil.setProperty(props, "spring.cloud.nacos.config.namespace", profile);
		PropsUtil.setProperty(props, "spring.cloud.nacos.config.group", profile);
		PropsUtil.setProperty(props, "spring.cloud.nacos.config.file-extension", "yaml");

		PropsUtil.setProperty(props, "spring.cloud.nacos.config.ext-config[0].data-id", LauncherConstant.NacosConfig.COMMON_YAML);
		PropsUtil.setProperty(props, "spring.cloud.nacos.config.ext-config[0].group", profile);
		PropsUtil.setProperty(props, "spring.cloud.nacos.config.ext-config[0].refresh", LauncherConstant.NacosConfig.REFRESH);

		PropsUtil.setProperty(props, "-Dnacos.logging.default.config.disable", "true");
		PropsUtil.setProperty(props, "-Djava.awt.headless", "true");
	}

	@Override
	public int getOrder() {
		return 20;
	}
}