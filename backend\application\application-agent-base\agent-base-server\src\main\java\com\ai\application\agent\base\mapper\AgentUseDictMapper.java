package com.ai.application.agent.base.mapper;

import com.ai.application.agent.base.api.dto.query.AgentUseKnowledgeDictQueryDTO;
import com.ai.application.agent.base.api.entity.AgentUseDict;
import com.ai.application.agent.base.api.vo.AgentUseKnowledgeDictQueryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 智能体关联字典表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface AgentUseDictMapper extends BaseMapper<AgentUseDict> {
    IPage<AgentUseKnowledgeDictQueryVO> selectUseDictByPage(IPage<AgentUseKnowledgeDictQueryVO> page, @Param("params") AgentUseKnowledgeDictQueryDTO dto);
}
