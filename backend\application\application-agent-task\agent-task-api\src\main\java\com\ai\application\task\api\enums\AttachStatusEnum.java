package com.ai.application.task.api.enums;

import lombok.Getter;

/**
 * 处理状态:1-待处理,2-处理中,3-处理完成,4-处理失败,5-跳过
 */
@Getter
public enum AttachStatusEnum {
    WAIT(1, "待处理"),
    RUN(2, "处理中"),
    FINISH(3, "处理完成"),
    FAIL(4, "处理失败"),
    SKIP(5, "跳过"),
    ;

    private final Integer code;
    private final String desc;

    AttachStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
