package com.ai.application.agent.base.api.vo;

import com.ai.application.agent.base.api.bo.AgentMetadataBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 智能体表
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@Schema(name = "")
public class AgentPageVO {
    /**
     * 智能体sn
     */
    @Schema(description = "智能体sn")
    private String agentSn;

    /**
     * 智能体名称
     */
    @Schema(description = "智能体名称")
    private String agentName;

    /**
     * 智能体描述
     */
    @Schema(description = "智能体描述")
    private String agentDesc;

    /**
     * 智能体类型: 10:对话流, 20:工作流, 30:master
     */
    @Schema(description = "智能体类型: 10:对话流, 20:工作流, 30:master")
    private Integer agentType;

    /**
     * 智能体元信息:logo,icon,创建人名等
     */
    @Schema(description = "智能体元信息:logo,icon,创建人名等")
    private AgentMetadataBO agentMetadata;

    /**
     * 智能体启用版本id
     */
    @Schema(description = "智能体版本名称")
    private String versionName;

    /**
     * 创建人id
     */
    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "更新时间")
    private String updateTime;
}