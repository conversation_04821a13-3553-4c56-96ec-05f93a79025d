package com.ai.application.agent.run.controller;

import com.ai.application.agent.run.service.IFlowRunService;
import com.ai.framework.core.vo.ResultVo;
import dto.FlowRunDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import vo.FlowRunVO;

@Tag(name = "工作流", description = "工作流")
@RestController
@RequestMapping("/v1/flow")
@AllArgsConstructor
public class FlowController {
    private final IFlowRunService flowSessionService;

    /**
     * 工作流执行
     * @return
     */
    @Operation(summary = "工作流会话")
    @PostMapping(produces = "text/event-stream")
    public Flux<ResultVo<FlowRunVO>> stream(@Validated @RequestBody FlowRunDTO dto) {
        return flowSessionService.stream(dto);
    }

    @Operation(summary = "工作流会话")
    @PostMapping("/noStream")
    public ResultVo<FlowRunVO> noStream(@Validated @RequestBody FlowRunDTO dto) {
        return flowSessionService.noStream(dto);
    }
}