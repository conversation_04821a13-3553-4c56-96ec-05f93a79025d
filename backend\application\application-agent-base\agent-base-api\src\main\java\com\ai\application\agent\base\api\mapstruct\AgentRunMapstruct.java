package com.ai.application.agent.base.api.mapstruct;
import com.ai.application.agent.base.api.entity.AgentRun;
import com.ai.application.agent.base.api.dto.AgentRunDTO;
import com.ai.application.agent.base.api.vo.AgentRunVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 智能体运行记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */

@Mapper(componentModel = "spring")
public interface AgentRunMapstruct {

    AgentRun toEntity(AgentRunDTO dto);
    List<AgentRun> toEntityList(List<AgentRunDTO> dtolist);
    AgentRunVO toVo(AgentRun entity);
    List<AgentRunVO> toVoList(List<AgentRun> entities);
}
