package com.ai.application.app.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 应用角色功能表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("app_role_function")
public class AppRoleFunction implements Serializable {
    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @TableId(type = IdType.AUTO)
    private Integer rfId;

    /**
     * 角色id
     */
    @Schema(description = "角色id")
    private Integer roleId;

    /**
     * 功能id
     */
    @Schema(description = "功能id")
    private Integer funId;

    /**
     * 状态 1有效 0无效
     */
    @Schema(description = "状态 1有效 0无效")
    private Integer rfStatus;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}