package com.ai.application.base.log.api.dto.query;

import com.ai.framework.core.vo.PageParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 错误日志表-查询条件
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Data
@Schema(name = "错误日志表QueryDTO")
public class ErrorLogQueryDTO extends PageParam {
    @Schema(description = "查询关键词")
    private String keywords;

    @Schema(description = "错误类型:10-运行错误,20-LLM错误,30-工具错误,40-知识库错误,50-系统错误")
    private Integer errorType;

    @Schema(description = "错误级别:10-致命,20-严重,30-警告,40-信息")
    private Integer errorLevel;

    @Schema(description = "创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
}