package com.ai.application.app.controller;

import com.ai.application.app.api.vo.AppRoleFunctionTreeVO;
import com.ai.application.app.api.vo.AppRoleListVO;
import com.github.pagehelper.PageInfo;
import com.ai.application.app.service.IAppFunctionService;
import com.ai.application.app.api.dto.AppFunctionDTO;
import com.ai.application.app.api.dto.query.AppFunctionQueryDTO;
import com.ai.application.app.api.vo.AppFunctionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Set;

/**
 * 应用功能表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Tag(name = "应用功能表", description = "应用功能表-相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/app/function")
public class AppFunctionController {

    @Autowired
    private IAppFunctionService  appFunctionService;

    /**
     * 分页查询
     *
     * @param queryDto
     * @return
     */
    @Operation(summary = "应用功能表-分页查询", description = "查询所有应用功能表 信息")
    @ApiResponse(responseCode = "0", description = "成功",
            content = @Content(schema = @Schema(implementation = AppFunctionVO.class)))
    @PostMapping("/page")
    public ResultVo<PageInfo<AppFunctionVO>> page(@Validated @RequestBody AppFunctionQueryDTO queryDto){
        return ResultVo.data(appFunctionService.page(queryDto));
    }

    @Operation(summary = "角色功能树形结构")
    @GetMapping("/tree")
    public ResultVo<List<AppRoleFunctionTreeVO>> queryRoleFunctionTree(){
        return ResultVo.data(appFunctionService.queryRoleFunctionTree());
    }


    /**
     * 保存
     *
     * @param dto
     * @return
     */
    @Operation(summary = "应用功能表-新增")
    @PostMapping("/save")
    public ResultVo<Void> save(@Validated @RequestBody AppFunctionDTO dto){
        appFunctionService.save(dto);
        return ResultVo.success("保存成功");
    }

    /**
     * 修改
     *
     * @param dto
     * @return
     */
    @Operation(summary = "应用功能表-修改")
    @PostMapping(value = "/update")
    public ResultVo<Void> update(@Validated @RequestBody AppFunctionDTO dto){
        appFunctionService.update(dto);
        return ResultVo.success("修改成功");
    }

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @Operation(summary = "应用功能表-查询详情", description = "根据ID应用功能表 信息")
    @ApiResponse(responseCode = "0", description = "成功",
            content = @Content(schema = @Schema(implementation = AppFunctionVO.class)))
    @GetMapping("/get")
    public ResultVo<AppFunctionVO> get(@RequestParam Long id){
        return ResultVo.data(appFunctionService.get(id));
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    @Operation(summary = "应用功能表-删除", description = "根据ID 应用功能表 信息")
    @RequestMapping(value = "/delete", method = {RequestMethod.GET})
    public ResultVo<String> delete(@RequestParam Set<Long> ids){
        appFunctionService.delete(ids);
        return ResultVo.success("删除成功");
    }
}