# -*- coding: utf-8 -*-
"""
向量编码服务模块

基于SentenceTransformer的文本向量化服务，提供：
- 中文文本向量编码（默认支持 thenlper/gte-base-zh 模型）
- 单文本和批量文本处理
- GPU/CPU自适应计算
- 向量相似度计算（余弦相似度）
- 异步并发处理和线程池管理
- 模型本地化部署支持

Project: Agent Memory Service
Author: 张小龙 <<EMAIL>>
Created: 2025-06-01
Version: 1.0.0
License: private
"""

import os
import asyncio
import multiprocessing
from typing import List, Union
from concurrent.futures import ThreadPoolExecutor
import numpy as np
from sentence_transformers import SentenceTransformer
from loguru import logger

from app.config.settings import settings

class EmbeddingService:
    """向量编码服务"""
    
    def __init__(self):
        self.model = None
        self.executor = None
        self._is_initialized = False
    
    def _get_optimal_workers(self) -> int:
        """
        根据硬件配置和设备类型计算最优线程数
        
        Returns:
            最优的worker线程数
        """
        cpu_count = multiprocessing.cpu_count()
        embedding_config = settings.embedding
        
        if embedding_config.device.lower() == 'cuda':
            # GPU模式：较少的线程，避免GPU资源争抢
            # 通常GPU可以高效处理批量数据，不需要太多并发线程
            optimal_workers = min(max(cpu_count // 2, 2), 6)
            logger.info(f"GPU模式，设置线程数: {optimal_workers} (基于CPU核心数: {cpu_count})")
        else:
            # CPU模式：更多线程以充分利用多核CPU
            # 设置为CPU核心数，但限制在合理范围内避免过度竞争
            optimal_workers = min(max(cpu_count, 4), 12)
            logger.info(f"CPU模式，设置线程数: {optimal_workers} (基于CPU核心数: {cpu_count})")
        
        return optimal_workers
    
    async def initialize(self):
        """异步初始化模型"""
        if self._is_initialized:
            return
        
        try:
            # 初始化线程池，使用动态计算的worker数量
            optimal_workers = self._get_optimal_workers()
            self.executor = ThreadPoolExecutor(max_workers=optimal_workers)
            
            embedding_config = settings.embedding
            logger.info(f"开始初始化向量编码模型: {embedding_config.model_name}")
            
            # 在线程池中初始化模型，避免阻塞主线程
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(self.executor, self._load_model)
            
            self._is_initialized = True
            logger.info("向量编码模型初始化完成")
            
        except Exception as e:
            logger.error(f"向量编码模型初始化失败: {e}")
            raise
    
    def _load_model(self):
        """加载模型（在线程池中执行）"""
        embedding_config = settings.embedding
        
        try:
            # 检查本地模型路径是否存在
            if os.path.exists(embedding_config.model_path):
                model_name_or_path = embedding_config.model_path
                logger.info(f"使用本地模型: {model_name_or_path}")
            else:
                model_name_or_path = embedding_config.model_name
                logger.info(f"使用在线模型: {model_name_or_path}")
            
            # 初始化SentenceTransformer
            self.model = SentenceTransformer(
                model_name_or_path,
                device=embedding_config.device
            )
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    async def encode_text(self, text: str) -> List[float]:
        """
        对单个文本进行向量编码
        
        Args:
            text: 输入文本
            
        Returns:
            文本向量
        """
        if not self._is_initialized:
            await self.initialize()
        
        try:
            # 在线程池中执行编码，避免阻塞主线程
            loop = asyncio.get_event_loop()
            vector = await loop.run_in_executor(
                self.executor, 
                self._encode_single_text, 
                text
            )
            return vector.tolist()
            
        except Exception as e:
            logger.error(f"文本向量编码失败: {e}")
            raise
    
    async def encode_texts(self, texts: List[str]) -> List[List[float]]:
        """
        对多个文本进行批量向量编码
        
        Args:
            texts: 输入文本列表
            
        Returns:
            文本向量列表
        """
        if not self._is_initialized:
            await self.initialize()
        
        try:
            # 在线程池中执行批量编码
            loop = asyncio.get_event_loop()
            vectors = await loop.run_in_executor(
                self.executor, 
                self._encode_batch_texts, 
                texts
            )
            return [vector.tolist() for vector in vectors]
            
        except Exception as e:
            logger.error(f"批量文本向量编码失败: {e}")
            raise
    
    def _encode_single_text(self, text: str) -> np.ndarray:
        """
        单个文本编码（在线程池中执行）
        
        Args:
            text: 输入文本
            
        Returns:
            文本向量
        """
        embedding_config = settings.embedding
        
        # 截断文本到最大长度
        if len(text) > embedding_config.max_length:
            text = text[:embedding_config.max_length]
        
        # 使用SentenceTransformer进行编码
        vector = self.model.encode(text, convert_to_numpy=True)
        return vector
    
    def _encode_batch_texts(self, texts: List[str]) -> List[np.ndarray]:
        """
        批量文本编码（在线程池中执行）
        
        Args:
            texts: 输入文本列表
            
        Returns:
            文本向量列表
        """
        embedding_config = settings.embedding
        
        # 截断文本到最大长度
        processed_texts = []
        for text in texts:
            if len(text) > embedding_config.max_length:
                text = text[:embedding_config.max_length]
            processed_texts.append(text)
        
        # 使用SentenceTransformer进行批量编码
        vectors = self.model.encode(processed_texts, convert_to_numpy=True)
        return vectors
    
    def calculate_cosine_similarity(self, vector1: List[float], vector2: List[float]) -> float:
        """
        计算两个向量的余弦相似度
        
        Args:
            vector1: 向量1
            vector2: 向量2
            
        Returns:
            余弦相似度值
        """
        try:
            # 转换为numpy数组
            v1 = np.array(vector1)
            v2 = np.array(vector2)
            
            # 计算余弦相似度
            dot_product = np.dot(v1, v2)
            norm1 = np.linalg.norm(v1)
            norm2 = np.linalg.norm(v2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
            
        except Exception as e:
            logger.error(f"计算余弦相似度失败: {e}")
            return 0.0
    
    def is_initialized(self) -> bool:
        """检查模型是否已初始化"""
        return self._is_initialized
    
    def get_worker_count(self) -> int:
        """获取当前线程池的worker数量"""
        if self.executor:
            return self.executor._max_workers
        return 0
    
    async def close(self):
        """关闭服务"""
        try:
            if self.executor:
                self.executor.shutdown(wait=True)
            self.model = None
            self._is_initialized = False
            logger.info("向量编码服务已关闭")
        except Exception as e:
            logger.error(f"关闭向量编码服务失败: {e}")


# 全局向量编码服务实例
embedding_service = EmbeddingService() 