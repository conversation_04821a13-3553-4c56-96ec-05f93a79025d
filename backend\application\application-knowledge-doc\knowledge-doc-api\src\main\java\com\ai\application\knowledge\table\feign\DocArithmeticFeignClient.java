package com.ai.application.knowledge.table.feign;

import com.ai.application.knowledge.table.dto.*;
import com.ai.application.knowledge.table.vo.*;
import com.ai.framework.core.vo.ResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "DocArithmeticFeignClient", url = "http://192.168.7.55:29222/")
//@FeignClient(value = "knowledge-retrieval-service", path = "/SmartTable/v1/", contextId = "TableFeignClient")
public interface DocArithmeticFeignClient {

    /**
     * embedding
     */
    @PostMapping("indexing")
    ResultVo<EmbeddingVo> indexing(@RequestBody EmbeddingDto dto);

    /**
     * 批量删除文件
     */
    @PostMapping("delete_files")
    ResultVo<FileDeleteEmbeddingVo> delete_files(@RequestBody FileDeleteEmbeddingDto dto);

    /**
     * 批量删除知识库
     */
    @PostMapping("delete_collections")
    ResultVo<BaseDeleteEmbeddingVo> delete_collections(@RequestBody BaseDeleteEmbeddingDto dto);

    /**
     * 知识库查询
     */
    @PostMapping("query")
    ResultVo<FileQueryEmbeddingVo> query(@RequestBody FileQueryEmbeddingDto dto);

    /**
     * 学习状态查询
     */
    @PostMapping("get_status")
    ResultVo<FileStatusEmbeddingVo> get_status(@RequestBody FileStatusEmbeddingDto dto);

    /**
     * chunk编辑
     */
    @PostMapping("edit_chunk")
    ResultVo<FileEditEmbeddingVo> edit_chunk(@RequestBody FileEditEmbeddingDto dto);

    /**
     * 获取文件的chunks
     */
    @PostMapping("get_chunks")
    ResultVo<FileChunksEmbeddingVo> get_chunks(@RequestBody FileChunksEmbeddingDto dto);

    /**
     * 创建知识库
     */
    @PostMapping("/api/kb/create")
    Object kbCreate(@RequestBody CreateBaseEmbeddingDto dto);

    /**
     * 知识库列表
     */
    @PostMapping("/api/kb/list")
    ListBaseEmbeddingVo kbList(@RequestBody ListBaseEmbeddingDto dto);

    /**
     * 删除知识库
     */
    @PostMapping("/api/kb/del")
    Object kbDel(@RequestBody DeleteBaseEmbeddingDto dto);


}
