package com.ai.application.knowledge.table.service;

import com.ai.application.knowledge.table.dto.AgentByTableDto;
import com.ai.application.knowledge.table.dto.TableCreateDto;
import com.ai.application.knowledge.table.dto.TableListDto;
import com.ai.application.knowledge.table.entity.KnowledgeTable;
import com.ai.application.knowledge.table.vo.AgentByTableVo;
import com.ai.application.knowledge.table.vo.TableDetailVo;
import com.ai.application.knowledge.table.vo.TableListVo;
import com.ai.framework.core.vo.ResultVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 智能表格表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public interface IKnowledgeTableService extends IService<KnowledgeTable> {


    /**
     * 智能表格列表
     */
    ResultVo<PageInfo<TableListVo>> list(TableListDto dto);

    /**
     * 智能表格详情
     */
    ResultVo<TableDetailVo> detail(String tableSn);

    /**
     * 智能表格详情
     */
    ResultVo<TableDetailVo> detailById(Integer tableId);

    /**
     * 新增智能表格
     */
    ResultVo<String> create(TableCreateDto dto);

    /**
     * 更新智能表格
     */
    ResultVo<String> update(TableCreateDto dto);

    /**
     * 删除智能表格
     */
    ResultVo<String> delete(String tableSn);

    /**
     * 根据智能表格编号查询智能表格
     */
    KnowledgeTable checkTable(String tableSn);

    /**
     * 根据智能表格编号查询引用的应用
     */
    ResultVo<List<AgentByTableVo>> agentByBb(AgentByTableDto dto);

}
