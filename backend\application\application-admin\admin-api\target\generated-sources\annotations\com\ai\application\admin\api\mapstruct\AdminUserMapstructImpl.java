package com.ai.application.admin.api.mapstruct;

import com.ai.application.admin.api.dto.AdminUserCreateDTO;
import com.ai.application.admin.api.entity.AdminUser;
import com.ai.application.admin.api.vo.AdminUserDetailVO;
import com.ai.application.admin.api.vo.AdminUserVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-13T10:32:30+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AdminUserMapstructImpl implements AdminUserMapstruct {

    @Override
    public List<AdminUserVO> toVoList(List<AdminUser> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AdminUserVO> list = new ArrayList<AdminUserVO>( entities.size() );
        for ( AdminUser adminUser : entities ) {
            list.add( adminUserToAdminUserVO( adminUser ) );
        }

        return list;
    }

    @Override
    public AdminUser toEntity(AdminUserCreateDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AdminUser adminUser = new AdminUser();

        adminUser.setUserAccount( dto.getUserAccount() );
        adminUser.setUserName( dto.getUserName() );

        return adminUser;
    }

    @Override
    public AdminUserDetailVO toDetailVo(AdminUser entity) {
        if ( entity == null ) {
            return null;
        }

        AdminUserDetailVO adminUserDetailVO = new AdminUserDetailVO();

        adminUserDetailVO.setUserSn( entity.getUserSn() );
        adminUserDetailVO.setUserAccount( entity.getUserAccount() );
        adminUserDetailVO.setUserName( entity.getUserName() );
        adminUserDetailVO.setUserStatus( entity.getUserStatus() );

        return adminUserDetailVO;
    }

    protected AdminUserVO adminUserToAdminUserVO(AdminUser adminUser) {
        if ( adminUser == null ) {
            return null;
        }

        AdminUserVO adminUserVO = new AdminUserVO();

        adminUserVO.setUserId( adminUser.getUserId() );
        adminUserVO.setUserSn( adminUser.getUserSn() );
        adminUserVO.setUserAccount( adminUser.getUserAccount() );
        adminUserVO.setUserName( adminUser.getUserName() );
        adminUserVO.setUserPassword( adminUser.getUserPassword() );
        adminUserVO.setUserMobile( adminUser.getUserMobile() );
        adminUserVO.setUserEmail( adminUser.getUserEmail() );
        adminUserVO.setUserAvatar( adminUser.getUserAvatar() );
        adminUserVO.setUserStaffSn( adminUser.getUserStaffSn() );
        adminUserVO.setUserStatus( adminUser.getUserStatus() );
        adminUserVO.setAppId( adminUser.getAppId() );
        adminUserVO.setRoleId( adminUser.getRoleId() );
        adminUserVO.setTenantId( adminUser.getTenantId() );
        adminUserVO.setDeptId( adminUser.getDeptId() );
        adminUserVO.setCreateTime( adminUser.getCreateTime() );
        adminUserVO.setUpdateTime( adminUser.getUpdateTime() );

        return adminUserVO;
    }
}
