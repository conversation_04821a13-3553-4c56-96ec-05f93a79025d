package com.ai.application.agent.base.api.vo;

import com.ai.application.agent.base.api.bo.AgentMetadataBO;
import com.ai.application.agent.base.api.dto.MasterAddDTO;
import com.ai.application.agent.base.api.dto.WorkFlowAddDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;
import java.util.List;

@Schema(name = "智体能详情VO")
@Data
public class AgentDetailVO {
    /**
     * 智能体sn
     */
    @Schema(description = "智能体sn")
    private String agentSn;

    /**
     * 智能体名称
     */
    @Schema(description = "智能体名称")
    private String agentName;

    /**
     * 智能体描述
     */
    @Schema(description = "智能体描述")
    private String agentDesc;

    /**
     * 智能体类型: 10:对话流, 20:工作流, 30:master
     */
    @Schema(description = "智能体类型: 10:对话流, 20:工作流, 30:master")
    private Integer agentType;

    /**
     * 智能体元信息:logo,icon,创建人名等
     */
    @Schema(description = "智能体元信息:logo,icon,创建人名等")
    private AgentMetadataBO agentMetadata;

    /**
     * 状态 0停用 1开发 5发布
     */
    @Schema(description = "状态 0停用 1开发 5发布")
    private Integer agentStatus;

    /**
     * 智能体启用版本id
     */
    @Schema(description = "智能体启用版本")
    private String versionSn;

    /**
     * 创建人id
     */
    @Schema(description = "创建人id")
    private String createBy;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Schema(description = "使用说明")
    private String explain;

    /**
     * 推荐问题 列表
     */
    @Schema(description = "推荐问题列表")
    private List<String> recommends;

    @Schema(description = "记忆开关 0关闭 1开启")
    private String memorySwitch = "0";

    @Schema(description = "市场发布状态 1发布 0未发布")
    private Integer versionOnSale;

    @Schema(description = "智能规划信息")
    private MasterAddDTO master;

    @Schema(description = "工作流信息")
    private WorkFlowAddDTO workFlow;

    @Schema(description = "引导")
    private Guide guide;

    @Data
    public static class Guide {
        @Schema(description = "能力")
        private List<String> capacities;

        @Schema(description = "关联技能")
        private List<GuideSkill> skill;

        @Schema(description = "关联知识库")
        private List<GuideKnowledge> knowledge;
    }

    @Data
    public static class GuideSkill {
        @Schema(description = "技能名称")
        private String name;

        @Schema(description = "技能类型")
        private Integer type;
    }

    @Data
    public static class GuideKnowledge {
        @Schema(description = "知识库名称")
        private String name;
    }

}
