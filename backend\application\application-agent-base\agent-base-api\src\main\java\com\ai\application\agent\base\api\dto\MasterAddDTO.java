package com.ai.application.agent.base.api.dto;

import com.ai.application.agent.base.api.bo.MasterDictBO;
import com.ai.application.agent.base.api.bo.MasterModelBO;
import com.ai.application.agent.base.api.bo.MasterPromptVarBO;
import com.ai.application.agent.base.api.bo.MasterSkillBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Schema(name = "MasterAddDTO")
@Data
public class MasterAddDTO {
    @Schema(description = "Agent编码")
    private String agentSn;

    @Schema(description = "版本名称")
    private String versionNumber;

    @Schema(description = "版本状态")
    private Integer versionStatus;

    @Schema(description = "模型相关")
    public MasterModelBO model;

    @Schema(description = "知识库编码")
    public List<Integer> knowledgeIds;

    @Schema(description = "技能")
    public List<MasterSkillBO> skills;

    @Schema(description = "prompt变量")
    public List<MasterPromptVarBO> promptVars;

    @Schema(description = "敏感词")
    public List<MasterDictBO> sensitives;

    @Schema(description = "词库")
    public List<MasterDictBO> dicts;
}
