package com.ai.application.tenant.audit.api.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import java.sql.Timestamp;
import java.util.Date;

/**
 * 租户审核表
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@Schema(name = "租户审核表DTO")
public class TenantAuditDTO {
    /**
     * 审核id
     */


    /**
     * 审核单号
     */
    @Schema(description = "审核单号")
    private String auditSn;

    @Schema(description = "审核状态:1-待审核,2-审核中,3-审核通过,4-审核拒绝,0-已撤销")
    private Integer auditStatus;
}