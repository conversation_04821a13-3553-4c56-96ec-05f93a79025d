package com.ai.application.knowledge.table.service.impl;

import com.ai.application.agent.base.api.feign.IAgentUseClient;
import com.ai.application.app.api.dto.query.AppUserQueryDTO;
import com.ai.application.app.api.feign.IAppUserClient;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.application.base.model.api.feign.IModelClient;
import com.ai.application.base.model.api.vo.ModelInfoApiVO;
import com.ai.application.knowledge.table.dto.*;
import com.ai.application.knowledge.table.entity.KnowledgeBase;
import com.ai.application.knowledge.table.entity.KnowledgeExtend;
import com.ai.application.knowledge.table.enums.KbStatusEnum;
import com.ai.application.knowledge.table.errors.AgentDocError;
import com.ai.application.knowledge.table.feign.DocArithmeticFeignClient;
import com.ai.application.knowledge.table.mapper.KnowledgeBaseMapper;
import com.ai.application.knowledge.table.service.IKnowledgeBaseService;
import com.ai.application.knowledge.table.service.IKnowledgeExtendService;
import com.ai.application.knowledge.table.vo.AgentByBbVo;
import com.ai.application.knowledge.table.vo.BaseDetailVo;
import com.ai.application.knowledge.table.vo.BaseListVo;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.vo.ResultVo;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 知识库表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
@Slf4j
public class KnowledgeBaseServiceImpl extends ServiceImpl<KnowledgeBaseMapper, KnowledgeBase> implements IKnowledgeBaseService {

    @Resource
    private IKnowledgeExtendService KnowledgeExtendService;

    @Resource
    private IAppUserClient userClient;

    @Resource
    private IModelClient modelClient;

    @Resource
    private DocArithmeticFeignClient docArithmeticFeignClient;

    @Resource
    private IAgentUseClient agentUseClient;

    @Override
    public ResultVo<PageInfo<BaseListVo>> list(BaseListDto dto) {
        dto.setTenantId(UserContext.getTenantId());
        dto.setUserId(UserContext.getUserId());

        //查询满足姓名模糊查询的用户
        likeNameQuery(dto);

        //权限处理
        //permissionHandler(dto);

        // 设置分页参数
        PageHelper.startPage(dto.getPageNo(), dto.getPageSize());

        // 查询数据源列表
        List<BaseListVo> result = this.getBaseMapper().list(dto);

        // 获取所有的创建人
        List<Integer> creators = result.stream().map(BaseListVo::getCreateUserId).distinct().toList();

        //获取创建人名字
        AppUserQueryDTO appUserQuery = new AppUserQueryDTO();
        appUserQuery.setUserIds(creators);
        log.info("查询满足姓名模糊查询的用户 入参：{}", JSON.toJSONString(appUserQuery));
        List<AppUserVO> list = userClient.list(appUserQuery).getData();
        log.info("查询满足姓名模糊查询的用户 出参：{}", JSON.toJSONString((list)));

        // 将查询到的词数据转换为Map，便于快速查找
        Map<Integer, AppUserVO> wordMap = list.stream().collect(Collectors.toMap(AppUserVO::getUserId, vo -> vo, (o1, o2) -> o1));

        //名字赋值 启动智能体数量赋值
        result.forEach(f -> {
            f.setCreator(wordMap.get(f.getCreateUserId()).getUserName());
            //TODO 智能体数量处理
//            f.setKbLaunchAgents(0);
        });

        //TODO 智能体数量处理

        // 返回分页结果
        return ResultVo.data(new PageInfo<>(result));
    }

    @Override
    public List<BaseListVo> allList(BaseListDto dto) {
        dto.setTenantId(UserContext.getTenantId());
        dto.setUserId(UserContext.getUserId());

        //查询满足姓名模糊查询的用户
        likeNameQuery(dto);

        //权限处理
        permissionHandler(dto);

        // 查询数据源列表
        List<BaseListVo> result = this.getBaseMapper().list(dto);

        // 获取所有的创建人
        List<Integer> creators = result.stream().map(BaseListVo::getCreateUserId).distinct().toList();

        //获取创建人名字
        AppUserQueryDTO appUserQuery = new AppUserQueryDTO();
        appUserQuery.setUserIds(creators);
        log.info("查询满足姓名模糊查询的用户 入参：{}", JSON.toJSONString(appUserQuery));
        List<AppUserVO> list = userClient.list(appUserQuery).getData();
        log.info("查询满足姓名模糊查询的用户 出参：{}", JSON.toJSONString((list)));

        // 将查询到的词数据转换为Map，便于快速查找
        Map<Integer, AppUserVO> wordMap = list.stream().collect(Collectors.toMap(AppUserVO::getUserId, vo -> vo, (o1, o2) -> o1));

        //名字赋值
        result.forEach(f -> f.setCreator(wordMap.get(f.getCreateUserId()).getUserName()));

        //TODO 关联的应用数 (启动)


        // 返回结果
        return result;
    }

    @Override
    public ResultVo<BaseDetailVo> detail(String kbSn) {
        BaseDetailVo baseDetailVo = new BaseDetailVo();
        KnowledgeBase base = checkBase(kbSn);
        BeanUtils.copyProperties(base, baseDetailVo);
        //分段规则处理
        BaseDetailParserDto detailParser = KnowledgeExtendService.parserByKb(base.getKbId());
        BeanUtils.copyProperties(detailParser, baseDetailVo);
        //时间处理
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        baseDetailVo.setCreateTime(sdf.format(base.getCreateTime()));
        baseDetailVo.setUpdateTime(sdf.format(base.getUpdateTime()));
        //模型名称处理
        log.info("模型获取详情FEIGN调用 入参:{}", JSON.toJSONString(detailParser));
        ModelInfoApiVO modelInfoVO = modelClient.getModelBySn(detailParser.getModelSn()).getData();
        log.info("模型获取详情FEIGN调用 出参:{}", JSON.toJSONString(modelInfoVO));
        baseDetailVo.setModelName(modelInfoVO.getModelName());
        //TODO 查询agent使用信息
//        detailVo.enableAgents(getAgentInfo(enableAgents));
//        detailVo.disableAgents(getAgentInfo(disableAgent));
        //创建人 更新人
        AppUserVO createUser = userClient.getUserById(base.getCreateUserId()).getData();
        baseDetailVo.setCreator(createUser.getUserName());
        AppUserVO updateUser = userClient.getUserById(base.getUpdateUserId()).getData();
        baseDetailVo.setUpdater(updateUser.getUserName());
        // 返回查询结果`
        return ResultVo.data(baseDetailVo);
    }


    @Override
    public ResultVo<BaseDetailVo> detailById(Integer kbId) {
        BaseDetailVo baseDetailVo = new BaseDetailVo();
        LambdaQueryWrapper<KnowledgeBase> query = Wrappers.lambdaQuery(KnowledgeBase.class).eq(KnowledgeBase::getKbId, kbId).eq(KnowledgeBase::getKbStatus, KbStatusEnum.VALID.getCode());
        KnowledgeBase existingBase = baseMapper.selectOne(query);
        if (ObjectUtils.isEmpty(existingBase)) {
            throw new ServiceException(AgentDocError.KNOWLEDGE_BASE_NOT_FOUND.getCode(), AgentDocError.KNOWLEDGE_BASE_NOT_FOUND.getMessage());
        }
        BeanUtils.copyProperties(existingBase, baseDetailVo);
        // 返回查询结果`
        return ResultVo.data(baseDetailVo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultVo<String> create(BaseCreateDto dto) {
        // 租户赋值
        dto.setTenantId(UserContext.getTenantId());

        KnowledgeBase base = new KnowledgeBase();

        // 校验模型是否可用
        checkModel(dto.getModelSn());

        // 校验名称是否重复
        checkName(dto.getKbName(), dto.getKbSn());

        // 属性拷贝
        BeanUtils.copyProperties(dto, base);

        // 构造参数
        buildBase(base);

        //知识库保存
        getBaseMapper().insert(base);

        // 构造分段规则编辑
        saveParser(dto, base);

        // TODO 同步算法
//        CreateBaseEmbeddingDto createBaseEmbeddingDto = new CreateBaseEmbeddingDto();
//        createBaseEmbeddingDto.setKb_sn(base.getKbSn());
//        createBaseEmbeddingDto.setUser_id("ragflow-kb-16257e6978814101a61ca74");
//        log.info("同步算法创建知识库 入参：{}", JSON.toJSONString(createBaseEmbeddingDto));
//        Object o = docArithmeticFeignClient.kbCreate(createBaseEmbeddingDto);
//        log.info("同步算法创建知识库 出参：{}", JSON.toJSONString(o));

        //TODO 授权

        return ResultVo.data(base.getKbSn());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultVo<String> update(BaseCreateDto dto) {
        // 校验知识库是否存在
        KnowledgeBase existingBase = checkBase(dto.getKbSn());

        //TODO 校验知识库是否被agent使用(启用状态)

        // 校验名称是否重复
        checkName(dto.getKbName(), dto.getKbSn());

        // 校验模型是否可用
        checkModel(dto.getModelSn());

        //获取分段规则
        BaseDetailParserDto parser = KnowledgeExtendService.parserByKb(existingBase.getKbId());

        // 判断分段规则是否发生变化
        boolean flag = isConfigChanged(parser, dto);

        // 属性拷贝
        rebuildBase(existingBase, dto);

        // 更新固定字段（如最后修改用户、时间等）
        existingBase.setUpdateTime(new Date());
        existingBase.setUpdateUserId(UserContext.getUserId());

        // 执行更新操作
        baseMapper.updateById(existingBase);

        //修改模型或者分段配置会触发算法重新embedding
        if (flag) {
            retryEmbedding();
        }

        return ResultVo.data(existingBase.getKbSn());

    }

    @Override
    public ResultVo<String> delete(String kbSn) {

        // 校验知识库是否存在
        KnowledgeBase existingBase = checkBase(kbSn);

        //TODO 已关联启用agent无法删除
//        AgentUseKnowledgeDocQueryDTO dto = new AgentUseKnowledgeDocQueryDTO();
//        dto.setDocId(String.valueOf(existingBase.getKbId()));
//        PageInfo<AgentUseKnowledgeDocQueryVO> voPageInfo = agentUseClient.selectUseDocByPage(dto).getData();
//        if (voPageInfo.getTotal() > 0) {
//            throw new ServiceException(AgentDocError.KNOWLEDGE_BASE_RELATIVE_AGENT.getCode(), AgentDocError.KNOWLEDGE_BASE_RELATIVE_AGENT.getMessage());
//        }

        // 删除知识库
        existingBase.setKbStatus(KbStatusEnum.INVALID.getCode());
        updateById(existingBase);

        // 删除分段规则
        KnowledgeExtendService.update(Wrappers.lambdaUpdate(KnowledgeExtend.class).set(KnowledgeExtend::getItemStatus, KbStatusEnum.INVALID.getCode()).eq(KnowledgeExtend::getKbId, existingBase.getKbId()));

        // TODO 同步算法
//        DeleteBaseEmbeddingDto deleteBaseEmbeddingDto = new DeleteBaseEmbeddingDto();
//        deleteBaseEmbeddingDto.setKb_sn(existingBase.getKbSn());
//        deleteBaseEmbeddingDto.setUser_id("ragflow-kb-16257e6978814101a61ca74");
//        log.info("同步算法删除知识库 入参：{}", JSON.toJSONString(deleteBaseEmbeddingDto));
//        Object o = docArithmeticFeignClient.kbDel(deleteBaseEmbeddingDto);
//        log.info("同步算法删除知识库 出参：{}", JSON.toJSONString(o));

        return ResultVo.data("操作成功");
    }

    private void checkName(String kbName, String kbSn) {
        LambdaQueryWrapper<KnowledgeBase> nameQuery = new LambdaQueryWrapper<>();
        nameQuery.eq(KnowledgeBase::getKbName, kbName).eq(KnowledgeBase::getTenantId, UserContext.getTenantId()).eq(KnowledgeBase::getKbStatus, KbStatusEnum.VALID.getCode()).last("limit 1");
        if (StringUtils.isNotBlank(kbSn)) {
            nameQuery.ne(KnowledgeBase::getKbSn, kbSn);
        }
        if (ObjectUtils.isNotEmpty(baseMapper.selectOne(nameQuery))) {
            throw new ServiceException(AgentDocError.KNOWLEDGE_BASE_NAME_HAS_EXISTS.getCode(), AgentDocError.KNOWLEDGE_BASE_NAME_HAS_EXISTS.getMessage());
        }
    }

    private void checkModel(String modelSn) {
        log.info("模型是否可用FEIGN调用 入参: {}", JSON.toJSONString(modelSn));
        ModelInfoApiVO modelInfoVO = modelClient.getEnableModelBySn(modelSn).getData();
        log.info("模型是否可用FEIGN调用 出参: {}", JSON.toJSONString(modelInfoVO));
        if (ObjectUtils.isEmpty(modelInfoVO)) {
            throw new ServiceException(AgentDocError.MODEL_NOT_EXISTS.getCode(), AgentDocError.MODEL_NOT_EXISTS.getMessage());
        }
    }

    public KnowledgeBase checkBase(String kbSn) {
        LambdaQueryWrapper<KnowledgeBase> query = Wrappers.lambdaQuery(KnowledgeBase.class).eq(KnowledgeBase::getKbSn, kbSn).eq(KnowledgeBase::getTenantId, UserContext.getTenantId()).eq(KnowledgeBase::getKbStatus, KbStatusEnum.VALID.getCode());
        KnowledgeBase existingBase = baseMapper.selectOne(query);
        if (ObjectUtils.isEmpty(existingBase)) {
            throw new ServiceException(AgentDocError.KNOWLEDGE_BASE_NOT_FOUND.getCode(), AgentDocError.KNOWLEDGE_BASE_NOT_FOUND.getMessage());
        }
        return existingBase;
    }

    @Override
    public ResultVo<List<AgentByBbVo>> agentByBb(AgentByBbDto dto) {
//        FindAgentByUseDTO findAgentByUseDTO = new FindAgentByUseDTO();
//        findAgentByUseDTO.setUseType(40);
//        log.info("获取知识库关联的应用 入参：{}", JSON.toJSONString(findAgentByUseDTO));
//
//        List<FindAgentByUseVO> agentData = agentUseClient.findAgentByUse(findAgentByUseDTO).getData();
//
//        List<AgentByBbVo> result = agentData.stream()
//            .map(f -> {
//                AgentByBbVo vo = new AgentByBbVo();
//                BeanUtils.copyProperties(vo, f);
//                return vo;
//            })
//            .filter(f -> {
//                if (dto.getStatus() == null) return true;
//                if (dto.getStatus().equals(AgentStatusEnum.VALID.getCode())) {
//                    return VersionStatusEnum.ENABLE.equals(f.getVersionStatusEnum());
//                } else if (dto.getStatus().equals(AgentStatusEnum.INVALID.getCode())) {
//                    return VersionStatusEnum.STOP.equals(f.getVersionStatusEnum());
//                }
//                return true;
//            })
//            .collect(Collectors.toList());
//
//        log.info("获取知识库关联的应用 出参：{}", JSON.toJSONString(result));
        return ResultVo.data(null);
    }

    private void buildBase(KnowledgeBase base) {
        base.setKbSn(UUIDUtil.genRandomSn("kb"));
        base.setTenantId(UserContext.getTenantId());
        base.setCreateUserId(UserContext.getUserId());
        base.setUpdateUserId(UserContext.getUserId());
        base.setKbStatus(KbStatusEnum.VALID.getCode());
    }

    private void saveParser(BaseCreateDto dto, KnowledgeBase base) {
        //分段规则处理
        List<BaseBuildParserDto> list = new ArrayList<>();
        BaseBuildParserDto modelSnd = new BaseBuildParserDto();
        modelSnd.setItemName("modelSn").setItemValue(dto.getModelSn()).setItemStatus(KbStatusEnum.VALID.getCode()).setKbId(base.getKbId());
        list.add(modelSnd);
        if (ObjectUtils.isNotEmpty(dto.getSeparatorContent())) {
            BaseBuildParserDto separatorContent = new BaseBuildParserDto();
            separatorContent.setItemName("separatorContent").setItemValue(JSON.toJSONString(dto.getSeparatorContent())).setItemStatus(KbStatusEnum.VALID.getCode()).setKbId(base.getKbId());
            list.add(separatorContent);
        }
        if (ObjectUtils.isNotEmpty(dto.getSplitRule())) {
            BaseBuildParserDto splitRule = new BaseBuildParserDto();
            splitRule.setItemName("splitRule").setItemValue(dto.getSplitRule()).setItemStatus(KbStatusEnum.VALID.getCode()).setKbId(base.getKbId());
            list.add(splitRule);
        }
        if (ObjectUtils.isNotEmpty(dto.getSplitter())) {
            BaseBuildParserDto splitter = new BaseBuildParserDto();
            splitter.setItemName("splitter").setItemValue(dto.getSplitter()).setItemStatus(KbStatusEnum.VALID.getCode()).setKbId(base.getKbId());
            list.add(splitter);
        }
        if (ObjectUtils.isNotEmpty(dto.getWordCountLimit())) {
            BaseBuildParserDto wordCountLimit = new BaseBuildParserDto();
            wordCountLimit.setItemName("wordCountLimit").setItemValue(dto.getWordCountLimit()).setItemStatus(KbStatusEnum.VALID.getCode()).setKbId(base.getKbId());
            list.add(wordCountLimit);
        }
        if (ObjectUtils.isNotEmpty(dto.getWordCountOverlap())) {
            BaseBuildParserDto wordCountOverlap = new BaseBuildParserDto();
            wordCountOverlap.setItemName("wordCountOverlap").setItemValue(dto.getWordCountOverlap()).setItemStatus(KbStatusEnum.VALID.getCode()).setKbId(base.getKbId());
            list.add(wordCountOverlap);
        }
        KnowledgeExtendService.buildParser(list);
    }


    private boolean isConfigChanged(BaseDetailParserDto old, BaseCreateDto dto) {
        return (!Objects.equals(old.getModelSn(), dto.getModelSn()) && ObjectUtils.isNotEmpty(dto.getModelSn())) || (!Objects.equals(old.getSplitRule(), dto.getSplitRule()) && ObjectUtils.isNotEmpty(dto.getSplitRule())) || (!Objects.equals(old.getSplitter(), dto.getSplitter()) && ObjectUtils.isNotEmpty(dto.getSplitter())) || (!Objects.equals(old.getWordCountLimit(), dto.getWordCountLimit()) && ObjectUtils.isNotEmpty(dto.getWordCountLimit())) || (!Objects.equals(old.getWordCountOverlap(), dto.getWordCountOverlap()) && ObjectUtils.isNotEmpty(dto.getWordCountOverlap()));
    }

    public void rebuildBase(KnowledgeBase existingBase, BaseCreateDto dto) {
        if (StringUtils.isNotBlank(dto.getKbName())) {
            existingBase.setKbName(dto.getKbName());
        }
        if (StringUtils.isNotBlank(dto.getKbDesc())) {
            existingBase.setKbDesc(dto.getKbDesc());
        }
        if (ObjectUtils.isNotEmpty(dto.getKbLogo())) {
            existingBase.setKbLogo(dto.getKbLogo());
        }
    }


    public void retryEmbedding() {
//        ReEmbeddingDto dto = new ReEmbeddingDto();
//        BatchEmbeddingDto.EmbeddingConfig config = new BatchEmbeddingDto.EmbeddingConfig();
//
//        config.setSplitRule(inventoryDto.getSplitRule());
//        config.setSplitter(inventoryDto.getSplitter());
//        config.setWordCountLimit(inventoryDto.getWordCountLimit());
//        config.setWordCountOverlap(inventoryDto.getWordCountOverlap());
//        if(Objects.equals(inventoryDto.getSplitter(),3)){
//            config.setSeparators(inventoryDto.getSeparatorContent());
//        }
//
//
//        dto.setDatasetId(old.getInventorySn());
//        dto.setModelSn(inventoryDto.getModelSn());
//        dto.setEmbeddingConfig(config);
//        dto.setEnableSummary(enableSummary);
//
//        try {
//            skillFileClient.reEmbedding(dto);
//            skillFileClient.resetDataset(dto);
//        }catch (Exception e){
//            log.error("调用文件服务异常", e);
//            throw new ServiceException(ErrorCodeEnum.LOCKED);
//        }
    }

    public void permissionHandler(BaseListDto dto) {
    }

    public void likeNameQuery(BaseListDto dto) {
        if (ObjectUtils.isNotEmpty(dto.getKeyword())) {
            AppUserQueryDTO appUserQuery = new AppUserQueryDTO();
            appUserQuery.setUserName(dto.getKeyword());
            log.info("查询满足姓名模糊查询的用户 入参：{}", JSON.toJSONString((appUserQuery)));
            List<AppUserVO> list = userClient.list(appUserQuery).getData();
            log.info("查询满足姓名模糊查询的用户 出参：{}", JSON.toJSONString(list));
            dto.setUserIdsByName(list.stream().map(AppUserVO::getUserId).toList());
        }
    }

}
