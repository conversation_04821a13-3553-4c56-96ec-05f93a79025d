<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.ai</groupId>
    <artifactId>backend</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>${project.artifactId}</name>
    <description>aiCloud</description>

    <modules>
        <module>framework</module>
        <module>application</module>
        <module>develop</module>
    </modules>

    <properties>
        <revision>0.0.1</revision>

        <!-- java -->
        <java.version>17</java.version>
        <maven.compiler.target>17</maven.compiler.target>
        <maven.compiler.source>17</maven.compiler.source>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <javax.servlet.version>4.0.1</javax.servlet.version>
        <jakarta.el.version>4.0.1</jakarta.el.version>
        <tomcat-servlet.version>10.0.27</tomcat-servlet.version>
        <validation-api.version>2.0.1.Final</validation-api.version>

        <!-- maven -->
        <maven.plugin.version>3.8.1</maven.plugin.version>
        <flatten.maven.plugin.version>1.2.7</flatten.maven.plugin.version>
        <spring.maven.version>2.5.4</spring.maven.version>

        <!-- Spring -->
        <spring-boot.version>3.3.4</spring-boot.version>
        <spring.test.version>6.0.9</spring.test.version>

        <!-- spring cloud-->
        <spring-cloud.version>2023.0.1</spring-cloud.version>

        <!-- alibaba spring cloud-->
        <spring-cloud-alibaba.version>2023.0.1.0</spring-cloud-alibaba.version>
        <nacos.client.version>3.0.0</nacos.client.version>
        <feign-reactor.version>4.2.1</feign-reactor.version>

        <!-- 中间件 -->
        <redisson.version>3.45.1</redisson.version>
        <lock4j-redisson.version>2.2.3</lock4j-redisson.version>
        <aws-java-sdk-s3.version>1.12.400</aws-java-sdk-s3.version>
        <springdoc.webmvc.version>2.1.0</springdoc.webmvc.version>
        <knife4j.springdoc.ui.version>3.0.3</knife4j.springdoc.ui.version>
        <jjwt.version>0.9.1</jjwt.version>
        <oceanbase.version>2.4.0</oceanbase.version>

        <!-- mysql -->
        <mybatis-plus.version>3.5.5</mybatis-plus.version>
        <dynamic-ds.version>4.1.2</dynamic-ds.version>
        <druid.version>1.2.8</druid.version>
        <pagehelper.version>1.4.6</pagehelper.version>
        <mybatis.version>3.5.16</mybatis.version>

        <!-- 工具 -->
        <lombok.version>1.18.30</lombok.version>
        <elasticsearch.version>7.17.3</elasticsearch.version>
        <hutool-all.version>5.8.35</hutool-all.version>
        <guava.version>31.1-jre</guava.version>
        <fastjson.version>2.0.26</fastjson.version>
        <feign-okhttp.version>10.10.1</feign-okhttp.version>
        <retrofit.version>2.9.0</retrofit.version>
        <jaxb.version>2.3.0</jaxb.version>
        <easyexcel.verion>3.3.1</easyexcel.verion>
        <truelicense.version>1.33</truelicense.version>
        <junit.version>4.13.1</junit.version>
        <toolkit-trace.version>8.7.0</toolkit-trace.version>
        <sonar.coverage.jacoco.xmlReportPaths>${basedir}/target/site/jacoco/jacoco.xml</sonar.coverage.jacoco.xmlReportPaths>
        <knife4j.version>4.5.0</knife4j.version>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
        <jose4j.version>0.7.0</jose4j.version>

        <!-- 应用模块 -->
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- java -->
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>${javax.servlet.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/junit/junit -->
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.tomcat</groupId>
                <artifactId>tomcat-servlet-api</artifactId>
                <version>${tomcat-servlet.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation-api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.glassfish</groupId>
                <artifactId>jakarta.el</artifactId>
                <version>${jakarta.el.version}</version>
            </dependency>


            <!-- spring -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- spring cloud-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- alibaba spring cloud-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud.ai</groupId>
                <artifactId>spring-ai-alibaba-bom</artifactId>
                <version>1.0.0.2</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>1.0.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${nacos.client.version}</version>
            </dependency>

            <!-- 中间件 -->
            <!-- redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.redisson</groupId>
                        <artifactId>redisson-spring-data-30</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-data-27</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j-redisson.version}</version>
            </dependency>

            <!-- oss -->
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>

            <!-- springdoc -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.webmvc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-springdoc-ui</artifactId>
                <version>${knife4j.springdoc.ui.version}</version>
            </dependency>

            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <dependency>
                <groupId>com.oceanbase</groupId>
                <artifactId>oceanbase-client</artifactId>
                <version>${oceanbase.version}</version>
            </dependency>

            <!-- 基础工具 -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-extra</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-okhttp</artifactId>
                <version>${feign-okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>retrofit</artifactId>
                <version>${retrofit.version}</version>
            </dependency>

            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>${jaxb.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.verion}</version>
            </dependency>

            <dependency>
                <groupId>de.schlichtherle.truelicense</groupId>
                <artifactId>truelicense-core</artifactId>
                <version>${truelicense.version}</version>
            </dependency>

            <!-- mysql -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.github.jsqlparser</groupId>
                        <artifactId>jsqlparser</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.beacon</groupId>
                <artifactId>beacon-common-datascope</artifactId>
                <version>3.6.5</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>4.2</version>
            </dependency>

            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.version}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch-x-content</artifactId>
                <version>${elasticsearch.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic-ds.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>com.googlecode.aviator</groupId>
                <artifactId>aviator</artifactId>
                <version>5.4.3</version>
            </dependency>

            <!-- jacoco -->
            <dependency>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.9</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>1.10.0</version>
            </dependency>

            <!-- jackson升级，json解析大小扩大到20M -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>2.15.1</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>2.15.1</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.15.1</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jdk8</artifactId>
                <version>2.15.1</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>2.15.1</version>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-parameter-names</artifactId>
                <version>2.15.1</version>
            </dependency>

            <dependency>
                <groupId>jakarta.mail</groupId>
                <artifactId>jakarta.mail-api</artifactId>
                <version>2.1.3</version>
            </dependency>

            <!-- module -->
            <dependency>
                <groupId>com.ai.framework.core</groupId>
                <artifactId>framework-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.framework.web</groupId>
                <artifactId>framework-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.framework.workflow</groupId>
                <artifactId>framework-workflow</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.framework.llm</groupId>
                <artifactId>framework-llm</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.framework.sse</groupId>
                <artifactId>framework-sse</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.framework.kafka</groupId>
                <artifactId>framework-kafka</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.framework.log</groupId>
                <artifactId>framework-log</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.framework.mail</groupId>
                <artifactId>framework-mail</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.framework.oss</groupId>
                <artifactId>framework-oss</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.framework.swagger</groupId>
                <artifactId>framework-swagger</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.framework.elasticsearch</groupId>
                <artifactId>framework-elasticsearch</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-dependencies</artifactId>
                <version>${knife4j.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.ai.framework.mybatis</groupId>
                <artifactId>framework-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.framework.redis</groupId>
                <artifactId>framework-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>3.1.8</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>3.2.4</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>market-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>market-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>app-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>app-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>admin-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>admin-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>agent-base-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>agent-base-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>agent-run-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>agent-run-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>agent-task-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>agent-task-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>app-session-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>app-session-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>base-file-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>base-file-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>base-log-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>base-log-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>base-model-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>base-model-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>base-notice-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>base-notice-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>base-search-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>base-search-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>knowledge-doc-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>knowledge-doc-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>knowledge-dict-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>knowledge-dict-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>knowledge-table-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>knowledge-table-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>skill-mcp-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>skill-mcp-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>skill-tool-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>skill-tool-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>tenant-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>tenant-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>tenant-audit-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>tenant-audit-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>tenant-authorize-server</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>tenant-authorize-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.ai.application</groupId>
                <artifactId>app-auth-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- JWT-->
            <dependency>
                <groupId>org.bitbucket.b_c</groupId>
                <artifactId>jose4j</artifactId>
                <version>${jose4j.version}</version>
            </dependency>

            <!--mapStruct依赖-->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
                <scope>provided</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <!-- 项目打包时会将java目录中的*.xml文件也进行打包 -->
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <!-- 编译 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten.maven.plugin.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>oss</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.9</version>
                <executions>
                    <execution>
                        <id>pre-test</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>
</project>
