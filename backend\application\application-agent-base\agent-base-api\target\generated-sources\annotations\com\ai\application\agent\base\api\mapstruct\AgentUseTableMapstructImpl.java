package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.dto.AgentUseTableAddDTO;
import com.ai.application.agent.base.api.dto.AgentUseTableUpdateDTO;
import com.ai.application.agent.base.api.entity.AgentUseTable;
import com.ai.application.agent.base.api.vo.AgentUseTableListVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-14T11:00:35+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentUseTableMapstructImpl implements AgentUseTableMapstruct {

    @Override
    public AgentUseTable toAddEntity(AgentUseTableAddDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentUseTable agentUseTable = new AgentUseTable();

        agentUseTable.setAtStatus( dto.getAtStatus() );
        agentUseTable.setAgentId( dto.getAgentId() );
        agentUseTable.setVersionId( dto.getVersionId() );
        agentUseTable.setTableId( dto.getTableId() );
        agentUseTable.setTableExtend( dto.getTableExtend() );

        return agentUseTable;
    }

    @Override
    public AgentUseTable toUpdateEntity(AgentUseTableUpdateDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentUseTable agentUseTable = new AgentUseTable();

        agentUseTable.setAtId( dto.getAtId() );
        agentUseTable.setAtStatus( dto.getAtStatus() );
        agentUseTable.setAgentId( dto.getAgentId() );
        agentUseTable.setVersionId( dto.getVersionId() );
        agentUseTable.setTableId( dto.getTableId() );
        agentUseTable.setTableExtend( dto.getTableExtend() );

        return agentUseTable;
    }

    @Override
    public AgentUseTableListVO toVo(AgentUseTable entity) {
        if ( entity == null ) {
            return null;
        }

        AgentUseTableListVO agentUseTableListVO = new AgentUseTableListVO();

        agentUseTableListVO.setAtId( entity.getAtId() );
        agentUseTableListVO.setAtStatus( entity.getAtStatus() );
        agentUseTableListVO.setAgentId( entity.getAgentId() );
        agentUseTableListVO.setVersionId( entity.getVersionId() );
        agentUseTableListVO.setTableId( entity.getTableId() );
        agentUseTableListVO.setTableExtend( entity.getTableExtend() );
        agentUseTableListVO.setCreateTime( entity.getCreateTime() );
        agentUseTableListVO.setUpdateTime( entity.getUpdateTime() );

        return agentUseTableListVO;
    }

    @Override
    public List<AgentUseTableListVO> toVoList(List<AgentUseTable> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AgentUseTableListVO> list = new ArrayList<AgentUseTableListVO>( entities.size() );
        for ( AgentUseTable agentUseTable : entities ) {
            list.add( toVo( agentUseTable ) );
        }

        return list;
    }
}
