package com.ai.application.base.file.feign;


import com.ai.application.base.file.api.dto.AppFileBatchDto;
import com.ai.application.base.file.api.dto.DocFileDto;
import com.ai.application.base.file.api.dto.FileDeleteDto;
import com.ai.application.base.file.api.dto.FileUploadNoticeDto;
import com.ai.application.base.file.api.feign.IFileFeignClient;
import com.ai.application.base.file.api.vo.DownloadBytesVo;
import com.ai.application.base.file.api.vo.DownloadUrlVo;
import com.ai.application.base.file.api.vo.FileMd5Vo;
import com.ai.application.base.file.service.IFileService;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 文件服务
 */
@RestController
@Slf4j
@AllArgsConstructor
public class FileFeignClient  implements IFileFeignClient {

    private final IFileService fileService;



    public void  batchFileSave( AppFileBatchDto dto){fileService.batchFileSave(dto);}

    /**
     * 文件上传
     */

    public ResultVo<DownloadUrlVo> upload( MultipartFile file,  String fileSn,
                                   String fileType) throws IOException {

         String fileHash = DigestUtils.md5Hex(file.getBytes());

         String url  = fileService.uploadFileReturnUrl(file, fileSn, fileType);
        DownloadUrlVo downloadUrlVo =new DownloadUrlVo();
        downloadUrlVo.setUrl(url);
        downloadUrlVo.setHash(fileHash);
        return ResultVo.data(downloadUrlVo);
    }

    /**
     * 获取文件下载url
     */

    public ResultVo<String> getUrl( String fileSn, String type,  String fileName){
        // 通过oss获取文件url
        return ResultVo.data(fileService.getUrl(fileSn, type, fileName));
    }

    /**
     * 获取当前用户历史上传文件的MD5
     *
     * @return
     */

    public ResultVo<FileMd5Vo> historyMd5(){
        return ResultVo.data(fileService.historyMd5());
    }

    /**
     * 前端通知文件上传成功
     */


    public ResultVo<String> uploadFile(FileUploadNoticeDto uploadInfo) {

        fileService.uploadFile(uploadInfo, false);
        return ResultVo.success("ok");
    }


    public ResultVo<String> deleteFile(@RequestBody @Valid FileDeleteDto fileDeleteDto){
        fileService.deleteFile(fileDeleteDto);
        return ResultVo.success("ok");
    }


    private static final String CONFIG_PREFIX = "CONFIG::";



    public ResultVo<DocFileDto> getDocFileByFileSn( String fileSn){
        return fileService.getDocFileByFileSn(fileSn).map(ResultVo::data).orElseGet(ResultVo::new);
    }

    public ResultVo<DocFileDto> getDocFileById(Integer fileId){
        return fileService.getDocFileByFileId(fileId).map(ResultVo::data).orElseGet(ResultVo::new);
    }

    public ResultVo<List<DocFileDto>>   getDocFileByFileIds(List<Integer> fileIds){
        return ResultVo.data(fileService.getDocFileByFileIds(fileIds));
    }

    public ResultVo<List<DocFileDto>>   getBatchFileByFileSn(List<String> fileSns){
        return ResultVo.data(fileService.getDocFileByFileSns(fileSns));
    }

    public ResultVo<String> deleteFiles(List<Integer> fileIds){
        fileService.batchUpdateStatus(fileIds);
        return ResultVo.data("ok");
    };



   public ResultVo<String> processFileUpload( byte[] content,
                                        String fileSn,
                                        String fileType,
                                       String fileName,
                                        String source){
        fileService.processFileUpload(content,fileSn,fileType,fileName,source);
        return ResultVo.data("ok");};


    public ResultVo<DownloadBytesVo> downloadAsBytes( String fileSn){
        return  ResultVo.data(fileService.downloadAsBytes(fileSn));};



}
