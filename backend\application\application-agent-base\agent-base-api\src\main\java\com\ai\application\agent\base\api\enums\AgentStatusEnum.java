package com.ai.application.agent.base.api.enums;

import lombok.Getter;

@Getter
public enum AgentStatusEnum {
    STOP(0, "停用"),
    DEV(1, "开发"),
    PUBLISH(5, "发布"),
    DELETE(-1, "删除")
    ;

    private final Integer code;
    private final String desc;

    AgentStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AgentStatusEnum ofCode(Integer value) {
        for (AgentStatusEnum sessionStatusEnum : AgentStatusEnum.values()) {
            if (sessionStatusEnum.code.equals(value)) {
                return sessionStatusEnum;
            }
        }
        return null;
    }
}
