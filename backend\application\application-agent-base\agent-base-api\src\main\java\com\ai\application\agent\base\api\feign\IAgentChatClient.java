package com.ai.application.agent.base.api.feign;

import com.ai.application.agent.base.api.dto.AgentChatDTO;
import com.ai.application.agent.base.api.vo.AgentChatVO;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.vo.ResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * Agent Chat API Client
 */
@FeignClient(
        value = ServiceConstant.AGENT_RUN,
        contextId = "IAgentChatClient"
)
public interface IAgentChatClient {
    String API_PREFIX = "/v1/feign/agent";

    /**
     * 调用智能体进行对话
     *
     * @param chatDto 对话请求
     * @param authorization 授权头
     * @return 对话结果
     */
    @PostMapping(value = API_PREFIX + "/chat")
    ResultVo<AgentChatVO> requestLLMWithAgent(
            @RequestBody @Valid AgentChatDTO chatDto,
            @RequestHeader(value = "Authorization", required = false) String authorization
    );

    /**
     * 检查智能体是否可用
     *
     * @param agentSn 智能体编号
     * @return 可用性结果
     */
    @GetMapping(value = API_PREFIX + "/check/available/{agentSn}")
    ResultVo<Boolean> checkAgentAvailable(@PathVariable(value = "agentSn") String agentSn);
}
