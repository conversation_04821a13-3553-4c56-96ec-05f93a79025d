package com.ai.application.agent.base.api.feign.fallback;

import com.ai.application.agent.base.api.dto.FindAgentByUseDTO;
import com.ai.application.agent.base.api.feign.IAgentUseClient;
import com.ai.application.agent.base.api.vo.FindAgentByUseVO;
import com.ai.framework.core.vo.ResultVo;
import java.util.List;

public class IAgentUseFallback implements IAgentUseClient {

    @Override
    public ResultVo<List<FindAgentByUseVO>> findAgentByUse(FindAgentByUseDTO dto) {
        return null;
    }
}