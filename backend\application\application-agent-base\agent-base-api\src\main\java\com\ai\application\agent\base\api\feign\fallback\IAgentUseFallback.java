package com.ai.application.agent.base.api.feign.fallback;

import com.ai.application.agent.base.api.dto.query.*;
import com.ai.application.agent.base.api.feign.IAgentUseClient;
import com.ai.application.agent.base.api.vo.*;
import com.ai.framework.core.vo.ResultVo;
import com.github.pagehelper.PageInfo;

public class IAgentUseFallback implements IAgentUseClient {

    @Override
    public ResultVo<PageInfo<AgentUseMcpQueryVO>> selectUseMcpByPage(AgentUseMcpQueryDTO dto) {
        return null;
    }

    @Override
    public ResultVo<PageInfo<AgentUseToolQueryVO>> selectUseToolByPage(AgentUseToolQueryDTO dto) {
        return null;
    }

    @Override
    public ResultVo<PageInfo<AgentUseKnowledgeTableQueryVO>> selectUseTableByPage(AgentUseKnowledgeTableQueryDTO dto) {
        return null;
    }

    @Override
    public ResultVo<PageInfo<AgentUseKnowledgeDocQueryVO>> selectUseDocByPage(AgentUseKnowledgeDocQueryDTO dto) {
        return null;
    }

    @Override
    public ResultVo<PageInfo<AgentUseKnowledgeDictQueryVO>> selectUseDictByPage(AgentUseKnowledgeDictQueryDTO dto) {
        return null;
    }
}