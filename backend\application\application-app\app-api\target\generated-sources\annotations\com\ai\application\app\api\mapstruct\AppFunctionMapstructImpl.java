package com.ai.application.app.api.mapstruct;

import com.ai.application.app.api.dto.AppFunctionDTO;
import com.ai.application.app.api.entity.AppFunction;
import com.ai.application.app.api.vo.AppFunctionVO;
import com.ai.application.app.api.vo.AppRoleFunctionTreeVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-12T18:39:56+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AppFunctionMapstructImpl implements AppFunctionMapstruct {

    @Override
    public AppFunction toEntity(AppFunctionDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AppFunction appFunction = new AppFunction();

        appFunction.setFunId( dto.getFunId() );
        appFunction.setFunType( dto.getFunType() );
        appFunction.setFunName( dto.getFunName() );
        appFunction.setFunSort( dto.getFunSort() );
        appFunction.setResCode( dto.getResCode() );
        appFunction.setFunStatus( dto.getFunStatus() );
        appFunction.setParentId( dto.getParentId() );
        appFunction.setRefIds( dto.getRefIds() );
        appFunction.setRefTenant( dto.getRefTenant() );
        appFunction.setAppId( dto.getAppId() );
        appFunction.setCreateTime( dto.getCreateTime() );
        appFunction.setUpdateTime( dto.getUpdateTime() );

        return appFunction;
    }

    @Override
    public List<AppFunction> toEntityList(List<AppFunctionDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<AppFunction> list = new ArrayList<AppFunction>( dtolist.size() );
        for ( AppFunctionDTO appFunctionDTO : dtolist ) {
            list.add( toEntity( appFunctionDTO ) );
        }

        return list;
    }

    @Override
    public AppFunctionVO toVo(AppFunction entity) {
        if ( entity == null ) {
            return null;
        }

        AppFunctionVO appFunctionVO = new AppFunctionVO();

        appFunctionVO.setFunId( entity.getFunId() );
        appFunctionVO.setFunType( entity.getFunType() );
        appFunctionVO.setFunName( entity.getFunName() );
        appFunctionVO.setFunSort( entity.getFunSort() );
        appFunctionVO.setResCode( entity.getResCode() );
        appFunctionVO.setFunStatus( entity.getFunStatus() );
        appFunctionVO.setParentId( entity.getParentId() );
        appFunctionVO.setRefIds( entity.getRefIds() );
        appFunctionVO.setRefTenant( entity.getRefTenant() );
        appFunctionVO.setAppId( entity.getAppId() );
        appFunctionVO.setCreateTime( entity.getCreateTime() );
        appFunctionVO.setUpdateTime( entity.getUpdateTime() );

        return appFunctionVO;
    }

    @Override
    public List<AppFunctionVO> toVoList(List<AppFunction> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AppFunctionVO> list = new ArrayList<AppFunctionVO>( entities.size() );
        for ( AppFunction appFunction : entities ) {
            list.add( toVo( appFunction ) );
        }

        return list;
    }

    @Override
    public List<AppRoleFunctionTreeVO> toTreeVoList(List<AppFunction> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AppRoleFunctionTreeVO> list = new ArrayList<AppRoleFunctionTreeVO>( entities.size() );
        for ( AppFunction appFunction : entities ) {
            list.add( appFunctionToAppRoleFunctionTreeVO( appFunction ) );
        }

        return list;
    }

    protected AppRoleFunctionTreeVO appFunctionToAppRoleFunctionTreeVO(AppFunction appFunction) {
        if ( appFunction == null ) {
            return null;
        }

        AppRoleFunctionTreeVO appRoleFunctionTreeVO = new AppRoleFunctionTreeVO();

        appRoleFunctionTreeVO.setFunId( appFunction.getFunId() );
        appRoleFunctionTreeVO.setFunType( appFunction.getFunType() );
        appRoleFunctionTreeVO.setFunName( appFunction.getFunName() );
        appRoleFunctionTreeVO.setFunSort( appFunction.getFunSort() );
        appRoleFunctionTreeVO.setParentId( appFunction.getParentId() );

        return appRoleFunctionTreeVO;
    }
}
