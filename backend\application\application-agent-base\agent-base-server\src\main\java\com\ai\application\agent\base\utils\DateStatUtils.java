package com.ai.application.agent.base.utils;

import com.ai.application.agent.base.api.bo.DataStatRangeBO;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.IsoFields;
import java.time.temporal.TemporalAdjusters;

public class DateStatUtils {

    /**
     * 获取当前日期 年/月/周 所在的日期范围
     *
     * @param index 1：本周,2:本月,3:本季，4:本年,0:全部
     * @return
     */
    public static DataStatRangeBO getDateStatRange(Integer index) {
        DataStatRangeBO res = null;
        switch (index) {
            case 0:
                res = new DataStatRangeBO();
                break;
            case 1:
                res = dateMonthWeekRange();
                break;
            case 2:
                res = dateYearMonthRange();
                break;
            case 3:
                res = dateYearQuarterRange();
                break;
            case 4:
                res = dateYearRange();
                break;
            default:
        }
        return res;
    }

    /**
     * 获取当前日期所在周的日期范围
     *
     * @return
     */
    public static DataStatRangeBO dateMonthWeekRange() {
        DataStatRangeBO res = new DataStatRangeBO();
        res.setStatFlag(1);
        LocalDate today = LocalDate.now();
        res.setStartDate(today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)));
        res.setEndDate(today.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY)).plusDays(1));

        res.setPreStartDate(res.getStartDate().minusDays(7));
        res.setPreEndDate(res.getStartDate());
        return res;
    }

    /**
     * 获取当前日期所在月的日期范围
     *
     * @return
     */
    public static DataStatRangeBO dateYearMonthRange() {
        DataStatRangeBO res = new DataStatRangeBO();
        res.setStatFlag(2);
        LocalDate today = LocalDate.now();
        res.setStartDate(today.with(TemporalAdjusters.firstDayOfMonth()));
        res.setEndDate(today.with(TemporalAdjusters.lastDayOfMonth()).plusDays(1));

        res.setPreStartDate(res.getStartDate().minusMonths(1));
        res.setPreEndDate(res.getStartDate());
        return res;
    }

    /**
     * 获取当前日期所在季度的日期范围
     *
     * @return
     */
    public static DataStatRangeBO dateYearQuarterRange() {
        DataStatRangeBO res = new DataStatRangeBO();
        res.setStatFlag(3);
        LocalDate today = LocalDate.now();
        int currentQuarter = today.get(IsoFields.QUARTER_OF_YEAR);
        LocalDate startOfQuarter = today.with(IsoFields.QUARTER_OF_YEAR, currentQuarter).with(IsoFields.DAY_OF_QUARTER, 1);
        LocalDate endOfQuarter = startOfQuarter.plusMonths(3).minusDays(1);
        res.setStartDate(startOfQuarter);
        res.setEndDate(endOfQuarter.plusDays(1));

        res.setPreStartDate(res.getStartDate().minusMonths(3));
        res.setPreEndDate(res.getStartDate());
        return res;
    }

    /**
     * 获取当前日期所在年度的日期范围
     *
     * @return
     */
    public static DataStatRangeBO dateYearRange() {
        DataStatRangeBO res = new DataStatRangeBO();
        res.setStatFlag(4);
        LocalDate today = LocalDate.now();
        res.setStartDate(today.with(TemporalAdjusters.firstDayOfYear()));
        res.setEndDate(today.with(TemporalAdjusters.lastDayOfYear()).plusDays(1));

        res.setPreStartDate(res.getStartDate().minusYears(1));
        res.setPreEndDate(res.getStartDate());
        return res;
    }


    public static void main(String[] args) {
        DataStatRangeBO dateStatRangeBO = dateMonthWeekRange();
        DataStatRangeBO dateStatRangeBO1 = dateYearMonthRange();
        DataStatRangeBO dateStatRangeBO2 = dateYearQuarterRange();
        DataStatRangeBO dateStatRangeBO3 = dateYearRange();

        System.out.println("上周开始日期");
    }
}
