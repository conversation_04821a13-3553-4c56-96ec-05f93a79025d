package com.ai.application.tenant.audit.api.feign;


import com.ai.application.tenant.audit.api.dto.TenantAuditDTO;
import com.ai.application.tenant.audit.api.dto.query.TenantAddDTO;
import com.ai.application.tenant.audit.api.feign.fallback.AuditClientFallback;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.vo.ResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(value = ServiceConstant.TENANT_AUDIT, fallback = AuditClientFallback.class, contextId = "IAuditFeignClient")
@Component
public interface IAuditFeignClient {

    String API_PREFIX = "/v1/audit/feign";
    @PostMapping(value =API_PREFIX+ "/update")
     ResultVo<Void> update(@Validated @RequestBody TenantAuditDTO dto);

    @PostMapping(value =API_PREFIX+ "/add")
    ResultVo<Void> add(@Validated @RequestBody TenantAddDTO dto);
}
