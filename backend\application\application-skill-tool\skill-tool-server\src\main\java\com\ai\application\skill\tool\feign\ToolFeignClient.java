package com.ai.application.skill.tool.feign;


import com.ai.application.base.file.api.dto.AppFileBatchDto;
import com.ai.application.base.file.api.dto.DocFileDto;
import com.ai.application.base.file.api.dto.FileDeleteDto;
import com.ai.application.base.file.api.dto.FileUploadNoticeDto;
import com.ai.application.base.file.api.vo.DownloadBytesVo;
import com.ai.application.base.file.api.vo.DownloadUrlVo;
import com.ai.application.base.file.api.vo.FileMd5Vo;
import com.ai.application.skill.tool.api.feign.IToolFeignClient;
import com.ai.application.skill.tool.api.vo.ToolDetailVo;
import com.ai.application.skill.tool.service.IToolService;
import com.ai.framework.core.vo.ResultVo;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 文件服务
 */
@RestController
@Slf4j
@AllArgsConstructor
public class ToolFeignClient implements IToolFeignClient {
    @Autowired
    private IToolService toolService;


    public ResultVo<ToolDetailVo> getSkillDetailById(@PathVariable("toolId") Integer toolId) {
        ToolDetailVo resultVo = toolService.getSkillDetail(toolId);
        return ResultVo.data(resultVo);
    }




}
