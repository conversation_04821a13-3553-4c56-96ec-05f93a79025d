package com.ai.application.base.log.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 通知类型配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("notification_type")
public class NotificationType implements Serializable {
    /**
     * 通知类型id
     */
    @Schema(description = "通知类型id")
    @TableId(type = IdType.AUTO)
    private Integer ntypeId;

    /**
     * 通知类型:10-邮件,20-短信,30-钉钉,40-企微,50-飞书,60-Slack,70-Webhook,80-站内消息
     */
    @Schema(description = "通知类型:10-邮件,20-短信,30-钉钉,40-企微,50-飞书,60-Slack,70-Webhook,80-站内消息")
    private Integer ntypeType;

    /**
     * 类型配置名称
     */
    @Schema(description = "类型配置名称")
    private String ntypeName;

    /**
     * 类型配置描述
     */
    @Schema(description = "类型配置描述")
    private String ntypeDesc;

    /**
     * 类型配置状态:1-启用,0-停用,-1-弃用
     */
    @Schema(description = "类型配置状态:1-启用,0-停用,-1-弃用")
    private Integer ntypeStatus;

    /**
     * 类型配置参数:渠道配置,重试配置等
     */
    @Schema(description = "类型配置参数:渠道配置,重试配置等")
    private String ntypeConfig;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Integer tenantId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}