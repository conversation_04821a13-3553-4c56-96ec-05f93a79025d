package com.ai.application.agent.base.api.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

/**
 * 智能体工具执行mcp工具记录表
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@Schema(name = "智能体工具执行mcp工具记录表DTO")
public class AgentRunMcpDTO {
    /**
     * mcp工具执行id
     */
    @Schema(description = "mcp工具执行id")
    private Integer mcpRunId;

    /**
     * mcp工具名称
     */
    @Schema(description = "mcp工具名称")
    private String mcpName;

    /**
     * mcp工具输入参数
     */
    @Schema(description = "mcp工具输入参数")
    private String mcpInput;

    /**
     * mcp工具输出结果
     */
    @Schema(description = "mcp工具输出结果")
    private String mcpOutput;

    /**
     * 执行状态:1-执行中,2-成功,3-失败,4-超时
     */
    @Schema(description = "执行状态:1-执行中,2-成功,3-失败,4-超时")
    private Integer mcpStatus;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String mcpError;

    /**
     * 执行时长(毫秒)
     */
    @Schema(description = "执行时长(毫秒)")
    private Integer mcpDuration;

    /**
     * mcp工具配置快照
     */
    @Schema(description = "mcp工具配置快照")
    private String mcpSnapshot;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private Date mcpStartTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    private Date mcpEndTime;

    /**
     * 运行记录id
     */
    @Schema(description = "运行记录id")
    private Integer runId;

    /**
     * 步骤id
     */
    @Schema(description = "步骤id")
    private Integer stepId;

    /**
     * mcp工具id
     */
    @Schema(description = "mcp工具id")
    private Integer mcpId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}