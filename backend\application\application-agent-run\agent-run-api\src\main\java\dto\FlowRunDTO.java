package dto;

import com.ai.application.agent.base.api.bo.FlowVariableBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Schema(name = "工作流传参")
@Data
public class FlowRunDTO {
    @Schema(description = "agent编码")
    private String agentSn;

    @Schema(description = "输入参数")
    List<FlowVariableBO> variables;

    @Schema(description = "是否流式")
    private Boolean stream;
}