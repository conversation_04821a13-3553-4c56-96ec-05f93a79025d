package com.ai.application.task.controller;

import com.github.pagehelper.PageInfo;
import com.ai.application.task.service.ITaskAttachmentService;
import com.ai.application.task.api.dto.TaskAttachmentDTO;
import com.ai.application.task.api.dto.query.TaskAttachmentQueryDTO;
import com.ai.application.task.api.vo.TaskAttachmentVO;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 任务附件表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Tag(name = "任务附件表", description = "任务附件表-相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/taskAttachment")
public class TaskAttachmentController {

    @Resource
    private ITaskAttachmentService  taskAttachmentService;

    /**
     * 分页查询
     *
     * @param queryDto
     * @return
     */
    @Operation(summary = "任务附件表-分页查询", description = "查询所有任务附件表 信息")
    @PostMapping("/page")
    public ResultVo<PageInfo<TaskAttachmentVO>> page(@Validated @RequestBody TaskAttachmentQueryDTO queryDto){
        return ResultVo.data(taskAttachmentService.page(queryDto));
    }

    /**
     * 保存
     *
     * @param dto
     * @return
     */
    @Operation(summary = "任务附件表-新增")
    @PostMapping("/add")
    public ResultVo<Void> add(@Validated @RequestBody TaskAttachmentDTO dto){
        taskAttachmentService.add(dto);
        return ResultVo.success("保存成功");
    }
    
    /**
     * 修改
     *
     * @param dto
     * @return
     */
    @Operation(summary = "任务附件表-修改")
    @PostMapping(value = "/update")
    public ResultVo<Void> update(@Validated @RequestBody TaskAttachmentDTO dto){
        taskAttachmentService.update(dto);
        return ResultVo.success("修改成功");
    }
}