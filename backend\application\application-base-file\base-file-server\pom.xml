<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.ai.application</groupId>
		<artifactId>application-base-file</artifactId>
		<version>${revision}</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<artifactId>base-file-server</artifactId>
	<name>${project.artifactId}</name>
	<version>${revision}</version>
	<packaging>jar</packaging>

	<dependencies>
		<dependency>
			<groupId>com.ai.application</groupId>
			<artifactId>base-file-api</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.5</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.ai.framework.web</groupId>
			<artifactId>framework-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-openfeign-core</artifactId>
		</dependency>

		<dependency>
			<groupId>com.ai.framework.core</groupId>
			<artifactId>framework-core</artifactId>
		</dependency>

		<dependency>
			<groupId>com.ai.framework.web</groupId>
			<artifactId>framework-web</artifactId>
		</dependency>

		<dependency>
			<groupId>com.ai.framework.redis</groupId>
			<artifactId>framework-redis</artifactId>
		</dependency>

		<dependency>
			<groupId>com.ai.framework.mybatis</groupId>
			<artifactId>framework-mybatis</artifactId>
		</dependency>

		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>3.8.0</version>
		</dependency>

		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-s3</artifactId>
			<version>1.12.544</version>
		</dependency>



		<dependency>
			<groupId>com.ai.application</groupId>
			<artifactId>base-file-api</artifactId>
			<scope>compile</scope>
		</dependency>

		<dependency>
			<groupId>com.ai.application</groupId>
			<artifactId>agent-base-api</artifactId>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>com.ai.application</groupId>-->
<!--			<artifactId>agent-doc-api</artifactId>-->
<!--		</dependency>-->

		<dependency>
			<groupId>com.ai.application</groupId>
			<artifactId>tenant-api</artifactId>
		</dependency>

		<dependency>
			<groupId>org.xhtmlrenderer</groupId>
			<artifactId>flying-saucer-core</artifactId>
			<version>9.1.22</version>
		</dependency>

		<dependency>
			<groupId>org.xhtmlrenderer</groupId>
			<artifactId>flying-saucer-pdf-itext5</artifactId>
			<version>9.1.22</version>
		</dependency>

		<dependency>
			<groupId>xerces</groupId>
			<artifactId>xercesImpl</artifactId>
			<version>2.9.1</version>
		</dependency>

		<dependency>
			<groupId>net.sourceforge.nekohtml</groupId>
			<artifactId>nekohtml</artifactId>
			<version>1.9.13</version>
		</dependency>

		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.14.3</version>
		</dependency>

		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>2.0.24</version>
		</dependency>
		<dependency>
			<groupId>io.minio</groupId>
			<artifactId>minio</artifactId>
			<version>8.5.7</version> <!-- 使用最新版本 -->
		</dependency>

		<!-- itext7html转pdf  -->
		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>html2pdf</artifactId>
			<version>3.0.2</version>
		</dependency>

		<!-- 中文字体支持 -->
		<dependency>
			<groupId>com.itextpdf</groupId>
			<artifactId>font-asian</artifactId>
			<version>7.1.13</version>
		</dependency>



		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.junit.vintage</groupId>
					<artifactId>junit-vintage-engine</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${spring.maven.version}</version>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
