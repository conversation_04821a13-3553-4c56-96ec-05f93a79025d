package com.ai.application.base.log.api.serializer;

import com.ai.application.base.log.api.enums.AffectedObjectTypeEnum;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

public class AffectedObjectTypeSerializer extends JsonSerializer<Integer> {
    @Override
    public void serialize(Integer i, JsonGenerator j, SerializerProvider s) throws IOException {
        if (i == null) {
            j.writeNull();
        } else {
            JSONObject object = new JSONObject();
            object.put("code", i);
            object.put("title", AffectedObjectTypeEnum.getTitle(i));
            j.writeObject(object);
        }

    }
}
