package com.ai.application.agent.run.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 知识添加嵌入参数 DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(name = "KnowledgeEmbeddingParamsDTO")
public class KnowledgeEmbeddingParamsDTO {

    /**
     * 知识库编号
     */
    @Schema(description = "知识库编号")
    private String knowledgeInventory;

    /**
     * 深度解析选项
     */
    @Schema(description = "深度解析选项")
    private List<String> deepParse;

    /**
     * 分段规则类型
     */
    @Schema(description = "分段规则类型")
    private String splitRuleType;

    /**
     * 遇到错误是否继续 (0-不继续, 1-继续)
     */
    @Schema(description = "遇到错误是否继续")
    private Integer continueWhenErr;

    /**
     * 分段规则
     */
    @Schema(description = "分段规则")
    private Integer splitRule;

    /**
     * 切分器类型
     */
    @Schema(description = "切分器类型")
    private Integer splitter;

    /**
     * 分段字数上限
     */
    @Schema(description = "分段字数上限")
    private Integer wordCountLimit;

    /**
     * 重叠区域字数
     */
    @Schema(description = "重叠区域字数")
    private Integer wordCountOverlap;

    /**
     * 分隔符内容
     */
    @Schema(description = "分隔符内容")
    private String separatorContent;
}
