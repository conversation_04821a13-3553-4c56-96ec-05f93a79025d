package com.ai.application.agent.base.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Schema(name = "智能体统计指标VO")
@Builder
public class AgentStatIndexVO {
    @Schema(description = "总数量")
    private Integer totalCount;

    @Schema(description = "数量")
    private Integer count;

    @Schema(description = "比例(%)")
    private String rate;
}
