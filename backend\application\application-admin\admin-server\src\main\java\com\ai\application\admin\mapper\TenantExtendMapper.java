package com.ai.application.admin.mapper;

import com.ai.application.admin.api.entity.TenantExtend;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 租户扩展配置表 Mapper接口
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Mapper
public interface TenantExtendMapper extends BaseMapper<TenantExtend> {
    @Select("select * from tenant_extend where tenant_id = #{tenantId} and item_status >= 0")
    List<TenantExtend> findByTenantId(@Param("tenantId") Integer tenantId);
}
