package com.ai.application.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 市场表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("market")
public class Market implements Serializable {
    @Schema(description = "")
    @TableId(type = IdType.AUTO)
    private Integer marketId;

    /**
     * 市场sn
     */
    @Schema(description = "市场sn")
    private String marketSn;

    /**
     * 10公共市场20团队市场
     */
    @Schema(description = "10公共市场20团队市场")
    private Integer marketType;

    /**
     * 所属租户ID
     */
    @Schema(description = "所属租户ID")
    private Integer tenantId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}