package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.dto.AgentUseToolAddDTO;
import com.ai.application.agent.base.api.dto.AgentUseToolUpdateDTO;
import com.ai.application.agent.base.api.entity.AgentUseTool;
import com.ai.application.agent.base.api.vo.AgentUseToolListVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-12T18:39:57+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentUseToolMapstructImpl implements AgentUseToolMapstruct {

    @Override
    public AgentUseTool toAddEntity(AgentUseToolAddDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentUseTool agentUseTool = new AgentUseTool();

        agentUseTool.setAtlStatus( dto.getAtlStatus() );
        agentUseTool.setAgentId( dto.getAgentId() );
        agentUseTool.setVersionId( dto.getVersionId() );
        agentUseTool.setToolId( dto.getToolId() );
        agentUseTool.setToolExtend( dto.getToolExtend() );

        return agentUseTool;
    }

    @Override
    public AgentUseTool toUpdateEntity(AgentUseToolUpdateDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentUseTool agentUseTool = new AgentUseTool();

        agentUseTool.setAtlId( dto.getAtlId() );
        agentUseTool.setAtlStatus( dto.getAtlStatus() );
        agentUseTool.setAgentId( dto.getAgentId() );
        agentUseTool.setVersionId( dto.getVersionId() );
        agentUseTool.setToolId( dto.getToolId() );
        agentUseTool.setToolExtend( dto.getToolExtend() );

        return agentUseTool;
    }

    @Override
    public AgentUseToolListVO toVo(AgentUseTool entity) {
        if ( entity == null ) {
            return null;
        }

        AgentUseToolListVO agentUseToolListVO = new AgentUseToolListVO();

        agentUseToolListVO.setAtlId( entity.getAtlId() );
        agentUseToolListVO.setAtlStatus( entity.getAtlStatus() );
        agentUseToolListVO.setAgentId( entity.getAgentId() );
        agentUseToolListVO.setVersionId( entity.getVersionId() );
        agentUseToolListVO.setToolId( entity.getToolId() );
        agentUseToolListVO.setToolExtend( entity.getToolExtend() );
        agentUseToolListVO.setCreateTime( entity.getCreateTime() );
        agentUseToolListVO.setUpdateTime( entity.getUpdateTime() );

        return agentUseToolListVO;
    }

    @Override
    public List<AgentUseToolListVO> toVoList(List<AgentUseTool> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AgentUseToolListVO> list = new ArrayList<AgentUseToolListVO>( entities.size() );
        for ( AgentUseTool agentUseTool : entities ) {
            list.add( toVo( agentUseTool ) );
        }

        return list;
    }
}
