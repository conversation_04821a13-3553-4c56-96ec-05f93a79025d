package com.ai.application.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 资源定义表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("resource")
public class Resource implements Serializable {
        /**
    * 资源id
    */
    @Schema(description = "资源id")
    @TableId(type = IdType.AUTO)
private Integer resourceId;

    /**
    * 资源类型:10-智能体,20-知识库,30-智能表格,40-字典,50-工具,60-MCP,70-模型
    */
    @Schema(description = "资源类型:10-智能体,20-知识库,30-智能表格,40-字典,50-工具,60-MCP,70-模型")
    private Integer resourceType;

    /**
    * 资源对象id
    */
    @Schema(description = "资源对象id")
    private Integer resourceObjectId;

    /**
    * 资源租户id
    */
    @Schema(description = "资源租户id")
    private Integer resourceTenantId;

    /**
    * 资源状态:1-可用,0-不可用,-1-删除
    */
    @Schema(description = "资源状态:1-可用,0-不可用,-1-删除")
    private Integer resourceStatus;

    /**
    * 可授权范围:10-用户级,20-角色级,30-部门级,40-租户级,50-平台级
    */
    @Schema(description = "可授权范围:10-用户级,20-角色级,30-部门级,40-租户级,50-平台级")
    private Integer resourceScope;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}