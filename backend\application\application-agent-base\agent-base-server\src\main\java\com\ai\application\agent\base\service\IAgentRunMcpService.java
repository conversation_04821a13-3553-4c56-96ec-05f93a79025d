package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.dto.AgentRunMcpListDTO;
import com.ai.application.agent.base.api.dto.AgentRunMcpDTO;
import com.ai.application.agent.base.api.vo.AgentRunMcpVO;

import java.util.List;

/**
 * 智能体工具执行mcp工具记录表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public interface IAgentRunMcpService {

    /**
     * 列表
     *
     * @param queryDto
     * @return
     */
    List<AgentRunMcpVO> list(AgentRunMcpListDTO queryDto);

    /**
     * 保存
     *
     * @param dto
     */
    void add(AgentRunMcpDTO dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(AgentRunMcpDTO dto);

    /**
     * 查看
     *
     * @param id
     * @return
     */
    AgentRunMcpVO get(Integer id);
}