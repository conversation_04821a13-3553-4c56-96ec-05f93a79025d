package com.ai.application.process.executor.impl;

import com.ai.application.process.enums.ProcessErrorCodeEnum;
import com.ai.application.process.enums.ShortTermMemoryExtractContentEnum;
import com.ai.application.process.enums.ShortTermMemoryExtractTypeEnum;
import com.ai.application.process.executor.BaseExecutor;
import com.ai.application.process.executor.ExecutionContext;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.list.CollectionUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @version 1.0.0
 */
@Service
@Slf4j
public class ShortTermMemoryExtractExecutorV2 implements BaseExecutor {

    private final ShortTermMemoryExtractExecutor shortTermMemoryExtractExecutor;

    public ShortTermMemoryExtractExecutorV2(ShortTermMemoryExtractExecutor shortTermMemoryExtractExecutor) {
        this.shortTermMemoryExtractExecutor = shortTermMemoryExtractExecutor;
    }


    @Override
    public Map<String, Object> execute(ExecutionContext executionContext) {
        // 获取参数并校验
        var processId = executionContext.getProcessId();
        var sessionSn = executionContext.getParameterAsString("sessionSn");
        var extractConfigs = executionContext.getParameterAsString("extractConfigs");
        log.debug("ShortTermMemory ExtractV2 sessionSn:{},processId:{},extractConfigs:{}", sessionSn, processId, extractConfigs);
        Map<String, Object> memoryMap = new HashMap<>();
        if (StringUtils.isBlank(sessionSn)) {
            // 兼容单组件调试可能无sessionSn问题
            return memoryMap;
        }
        if (StringUtils.isBlank(extractConfigs)) {
            log.error("ShortTermMemory ExtractV2 fail sessionSn:{},processId:{},extractConfigs:{}", sessionSn, processId, extractConfigs);
            throw new ServiceException(ProcessErrorCodeEnum.SHORT_TERM_MEMORY_EXTRACT_FAIL);
        }

        var extractConfigList = JsonUtils.parseArray(extractConfigs, ExtractConfig.class);
        for (var extractConfig : extractConfigList) {
            // 读取短期记忆列表 全部暂时最大提取99条
            var extractType = extractConfig.getExtractType();
            var extractCountStr = extractConfig.getExtractCount();
            var extractContent = extractConfig.getExtractContent();
            var bucketSn = extractConfig.getBucketSn();
            var index = extractConfig.getIndex();

            var extractCount = ShortTermMemoryExtractTypeEnum.ALL.getType().equals(extractType) ? 99 : Integer.parseInt(extractCountStr);
            var shortTermMemoryList = shortTermMemoryExtractExecutor.getShortTermMemoryList(sessionSn, extractCount, StringUtils.isBlank(bucketSn) ? "default" : bucketSn, processId);
            if (CollectionUtils.isNotEmpty(shortTermMemoryList)) {
                if ("1".equals(extractCountStr)) {
                    var shortTermMemory = shortTermMemoryList.get(0);
                    if (ShortTermMemoryExtractContentEnum.ALL.getType().equals(extractContent)) {
                        var memory = JsonUtils.toJsonString(shortTermMemory);
                        memoryMap.put(index,memory);
                    } else if (ShortTermMemoryExtractContentEnum.CONTENT.getType().equals(extractContent)) {
                        var memory = shortTermMemory.getMemoryContent();
                        memoryMap.put(index,memory);
                    }
                } else {
                    if (ShortTermMemoryExtractContentEnum.ALL.getType().equals(extractContent)) {
                        var memory = shortTermMemoryList.stream().map(JsonUtils::toJsonString).toList();
                        memoryMap.put(index,memory);
                    } else if (ShortTermMemoryExtractContentEnum.CONTENT.getType().equals(extractContent)) {
                        var memory = shortTermMemoryList.stream()
                                .map(ShortTermMemoryExtractExecutor.ShortTermMemoryDTO::getMemoryContent)
                                .collect(Collectors.toList());
                        memoryMap.put(index,memory);
                    }
                }
            }
        }
        log.debug("ShortTermMemory ExtractV2 execute sessionSn:{},processId:{},extractConfigs:{},memoryMap:{}",
                sessionSn, processId, extractConfigs, JsonUtils.toJsonString(memoryMap));
        return memoryMap;
    }

    @Data
    private static class ExtractConfig {
        private String extractType;
        private String extractCount;
        private String extractContent;
        private String bucketSn;
        private String index;
    }


    @Override
    public String getId() {
        return "SHORT_TERM_MEMORY_EXTRACT_V2";
    }

}
