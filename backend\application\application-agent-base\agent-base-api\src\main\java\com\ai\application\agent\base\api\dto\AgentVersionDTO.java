package com.ai.application.agent.base.api.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

/**
 * 智能体版本表
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Data
@Schema(name = "智能体版本表DTO")
public class AgentVersionDTO {
    /**
     * 智能体版本id
     */
    @Schema(description = "智能体版本id")
    private Integer versionId;

    /**
     * 智能体版本sn
     */
    @Schema(description = "智能体版本sn")
    private String versionSn;

    /**
     * 智能体版本号
     */
    @Schema(description = "智能体版本号")
    private String versionNumber;

    /**
     * 版本元信息
     */
    @Schema(description = "版本元信息")
    private String versionMetadata;

    /**
     * 版本快照
     */
    @Schema(description = "版本快照")
    private String versionSnapshot;

    /**
     * 版本状态: 1:草稿,3:审批, 5:启用, 0:停用
     */
    @Schema(description = "版本状态: 1:草稿,3:审批, 5:启用, 0:停用")
    private Integer versionStatus;

    /**
     * 是否市场上架: 0否 1是
     */
    @Schema(description = "是否市场上架: 0否 1是")
    private Integer versionOnsale;

    /**
     * 智能体id
     */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
     * 创建人id
     */
    @Schema(description = "创建人id")
    private Integer userId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}