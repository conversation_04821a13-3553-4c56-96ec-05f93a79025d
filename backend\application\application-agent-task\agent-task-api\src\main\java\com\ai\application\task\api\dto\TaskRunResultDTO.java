package com.ai.application.task.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class TaskRunResultDTO {
    @Schema(description = "任务Sn")
    @NotBlank(message = "任务sn不能为空")
    private String taskSn;

    @Schema(description = "关联的智能体运行记录id")
    private Integer agentRunId;

    @Schema(description = "执行状态:1-等待,2-执行中,3-成功,4-失败,5-超时,6-中断")
    private Integer runStatus;

    @Schema(description = "执行输入参数")
    private String runInput;

    @Schema(description = "执行输出结果")
    private String runOutput;

    @Schema(description = "执行错误信息")
    private String runError;

    @Schema(description = "执行输出结果Sn")
    private String runOutputSn;
}
