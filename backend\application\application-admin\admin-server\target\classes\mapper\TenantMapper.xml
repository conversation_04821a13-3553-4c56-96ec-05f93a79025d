<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.admin.mapper.TenantMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.admin.api.entity.Tenant">
                    <id column="tenant_id" property="tenantId" />
                    <result column="tenant_sn" property="tenantSn" />
                    <result column="tenant_name" property="tenantName" />
                    <result column="tenant_domain" property="tenantDomain" />
                    <result column="tenant_desc" property="tenantDesc" />
                    <result column="tenant_expire_time" property="tenantExpireTime" />
                    <result column="tenant_status" property="tenantStatus" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        tenant_id, tenant_sn, tenant_name, tenant_domain, tenant_desc, tenant_expire_time, tenant_status, create_time, update_time
    </sql>
</mapper>