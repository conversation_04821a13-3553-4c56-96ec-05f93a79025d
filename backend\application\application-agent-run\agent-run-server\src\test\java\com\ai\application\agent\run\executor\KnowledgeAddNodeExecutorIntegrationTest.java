package com.ai.application.agent.run.executor;

import com.ai.application.agent.run.service.IKnowledgeService;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 知识添加节点执行器集成测试
 * 模拟真实的工作流场景
 */
@SpringJUnitConfig
@Slf4j
class KnowledgeAddNodeExecutorIntegrationTest {

    @MockBean
    private IKnowledgeService knowledgeService;

    private KnowledgeAddNodeExecutor knowledgeAddNodeExecutor;
    private WorkflowContext workflowContext;

    @BeforeEach
    void setUp() {
        knowledgeAddNodeExecutor = new KnowledgeAddNodeExecutor();
        setupWorkflowContext();
    }

    private void setupWorkflowContext() {
        // 模拟真实的工作流上下文
        workflowContext = new WorkflowContext();
        workflowContext.setWorkflowInstanceId(12345L);
        workflowContext.setCurrentNodeKey("knowledge_add_001");
        
        // 设置全局变量
        Map<String, Object> globalVars = new HashMap<>();
        globalVars.put("authorization", "Bearer test-token");
        globalVars.put("tenantName", "TEST_TENANT");
        globalVars.put("knowledgeBase", "kb_001");
        globalVars.put("documentContent", "这是一个测试文档的内容，用于验证知识添加功能。");
        workflowContext.setGlobalVars(globalVars);

        // 创建节点上下文
        NodeContext nodeContext = new NodeContext();
        nodeContext.setNodeKey("knowledge_add_001");
        nodeContext.setStatus(NodeStatus.INIT);
        nodeContext.setOutput(new HashMap<>());

        // 设置节点定义
        Map<String, Object> nodeDefinition = new HashMap<>();
        
        // 输入参数
        Map<String, Object> inputParameters = new HashMap<>();
        inputParameters.put("knowledgeInventorySn", "$knowledgeBase");
        inputParameters.put("input", "$documentContent");
        inputParameters.put("continueWhenErr", "0");
        inputParameters.put("knowledgeType", "sn");
        inputParameters.put("deepParse", Arrays.asList("parseWord", "parseImage"));
        inputParameters.put("splitRuleType", "0");
        nodeDefinition.put("inputParameters", inputParameters);
        
        // 输出参数
        Map<String, Object> outputParameters = new HashMap<>();
        outputParameters.put("embeddingFiles", "embeddingFilesResult");
        outputParameters.put("knowledgeInventorySn", "knowledgeInventoryResult");
        outputParameters.put("resultMessage", "processResult");
        outputParameters.put("knowledgeNames", "knowledgeNamesResult");
        nodeDefinition.put("outputParameters", outputParameters);
        
        nodeContext.setNodeDefinition(nodeDefinition);
        workflowContext.getNodeContexts().put("knowledge_add_001", nodeContext);
    }

    @Test
    void testRealWorldKnowledgeAddWorkflow() {
        // 模拟知识库服务返回
        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
        inventoryInfo.setModelSn("embedding-model-v1");
        inventoryInfo.setSplitRule(1);
        inventoryInfo.setSplitter(1);
        inventoryInfo.setWordCountLimit(500);
        inventoryInfo.setWordCountOverlap(50);
        inventoryInfo.setSeparatorContent("[\"\n\", \"\n\n\", \" \"]");

        when(knowledgeService.checkKnowledgeInventory("kb_001")).thenReturn(inventoryInfo);

        // 执行知识添加节点
        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));

        // 验证执行结果
        NodeContext nodeContext = workflowContext.getNodeContexts().get("knowledge_add_001");
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        
        // 验证输出结果
        assertNotNull(nodeContext.getOutput().get("embeddingFiles"));
        assertNotNull(nodeContext.getOutput().get("resultMessage"));
        assertNotNull(nodeContext.getOutput().get("knowledgeNames"));
        
        // 验证全局变量被正确设置
        assertNotNull(workflowContext.getGlobalVars().get("embeddingFilesResult"));
        assertNotNull(workflowContext.getGlobalVars().get("processResult"));
        
        // 验证节点执行时间被设置
        assertNotNull(nodeContext.getEndTime());
    }

    @Test
    void testKnowledgeAddWithCustomSplitRule() {
        // 修改输入参数使用自定义分段规则
        NodeContext nodeContext = workflowContext.getNodeContexts().get("knowledge_add_001");
        Map<String, Object> nodeDefinition = nodeContext.getNodeDefinition();
        Map<String, Object> inputParameters = (Map<String, Object>) nodeDefinition.get("inputParameters");
        
        // 设置自定义分段规则
        inputParameters.put("splitRuleType", "1");
        inputParameters.put("splitRule", "1");
        inputParameters.put("splitter", "2");
        inputParameters.put("wordCountLimit", "300");
        inputParameters.put("wordCountOverlap", "30");
        
        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
        inventoryInfo.setModelSn("embedding-model-v1");

        when(knowledgeService.checkKnowledgeInventory(anyString())).thenReturn(inventoryInfo);

        // 执行知识添加节点
        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));

        // 验证执行结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        assertNotNull(nodeContext.getOutput().get("resultMessage"));
    }

    @Test
    void testKnowledgeAddWithMultipleDocuments() {
        // 修改输入参数为多个文档
        NodeContext nodeContext = workflowContext.getNodeContexts().get("knowledge_add_001");
        Map<String, Object> nodeDefinition = nodeContext.getNodeDefinition();
        Map<String, Object> inputParameters = (Map<String, Object>) nodeDefinition.get("inputParameters");
        
        // 设置多个文档输入
        List<String> documents = Arrays.asList(
            "第一个文档：关于人工智能的基础知识",
            "第二个文档：机器学习算法详解",
            "第三个文档：深度学习应用案例"
        );
        workflowContext.getGlobalVars().put("multipleDocuments", documents);
        inputParameters.put("input", "$multipleDocuments");

        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
        inventoryInfo.setModelSn("embedding-model-v1");

        when(knowledgeService.checkKnowledgeInventory(anyString())).thenReturn(inventoryInfo);

        // 执行知识添加节点
        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));

        // 验证执行结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        
        // 验证处理了多个文档
        List<String> embeddingFiles = (List<String>) nodeContext.getOutput().get("embeddingFiles");
        assertNotNull(embeddingFiles);
        
        String resultMessage = (String) nodeContext.getOutput().get("resultMessage");
        assertNotNull(resultMessage);
        assertTrue(resultMessage.contains("成功"));
    }

    @Test
    void testKnowledgeAddWithDeepParsing() {
        // 修改输入参数启用深度解析
        NodeContext nodeContext = workflowContext.getNodeContexts().get("knowledge_add_001");
        Map<String, Object> nodeDefinition = nodeContext.getNodeDefinition();
        Map<String, Object> inputParameters = (Map<String, Object>) nodeDefinition.get("inputParameters");
        
        // 设置深度解析选项
        inputParameters.put("deepParse", Arrays.asList("parseForm", "parseImage", "parseWord"));

        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
        inventoryInfo.setModelSn("embedding-model-v1");

        when(knowledgeService.checkKnowledgeInventory(anyString())).thenReturn(inventoryInfo);

        // 执行知识添加节点
        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));

        // 验证执行结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        assertNotNull(nodeContext.getOutput().get("resultMessage"));
    }

    @Test
    void testKnowledgeAddFailure() {
        // 模拟知识库服务失败
        when(knowledgeService.checkKnowledgeInventory(anyString()))
                .thenThrow(new RuntimeException("知识库不存在"));

        // 执行知识添加节点并验证异常
        assertThrows(Exception.class, () -> knowledgeAddNodeExecutor.execute(workflowContext));

        // 验证节点状态
        NodeContext nodeContext = workflowContext.getNodeContexts().get("knowledge_add_001");
        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
        assertNotNull(nodeContext.getErrorMsg());
        assertTrue(nodeContext.getErrorMsg().contains("知识添加执行失败"));
    }

    @Test
    void testCompleteKnowledgeWorkflowScenario() {
        // 模拟完整的知识添加工作流场景
        // 1. 设置复杂的输入数据
        workflowContext.getGlobalVars().put("companyKnowledge", "公司产品手册内容...");
        workflowContext.getGlobalVars().put("targetKnowledgeBase", "company_kb_2024");

        // 2. 修改知识添加节点配置
        NodeContext nodeContext = workflowContext.getNodeContexts().get("knowledge_add_001");
        Map<String, Object> nodeDefinition = nodeContext.getNodeDefinition();
        Map<String, Object> inputParameters = (Map<String, Object>) nodeDefinition.get("inputParameters");
        
        inputParameters.put("knowledgeInventorySn", "$targetKnowledgeBase");
        inputParameters.put("input", "$companyKnowledge");
        inputParameters.put("continueWhenErr", "1"); // 遇到错误继续
        inputParameters.put("splitRuleType", "1"); // 自定义分段规则
        inputParameters.put("splitRule", "1");
        inputParameters.put("splitter", "2");
        inputParameters.put("wordCountLimit", "800");
        inputParameters.put("wordCountOverlap", "80");

        // 3. 模拟知识库服务返回
        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
        inventoryInfo.setModelSn("company-embedding-model");
        inventoryInfo.setSplitRule(1);
        inventoryInfo.setSplitter(2);
        inventoryInfo.setWordCountLimit(800);
        inventoryInfo.setWordCountOverlap(80);

        when(knowledgeService.checkKnowledgeInventory("company_kb_2024")).thenReturn(inventoryInfo);

        // 4. 执行知识添加节点
        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));

        // 5. 验证完整的执行结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        assertNotNull(nodeContext.getOutput().get("embeddingFiles"));
        assertNotNull(nodeContext.getOutput().get("resultMessage"));
        
        // 6. 验证全局变量传递
        assertEquals("company_kb_2024", workflowContext.getGlobalVars().get("knowledgeInventoryResult"));
        assertNotNull(workflowContext.getGlobalVars().get("embeddingFilesResult"));
        
        // 7. 验证可以传递给下一个节点
        String resultMessage = (String) workflowContext.getGlobalVars().get("processResult");
        assertNotNull(resultMessage);
        assertTrue(resultMessage.contains("成功"));
    }

    @Test
    void testRealWorldKnowledgeAddScenarioFromBackend2() {
        // 模拟真实的 backend2 场景
        // 基于您提供的真实入参进行测试
        workflowContext.getGlobalVars().put("d3ef893c5c", "这是从LLM节点输出的文本内容");
        workflowContext.getGlobalVars().put("realKnowledgeBase", "f6935b89-09a4-4d96-9dfc-e3e04a00cf66");

        // 修改知识添加节点配置（完全按照 backend2 的真实配置）
        NodeContext nodeContext = workflowContext.getNodeContexts().get("knowledge_add_001");
        Map<String, Object> nodeDefinition = nodeContext.getNodeDefinition();
        Map<String, Object> inputParameters = (Map<String, Object>) nodeDefinition.get("inputParameters");

        // 设置真实的 backend2 参数
        inputParameters.put("input", "{{d3ef893c5c}}"); // 使用变量引用语法
        inputParameters.put("knowledgeInventorySn", "$realKnowledgeBase");
        inputParameters.put("deepParse", Arrays.asList("parseForm", "parseImage"));
        inputParameters.put("continueWhenErr", "0");
        inputParameters.put("knowledgeType", "sn");
        inputParameters.put("splitRuleType", 0);
        inputParameters.put("splitRule", 0);

        // 设置输出参数（按照 backend2 的真实配置）
        Map<String, Object> outputParameters = (Map<String, Object>) nodeDefinition.get("outputParameters");
        outputParameters.put("embeddingFiles", "2309baf83c");
        outputParameters.put("knowledgeNames", "_currentDate");

        // 模拟知识库服务返回
        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
        inventoryInfo.setModelSn("localModel");
        inventoryInfo.setSplitRule(0);
        inventoryInfo.setSplitter(1);
        inventoryInfo.setWordCountLimit(500);
        inventoryInfo.setWordCountOverlap(50);

        when(knowledgeService.checkKnowledgeInventory("f6935b89-09a4-4d96-9dfc-e3e04a00cf66")).thenReturn(inventoryInfo);

        // 执行知识添加节点
        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));

        // 验证执行结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        assertNotNull(nodeContext.getOutput().get("embeddingFiles"));
        assertNotNull(nodeContext.getOutput().get("knowledgeNames"));

        // 验证全局变量被正确设置（按照 backend2 的输出变量）
        assertNotNull(workflowContext.getGlobalVars().get("2309baf83c"));
        assertNotNull(workflowContext.getGlobalVars().get("_currentDate"));

        // 验证节点执行时间被设置
        assertNotNull(nodeContext.getEndTime());

        log.info("Real world backend2 scenario test completed successfully");
    }
}
