package com.ai.application.agent.run.service.impl;

import com.ai.application.agent.run.dto.ProcessChatDTO;
import com.ai.framework.core.vo.ResultVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LLM 聊天服务实现类单元测试
 */
@ExtendWith(MockitoExtension.class)
class LlmChatServiceImplTest {

    @InjectMocks
    private LlmChatServiceImpl llmChatService;

    private ProcessChatDTO validChatDto;

    @BeforeEach
    void setUp() {
        validChatDto = ProcessChatDTO.builder()
                .model("localModel")
                .prompt("s z d f g h j")
                .sysPrompt("你是一个专业的AI助手")
                .temperature("0.6")
                .timeoutInSeconds(30)
                .build();
    }

    @Test
    void testChatSuccess() {
        // 执行测试
        ResultVo<String> result = llmChatService.chat(validChatDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertTrue(result.getData().contains("s z d f g h j"));
        assertTrue(result.getData().contains("localModel"));
        assertTrue(result.getData().contains("你是一个专业的AI助手"));
    }

    @Test
    void testChatWithoutSysPrompt() {
        // 准备测试数据 - 没有系统提示词
        validChatDto.setSysPrompt(null);

        // 执行测试
        ResultVo<String> result = llmChatService.chat(validChatDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertTrue(result.getData().contains("s z d f g h j"));
        assertFalse(result.getData().contains("基于系统提示词"));
    }

    @Test
    void testChatWithoutTemperature() {
        // 准备测试数据 - 没有温度参数
        validChatDto.setTemperature(null);

        // 执行测试
        ResultVo<String> result = llmChatService.chat(validChatDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertFalse(result.getData().contains("温度参数"));
    }

    @Test
    void testChatBlankPrompt() {
        // 准备测试数据 - 空的 prompt
        validChatDto.setPrompt("");

        // 执行测试
        ResultVo<String> result = llmChatService.chat(validChatDto);

        // 验证结果
        assertNotNull(result);
        assertNotEquals(0, result.getCode());
        assertEquals("prompt 不能为空", result.getMessage());
    }

    @Test
    void testChatNullPrompt() {
        // 准备测试数据 - null prompt
        validChatDto.setPrompt(null);

        // 执行测试
        ResultVo<String> result = llmChatService.chat(validChatDto);

        // 验证结果
        assertNotNull(result);
        assertNotEquals(0, result.getCode());
        assertEquals("prompt 不能为空", result.getMessage());
    }

    @Test
    void testChatBlankModel() {
        // 准备测试数据 - 空的 model
        validChatDto.setModel("");

        // 执行测试
        ResultVo<String> result = llmChatService.chat(validChatDto);

        // 验证结果
        assertNotNull(result);
        assertNotEquals(0, result.getCode());
        assertEquals("model 不能为空", result.getMessage());
    }

    @Test
    void testChatNullModel() {
        // 准备测试数据 - null model
        validChatDto.setModel(null);

        // 执行测试
        ResultVo<String> result = llmChatService.chat(validChatDto);

        // 验证结果
        assertNotNull(result);
        assertNotEquals(0, result.getCode());
        assertEquals("model 不能为空", result.getMessage());
    }

    @Test
    void testChatComplexScenario() {
        // 准备测试数据 - 复杂场景
        ProcessChatDTO complexDto = ProcessChatDTO.builder()
                .model("gpt-4")
                .prompt("请帮我分析一下这个问题：如何提高工作效率？")
                .sysPrompt("你是一个专业的效率管理专家，请提供实用的建议")
                .temperature("0.8")
                .timeoutInSeconds(60)
                .build();

        // 执行测试
        ResultVo<String> result = llmChatService.chat(complexDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertTrue(result.getData().contains("如何提高工作效率"));
        assertTrue(result.getData().contains("gpt-4"));
        assertTrue(result.getData().contains("效率管理专家"));
        assertTrue(result.getData().contains("0.8"));
    }

    @Test
    void testChatMinimalRequiredFields() {
        // 准备测试数据 - 只包含必需字段
        ProcessChatDTO minimalDto = ProcessChatDTO.builder()
                .model("test-model")
                .prompt("Hello")
                .build();

        // 执行测试
        ResultVo<String> result = llmChatService.chat(minimalDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getCode());
        assertNotNull(result.getData());
        assertTrue(result.getData().contains("Hello"));
        assertTrue(result.getData().contains("test-model"));
    }
}
