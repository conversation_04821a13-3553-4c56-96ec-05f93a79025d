package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.dto.AgentRunDTO;
import com.ai.application.agent.base.api.dto.AgentRunListDTO;
import com.ai.application.agent.base.api.vo.AgentRunVO;
import java.util.List;

/**
 * 智能体运行记录表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public interface IAgentRunService {

    /**
     * 列表
     *
     * @param queryDto
     * @return
     */
    List<AgentRunVO> list(AgentRunListDTO queryDto);

    /**
     * 保存
     *
     * @param dto
     */
    void add(AgentRunDTO dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(AgentRunDTO dto);

    /**
     * 查看
     *
     * @param id
     * @return
     */
    AgentRunVO get(Integer id);
}