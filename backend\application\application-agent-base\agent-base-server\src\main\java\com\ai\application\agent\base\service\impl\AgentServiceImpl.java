package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.bo.*;
import com.ai.application.agent.base.api.dto.*;
import com.ai.application.agent.base.api.dto.query.AgentPageDTO;
import com.ai.application.agent.base.api.dto.query.AgentStatDetailQueryDTO;
import com.ai.application.agent.base.api.dto.query.AgentVersionExtendQueryDTO;
import com.ai.application.agent.base.api.entity.*;
import com.ai.application.agent.base.api.enums.*;
import com.ai.application.agent.base.api.vo.*;
import com.ai.application.agent.base.helper.AgentExtendHelper;
import com.ai.application.agent.base.helper.AgentVersionExtendHelper;
import com.ai.application.agent.base.mapper.*;
import com.ai.application.agent.base.service.*;
import com.ai.application.app.api.feign.IAppUserClient;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.application.market.api.feign.IMarketClient;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.util.date.RelativeDateFormat;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.list.CollectionUtils;
import com.ai.framework.core.util.string.StringUtil;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.util.validator.AssertUtil;
import com.ai.framework.core.vo.ResultVo;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ai.application.agent.base.api.dto.query.AgentQueryDTO;
import com.ai.application.agent.base.api.mapstruct.AgentMapstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 智能体表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Slf4j
@Service
public class AgentServiceImpl implements IAgentService {

    @Resource
    private AgentMapper agentMapper;

    @Resource
    private AgentMapstruct agentMapstruct;

    @Resource
    private AgentRunSessionMapper agentRunSessionMapper;

    @Resource
    private AgentVersionMapper agentVersionMapper;

    @Resource
    private IAppUserClient appUserClient;

    @Resource
    private IAgentVersionExtendService agentVersionExtendService;

    @Resource
    private IAgentCommService agentCommService;

    @Resource
    private IAgentExtendService agentExtendService;

    @Resource
    private AgentExtendMapper agentExtendMapper;

    @Resource
    private IMarketClient marketClient;

    @Resource
    private AgentVersionExtendMapper agentVersionExtendMapper;

    @Resource
    private IAgentUseMcpService agentUseMcpService;

    @Resource
    private IAgentUseKbService agentUseKbService;

    @Resource
    private IAgentUseToolService agentUseToolService;

    @Resource
    private IAgentUseWorkflowService agentUseWorkflowService;

    @Resource
    private IAgentUseDictService agentUseDictService;

    @Transactional(readOnly = true)
    @Override
    public PageInfo<AgentPageVO> page(AgentPageDTO queryDto) {
        Page<Agent> page = new Page<>(queryDto.getPageNo(), queryDto.getPageSize());

        //获取有权限的智能体id集合
        List<Integer> listAgentId = agentCommService.queryGrantAgentIdList(queryDto.getPageSource());

        // agent查询
        LambdaQueryWrapper<Agent> queryWrapper = buildQuery(queryDto);;
        //queryWrapper.in(Agent::getAgentId, listAgentId);
        //queryWrapper.like(StringUtil.isNotBlank(queryDto.getKeywords()), Agent::getAgentName, queryDto.getKeywords());
        Page<Agent> result = this.agentMapper.selectPage(page, queryWrapper);

        List<AgentPageVO> agentPageVOList =  result.getRecords().stream().map(agent -> {
            AgentPageVO agentPageVO = new AgentPageVO();
            BeanUtils.copyProperties(agent, agentPageVO);
            AgentVersion agentVersion = agentVersionMapper.selectById(agent.getVersionId());
            if (Objects.nonNull(agentVersion)) {
                agentPageVO.setVersionName(agentVersion.getVersionNumber());
            }

            ResultVo<AppUserVO> appUserVOResultVo = appUserClient.getUserById(agent.getUserId());
            if (appUserVOResultVo.isSuccess() && Objects.nonNull(appUserVOResultVo.getData())) {
                agentPageVO.setCreateBy(appUserVOResultVo.getData().getUserName());
            }

            agentPageVO.setAgentMetadata(JsonUtils.parseObject(agent.getAgentMetadata(), AgentMetadataBO.class));
            agentPageVO.setUpdateTime(RelativeDateFormat.format(agent.getUpdateTime()));

            return agentPageVO;
        }).collect(Collectors.toList());

        return PageInfo.of(agentPageVOList);
    }

    @Transactional(readOnly = true)
    @Override
    public List<AgentVO> list(AgentQueryDTO queryDto) {
        QueryWrapper<Agent> queryWrapper = this.buildQuery(queryDto);
        return agentMapstruct.toVoList(this.agentMapper.selectList(queryWrapper));
    }

    @Transactional(readOnly = true)
    @Override
    public List<LastSessionAgentVO> queryLastSessionAgent() {
        List<LastSessionAgentVO> agentRunSessions = agentRunSessionMapper.queryLastSessionAgentByUserId(UserContext.getTenantId(), UserContext.getUserId());
        if(CollectionUtils.isEmpty(agentRunSessions)){
            return Lists.newArrayList();
        }

        //去重智能体
        List<LastSessionAgentVO> resultList = new ArrayList<>();
        agentRunSessions.forEach(session -> {
            if(!resultList.contains(session)){
                resultList.add(session);
            }
        });

        List<Integer> listAgentId = resultList.stream().map(LastSessionAgentVO::getAgentId).toList();

        AgentQueryDTO agentQueryDTO = new AgentQueryDTO();
        agentQueryDTO.setAgentIds(listAgentId);
        List<AgentVO> listAgent = this.list(agentQueryDTO);
        if(CollectionUtils.isEmpty(listAgent)){
            return Lists.newArrayList();
        }

        resultList.forEach(lastSessionAgentVO -> {
            Optional<AgentVO> first = listAgent.stream().filter(a -> a.getAgentId().equals(lastSessionAgentVO.getAgentId())).findFirst();
            first.ifPresent(agentVO -> {
                lastSessionAgentVO.setAgentName(agentVO.getAgentName());
                lastSessionAgentVO.setAgentSn(agentVO.getAgentSn());
                lastSessionAgentVO.setAgentStatus(agentVO.getAgentStatus());
                AgentMetadataBO agentMetadataBO = JsonUtils.parseObject(agentVO.getAgentMetadata(), AgentMetadataBO.class);
                lastSessionAgentVO.setAgentIcon(agentMetadataBO.getIcon());
            });

        });
        return resultList;
    }

    @Transactional(readOnly = true)
    @Override
    public List<AgentStatDetailVO> queryAgentStatDetail(AgentStatDetailQueryDTO dto) {
        List<Integer> agentIds = null;
        //根据条件查询智能体表
        if(StringUtils.isNotBlank(dto.getAgentName())){
            AgentQueryDTO agentQueryDTO = new AgentQueryDTO();
            agentQueryDTO.setAgentName(dto.getAgentName());
            agentQueryDTO.setAgentType(dto.getAgentType());
            List<AgentVO> list = list(agentQueryDTO);
            if(CollectionUtils.isEmpty(list)){
                return Lists.newArrayList();
            }
            agentIds = list.stream().map(AgentVO::getAgentId).distinct().toList();
        }

        //根据条件查询扩展表
        AgentVersionExtendQueryDTO agentVersionExtendQueryDTO = new AgentVersionExtendQueryDTO();
        agentVersionExtendQueryDTO.setItemName(dto.getItemName());
        agentVersionExtendQueryDTO.setItemValue(dto.getModelId().toString());
        agentVersionExtendQueryDTO.setAgentIds(agentIds);
        List<AgentVersionExtendVO> listAgentVersionExtendVO = agentVersionExtendService.list(agentVersionExtendQueryDTO);
        if(CollectionUtils.isEmpty(listAgentVersionExtendVO)){
            return Lists.newArrayList();
        }

        List<Integer> ids = listAgentVersionExtendVO.stream().map(AgentVersionExtendVO::getAgentId).toList();
        AgentQueryDTO agentQueryDTO = new AgentQueryDTO();
        agentQueryDTO.setAgentIds(ids);
        List<AgentVO> list = list(agentQueryDTO);
        if(CollectionUtils.isEmpty(list)){
            return Lists.newArrayList();
        }

        List<AgentStatDetailVO> agentVoList = agentMapstruct.toAgentVoList(list);
        agentVoList.forEach(agentVO -> {
            ResultVo<AppUserVO> appUserVOResultVo = appUserClient.getUserById(UserContext.getUserId());
            if (appUserVOResultVo.isSuccess() && Objects.nonNull(appUserVOResultVo.getData())) {
                agentVO.setCreateBy(appUserVOResultVo.getData().getUserName());
            }

            AgentVersion agentVersion = agentVersionMapper.selectById(agentVO.getVersionId());
            if (Objects.nonNull(agentVersion)) {
                agentVO.setVersionName(agentVersion.getVersionNumber());
            }
        });
        return agentVoList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void create(AgentCreateDTO dto) {
        Agent agent = new Agent();
        agent.setAgentName(dto.getAgentName());
        agent.setAgentDesc(dto.getAgentDesc());
        agent.setAgentMetadata(JsonUtils.toJsonString(dto.getAgentMetadata()));
        agent.setAgentType(dto.getAgentType());

        agent.setAgentSn(UUIDUtil.genRandomSn("agent"));
        agent.setCreateTime(new Date());
        agent.setUpdateTime(new Date());
        agent.setAgentStatus(AgentStatusEnum.DEV.getCode());
        agent.setUserId(UserContext.getUserId());
        agent.setTenantId(UserContext.getTenantId());
        agentMapper.insert(agent);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AgentUpdateDTO dto) {
        Agent agent = agentMapper.selectByAgentSn(dto.getAgentSn());
        BusinessAssertUtil.notNull(agent, "找不到AgentSn为 " + dto.getAgentSn() + " 的记录");

        Agent entityList = agentMapstruct.toAgentUpdateEntity(dto);
        entityList.setUpdateTime(new Date());
        agentMapper.updateById(entityList);

        if (CollectionUtils.isNotEmpty(dto.getRecommends())) {
            agentExtendService.setItem(agent.getAgentId(),
                    AgentExtendNameEnum.RECOMMENDS.getCode(),
                    JsonUtils.toJsonString(dto.getRecommends()));
        }

        if (StringUtil.isNotBlank(dto.getExplain())) {
            agentExtendService.setItem(agent.getAgentId(),
                    AgentExtendNameEnum.EXPLAIN.getCode(),
                    dto.getExplain());
        }
    }

    @Transactional(readOnly = true)
    @Override
    public AgentVO getAgentBySn(String sn) {
        BusinessAssertUtil.notNull(sn, "智能体编号不能为空");

        Agent entity = agentMapper.selectByAgentSn(sn);
        BusinessAssertUtil.notNull(entity, "找不到AgentId为 " + sn + " 的记录");

        return agentMapstruct.toVo(entity);
    }

    @Transactional(readOnly = true)
    @Override
    public Agent get(Integer id) {
        BusinessAssertUtil.notNull(id, "AgentId不能为空");

        Agent entity = agentMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到AgentId为 " + id + " 的记录");
        return entity;
    }

    @Override
    public AgentDetailVO detailById(Integer agentId) {
        Agent agent = agentMapper.selectById(agentId);
        if (Objects.isNull(agent)) {
            return null;
        }
        return this.toDetailVO(agent);
    }

    @Override
    public void delete(String agentSn) {
        this.changeAgentStatus(agentSn, VersionStatusEnum.DELETED);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void enable(AgentEnableDTO dto) {
        VersionStatusEnum enable = VersionStatusEnum.STOP;
        if (dto.getStatus()) {
            enable = VersionStatusEnum.ENABLE;
        }
        this.changeAgentStatus(dto.getAgentSn(), enable);
    }

    void changeAgentStatus(String agentSn, VersionStatusEnum versionStatusEnum) {
        Agent agent = agentMapper.selectByAgentSn(agentSn);
        AssertUtil.isNotNull(agent, "智能体不存在");

        // 停用
        if (VersionStatusEnum.STOP.getCode().equals(versionStatusEnum.getCode())) {
            agent.setAgentStatus(AgentStatusEnum.STOP.getCode());
            changeMarketStatus(agent.getAgentId(), agent.getVersionId(), versionStatusEnum);
            agentMapper.updateById(agent);
        }

        // 启用
        if (VersionStatusEnum.ENABLE.getCode().equals(versionStatusEnum.getCode())) {
            // 有版本则为发布发布状态
            if (Objects.nonNull(agent.getVersionId())) {
                agent.setAgentStatus(AgentStatusEnum.PUBLISH.getCode());
            }
            agentMapper.updateById(agent);
        }

        // 删除
        if (VersionStatusEnum.DELETED.getCode().equals(versionStatusEnum.getCode())) {
            agent.setAgentStatus(AgentStatusEnum.DELETE.getCode());
            agentMapper.updateById(agent);

            // 删除所有会话
            LambdaUpdateWrapper<AgentRunSession> agentRunSessionLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            agentRunSessionLambdaUpdateWrapper.eq(AgentRunSession::getAgentId, agentSn);
            agentRunSessionLambdaUpdateWrapper.set(AgentRunSession::getSessionStatus, SessionStatusEnum.DELETE.getCode());
            agentRunSessionMapper.update(agentRunSessionLambdaUpdateWrapper);

            // 将关联的Agent扩展设置为失效
            LambdaUpdateWrapper<AgentExtend> agentExtendLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            agentExtendLambdaUpdateWrapper.eq(AgentExtend::getAgentId, agentSn);
            agentExtendLambdaUpdateWrapper.set(AgentExtend::getItemStatus,0);
            agentExtendMapper.update(agentExtendLambdaUpdateWrapper);
        }

        changeMarketStatus(agent.getAgentId(), agent.getVersionId(), versionStatusEnum);
        changeAgentVersion(agent.getAgentId(), versionStatusEnum);
    }

    // AgentVersion变更
    void changeAgentVersion (Integer versionId, VersionStatusEnum versionStatusEnum) {
        if (Objects.isNull(versionId)) {
           return;
        }

        AgentVersion agentVersion = agentVersionMapper.selectById(versionId);
        agentVersion.setVersionStatus(versionStatusEnum.getCode());
        agentVersionMapper.updateById(agentVersion);

        // 删除版本扩展表信息
        if (VersionStatusEnum.DELETED.getCode().equals(versionStatusEnum.getCode())) {
            LambdaUpdateWrapper<AgentVersionExtend> versionUpdateWrapper = new LambdaUpdateWrapper<>();
            versionUpdateWrapper.eq(AgentVersionExtend::getAgentId, agentVersion.getAgentId());
            versionUpdateWrapper.set(AgentVersionExtend::getItemStatus, 0);
            agentVersionExtendMapper.update(versionUpdateWrapper);
        }
    }

    // 变更市场状态
    void changeMarketStatus(Integer agentId, Integer versionId, VersionStatusEnum versionStatusEnum) {
        try {
            if (Arrays.asList(VersionStatusEnum.DELETED.getCode(), VersionStatusEnum.STOP.getCode()).contains(versionStatusEnum.getCode())) {
                marketClient.downAgent(agentId, versionId);
            } else if (VersionStatusEnum.ENABLE.getCode().equals(versionStatusEnum.getCode())) {
                marketClient.upAgent(agentId, versionId);
            }
        } catch (Exception e) {
            log.info("未发布到市场:{}", e.getMessage());
        }
    }

    @Override
    public AgentDetailVO detail(String agentSn) {
        Agent agent = agentMapper.selectByAgentSn(agentSn);
        if (Objects.isNull(agent)) {
            return null;
        }
        return toDetailVO(agent);
    }

    AgentDetailVO toDetailVO(Agent agent) {
        AgentDetailVO detail = agentMapstruct.toDetail(agent);

        // 获取推荐问题
        List<AgentExtend> agentExtends = agentExtendMapper.findByAgentId(agent.getAgentId());
        Map<String, String> agentExtendItemValue = AgentExtendHelper.getAgentExtendItemValue(agentExtends);
        String recommends = agentExtendItemValue.get(AgentExtendNameEnum.RECOMMENDS.getCode());
        if (StringUtil.isNotBlank(recommends)) {
            detail.setRecommends(JsonUtils.parseArray(recommends, String.class));
        }

        // 使用说明
        detail.setExplain(agentExtendItemValue.get(AgentExtendNameEnum.EXPLAIN.getCode()));

        // 记忆开关
        String memory = agentExtendItemValue.get(AgentExtendNameEnum.MEMORY.getCode());
        if (StringUtil.isNotBlank(recommends)) {
            detail.setMemorySwitch(memory);
        }

        // 智能引导
        this.setGuide(detail, agent);

        // 用户信息
        ResultVo<AppUserVO> appUserVOResultVo = appUserClient.getUserById(agent.getUserId());
        if (Objects.nonNull(appUserVOResultVo)) {
            detail.setCreateBy(appUserVOResultVo.getData().getUserName());
        }

        // 版本信息
        AgentVersion agentVersion = agentVersionMapper.selectById(agent.getVersionId());
        if (Objects.nonNull(agentVersion)) {
            detail.setVersionSn(agentVersion.getVersionNumber());
            detail.setVersionOnSale(agentVersion.getVersionOnsale());

            if (AgentTypeEnum.MASTER.getCode().equals(agent.getAgentType())) {
                this.setMaster(detail, agentVersion);
            }

            if (Arrays.asList(AgentTypeEnum.WORK_FLOW.getCode(), AgentTypeEnum.CHAT_AGENT.getCode()).contains(agent.getAgentType())) {
                this.setWorkFlow(detail, agentVersion);
            }
        }

        return detail;
    }

    // 使用引导中的知识库
    private List<AgentDetailVO.GuideKnowledge> getKnowledge(AgentDetailVO detail) {
        return List.of();
    }

    // 使用引导中的技能
    private List<AgentDetailVO.GuideSkill> getSkill(AgentDetailVO detail) {
        return List.of();
    }

    // 使用引导
    private void setGuide(AgentDetailVO detail, Agent agent) {
        AgentDetailVO.Guide guide = new AgentDetailVO.Guide();
        if (AgentTypeEnum.CHAT_AGENT.getCode().equals(agent.getAgentType())) {
            guide.setCapacities(List.of("知识问答","文档检索","意图理解", "任务规划", "使用技能", "执行任务"));
        }
        guide.setSkill(this.getSkill(detail));
        guide.setKnowledge(this.getKnowledge(detail));
        detail.setGuide(guide);
    }

    /**
     * 智能规划
     * @param detail
     * @param agentVersion
     */
    private void setMaster(AgentDetailVO detail, AgentVersion agentVersion) {
        List<AgentVersionExtend> agentVersionExtends = agentVersionExtendMapper.findByVersionId(agentVersion.getVersionId());
        Map<String, String> itemValue = AgentVersionExtendHelper.getVersionExtendItemValue(agentVersionExtends);
        MasterAddDTO master = detail.getMaster();
        master.setPromptVars(JsonUtils.parseArray(itemValue.get(AgentVersionExtendNameEnum.VAR.getCode()), MasterPromptVarBO.class));
        master.setSkills(Stream.of(agentUseMcpService.toDetail(agentVersion.getAgentId()),
                        agentUseToolService.toDetail(agentVersion.getAgentId()))
                .flatMap(List::stream).collect(Collectors.toList()));
        master.setSensitives(agentUseDictService.toDetail(agentVersion.getVersionId()));
        master.setDicts(agentUseDictService.toDetail(agentVersion.getVersionId()));
        master.setKnowledgeIds(agentUseKbService.toDetail(agentVersion.getVersionId()));
        master.setModel(JsonUtils.parseObject(itemValue.get(AgentVersionExtendNameEnum.VAR.getCode()), MasterModelBO.class));
        detail.setMaster(master);
    }

    /**
     * 工作流与对话流
     * @param detail
     * @param agentVersion
     * @param agent
     */
    private void setWorkFlow(AgentDetailVO detail, AgentVersion agentVersion) {
        List<AgentVersionExtend> agentVersionExtends = agentVersionExtendMapper.findByVersionId(agentVersion.getVersionId());
        WorkFlowAddDTO workFlow = detail.getWorkFlow();
        Map<String, String> itemValue = AgentVersionExtendHelper.getVersionExtendItemValue(agentVersionExtends);

        AgentUseWorkflowVO agentUseWorkflowVO = agentUseWorkflowService.selectByVersion(agentVersion.getAgentId());
        if (Objects.nonNull(agentUseWorkflowVO)) {
            workFlow.setExtensions(JsonUtils.parseObject(agentUseWorkflowVO.getFlowExtensions(), JSONObject.class));
            workFlow.setVariables(JsonUtils.parseArray(agentUseWorkflowVO.getFlowVariables(), FlowVariableBO.class));
            workFlow.setStartVariables(JsonUtils.parseArray(agentUseWorkflowVO.getFlowStartVariables(), FlowVariableBO.class));
            workFlow.setEndVariables(JsonUtils.parseArray(agentUseWorkflowVO.getFlowEndVariables(), FlowVariableBO.class));
            workFlow.setDefinition(JsonUtils.parseArray(agentUseWorkflowVO.getFlowDefinition(), FlowElementBO.class));
        }

        String shortMemory = itemValue.get(AgentVersionExtendNameEnum.SHORT_MEMORY.getCode());
        if (StringUtil.isNotBlank(shortMemory)) {
            workFlow.setShortMemory(JsonUtils.parseArray(shortMemory, ShortMemoryBO.class));
        }

        String longMemory = itemValue.get(AgentVersionExtendNameEnum.LONG_MEMORY.getCode());
        if (StringUtil.isNotBlank(shortMemory)) {
            workFlow.setLongMemory(JsonUtils.parseObject(longMemory, LongMemoryBO.class));
        }

        detail.setWorkFlow(workFlow);
    }

    private QueryWrapper<Agent> buildQuery(AgentQueryDTO queryDto) {
        QueryWrapper<Agent> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Agent::getTenantId,UserContext.getTenantId());
        queryWrapper.lambda().eq(Agent::getUserId,UserContext.getUserId());
        queryWrapper.lambda().eq(Objects.nonNull(queryDto.getAgentStatus()),Agent::getAgentStatus,queryDto.getAgentStatus());
        queryWrapper.lambda().in(CollectionUtils.isNotEmpty(queryDto.getAgentIds()),Agent::getAgentId,queryDto.getAgentIds());
        queryWrapper.lambda().eq(Objects.nonNull(queryDto.getAgentType()),Agent::getAgentType,queryDto.getAgentType());
        queryWrapper.lambda().like(Objects.nonNull(queryDto.getAgentName()),Agent::getAgentName,queryDto.getAgentName());
        return queryWrapper;
    }

    private LambdaQueryWrapper<Agent> buildQuery(AgentPageDTO queryDto) {
        LambdaQueryWrapper<Agent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Agent::getTenantId,UserContext.getTenantId());
        queryWrapper.eq(Agent::getUserId,UserContext.getUserId());
        queryWrapper.eq(Objects.nonNull(queryDto.getAgentStatus()),Agent::getAgentStatus,queryDto.getAgentStatus());
        queryWrapper.eq(Objects.nonNull(queryDto.getAgentType()),Agent::getAgentType,queryDto.getAgentType());
        queryWrapper.like(Objects.nonNull(queryDto.getAgentName()),Agent::getAgentName,queryDto.getAgentName());
        if(org.apache.commons.lang3.StringUtils.isNotBlank(queryDto.getKeywords())) {
            queryWrapper.and(wrapper ->
                    wrapper.like(Agent::getAgentName, queryDto.getKeywords()))
                    .or().like(Agent::getAgentDesc, queryDto.getKeywords())
                    .or().like(Agent::getAgentMetadata, queryDto.getKeywords());
        }
        return queryWrapper;
    }

}