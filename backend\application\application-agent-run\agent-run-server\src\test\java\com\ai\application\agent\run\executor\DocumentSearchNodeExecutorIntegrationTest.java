package com.ai.application.agent.run.executor;

import com.ai.application.agent.run.dto.DocumentSearchResultDTO;
import com.ai.application.agent.run.service.IDocumentSearchService;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 文档知识检索节点执行器集成测试
 * 模拟真实的工作流场景
 */
@Slf4j
@SpringJUnitConfig
class DocumentSearchNodeExecutorIntegrationTest {

    @MockBean
    private IDocumentSearchService documentSearchService;

    private DocumentSearchNodeExecutor documentSearchNodeExecutor;
    private WorkflowContext workflowContext;

    @BeforeEach
    void setUp() {
        documentSearchNodeExecutor = new DocumentSearchNodeExecutor();
        setupWorkflowContext();
    }

    private void setupWorkflowContext() {
        // 模拟真实的工作流上下文
        workflowContext = new WorkflowContext();
        workflowContext.setWorkflowInstanceId(12345L);
        workflowContext.setCurrentNodeKey("document_search_001");
        
        // 设置全局变量
        Map<String, Object> globalVars = new HashMap<>();
        globalVars.put("authorization", "Bearer test-token");
        globalVars.put("tenantName", "TEST_TENANT");
        globalVars.put("searchQuery", "人工智能相关文档");
        globalVars.put("knowledgeBase", "7c1d4ec1-fba9-458c-aa3a-a72bbe77f99f");
        workflowContext.setGlobalVars(globalVars);

        // 创建节点上下文
        NodeContext nodeContext = new NodeContext();
        nodeContext.setNodeKey("document_search_001");
        nodeContext.setStatus(NodeStatus.INIT);
        nodeContext.setOutput(new HashMap<>());

        // 设置节点定义
        Map<String, Object> nodeDefinition = new HashMap<>();
        
        // 输入参数
        Map<String, Object> inputParameters = new HashMap<>();
        inputParameters.put("type", "embedding");
        inputParameters.put("knowledgeInventorySn", "$knowledgeBase");
        inputParameters.put("knowledgeType", "sn");
        inputParameters.put("searchContent", "documentName");
        inputParameters.put("searchKnowledgeContent", "$searchQuery");
        inputParameters.put("topK", "10");
        inputParameters.put("conditions", Arrays.asList(
                Map.of("target", null),
                Map.of("target", null)
        ));
        nodeDefinition.put("inputParameters", inputParameters);
        
        // 输出参数
        Map<String, Object> outputParameters = new HashMap<>();
        outputParameters.put("docFile", "2309baf83c");
        outputParameters.put("fragments", "searchFragments");
        outputParameters.put("totalCount", "searchCount");
        nodeDefinition.put("outputParameters", outputParameters);
        
        nodeContext.setNodeDefinition(nodeDefinition);
        workflowContext.getNodeContexts().put("document_search_001", nodeContext);
    }

    @Test
    void testRealWorldDocumentSearchWorkflow() {
        // 模拟文档检索服务返回
        DocumentSearchResultDTO mockResult = DocumentSearchResultDTO.builder()
                .output("[{\"fragmentId\":\"frag1\",\"content\":\"人工智能是一种模拟人类智能的技术\"},{\"fragmentId\":\"frag2\",\"content\":\"机器学习是人工智能的核心技术\"}]")
                .success(true)
                .totalCount(2)
                .fragments(Arrays.asList(
                        DocumentSearchResultDTO.DocumentFragmentDTO.builder()
                                .fragmentId("frag1")
                                .documentId("doc1")
                                .fileName("ai_introduction.pdf")
                                .fileSn("file_sn_001")
                                .content("人工智能是一种模拟人类智能的技术，它能够让机器具备学习、推理、感知和决策的能力。")
                                .score(0.95)
                                .pageNumber(1)
                                .knowledgeInventorySn("7c1d4ec1-fba9-458c-aa3a-a72bbe77f99f")
                                .method("embedding")
                                .createTime(System.currentTimeMillis())
                                .build(),
                        DocumentSearchResultDTO.DocumentFragmentDTO.builder()
                                .fragmentId("frag2")
                                .documentId("doc2")
                                .fileName("ml_basics.pdf")
                                .fileSn("file_sn_002")
                                .content("机器学习是人工智能的核心技术，通过算法让计算机从数据中学习模式。")
                                .score(0.88)
                                .pageNumber(3)
                                .knowledgeInventorySn("7c1d4ec1-fba9-458c-aa3a-a72bbe77f99f")
                                .method("embedding")
                                .createTime(System.currentTimeMillis())
                                .build()
                ))
                .build();

        when(documentSearchService.executeDocumentSearch(any(), anyString())).thenReturn(ResultVo.data(mockResult));

        // 执行文档检索节点
        assertDoesNotThrow(() -> documentSearchNodeExecutor.execute(workflowContext));

        // 验证执行结果
        NodeContext nodeContext = workflowContext.getNodeContexts().get("document_search_001");
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        
        // 验证输出结果
        assertNotNull(nodeContext.getOutput().get("docFile"));
        assertNotNull(nodeContext.getOutput().get("fragments"));
        assertEquals(2, nodeContext.getOutput().get("totalCount"));
        
        // 验证全局变量被正确设置
        assertNotNull(workflowContext.getGlobalVars().get("2309baf83c"));
        assertNotNull(workflowContext.getGlobalVars().get("searchFragments"));
        assertEquals(2, workflowContext.getGlobalVars().get("searchCount"));
        
        // 验证节点执行时间被设置
        assertNotNull(nodeContext.getEndTime());
    }

    @Test
    void testDocumentSearchWithMultipleSearchMethods() {
        // 修改输入参数使用多种检索方法
        NodeContext nodeContext = workflowContext.getNodeContexts().get("document_search_001");
        Map<String, Object> nodeDefinition = nodeContext.getNodeDefinition();
        Map<String, Object> inputParameters = (Map<String, Object>) nodeDefinition.get("inputParameters");
        
        // 设置多种检索方法的TopK
        inputParameters.put("embedding", "5");
        inputParameters.put("keyword", "3");
        inputParameters.put("text", "2");

        DocumentSearchResultDTO mockResult = DocumentSearchResultDTO.builder()
                .output("[]")
                .success(true)
                .totalCount(0)
                .fragments(Collections.emptyList())
                .build();

        when(documentSearchService.executeDocumentSearch(any(), anyString())).thenReturn(ResultVo.data(mockResult));

        // 执行文档检索节点
        assertDoesNotThrow(() -> documentSearchNodeExecutor.execute(workflowContext));

        // 验证执行结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        assertEquals("[]", nodeContext.getOutput().get("docFile"));
    }

    @Test
    void testDocumentSearchWithExpandMode() {
        // 修改输入参数使用扩展检索模式
        NodeContext nodeContext = workflowContext.getNodeContexts().get("document_search_001");
        Map<String, Object> nodeDefinition = nodeContext.getNodeDefinition();
        Map<String, Object> inputParameters = (Map<String, Object>) nodeDefinition.get("inputParameters");
        
        // 设置扩展检索模式
        inputParameters.put("searchModel", "expand");
        inputParameters.put("expandParams", Map.of(
                "fragmentType", "paragraph",
                "length", 500,
                "forward", 2,
                "backward", 2,
                "expand", "[{\"id\":\"expand1\",\"content\":\"扩展内容\"}]"
        ));

        DocumentSearchResultDTO mockResult = DocumentSearchResultDTO.builder()
                .output("[{\"fragmentId\":\"expand_frag1\",\"content\":\"扩展检索返回的内容\"}]")
                .success(true)
                .totalCount(1)
                .fragments(Arrays.asList(
                        DocumentSearchResultDTO.DocumentFragmentDTO.builder()
                                .fragmentId("expand_frag1")
                                .content("扩展检索返回的详细内容")
                                .method("expand")
                                .score(0.92)
                                .build()
                ))
                .build();

        when(documentSearchService.executeDocumentSearch(any(), anyString())).thenReturn(ResultVo.data(mockResult));

        // 执行文档检索节点
        assertDoesNotThrow(() -> documentSearchNodeExecutor.execute(workflowContext));

        // 验证执行结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        assertTrue(nodeContext.getOutput().get("docFile").toString().contains("expand_frag1"));
    }

    @Test
    void testDocumentSearchWithManualRetriever() {
        // 修改输入参数使用手动检索模式
        NodeContext nodeContext = workflowContext.getNodeContexts().get("document_search_001");
        Map<String, Object> nodeDefinition = nodeContext.getNodeDefinition();
        Map<String, Object> inputParameters = (Map<String, Object>) nodeDefinition.get("inputParameters");
        
        // 设置手动检索模式
        inputParameters.put("searchModel", "all");
        inputParameters.put("searchKnowledge", "[{\"id\":\"manual1\",\"content\":\"手动指定的知识内容\"}]");

        DocumentSearchResultDTO mockResult = DocumentSearchResultDTO.builder()
                .output("[{\"fragmentId\":\"manual_frag1\",\"content\":\"手动检索返回的内容\"}]")
                .success(true)
                .totalCount(1)
                .fragments(Arrays.asList(
                        DocumentSearchResultDTO.DocumentFragmentDTO.builder()
                                .fragmentId("manual_frag1")
                                .content("手动检索返回的详细内容")
                                .method("manual")
                                .score(1.0)
                                .build()
                ))
                .build();

        when(documentSearchService.executeDocumentSearch(any(), anyString())).thenReturn(ResultVo.data(mockResult));

        // 执行文档检索节点
        assertDoesNotThrow(() -> documentSearchNodeExecutor.execute(workflowContext));

        // 验证执行结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        assertTrue(nodeContext.getOutput().get("docFile").toString().contains("manual_frag1"));
    }

    @Test
    void testDocumentSearchFailure() {
        // 模拟文档检索服务失败
        when(documentSearchService.executeDocumentSearch(any(), anyString()))
                .thenReturn(ResultVo.error("文档检索服务异常"));

        // 执行文档检索节点并验证异常
        assertThrows(Exception.class, () -> documentSearchNodeExecutor.execute(workflowContext));

        // 验证节点状态
        NodeContext nodeContext = workflowContext.getNodeContexts().get("document_search_001");
        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
        assertNotNull(nodeContext.getErrorMsg());
        assertTrue(nodeContext.getErrorMsg().contains("文档知识检索执行失败"));
    }

    @Test
    void testRealWorldDocumentSearchScenarioFromBackend() {
        // 模拟真实的 backend 场景
        // 基于您提供的真实入参进行测试
        workflowContext.getGlobalVars().put("realSearchContent", "adf");
        workflowContext.getGlobalVars().put("realKnowledgeBase", "7c1d4ec1-fba9-458c-aa3a-a72bbe77f99f");

        // 修改文档检索节点配置（完全按照 backend 的真实配置）
        NodeContext nodeContext = workflowContext.getNodeContexts().get("document_search_001");
        Map<String, Object> nodeDefinition = nodeContext.getNodeDefinition();
        Map<String, Object> inputParameters = (Map<String, Object>) nodeDefinition.get("inputParameters");
        
        // 设置真实的 backend 参数
        inputParameters.put("type", "embedding");
        inputParameters.put("knowledgeInventorySn", "7c1d4ec1-fba9-458c-aa3a-a72bbe77f99f");
        inputParameters.put("knowledgeType", "sn");
        inputParameters.put("topK", null);
        inputParameters.put("searchContent", "documentName");
        inputParameters.put("searchKnowledgeContent", "adf");
        inputParameters.put("conditions", Arrays.asList(
                Map.of("target", null),
                Map.of("target", null)
        ));

        // 设置输出参数（按照 backend 的真实配置）
        Map<String, Object> outputParameters = (Map<String, Object>) nodeDefinition.get("outputParameters");
        outputParameters.put("docFile", "2309baf83c");

        // 模拟文档检索服务返回
        DocumentSearchResultDTO mockResult = DocumentSearchResultDTO.builder()
                .output("[{\"fragmentId\":\"real_frag1\",\"content\":\"根据adf检索到的相关文档片段\"}]")
                .success(true)
                .totalCount(1)
                .fragments(Arrays.asList(
                        DocumentSearchResultDTO.DocumentFragmentDTO.builder()
                                .fragmentId("real_frag1")
                                .documentId("real_doc1")
                                .fileName("adf_related_document.pdf")
                                .fileSn("real_file_sn_001")
                                .content("根据adf关键词检索到的相关文档片段内容")
                                .score(0.89)
                                .pageNumber(2)
                                .knowledgeInventorySn("7c1d4ec1-fba9-458c-aa3a-a72bbe77f99f")
                                .method("embedding")
                                .createTime(System.currentTimeMillis())
                                .build()
                ))
                .build();

        when(documentSearchService.executeDocumentSearch(any(), anyString())).thenReturn(ResultVo.data(mockResult));

        // 执行文档检索节点
        assertDoesNotThrow(() -> documentSearchNodeExecutor.execute(workflowContext));

        // 验证执行结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        assertNotNull(nodeContext.getOutput().get("docFile"));
        assertNotNull(nodeContext.getOutput().get("fragments"));
        
        // 验证全局变量被正确设置（按照 backend 的输出变量）
        assertNotNull(workflowContext.getGlobalVars().get("2309baf83c"));
        
        // 验证节点执行时间被设置
        assertNotNull(nodeContext.getEndTime());
        
        log.info("Real world backend scenario test completed successfully");
    }
}
