package com.ai.application.agent.base.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

/**
 * 智能体运行历史会话表
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@Schema(name = "")
public class SessionHistoryVO {
    /**
     * 会话session
     */
    @Schema(description = "会话session")
    private String sessionSn;

    /**
     * 会话标题(智能生成)
     */
    @Schema(description = "会话标题(智能生成)")
    private String sessionTitle;

    /**
     * 最后运行时间
     */
    @Schema(description = "最后运行时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private Date lastRunTime;
}