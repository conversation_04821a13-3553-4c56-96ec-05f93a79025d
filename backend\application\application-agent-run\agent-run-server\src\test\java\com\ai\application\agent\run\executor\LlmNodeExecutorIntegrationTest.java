package com.ai.application.agent.run.executor;

import com.ai.application.agent.run.dto.ProcessChatDTO;
import com.ai.application.agent.run.service.ILlmChatService;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * LLM 节点执行器集成测试
 * 模拟真实的工作流场景
 */
@SpringJUnitConfig
class LlmNodeExecutorIntegrationTest {

    @MockBean
    private ILlmChatService llmChatService;

    private LlmNodeExecutor llmNodeExecutor;
    private WorkflowContext workflowContext;

    @BeforeEach
    void setUp() {
        llmNodeExecutor = new LlmNodeExecutor();
        // 手动注入模拟的服务（在真实环境中由 Spring 自动注入）
        // llmNodeExecutor.setLlmChatService(llmChatService);
        
        setupWorkflowContext();
    }

    private void setupWorkflowContext() {
        // 模拟真实的工作流上下文，基于您提供的组件定义
        workflowContext = new WorkflowContext();
        workflowContext.setWorkflowInstanceId(89L);
        workflowContext.setCurrentNodeKey("f3a608265f");
        
        // 设置全局变量
        Map<String, Object> globalVars = new HashMap<>();
        globalVars.put("authorization", "Bearer test-token");
        globalVars.put("d3ef893c5c", ""); // 输出变量初始值
        globalVars.put("ebf173b133", "用户输入的文本"); // 用户输入
        workflowContext.setGlobalVars(globalVars);

        // 创建节点上下文
        NodeContext nodeContext = new NodeContext();
        nodeContext.setNodeKey("f3a608265f");
        nodeContext.setStatus(NodeStatus.INIT);
        nodeContext.setOutput(new HashMap<>());

        // 设置节点定义（基于您提供的真实配置）
        Map<String, Object> nodeDefinition = new HashMap<>();
        
        // 输入参数
        Map<String, Object> inputParameters = new HashMap<>();
        inputParameters.put("model", "localModel");
        inputParameters.put("prompt", "s z d f g h j");
        inputParameters.put("sysPrompt", "");
        inputParameters.put("temperature", "0.6");
        nodeDefinition.put("inputParameters", inputParameters);
        
        // 输出参数
        Map<String, Object> outputParameters = new HashMap<>();
        outputParameters.put("message", "d3ef893c5c");
        nodeDefinition.put("outputParameters", outputParameters);
        
        nodeContext.setNodeDefinition(nodeDefinition);
        workflowContext.getNodeContexts().put("f3a608265f", nodeContext);
    }

    @Test
    void testRealWorldLlmWorkflow() {
        // 模拟 LLM 服务返回
        String expectedResponse = "基于您的输入 's z d f g h j'，这是大模型 localModel 的专业回复。";
        when(llmChatService.chat(any(ProcessChatDTO.class))).thenReturn(ResultVo.data(expectedResponse));

        // 执行 LLM 节点
        assertDoesNotThrow(() -> llmNodeExecutor.execute(workflowContext));

        // 验证执行结果
        NodeContext nodeContext = workflowContext.getNodeContexts().get("f3a608265f");
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        assertEquals(expectedResponse, nodeContext.getOutput().get("message"));
        assertEquals(true, nodeContext.getOutput().get("success"));
        
        // 验证全局变量被正确设置
        assertEquals(expectedResponse, workflowContext.getGlobalVars().get("d3ef893c5c"));
        
        // 验证节点执行时间被设置
        assertNotNull(nodeContext.getEndTime());
    }

    @Test
    void testLlmWorkflowWithVariableInput() {
        // 修改输入参数使用变量替换
        NodeContext nodeContext = workflowContext.getNodeContexts().get("f3a608265f");
        Map<String, Object> nodeDefinition = nodeContext.getNodeDefinition();
        Map<String, Object> inputParameters = (Map<String, Object>) nodeDefinition.get("inputParameters");
        
        // 使用变量作为 prompt
        inputParameters.put("prompt", "$ebf173b133");
        
        // 模拟 LLM 服务返回
        String expectedResponse = "针对您的问题：用户输入的文本，这是AI的回复。";
        when(llmChatService.chat(any(ProcessChatDTO.class))).thenReturn(ResultVo.data(expectedResponse));

        // 执行 LLM 节点
        assertDoesNotThrow(() -> llmNodeExecutor.execute(workflowContext));

        // 验证执行结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        assertEquals(expectedResponse, workflowContext.getGlobalVars().get("d3ef893c5c"));
    }

    @Test
    void testLlmWorkflowWithSystemPrompt() {
        // 修改输入参数添加系统提示词
        NodeContext nodeContext = workflowContext.getNodeContexts().get("f3a608265f");
        Map<String, Object> nodeDefinition = nodeContext.getNodeDefinition();
        Map<String, Object> inputParameters = (Map<String, Object>) nodeDefinition.get("inputParameters");
        
        // 设置系统提示词
        inputParameters.put("sysPrompt", "你是一个专业的AI助手，请提供有用的回答");
        
        // 模拟 LLM 服务返回
        String expectedResponse = "作为专业AI助手，我为您提供以下回答...";
        when(llmChatService.chat(any(ProcessChatDTO.class))).thenReturn(ResultVo.data(expectedResponse));

        // 执行 LLM 节点
        assertDoesNotThrow(() -> llmNodeExecutor.execute(workflowContext));

        // 验证执行结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        assertEquals(expectedResponse, workflowContext.getGlobalVars().get("d3ef893c5c"));
    }

    @Test
    void testLlmWorkflowFailure() {
        // 模拟 LLM 服务失败
        when(llmChatService.chat(any(ProcessChatDTO.class))).thenReturn(ResultVo.fail("LLM service error"));

        // 执行 LLM 节点并验证异常
        assertThrows(Exception.class, () -> llmNodeExecutor.execute(workflowContext));

        // 验证节点状态
        NodeContext nodeContext = workflowContext.getNodeContexts().get("f3a608265f");
        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
        assertNotNull(nodeContext.getErrorMsg());
        assertTrue(nodeContext.getErrorMsg().contains("LLM执行失败"));
    }

    @Test
    void testCompleteWorkflowScenario() {
        // 模拟完整的工作流场景
        // 1. 输入节点设置用户输入
        workflowContext.getGlobalVars().put("ebf173b133", "请帮我写一首诗");
        workflowContext.getGlobalVars().put("d3ef893c5c", "关于春天");

        // 2. 修改 LLM 节点使用动态输入
        NodeContext nodeContext = workflowContext.getNodeContexts().get("f3a608265f");
        Map<String, Object> nodeDefinition = nodeContext.getNodeDefinition();
        Map<String, Object> inputParameters = (Map<String, Object>) nodeDefinition.get("inputParameters");
        
        inputParameters.put("prompt", "$ebf173b133");
        inputParameters.put("sysPrompt", "你是一个诗人，请创作优美的诗歌");
        inputParameters.put("model", "gpt-4");
        inputParameters.put("temperature", "0.8");

        // 3. 模拟 LLM 返回诗歌
        String poemResponse = "春风轻拂柳絮飞，\n花开满园蝶舞归。\n绿意盎然生机现，\n诗意盎然入心扉。";
        when(llmChatService.chat(any(ProcessChatDTO.class))).thenReturn(ResultVo.data(poemResponse));

        // 4. 执行 LLM 节点
        assertDoesNotThrow(() -> llmNodeExecutor.execute(workflowContext));

        // 5. 验证完整的执行结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        assertEquals(poemResponse, nodeContext.getOutput().get("message"));
        assertEquals(poemResponse, workflowContext.getGlobalVars().get("d3ef893c5c"));
        
        // 6. 验证可以传递给下一个节点
        assertNotNull(workflowContext.getGlobalVars().get("d3ef893c5c"));
        assertTrue(workflowContext.getGlobalVars().get("d3ef893c5c").toString().contains("春风"));
    }
}
