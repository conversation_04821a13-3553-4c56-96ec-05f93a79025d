package com.ai.application.agent.base.mapper;

import com.ai.application.agent.base.api.dto.AgentStatDTO;
import com.ai.application.agent.base.api.entity.AgentRunSession;
import com.ai.application.agent.base.api.vo.AgentRunSessionVO;
import com.ai.application.agent.base.api.vo.AgentStatVO;
import com.ai.application.agent.base.api.vo.LastSessionAgentVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 智能体运行会话表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface AgentRunSessionMapper extends BaseMapper<AgentRunSession> {
    @Select("select * from agent_run_session where session_sn = #{sessionSn}")
    AgentRunSession selectBySessionSn(@Param("sessionSn") String sessionSn);
    /**
     * 查询智能体运行会话表
     *
     * @return
     */
    List<AgentRunSessionVO> selectAgentRunSessionList();

    @Select("SELECT agent_id,session_sn, last_run_time lastSessionTime FROM `agent_run_session` WHERE tenant_id=#{tenantId} and user_id=#{userId} ORDER BY last_run_time DESC")
    List<LastSessionAgentVO> queryLastSessionAgentByUserId(@Param("tenantId") Integer tenantId, @Param("userId") Integer userId);

    List<AgentStatVO> querySessionCountAgentByUserId(@Param("tenantId") Integer tenantId, @Param("userId") Integer userId);

    Integer getAgentTotalSessions(AgentStatDTO dto);
}
