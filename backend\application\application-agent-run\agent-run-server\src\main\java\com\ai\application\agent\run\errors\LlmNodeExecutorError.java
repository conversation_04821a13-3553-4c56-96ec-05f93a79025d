package com.ai.application.agent.run.errors;

import com.ai.framework.core.enums.IErrorCode;

/**
 * LLM 节点执行器错误枚举
 */
public enum LlmNodeExecutorError implements IErrorCode {

    /**
     * LLM节点缺少输入参数配置
     */
    LLM_NODE_MISSING_INPUT_PARAMETERS(51001, "LLM节点缺少inputParameters配置"),

    /**
     * prompt 不能为空
     */
    LLM_PROMPT_IS_BLANK(51002, "prompt 不能为空"),

    /**
     * model 不能为空
     */
    LLM_MODEL_IS_BLANK(51003, "model 不能为空"),

    /**
     * 调用大模型失败
     */
    LLM_CALL_FAILED(51004, "调用大模型失败"),

    /**
     * 大模型返回结果为空
     */
    LLM_RESPONSE_IS_NULL(51005, "大模型返回结果为空"),

    /**
     * 温度参数格式错误
     */
    LLM_TEMPERATURE_FORMAT_ERROR(51006, "温度参数格式错误"),

    /**
     * 超时参数格式错误
     */
    LLM_TIMEOUT_FORMAT_ERROR(51007, "超时参数格式错误");

    private final int code;
    private final String message;

    LlmNodeExecutorError(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
