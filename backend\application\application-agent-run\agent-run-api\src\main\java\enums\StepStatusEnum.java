package enums;

import lombok.Getter;

@Getter
public enum StepStatusEnum {
    RUNNING("running", "运行中"),
    COMPLETE("complete", "运行结束"),
    ;
    private final String code;
    private final String desc;

    StepStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static StepStatusEnum ofCode(String value) {
        for (StepStatusEnum sessionStatusEnum : StepStatusEnum.values()) {
            if (sessionStatusEnum.code.equals(value)) {
                return sessionStatusEnum;
            }
        }
        return null;
    }
}
