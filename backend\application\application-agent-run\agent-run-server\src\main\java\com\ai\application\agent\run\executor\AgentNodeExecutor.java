package com.ai.application.agent.run.executor;

import com.ai.application.agent.base.api.dto.AgentChatDTO;
import com.ai.application.agent.base.api.feign.IAgentClient;
import com.ai.application.agent.base.api.vo.AgentChatVO;
import com.ai.application.agent.run.errors.AgentNodeExecutorError;
import com.ai.application.agent.run.executor.agent.ChatAgentExecutor;
import com.ai.application.agent.run.executor.agent.DocAgentExecutor;
import com.ai.application.agent.run.executor.agent.FormAgentExecutor;
import com.ai.application.agent.run.executor.agent.ProcessAgentExecutor;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import com.ai.framework.workflow.excutor.NodeExecutor;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Agent 节点执行器
 * 用于在工作流中调用智能体
 */
@Slf4j
@Component
public class AgentNodeExecutor implements NodeExecutor {

    @Autowired
    private IAgentClient agentClient;

    @Autowired
    private ChatAgentExecutor chatAgentExecutor;

    @Autowired
    private FormAgentExecutor formAgentExecutor;

    @Autowired
    private DocAgentExecutor docAgentExecutor;

    @Autowired
    private ProcessAgentExecutor processAgentExecutor;

    @Override
    public void execute(WorkflowContext context) {
        String nodeKey = context.getCurrentNodeKey();
        NodeContext nodeCtx = context.getNodeContexts().get(nodeKey);
        Map<String, Object> nodeDef = nodeCtx.getNodeDefinition();

        log.info("AgentNodeExecutor execute start, nodeKey: {}, nodeDef: {}", nodeKey, JsonUtils.toJsonString(nodeDef));

        try {
            // 设置节点状态为运行中
            nodeCtx.setStatus(NodeStatus.RUNNING);

            // 初始化节点输出
            if (nodeCtx.getOutput() == null) {
                nodeCtx.setOutput(new HashMap<>());
            }

            // 从节点定义中获取输入参数
            Map<String, Object> inputParameters = (Map<String, Object>) nodeDef.get("inputParameters");
            if (inputParameters == null) {
                throw new ServiceException(AgentNodeExecutorError.AGENT_NODE_MISSING_INPUT_PARAMETERS);
            }

            // 获取参数值，支持变量替换
            String agentSn = getParameterValue(inputParameters, "agentSn", context);
            String agentType = getParameterValue(inputParameters, "agentType", context);
            String msgContent = getParameterValue(inputParameters, "msgContent", context);

            // 参数校验
            if (StringUtils.isBlank(agentSn)) {
                throw new ServiceException(AgentNodeExecutorError.AGENT_NODE_EXECUTOR_ERROR);
            }
            if (StringUtils.isBlank(agentType)) {
                throw new ServiceException(AgentNodeExecutorError.AGENT_TYPE_IS_NULL);
            }
            if (StringUtils.isBlank(msgContent)) {
                throw new ServiceException(AgentNodeExecutorError.AGENT_MSG_CONTENT_IS_NULL);
            }

            // 根据 agentType 分发到不同的执行器
            Map<String, Object> executionResult = executeByAgentType(agentType, inputParameters, context);

            // 将结果写入输出参数
            Map<String, Object> outputParameters = (Map<String, Object>) nodeDef.get("outputParameters");
            if (outputParameters != null && executionResult != null) {
                for (Map.Entry<String, Object> entry : outputParameters.entrySet()) {
                    String outputKey = entry.getKey();
                    String variableName = entry.getValue().toString();

                    Object resultValue = executionResult.get(outputKey);
                    if (resultValue != null) {
                        context.setVar(variableName, resultValue);
                        log.info("Set variable {} = {}", variableName, resultValue);
                    }
                }
            }

            // 设置节点输出
            if (executionResult != null) {
                nodeCtx.getOutput().putAll(executionResult);
            }

            // 设置节点状态为成功
            nodeCtx.setStatus(NodeStatus.SUCCESS);
            nodeCtx.setEndTime(java.time.LocalDateTime.now());

            log.info("AgentNodeExecutor execute success, reply: {}", reply);

        } catch (Exception e) {
            log.error("AgentNodeExecutor execute error", e);
            nodeCtx.setStatus(NodeStatus.FAILED);
            nodeCtx.setErrorMsg("Agent执行失败: " + e.getMessage());
            nodeCtx.setEndTime(java.time.LocalDateTime.now());
            throw e;
        }
    }

    /**
     * 根据智能体类型分发到不同的执行器
     */
    private Map<String, Object> executeByAgentType(String agentType, Map<String, Object> inputParameters, WorkflowContext context) {
        log.info("Executing agent with type: {}", agentType);

        // 创建执行上下文适配器
        AgentExecutionContext executionContext = new AgentExecutionContext(inputParameters, context);

        switch (agentType) {
            case "form":
                return formAgentExecutor.execute(executionContext);
            case "chat":
                return chatAgentExecutor.execute(executionContext);
            case "doc":
                return docAgentExecutor.execute(executionContext);
            case "process":
                return processAgentExecutor.execute(executionContext);
            default:
                // 默认使用通用的智能体调用
                return executeDefaultAgent(inputParameters, context);
        }
    }

    /**
     * 默认智能体执行逻辑（兼容原有逻辑）
     */
    private Map<String, Object> executeDefaultAgent(Map<String, Object> inputParameters, WorkflowContext context) {
        String agentSn = getParameterValue(inputParameters, "agentSn", context);
        String agentType = getParameterValue(inputParameters, "agentType", context);
        String msgContent = getParameterValue(inputParameters, "msgContent", context);

        // 构建智能体请求
        AgentChatDTO chatDto = AgentChatDTO.builder()
                .agentSn(agentSn)
                .agentType(agentType)
                .msgContent(msgContent)
                .msgType("text")
                .delayInMs(20L)
                .fromCode("Workflow")
                .processId(context.getWorkflowInstanceId() != null ? context.getWorkflowInstanceId().toString() : null)
                .debug(false)
                .build();

        log.info("Default AgentExecutor request: {}", JsonUtils.toJsonString(chatDto));

        // 调用智能体服务
        ResultVo<AgentChatVO> result = agentClient.chat(chatDto);

        if (result == null || !Objects.equals(result.getCode(), 0)) {
            String errorMsg = result != null ? result.getMessage() : "调用智能体失败";
            throw new ServiceException(51004, "调用智能体失败: " + errorMsg);
        }

        AgentChatVO agentChatVo = result.getData();
        if (agentChatVo == null) {
            throw new ServiceException(AgentNodeExecutorError.AGENT_RESPONSE_IS_NULL);
        }

        // 解析返回结果
        String reply = parseMsgContent(agentChatVo);
        log.info("Default AgentExecutor reply: {}", reply);

        // 构建返回结果
        Map<String, Object> result_map = new HashMap<>();
        result_map.put("message", reply);
        result_map.put("sessionSn", agentChatVo.getSessionSn());
        result_map.put("success", agentChatVo.getSuccess());

        return result_map;
    }

    /**
     * 获取参数值，支持变量替换
     */
    private String getParameterValue(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        String strValue = value.toString();

        // 如果是变量引用（以$开头），从全局变量中获取
        if (strValue.startsWith("$")) {
            String varName = strValue.substring(1);
            Object varValue = context.getGlobalVars().get(varName);
            return varValue != null ? varValue.toString() : null;
        }

        return strValue;
    }

    /**
     * 解析消息内容
     */
    private String parseMsgContent(AgentChatVO agentChatVo) {
        if (agentChatVo.getContent() != null) {
            JsonNode answerNode = agentChatVo.getContent().get("answer");
            if (answerNode != null && answerNode.isTextual()) {
                return answerNode.asText();
            }
        }

        // 如果没有content或answer，返回reply字段
        return StringUtils.defaultIfBlank(agentChatVo.getReply(), "");
    }

    @Override
    public String getType() {
        return "AGENT";
    }
}