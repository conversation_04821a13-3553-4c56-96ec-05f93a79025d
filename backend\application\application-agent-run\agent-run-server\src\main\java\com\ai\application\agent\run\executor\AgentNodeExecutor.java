package com.ai.application.agent.run.executor;

import com.ai.application.agent.base.api.dto.AgentChatDTO;
import com.ai.application.agent.base.api.feign.IAgentChatClient;
import com.ai.application.agent.base.api.vo.AgentChatVO;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import com.ai.framework.workflow.excutor.NodeExecutor;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * Agent 节点执行器 - 专门用于工作流引擎
 */
@Slf4j
@Component
@AllArgsConstructor
public class AgentNodeExecutor implements NodeExecutor {

    private final IAgentChatClient agentChatClient;

    @Override
    public void execute(WorkflowContext context) {
        String nodeKey = context.getCurrentNodeKey();
        NodeContext nodeCtx = context.getNodeContexts().get(nodeKey);
        Map<String, Object> nodeDef = nodeCtx.getNodeDefinition();

        log.info("AgentNodeExecutor execute start, nodeKey: {}, nodeDef: {}", nodeKey, JsonUtils.toJsonString(nodeDef));

        try {
            // 从节点定义中获取参数
            Map<String, Object> inputParameters = (Map<String, Object>) nodeDef.get("inputParameters");
            if (inputParameters == null) {
                throw new ServiceException("Agent节点缺少inputParameters配置");
            }

            String agentSn = getParameterValue(inputParameters, "agentSn", context);
            String agentType = getParameterValue(inputParameters, "agentType", context);
            String msgContent = getParameterValue(inputParameters, "msgContent", context);

            // 参数校验
            if (StringUtils.isBlank(agentSn)) {
                throw new ServiceException("agentSn 不能为空");
            }
            if (StringUtils.isBlank(agentType)) {
                throw new ServiceException("agentType 不能为空");
            }
            if (StringUtils.isBlank(msgContent)) {
                throw new ServiceException("msgContent 不能为空");
            }

            // 构建请求
            AgentChatDTO chatDto = AgentChatDTO.builder()
                    .agentSn(agentSn)
                    .agentType(agentType)
                    .msgContent(msgContent)
                    .msgType("text")
                    .delayInMs(20L)
                    .fromCode("Workflow")
                    .processId(context.getWorkflowInstanceId() != null ? context.getWorkflowInstanceId().toString() : null)
                    .debug(false)
                    .build();

            log.info("AgentNodeExecutor request: {}", JsonUtils.toJsonString(chatDto));

            // 调用智能体
            ResultVo<AgentChatVO> result = agentChatClient.requestLLMWithAgent(chatDto, 
                    (String) context.getGlobalVars().get("authorization"));

            if (result == null || !Objects.equals(result.getCode(), 0)) {
                String errorMsg = result != null ? result.getMessage() : "调用智能体失败";
                throw new ServiceException("调用智能体失败: " + errorMsg);
            }

            AgentChatVO agentChatVo = result.getData();
            if (agentChatVo == null) {
                throw new ServiceException("智能体返回结果为空");
            }

            // 解析返回结果
            String reply = parseMsgContent(agentChatVo);

            // 将结果写入输出参数
            Map<String, Object> outputParameters = (Map<String, Object>) nodeDef.get("outputParameters");
            if (outputParameters != null) {
                for (Map.Entry<String, Object> entry : outputParameters.entrySet()) {
                    String outputKey = entry.getKey();
                    String variableName = entry.getValue().toString();
                    
                    if ("message".equals(outputKey)) {
                        context.setVar(variableName, reply);
                    } else if ("reply".equals(outputKey)) {
                        context.setVar(variableName, reply);
                    } else if ("sessionSn".equals(outputKey)) {
                        context.setVar(variableName, agentChatVo.getSessionSn());
                    } else if ("success".equals(outputKey)) {
                        context.setVar(variableName, agentChatVo.getSuccess());
                    }
                }
            }

            // 设置节点状态为成功
            nodeCtx.setStatus(NodeStatus.SUCCESS);
            nodeCtx.setEndTime(java.time.LocalDateTime.now());

            log.info("AgentNodeExecutor execute success, reply: {}", reply);

        } catch (Exception e) {
            log.error("AgentNodeExecutor execute error", e);
            nodeCtx.setStatus(NodeStatus.FAILED);
            nodeCtx.setErrorMsg("Agent执行失败: " + e.getMessage());
            nodeCtx.setEndTime(java.time.LocalDateTime.now());
            throw e;
        }
    }

    /**
     * 获取参数值，支持变量替换
     */
    private String getParameterValue(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        String strValue = value.toString();
        
        // 如果是变量引用（以$开头），从全局变量中获取
        if (strValue.startsWith("$")) {
            String varName = strValue.substring(1);
            Object varValue = context.getGlobalVars().get(varName);
            return varValue != null ? varValue.toString() : null;
        }
        
        return strValue;
    }

    /**
     * 解析消息内容
     */
    private String parseMsgContent(AgentChatVO agentChatVo) {
        if (agentChatVo.getContent() != null) {
            JsonNode answerNode = agentChatVo.getContent().get("answer");
            if (answerNode != null && answerNode.isTextual()) {
                return answerNode.asText();
            }
        }
        
        // 如果没有content或answer，返回reply字段
        return StringUtils.defaultIfBlank(agentChatVo.getReply(), "");
    }

    @Override
    public String getType() {
        return "AGENT";
    }
}
