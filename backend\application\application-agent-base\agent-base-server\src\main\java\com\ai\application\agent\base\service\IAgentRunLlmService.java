package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.dto.AgentRunLlmDTO;
import com.ai.application.agent.base.api.dto.AgentRunLlmListDTO;
import com.ai.application.agent.base.api.vo.AgentRunLlmVO;
import java.util.List;

/**
 * 智能体LLM调用记录表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public interface IAgentRunLlmService {
    /**
     * 列表
     *
     * @param queryDto
     * @return
     */
    List<AgentRunLlmVO> list(AgentRunLlmListDTO queryDto);

    /**
     * 保存
     *
     * @param dto
     */
    void add(AgentRunLlmDTO dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(AgentRunLlmDTO dto);

    /**
     * 查看
     *
     * @param id
     * @return
     */
    AgentRunLlmVO get(Integer id);
}