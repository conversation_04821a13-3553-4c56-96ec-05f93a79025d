<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.task.mapper.TaskAttachmentMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.task.api.entity.TaskAttachment">
                    <id column="attach_id" property="attachId" />
                    <result column="attach_status" property="attachStatus" />
                    <result column="process_start_time" property="processStartTime" />
                    <result column="process_end_time" property="processEndTime" />
                    <result column="process_duration" property="processDuration" />
                    <result column="process_input" property="processInput" />
                    <result column="process_output" property="processOutput" />
                    <result column="process_flag" property="processFlag" />
                    <result column="process_error" property="processError" />
                    <result column="process_metadata" property="processMetadata" />
                    <result column="retry_count" property="retryCount" />
                    <result column="tokens_used" property="tokensUsed" />
                    <result column="file_result_path" property="fileResultPath" />
                    <result column="task_id" property="taskId" />
                    <result column="task_run_id" property="taskRunId" />
                    <result column="file_id" property="fileId" />
                    <result column="agent_run_id" property="agentRunId" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        attach_id, attach_status, process_start_time, process_end_time, process_duration, process_input, process_output, process_flag, process_error, process_metadata, retry_count, tokens_used, file_result_path, task_id, task_run_id, file_id, agent_run_id, create_time, update_time
    </sql>

    <select id="selectTaskAttachmentList" resultType="com.ai.application.task.api.vo.TaskAttachmentVO">
        select
        <include refid="com.ai.application.task.mapper.TaskAttachmentMapper.Base_Column_List"></include>
        from task_attachment
        order by create_time desc limit 10;
    </select>
</mapper>