package com.ai.application.admin.api.mapstruct;

import com.ai.application.admin.api.entity.Agent;
import com.ai.application.admin.api.vo.AgentVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T09:54:06+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentMapstructImpl implements AgentMapstruct {

    @Override
    public List<AgentVO> toVoList(List<Agent> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AgentVO> list = new ArrayList<AgentVO>( entities.size() );
        for ( Agent agent : entities ) {
            list.add( toVo( agent ) );
        }

        return list;
    }

    @Override
    public AgentVO toVo(Agent entity) {
        if ( entity == null ) {
            return null;
        }

        AgentVO agentVO = new AgentVO();

        agentVO.setAgentSn( entity.getAgentSn() );
        agentVO.setAgentName( entity.getAgentName() );
        agentVO.setAgentDesc( entity.getAgentDesc() );
        agentVO.setAgentType( entity.getAgentType() );
        agentVO.setVersionId( entity.getVersionId() );

        return agentVO;
    }
}
