package com.ai.application.task.service.impl;

import com.ai.application.agent.base.api.entity.Agent;
import com.ai.application.app.api.dto.query.AppUserQueryDTO;
import com.ai.application.app.api.feign.IAppUserClient;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.application.base.file.api.feign.IFileFeignClient;
import com.ai.application.task.api.bo.TaskCronConfigBO;
import com.ai.application.task.api.dto.TaskCronAddDTO;
import com.ai.application.task.api.dto.query.TaskQueryDTO;
import com.ai.application.task.api.entity.Task;
import com.ai.application.task.api.entity.TaskRun;
import com.ai.application.task.api.enums.*;
import com.ai.application.task.api.mapstruct.TaskMapstruct;
import com.ai.application.task.api.vo.TaskCronDetailVO;
import com.ai.application.task.api.vo.TaskCronPageVO;
import com.ai.application.task.api.vo.TaskVO;
import com.ai.application.task.mapper.*;
import com.ai.application.task.service.ITaskCronService;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.util.BusinessAssertUtil;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.util.validator.AssertUtil;
import com.ai.framework.core.vo.ResultVo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 定时任务表
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Service
@Slf4j
public class TaskCronServiceImpl implements ITaskCronService {

    @Resource
    private TaskMapper taskMapper;

    @Resource
    private TaskMapstruct taskMapstruct;
    @Resource
    private AgentVersionMapper agentVersionMapper;
    @Resource
    private AgentMapper agentMapper;
    @Resource
    private TaskRunMapper taskRunMapper;
    @Resource
    private TaskAttachmentMapper taskAttachmentMapper;
    @Resource
    private IFileFeignClient fileFeignClient;
    @Resource
    private IAppUserClient appUserClient;

    @Transactional(readOnly = true)
    @Override
    public PageInfo<TaskCronPageVO> page(TaskQueryDTO queryDto) {
        QueryWrapper<Task> queryWrapper = this.buildQuery(queryDto);
        Page<Task> page = new Page<>(queryDto.getPageNo(), queryDto.getPageSize());
        Page<Task> result = this.taskMapper.selectPage(page, queryWrapper);
        List<Task> records = result.getRecords();
        if(CollectionUtils.isEmpty(records)){
            return PageInfo.of(new ArrayList<>());
        }

        List<Integer> userIds = records.stream().map(Task::getCreateUserId).toList();
        List<TaskCronPageVO> retList = taskMapstruct.toCronPageVoList(records);
        //创建人查询
        AppUserQueryDTO appUserQueryDTO = new AppUserQueryDTO();
        appUserQueryDTO.setUserIds(userIds);
        ResultVo<List<AppUserVO>> resUser = appUserClient.list(appUserQueryDTO);
        AssertUtil.isFalse(resUser.isSuccess(), "查询用户失败");

        List<AppUserVO> listUser = resUser.getData();
        retList.forEach(taskVO -> {
           if(CollectionUtils.isNotEmpty(listUser)){
               //创建人设置
               listUser.stream().filter(a->a.getUserId().equals(taskVO.getCreateUserId())).findFirst().ifPresent(a->{
                   taskVO.setCreateUserName(a.getUserName());
                   taskVO.setCreateUserId(null);
               });
               TaskCronConfigBO taskConfigBO = JsonUtils.parseObject(taskVO.getTaskConfig(), TaskCronConfigBO.class);
               //设置生效状态
               if(ExecuteCycleEnum.DAY_REPEAT.getCode().equals(taskConfigBO.getExecuteCycle())
                    || ExecuteCycleEnum.WEEK_REPEAT.getCode().equals(taskConfigBO.getExecuteCycle())){
                   taskVO.setEffectiveStatus(EffectiveStatusEnum.IN_EFFECT.getCode());
               }
               if(ExecuteCycleEnum.ONCE.getCode().equals(taskConfigBO.getExecuteCycle())){
                   if(Objects.nonNull(taskVO.getLastRunTime())){
                       taskVO.setEffectiveStatus(EffectiveStatusEnum.COMPLETED.getCode());
                   }else {
                       taskVO.setEffectiveStatus(EffectiveStatusEnum.IN_EFFECT.getCode());
                   }
               }
               //运行结果设置
               TaskRun taskRun = taskRunMapper.selectLastTaskRunByTaskId(taskVO.getTaskId());
               if(Objects.nonNull(taskRun)){
                   taskVO.setRunResult(RunStatusEnum.getTitle(taskRun.getRunStatus()));
                   taskVO.setTaskId(null);
               }
           }
        });
        return PageInfo.of(retList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(TaskCronAddDTO dto) {
        log.info("定时任务创建开始,参数={}", JsonUtils.toJsonString(dto));
        Agent agent = agentMapper.selectByAgentSn(dto.getAgentSn());
        AssertUtil.isNotNull(agent, "找不到AgentSn为 " + dto.getAgentSn() + " 的工作流记录");

        //任务表
        String taskSn = UUIDUtil.genRandomSn("task");
        Task task = taskMapstruct.toEntity(dto);
        task.setTenantId(UserContext.getTenantId());
        task.setAgentId(agent.getAgentId());
        task.setVersionId(0);
        task.setTaskSn(taskSn);
        task.setTaskStatus(TaskStatusEnum.ENABLE.getCode());
        task.setTaskType(TaskTypeEnum.PERIODICITY.getCode());
        task.setCreateUserId(UserContext.getUserId());
        task.setUpdateUserId(UserContext.getUserId());
        task.setStartTime(new Date());
        TaskCronConfigBO taskConfigBO = new TaskCronConfigBO();
        taskConfigBO.setNotificationTemplateId(dto.getNotificationTemplateId());
        taskConfigBO.setExecuteCycle(dto.getExecuteCycle());
        taskConfigBO.setExecuteWeeks(dto.getExecuteWeeks());
        task.setTaskConfig(JsonUtils.toJsonString(taskConfigBO));
        taskMapper.insert(task);

        //任务运行表
        TaskRun taskRun = new TaskRun();
        taskRun.setTaskId(task.getTaskId());
        taskRun.setRunType(RunTypeEnum.MANUAL_RUN.getCode());
        taskRun.setRunStatus(RunStatusEnum.RUN.getCode());
        taskRun.setScheduledTime(new Date());
        taskRun.setActualStartTime(new Date());
        taskRunMapper.insert(taskRun);

        //调用智能体接口执行任务
        //todo
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(String taskSn) {
        BusinessAssertUtil.notNull(taskSn, "TaskSn不能为空");

        Task entity = taskMapper.selectByTaskSn(taskSn);
        BusinessAssertUtil.notNull(entity, "找不到taskSn为 " + taskSn + " 的记录");
        entity.setTaskStatus(TaskStatusEnum.DELETE.getCode());
        entity.setUpdateTime(null);
        entity.setUpdateUserId(UserContext.getUserId());
        taskMapper.updateById(entity);

        //任务运行表
        taskRunMapper.queryTaskRunByTaskId(entity.getTaskId()).forEach(taskRun -> {
            taskRun.setRunStatus(RunStatusEnum.STOP.getCode());
            taskRun.setActualEndTime(new Date());
            taskRun.setDuration(Math.toIntExact(new Date().getTime() - taskRun.getActualStartTime().getTime()));
            taskRun.setUpdateTime(null);
            taskRunMapper.updateById(taskRun);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void stop(String taskSn) {
        BusinessAssertUtil.notNull(taskSn, "TaskSn不能为空");

        Task entity = taskMapper.selectByTaskSn(taskSn);
        BusinessAssertUtil.notNull(entity, "找不到taskSn为 " + taskSn + " 的记录");
        entity.setTaskStatus(TaskStatusEnum.PAUSE.getCode());
        entity.setUpdateTime(null);
        entity.setUpdateUserId(UserContext.getUserId());
        taskMapper.updateById(entity);

        //任务运行表
        taskRunMapper.queryTaskRunByTaskId(entity.getTaskId()).forEach(taskRun -> {
            taskRun.setRunStatus(RunStatusEnum.STOP.getCode());
            taskRun.setActualEndTime(new Date());
            taskRun.setDuration(Math.toIntExact(new Date().getTime() - taskRun.getActualStartTime().getTime()));
            taskRun.setUpdateTime(null);
            taskRunMapper.updateById(taskRun);
        });

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void enable(String taskSn) {
        BusinessAssertUtil.notNull(taskSn, "TaskSn不能为空");

        Task entity = taskMapper.selectByTaskSn(taskSn);
        BusinessAssertUtil.notNull(entity, "找不到taskSn为 " + taskSn + " 的记录");
        entity.setTaskStatus(TaskStatusEnum.ENABLE.getCode());
        entity.setUpdateTime(null);
        entity.setUpdateUserId(UserContext.getUserId());
        taskMapper.updateById(entity);
    }

    @Transactional(readOnly = true)
    @Override
    public TaskVO get(Integer id) {
        BusinessAssertUtil.notNull(id, "TaskId不能为空");

        Task entity = taskMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到TaskId为 " + id + " 的记录");

        return taskMapstruct.toVo(entity);
    }

    @Transactional(readOnly = true)
    @Override
    public TaskCronDetailVO detail(String taskSn) {
        BusinessAssertUtil.notNull(taskSn, "TaskSn不能为空");

        Task entity = taskMapper.selectByTaskSn(taskSn);
        BusinessAssertUtil.notNull(entity, "找不到taskSn为 " + taskSn + " 的记录");

        TaskCronDetailVO detailVo = taskMapstruct.toCronDetailVo(entity);
        TaskCronConfigBO taskConfigBO = JsonUtils.parseObject(detailVo.getTaskConfig(), TaskCronConfigBO.class);
        detailVo.setTaskCronConfigData(taskConfigBO);
        return detailVo;
    }

    private QueryWrapper<Task> buildQuery(TaskQueryDTO queryDto) {
        QueryWrapper<Task> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Task::getTenantId, UserContext.getTenantId());
        queryWrapper.lambda().ne(Task::getTaskStatus, TaskStatusEnum.DELETE.getCode());
        queryWrapper.lambda().like(StringUtils.isNoneBlank(queryDto.getKeyword()),Task::getTaskName, queryDto.getKeyword());
        return queryWrapper;
    }
}