package com.ai.application.admin.service;

import com.ai.application.admin.api.dto.AdminUserCreateDTO;
import com.ai.application.admin.api.dto.AdminUserUpdateDTO;
import com.ai.application.admin.api.dto.query.AdminUserQueryDTO;
import com.ai.application.admin.api.vo.AdminUserDetailVO;
import com.ai.application.admin.api.vo.AdminUserVO;
import com.github.pagehelper.PageInfo;

/**
 * 应用用户表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public interface IAdminUserService {

    /**
     * 分页
     *
     * @param queryDto
     * @return
     */
    PageInfo<AdminUserVO> page(AdminUserQueryDTO queryDto);

    void create(AdminUserCreateDTO dto);

    void update(String userSn,AdminUserUpdateDTO dto);

    void resetPassword(String userSn);

    void saveUserStatus(String userSn, Boolean enable);

    AdminUserDetailVO detail(String userSn);

    void initAdmin();
}