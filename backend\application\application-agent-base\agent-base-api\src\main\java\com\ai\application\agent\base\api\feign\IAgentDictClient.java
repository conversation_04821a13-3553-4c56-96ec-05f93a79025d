package com.ai.application.agent.base.api.feign;

import com.ai.application.agent.base.api.feign.fallback.IAgentClientFallback;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import java.util.List;

@Tag(name = "智能体关联的词库Feign接口", description = "智能体关联的词库基本操作")
@FeignClient(
        value = ServiceConstant.AGENT_BASE,
        fallback = IAgentClientFallback.class,
        contextId = "IAgentDictClient"
)
public interface IAgentDictClient {
    String API_PREFIX = "/v1/feign/agent/dict";

    @Operation(summary = "根据版本编号查词库列表")
    @GetMapping(API_PREFIX + "/selectDictByVersionSn/{versionSn}")
    ResultVo<List<Integer>> selectDictByVersionSn(@PathVariable("versionSn") String versionSn);
}