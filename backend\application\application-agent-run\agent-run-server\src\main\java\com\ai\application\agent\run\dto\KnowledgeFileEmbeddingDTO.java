package com.ai.application.agent.run.dto;

import com.ai.application.base.file.api.dto.FileInfoDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 知识文件嵌入 DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(name = "KnowledgeFileEmbeddingDTO")
public class KnowledgeFileEmbeddingDTO {

    /**
     * 文件信息
     */
    @Schema(description = "文件信息")
    private FileInfoDto fileInfo;

    /**
     * 嵌入配置
     */
    @Schema(description = "嵌入配置")
    private EmbeddingConfig embeddingConfig;

    /**
     * 知识库ID
     */
    @Schema(description = "知识库ID")
    private String datasetId;

    /**
     * 模型编号
     */
    @Schema(description = "模型编号")
    private String modelSn;

    /**
     * 上传类型
     */
    @Schema(description = "上传类型")
    private Integer uploadType;

    /**
     * 嵌入类型
     */
    @Schema(description = "嵌入类型")
    private String embeddingType;

    /**
     * 分段规则类型
     */
    @Schema(description = "分段规则类型")
    private Integer splitRuleType;

    /**
     * 嵌入配置
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EmbeddingConfig {
        
        /**
         * OCR表格识别
         */
        @Schema(description = "OCR表格识别")
        private Boolean ocrTable;

        /**
         * OCR图片识别
         */
        @Schema(description = "OCR图片识别")
        private Boolean ocrImage;

        /**
         * OCR文字识别
         */
        @Schema(description = "OCR文字识别")
        private Boolean ocrWord;

        /**
         * 分段规则
         */
        @Schema(description = "分段规则")
        private String splitRule;

        /**
         * 切分器类型
         */
        @Schema(description = "切分器类型")
        private String splitter;

        /**
         * 分段字数上限
         */
        @Schema(description = "分段字数上限")
        private String wordCountLimit;

        /**
         * 重叠区域字数
         */
        @Schema(description = "重叠区域字数")
        private String wordCountOverlap;

        /**
         * 分隔符列表
         */
        @Schema(description = "分隔符列表")
        private List<String> separators;

        /**
         * 优先级
         */
        @Schema(description = "优先级")
        private Integer priority;
    }
}
