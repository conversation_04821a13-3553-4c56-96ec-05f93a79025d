package com.ai.application.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 模型信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("model_info")
public class ModelInfo implements Serializable {
        @Schema(description = "")
        @TableId(type = IdType.AUTO)
    private Integer modelId;

    /**
    * 模型sn
    */
    @Schema(description = "模型sn")
        private String modelSn;

    /**
    * 模型名称
    */
    @Schema(description = "模型名称")
        private String modelName;

    /**
    * 调用名称：比如gpt-3.5-tubro
    */
    @Schema(description = "调用名称：比如gpt-3.5-tubro")
        private String modelEngine;

    /**
    * 模型类型10文本,11推理,20图文,21生图,22图图,31stt,32tts,33音音,40视频理解,41视频生成，42视频混合,50WM,70嵌入,71重排,72OCR
    */
    @Schema(description = "模型类型10文本,11推理,20图文,21生图,22图图,31stt,32tts,33音音,40视频理解,41视频生成，42视频混合,50WM,70嵌入,71重排,72OCR")
        private Integer modelType;

    /**
    * 0:不支持 1:function 2:tools 3:both
    */
    @Schema(description = "0:不支持 1:function 2:tools 3:both")
        private Integer modelToolcall;

    /**
    * 模型描述
    */
    @Schema(description = "模型描述")
        private String modelDesc;

    /**
    * 模型基础配置参数
    */
    @Schema(description = "模型基础配置参数")
        private String modelConfig;

    /**
    * 模型状态0:禁用,1:启用,-1:弃用
    */
    @Schema(description = "模型状态0:禁用,1:启用,-1:弃用")
        private Integer modelStatus;

    /**
    * 供应商id
    */
    @Schema(description = "供应商id")
        private Integer supplierId;

    @Schema(description = "")
        private Date createTime;

    @Schema(description = "")
        private Date updateTime;

}