package com.ai.application.agent.base.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 智能体运行会话表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("agent_run_session")
public class AgentRunSession implements Serializable {
        /**
    * 会话id
    */
    @Schema(description = "会话id")
    @TableId(type = IdType.AUTO)
    private Integer sessionId;

    /**
    * 会话session
    */
    @Schema(description = "会话session")
    private String sessionSn;

    /**
    * 会话标题(智能生成)
    */
    @Schema(description = "会话标题(智能生成)")
    private String sessionTitle;

    /**
    * 预留类型
    */
    @Schema(description = "预留类型")
    private Integer sessionType;

    /**
    * 会话状态:1-活跃,3-结束,-1-删除
    */
    @Schema(description = "会话状态:1-活跃,3-结束,-1-删除")
    private Integer sessionStatus;

    /**
    * 会话元数据
    */
    @Schema(description = "会话元数据")
    private String sessionMetadata;

    /**
    * 智能体id
    */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
    * 智能体版本id
    */
    @Schema(description = "智能体版本id")
    private Integer versionId;

    /**
    * 用户id
    */
    @Schema(description = "用户id")
    private Integer userId;

    /**
    * 租户id
    */
    @Schema(description = "租户id")
    private Integer tenantId;

    /**
    * 运行次数
    */
    @Schema(description = "运行次数")
    private Integer runCount;

    /**
    * 首次运行id
    */
    @Schema(description = "首次运行id")
    private Integer firstRunId;

    /**
    * 最后运行id
    */
    @Schema(description = "最后运行id")
    private Integer lastRunId;

    /**
    * 最后运行时间
    */
    @Schema(description = "最后运行时间")
    private Date lastRunTime;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}