package com.ai.application.agent.base.api.dto.query;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;

import com.ai.framework.core.vo.PageParam;

/**
 * agent版本扩展信息-查询条件
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@Schema(name = "agent版本扩展信息QueryDTO")
public class AgentVersionExtendQueryDTO extends PageParam {
    private List<Integer> agentIds;
    private String itemName;
    private String itemValue;

}