package com.ai.application.app.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 应用角色表
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(name = "")
public class AppRoleFunctionTreeVO {
    @Schema(description = "id")
    private Integer funId;

    /**
     * 类型，10目录 11菜单 12按钮，20接口
     */
    @Schema(description = "类型，10目录 11菜单 12按钮，20接口")
    private Integer funType;

    /**
     * 功能名称
     */
    @Schema(description = "功能名称")
    private String funName;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer funSort;

    /**
     * 父级ID
     */
    @Schema(description = "父级ID")
    private Integer parentId;

    @Schema(description="子菜单列表")
    private List<AppRoleFunctionTreeVO> children;
}