package com.ai.application.admin.api.mapstruct;

import com.ai.application.admin.api.dto.TenantExtendDTO;
import com.ai.application.admin.api.entity.TenantExtend;
import com.ai.application.admin.api.vo.TenantExtendVO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 * 租户扩展配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */

@Mapper(componentModel = "spring")
public interface TenantExtendMapstruct {

    TenantExtend toEntity(TenantExtendDTO dto);
    List<TenantExtend> toEntityList(List<TenantExtendDTO> dtolist);
    TenantExtendVO toVo(TenantExtend entity);
    TenantExtendDTO toVo(TenantExtendVO vo);
    List<TenantExtendVO> toVoList(List<TenantExtend> entities);
}
