package com.ai.application.agent.run.service;

import com.ai.application.agent.run.dto.SmartTableWriteRequestDTO;
import com.ai.application.agent.run.dto.SmartTableWriteResultDTO;
import com.ai.framework.core.vo.ResultVo;

/**
 * 智能表格写入服务接口
 */
public interface ISmartTableWriteService {

    /**
     * 执行智能表格写入
     *
     * @param request 写入请求
     * @param authorization 授权信息
     * @return 写入结果
     */
    ResultVo<SmartTableWriteResultDTO> executeSmartTableWrite(SmartTableWriteRequestDTO request, String authorization);

    /**
     * 验证表格数据格式
     *
     * @param data 数据
     * @param type 类型
     * @return 是否有效
     */
    boolean validateTableData(Object data, Integer type);

    /**
     * 转换数据格式
     *
     * @param data 原始数据
     * @return 转换后的数据
     */
    Object convertDataFormat(Object data);
}
