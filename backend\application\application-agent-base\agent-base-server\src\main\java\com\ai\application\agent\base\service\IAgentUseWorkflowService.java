package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.bo.MasterSkillBO;
import com.ai.application.agent.base.api.dto.AgentUseWorkflowAddDTO;
import com.ai.application.agent.base.api.dto.AgentUseWorkflowListDTO;
import com.ai.application.agent.base.api.dto.AgentUseWorkflowUpdateDTO;
import com.ai.application.agent.base.api.vo.AgentUseWorkflowListVO;
import com.ai.application.agent.base.api.vo.AgentUseWorkflowVO;
import java.util.List;

public interface IAgentUseWorkflowService {
    /**
     * 列表
     *
     * @param queryDto
     * @return
     */
    List<AgentUseWorkflowListVO> list(AgentUseWorkflowListDTO queryDto);

    /**
     * 保存
     *
     * @param dto
     */
    void add(AgentUseWorkflowAddDTO dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(AgentUseWorkflowUpdateDTO dto);

    AgentUseWorkflowVO selectByVersion(Integer versionId);

    List<MasterSkillBO> toDetail(Integer versionId);
}
