package com.ai.application.agent.base.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class LongMemoryBO {
    @Schema(description = "默认配置")
    @NotEmpty(message = "默认配置不能为空")
    private List<DefaultConfig> defaultConfigs;

    @Schema(description = "自定义配置")
    private List<CustomizeConfig> customizeConfigs;

    @Data
    public static class DefaultConfig {
        @Schema(description = "字段名称")
        @NotBlank(message = "字段名称不能为空")
        private String name;

        @Schema(description = "描述")
        @NotBlank(message = "描述不能为空")
        private String desc;
    }

    @Data
    public static class CustomizeConfig {
        @Schema(description = "sn")
        @NotBlank(message = "sn不能为空")
        private String sn;

        @Schema(description = "字段名称")
        @NotBlank(message = "字段名称不能为空")
        private String name;

        @Schema(description = "字段描述")
        @NotBlank(message = "描述不能为空")
        private String desc;

        @Schema(description = "变量ID")
        @NotBlank(message = "变量id不能为空")
        private String variableId;
    }
}
