package com.ai.application.task.service;

import com.ai.application.task.api.dto.TaskCronAddDTO;
import com.ai.application.task.api.dto.query.TaskQueryDTO;
import com.ai.application.task.api.vo.TaskCronDetailVO;
import com.ai.application.task.api.vo.TaskCronPageVO;
import com.ai.application.task.api.vo.TaskVO;
import com.github.pagehelper.PageInfo;
import org.springframework.transaction.annotation.Transactional;

/**
 * 定时任务表
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
public interface ITaskCronService {

        PageInfo<TaskCronPageVO> page(TaskQueryDTO queryDto);

        void add(TaskCronAddDTO dto);

        void delete(String taskSn);

        void stop(String taskSn);

        @Transactional(rollbackFor = Exception.class)
        void enable(String taskSn);

        TaskVO get(Integer id);

        TaskCronDetailVO detail(String taskSn);

}