package com.ai.application.admin.controller;

import com.ai.application.admin.api.dto.AdminUserCreateDTO;
import com.ai.application.admin.api.dto.AdminUserStatusDTO;
import com.ai.application.admin.api.dto.AdminUserUpdateDTO;
import com.ai.application.admin.api.dto.query.AdminUserQueryDTO;
import com.ai.application.admin.api.vo.AdminUserDetailVO;
import com.ai.application.admin.api.vo.AdminUserVO;
import com.ai.application.admin.service.IAdminUserService;
import com.ai.framework.core.vo.ResultVo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 应用用户表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Tag(name = "管理后台-用户列表", description = "管理后台-用户列表相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1/user")
public class AdminUserController {

    @Autowired
    private IAdminUserService adminUserService;

    @Operation(summary = "用户列表-分页查询", description = "查询所有应用用户表 信息")
    @ApiResponse(responseCode = "0", description = "成功",
            content = @Content(schema = @Schema(implementation = AdminUserVO.class)))
    @RequestMapping(value = "/page", method = {RequestMethod.POST})
    public ResultVo<PageInfo<AdminUserVO>> page(@Validated @RequestBody AdminUserQueryDTO queryDto) {
        return ResultVo.data(adminUserService.page(queryDto));
    }

    @Operation(summary = "用户新增")
    @PostMapping("/create")
    public ResultVo<Void> create(@Validated @RequestBody AdminUserCreateDTO dto) {
        adminUserService.create(dto);
        return ResultVo.success("保存成功");
    }

    @Operation(summary = "用户修改")
    @PostMapping(value = "/{userSn}/update")
    public ResultVo<Void> update(@PathVariable("userSn") String userSn,@Validated @RequestBody AdminUserUpdateDTO dto) {
        adminUserService.update(userSn,dto);
        return ResultVo.success("修改成功");
    }

    @Operation(summary = "用户-启用/禁用", description = "根据用户编码设置启用/禁用")
    @PostMapping("/{userSn}/enable")
    public ResultVo<Boolean> updateStatus(@PathVariable("userSn") String userSn, @RequestBody @Validated AdminUserStatusDTO dto) {
        adminUserService.saveUserStatus(userSn, dto.getEnable());
        return ResultVo.success("状态设置成功");
    }

    @Operation(summary = "用户-重置密码", description = "根据用户编码重置密码")
    @PostMapping("/{userSn}/resetPassword")
    public ResultVo<Void> resetPassword(@PathVariable("userSn") String userSn) {
        adminUserService.resetPassword(userSn);
        return ResultVo.success("重置密码成功");
    }

    @Operation(summary = "应用用户表-查询详情", description = "根据用户编号应用用户表 信息")
    @ApiResponse(responseCode = "0", description = "成功",
            content = @Content(schema = @Schema(implementation = AdminUserDetailVO.class)))
    @GetMapping("/{userSn}/detail")
    public ResultVo<AdminUserDetailVO> detail(@PathVariable("userSn") String userSn) {
        return ResultVo.data(adminUserService.detail(userSn));
    }
}