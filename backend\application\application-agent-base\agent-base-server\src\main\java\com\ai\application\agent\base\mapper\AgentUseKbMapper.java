package com.ai.application.agent.base.mapper;

import com.ai.application.agent.base.api.dto.query.AgentUseKnowledgeDocQueryDTO;
import com.ai.application.agent.base.api.entity.AgentUseKb;
import com.ai.application.agent.base.api.vo.AgentUseKnowledgeDocQueryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 智能体关联知识库表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface AgentUseKbMapper extends BaseMapper<AgentUseKb> {
    IPage<AgentUseKnowledgeDocQueryVO> selectUseDocByPage(IPage<AgentUseKnowledgeDocQueryVO> page, @Param("params") AgentUseKnowledgeDocQueryDTO dto);
}
