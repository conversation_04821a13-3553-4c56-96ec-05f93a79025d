package com.ai.application.agent.base.api.enums;

import lombok.Getter;

@Getter
public enum AgentExtendNameEnum {
    RECOMMENDS("recommends", "推荐问题"),
    MEMORY("memory", "记忆开关"),
    ;

    private final String code;
    private final String desc;

    AgentExtendNameEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AgentExtendNameEnum ofCode(String value) {
        for (AgentExtendNameEnum enums : AgentExtendNameEnum.values()) {
            if (enums.code.equals(value)) {
                return enums;
            }
        }
        return null;
    }
}
