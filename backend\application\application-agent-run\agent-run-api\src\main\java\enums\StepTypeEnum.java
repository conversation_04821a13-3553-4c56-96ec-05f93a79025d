package enums;

import lombok.Getter;

@Getter
public enum StepTypeEnum {
    FUNCATION(10, "funcation"),
    ;

    private final Integer code;
    private final String desc;

    StepTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static StepTypeEnum ofCode(Integer value) {
        for (StepTypeEnum sessionStatusEnum : StepTypeEnum.values()) {
            if (sessionStatusEnum.code.equals(value)) {
                return sessionStatusEnum;
            }
        }
        return null;
    }
}
