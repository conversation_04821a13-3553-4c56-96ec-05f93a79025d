package com.ai.application.agent.run.executor;

import com.ai.framework.core.context.UserContext;
import com.ai.framework.workflow.context.WorkflowContext;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Map;

/**
 * Agent 执行上下文适配器
 * 将 WorkflowContext 适配为 backend2 项目中的 ExecutionContext 接口
 */
@Data
@AllArgsConstructor
public class AgentExecutionContext {
    
    private final Map<String, Object> parameters;
    private final WorkflowContext workflowContext;

    /**
     * 获取工作流ID
     */
    public String getProcessId() {
        return workflowContext.getWorkflowInstanceId() != null ? 
               workflowContext.getWorkflowInstanceId().toString() : null;
    }

    /**
     * 获取工作流实例ID
     */
    public String getProcessInstanceId() {
        return getProcessId();
    }

    /**
     * 获取节点ID
     */
    public String getNodeId() {
        return workflowContext.getCurrentNodeKey();
    }

    /**
     * 获取节点名称
     */
    public String getNodeName() {
        return workflowContext.getCurrentNodeKey();
    }

    /**
     * 获取所有参数
     */
    public Map<String, Object> getParameters() {
        return parameters;
    }

    /**
     * 获取字符串参数
     */
    public String getParameterAsString(String parameter) {
        Object value = parameters.get(parameter);
        if (value == null) {
            // 尝试从工作流全局变量中获取
            value = workflowContext.getGlobalVars().get(parameter);
        }
        return value != null ? value.toString() : null;
    }

    /**
     * 获取用户信息
     */
    public UserContext.UserContextItem getUserInfo() {
        // TODO 从工作流上下文中获取用户信息
//        Object userInfo = workflowContext.getGlobalVars().get("userInfo");
//        if (userInfo instanceof UserContext.UserContextItem) {
//            return (UserContext.UserContextItem) userInfo;
//        }
        return null;
    }

    /**
     * 获取授权信息
     */
    public String getAuthorization() {
        // TODO 从工作流上下文中获取授权信息

//        Object auth = workflowContext.getGlobalVars().get("authorization");
//        return auth != null ? auth.toString() : null;

        return null;
    }

    /**
     * 获取模块类型
     */
    public String getModule() {
        return "agent";
    }

    /**
     * 是否调试运行
     */
    public boolean isDebugRun() {
        Object debug = workflowContext.getGlobalVars().get("debug");
        return Boolean.TRUE.equals(debug);
    }

    /**
     * 获取流程触发ID
     */
    public String getProcessTriggerId() {
        Object triggerId = workflowContext.getGlobalVars().get("triggerId");
        return triggerId != null ? triggerId.toString() : "";
    }

    /**
     * 获取流程触发类型
     */
    public String getProcessTriggerType() {
        return "WORKFLOW";
    }

    /**
     * 获取流程触发名称
     */
    public String getProcessTriggerName() {
        Object triggerName = workflowContext.getGlobalVars().get("triggerName");
        return triggerName != null ? triggerName.toString() : "";
    }

    /**
     * 发送输出增量（用于流式输出）
     */
    public void sendOutputDeltas(Map<String, String> outputDeltaMapping) {
        // 在工作流上下文中，可以通过设置变量来实现增量输出
        if (outputDeltaMapping != null) {
            for (Map.Entry<String, String> entry : outputDeltaMapping.entrySet()) {
                String key = entry.getKey() + "_delta";
                workflowContext.setVar(key, entry.getValue());
            }
        }
    }
}
