package dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

@Schema(name = "对话流传参")
@Data
public class ChatFlowRunDTO extends RunBaseDTO {
    /**
     * 长期记忆状态
     */
    @Schema(description = "长期记忆状态 true开启, false关闭")
    private Boolean longTermMemory;

    /**
     * 词库编码
     */
    @Schema(description = "词库编码")
    private List<String> dictSn;

    /**
     * 匹配到的词库
     */
    @Schema(description = "匹配到的词库")
    private List<String> matchDict;

    /**
     * 附件
     */
    @Schema(description = "附件")
    private Attachment attachments;

    @Schema(description = "附件")
    @Data
    private static class Attachment {
        @Schema(description = "文件编码")
        private String fileSn;

        @Schema(description = "文件名称")
        private String fileName;

        @Schema(description = "文件类型")
        private String fileType;

        @Schema(description = "文件大小")
        private String fileSize;
    }
}
