package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.dto.AgentUseTableUpdateDTO;
import com.ai.application.agent.base.api.entity.AgentUseTable;
import com.ai.application.agent.base.api.dto.AgentUseTableAddDTO;
import com.ai.application.agent.base.api.vo.AgentUseTableListVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 智能体关联智能表格表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-07
 */

@Mapper(componentModel = "spring")
public interface AgentUseTableMapstruct {

    AgentUseTable toAddEntity(AgentUseTableAddDTO dto);
    AgentUseTable toUpdateEntity(AgentUseTableUpdateDTO dto);
    AgentUseTableListVO toVo(AgentUseTable entity);
    List<AgentUseTableListVO> toVoList(List<AgentUseTable> entities);
}
