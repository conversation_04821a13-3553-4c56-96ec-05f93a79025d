package com.ai.application.agent.run.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 记忆搜索响应DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "MemorySearchResponseDTO")
public class MemorySearchResponseDTO {

    /**
     * 是否成功
     */
    @JsonProperty("success")
    @Schema(description = "是否成功")
    private Boolean success;

    /**
     * 响应消息
     */
    @JsonProperty("message")
    @Schema(description = "响应消息")
    private String message;

    /**
     * 响应时间
     */
    @JsonProperty("timestamp")
    @Schema(description = "响应时间")
    private LocalDateTime timestamp;

    /**
     * 响应数据
     */
    @JsonProperty("data")
    @Schema(description = "响应数据")
    private MemorySearchData data;

    /**
     * 记忆搜索数据
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class MemorySearchData {
        /**
         * 总数
         */
        @JsonProperty("total")
        @Schema(description = "总数")
        private Integer total;

        /**
         * 记忆列表
         */
        @JsonProperty("memories")
        @Schema(description = "记忆列表")
        private List<MemoryItemDTO> memories;
    }

    /**
     * 记忆项DTO
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class MemoryItemDTO {
        /**
         * 记忆ID
         */
        @JsonProperty("id")
        @Schema(description = "记忆ID")
        private String id;

        /**
         * 智能体ID
         */
        @JsonProperty("agent_id")
        @Schema(description = "智能体ID")
        private String agentId;

        /**
         * 用户ID
         */
        @JsonProperty("user_id")
        @Schema(description = "用户ID")
        private String userId;

        /**
         * 记忆类别
         */
        @JsonProperty("category")
        @Schema(description = "记忆类别")
        private String category;

        /**
         * 用户问题
         */
        @JsonProperty("user_question")
        @Schema(description = "用户问题")
        private String userQuestion;

        /**
         * 问题回复
         */
        @JsonProperty("question_reply")
        @Schema(description = "问题回复")
        private String questionReply;

        /**
         * 问题时间
         */
        @JsonProperty("question_time")
        @Schema(description = "问题时间")
        private LocalDateTime questionTime;

        /**
         * 相似度分数
         */
        @JsonProperty("similarity_score")
        @Schema(description = "相似度分数")
        private Double similarityScore;

        /**
         * 创建时间
         */
        @JsonProperty("created_at")
        @Schema(description = "创建时间")
        private LocalDateTime createdAt;

        /**
         * 更新时间
         */
        @JsonProperty("updated_at")
        @Schema(description = "更新时间")
        private LocalDateTime updatedAt;
    }
}
