package com.ai.application.agent.base.feign;

import com.ai.application.agent.base.api.dto.query.AgentPageDTO;
import com.ai.application.agent.base.api.dto.query.AgentStatDetailQueryDTO;
import com.ai.application.agent.base.api.feign.IAgentClient;
import com.ai.application.agent.base.api.vo.AgentPageVO;
import com.ai.application.agent.base.api.vo.AgentStatDetailVO;
import com.ai.application.agent.base.api.vo.AgentVO;
import com.ai.application.agent.base.service.IAgentCommService;
import com.ai.application.agent.base.service.IAgentService;
import com.ai.framework.core.vo.ResultVo;
import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@AllArgsConstructor
public class AgentClient implements IAgentClient {
    private final IAgentService agentService;
    private final IAgentCommService agentCommService;

    @Override
    public ResultVo<List<AgentStatDetailVO>> queryAgentByModelId(@RequestBody @Validated AgentStatDetailQueryDTO dto){
        return ResultVo.data(agentService.queryAgentStatDetail(dto));
    }

    @Override
    public ResultVo<AgentVO> getAgentBySn(String agentSn) {
        return ResultVo.data(agentService.getAgentBySn(agentSn));
    }

    @Override
    public ResultVo<List<Integer>> queryGrantAgentIdList(Integer pageSource) {
        return ResultVo.data(agentCommService.queryGrantAgentIdList(pageSource));
    }

    @Override
    public ResultVo<PageInfo<AgentPageVO>> page(AgentPageDTO dto) {
        return ResultVo.data(agentService.page(dto));
    }
}
