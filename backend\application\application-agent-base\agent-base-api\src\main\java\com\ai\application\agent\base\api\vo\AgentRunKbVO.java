package com.ai.application.agent.base.api.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 智能体知识库检索记录表
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@Schema(name = "")
public class AgentRunKbVO {
    /**
     * 知识库检索id
     */
    @Schema(description = "知识库检索id")
    private Integer kbRunId;

    /**
     * 知识库名称
     */
    @Schema(description = "知识库名称")
    private String kbName;

    /**
     * 检索查询
     */
    @Schema(description = "检索查询")
    private String kbQuery;

    /**
     * 查询向量
     */
    @Schema(description = "查询向量")
    private String kbQueryVector;

    /**
     * 检索结果
     */
    @Schema(description = "检索结果")
    private String kbResults;

    /**
     * 结果数量
     */
    @Schema(description = "结果数量")
    private Integer kbResultsCount;

    /**
     * 相似度阈值
     */
    @Schema(description = "相似度阈值")
    private Float kbSimilarityThreshold;

    /**
     * 检索状态:1-检索中,2-成功,3-失败,4-无结果
     */
    @Schema(description = "检索状态:1-检索中,2-成功,3-失败,4-无结果")
    private Integer kbStatus;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String kbError;

    /**
     * 检索时长(毫秒)
     */
    @Schema(description = "检索时长(毫秒)")
    private Integer kbDuration;

    /**
     * 检索元数据
     */
    @Schema(description = "检索元数据")
    private String kbMetadata;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date kbStartTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date kbEndTime;

    /**
     * 运行记录id
     */
    @Schema(description = "运行记录id")
    private Integer runId;

    /**
     * 步骤id
     */
    @Schema(description = "步骤id")
    private Integer stepId;

    /**
     * 知识库id
     */
    @Schema(description = "知识库id")
    private Integer kbId;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}