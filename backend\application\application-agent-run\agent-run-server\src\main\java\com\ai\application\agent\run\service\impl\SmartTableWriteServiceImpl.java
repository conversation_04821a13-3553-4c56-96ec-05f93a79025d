package com.ai.application.agent.run.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.ai.application.agent.run.dto.SmartTableWriteRequestDTO;
import com.ai.application.agent.run.dto.SmartTableWriteResultDTO;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.service.ISmartTableWriteService;
import com.ai.application.knowledge.table.dto.TableDataCreateDto;
import com.ai.application.knowledge.table.feign.ITableFeignClient;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 智能表格写入服务实现
 */
@Slf4j
@Component
public class SmartTableWriteServiceImpl implements ISmartTableWriteService {

    @Autowired
    private ITableFeignClient tableFeignClient;

    @Override
    public ResultVo<SmartTableWriteResultDTO> executeSmartTableWrite(SmartTableWriteRequestDTO request, String authorization) {
        log.info("SmartTableWriteService executeSmartTableWrite start, request: {}", JsonUtils.toJsonString(request));

        try {
            // 参数校验
            validateSmartTableWriteRequest(request);

            // 数据格式转换和校验
            JSONArray jsonArray = convertAndValidateData(request.getData(), request.getType());

            // 构建表格数据创建请求
            List<TableDataCreateDto> createRequests = buildTableDataCreateRequests(request.getTableSn(), jsonArray);

            // 执行数据写入
            List<String> results = new ArrayList<>();
            for (TableDataCreateDto createRequest : createRequests) {
                ResultVo<String> result = tableFeignClient.dataCreate(createRequest,authorization);
                if (result.getCode() != 0) {
                    throw new ServiceException(result.getCode(), result.getMessage());
                }
                if (StringUtils.isNotBlank(result.getData())) {
                    results.add(result.getData());
                }
            }

            // 构建返回结果
            SmartTableWriteResultDTO writeResult = SmartTableWriteResultDTO.builder()
                    .success(true)
                    .output(results)
                    .tableSn(request.getTableSn())
                    .rowCount(createRequests.size())
                    .build();

            log.info("SmartTableWriteService executeSmartTableWrite success, result: {}", JsonUtils.toJsonString(writeResult));
            return ResultVo.data(writeResult);

        } catch (Exception e) {
            log.error("SmartTableWriteService executeSmartTableWrite error", e);
            SmartTableWriteResultDTO errorResult = SmartTableWriteResultDTO.builder()
                    .success(false)
                    .errorMessage(e.getMessage())
                    .tableSn(request.getTableSn())
                    .rowCount(0)
                    .build();
            return ResultVo.data(errorResult);
        }
    }

    @Override
    public boolean validateTableData(Object data, Integer type) {
        if (data == null) {
            return false;
        }

        String strData = String.valueOf(data);
        if (StringUtils.isBlank(strData) || "null".equals(strData) || "[]".equals(strData)) {
            return false;
        }

        try {
            if (data instanceof Map || data instanceof List) {
                return true;
            }
            
            // 尝试解析为JSON
            JSONUtil.parseObj(strData);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public Object convertDataFormat(Object data) {
        if (data == null) {
            return new JSONArray();
        }

        if (data instanceof JSONArray) {
            return data;
        }

        if (data instanceof List) {
            return JSONUtil.parseArray(data);
        }

        if (data instanceof Map) {
            JSONArray jsonArray = new JSONArray();
            jsonArray.add(JSONUtil.parseObj(data));
            return jsonArray;
        }

        // 尝试解析字符串
        String strData = data.toString();
        try {
            Object parsed = JSONUtil.parse(strData);
            if (parsed instanceof JSONArray) {
                return parsed;
            } else {
                JSONArray jsonArray = new JSONArray();
                jsonArray.add(parsed);
                return jsonArray;
            }
        } catch (Exception e) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "数据格式错误，无法解析为JSON");
        }
    }

    /**
     * 校验智能表格写入请求
     */
    private void validateSmartTableWriteRequest(SmartTableWriteRequestDTO request) {
        if (StringUtils.isBlank(request.getTableSn())) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "表格编号不能为空");
        }

        if (request.getData() == null) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "写入数据不能为空");
        }

        if (!validateTableData(request.getData(), request.getType())) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "数据格式错误");
        }
    }

    /**
     * 转换和校验数据
     */
    private JSONArray convertAndValidateData(Object data, Integer type) {
        JSONArray jsonArray = new JSONArray();

        if (data instanceof Map<?, ?> jsonData) {
            jsonArray.add(JSONUtil.parseObj(jsonData));
        } else if (data instanceof List<?> array) {
            jsonArray = JSONUtil.parseArray(array);
        } else {
            // 格式不正确的数据
            try {
                String strData = String.valueOf(data);
                Object parsed = JSONUtil.parse(strData);
                if (parsed instanceof JSONArray) {
                    jsonArray = (JSONArray) parsed;
                } else {
                    jsonArray.add(parsed);
                }
            } catch (Exception e) {
                throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "数据格式错误");
            }
        }

        // 数据结构校验，删除所有空数据
        validateAndCleanData(jsonArray, type);

        return jsonArray;
    }

    /**
     * 校验和清理数据
     */
    private void validateAndCleanData(JSONArray jsonArray, Integer type) {
        if (type != null && type == 0) {
            // 覆盖模式，检查第一条数据
            if (jsonArray.size() > 0) {
                Object object = jsonArray.get(0);
                List<Object> list = getValidDataList(object);
                if (CollectionUtils.isEmpty(list)) {
                    throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "所有字段都为空");
                }
            }
        } else {
            // 追加模式，校验每条数据格式
            jsonArray.forEach(jsonObject -> {
                if (!(jsonObject instanceof String || jsonObject instanceof Map)) {
                    throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "数据格式错误");
                } else if (jsonObject instanceof String string) {
                    try {
                        JSONUtil.parseObj(string);
                    } catch (Exception e) {
                        throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "数据格式错误");
                    }
                }
            });

            // 删除所有空数据
            jsonArray.removeIf(jsonObject -> {
                List<Object> list = getValidDataList(jsonObject);
                return CollectionUtils.isEmpty(list);
            });
        }
    }

    /**
     * 获取有效数据列表
     */
    private List<Object> getValidDataList(Object object) {
        Map<String, Object> map;
        if (object instanceof String str) {
            map = JSONUtil.toBean(str, Map.class);
        } else {
            map = (Map<String, Object>) object;
        }

        List<Object> list = new ArrayList<>();
        if (map != null) {
            map.forEach((key, value) -> {
                if (Objects.nonNull(value)) {
                    String str = value.toString();
                    if (StringUtils.isNotBlank(str) && !"null".equals(str) && !"[]".equals(str)) {
                        list.add(value);
                    }
                }
            });
        }
        return list;
    }

    /**
     * 构建表格数据创建请求
     */
    private List<TableDataCreateDto> buildTableDataCreateRequests(String tableSn, JSONArray jsonArray) {
        List<TableDataCreateDto> requests = new ArrayList<>();

        for (Object item : jsonArray) {
            Map<String, Object> dataMap;
            if (item instanceof String str) {
                dataMap = JSONUtil.toBean(str, Map.class);
            } else {
                dataMap = (Map<String, Object>) item;
            }

            if (dataMap != null && !dataMap.isEmpty()) {
                TableDataCreateDto createRequest = new TableDataCreateDto();
                createRequest.setTableSn(tableSn);

                List<TableDataCreateDto.TableData> tableDataList = new ArrayList<>();
                dataMap.forEach((fieldSn, value) -> {
                    if (value != null) {
                        TableDataCreateDto.TableData tableData = new TableDataCreateDto.TableData();
                        tableData.setFieldSn(fieldSn);
                        tableData.setData(value.toString());
                        tableDataList.add(tableData);
                    }
                });

                createRequest.setDatas(tableDataList);
                requests.add(createRequest);
            }
        }

        return requests;
    }
}
