package com.ai.application.task.service;

import com.github.pagehelper.PageInfo;
import com.ai.application.task.api.dto.TaskRunDTO;
import com.ai.application.task.api.dto.query.TaskRunQueryDTO;
import com.ai.application.task.api.vo.TaskRunVO;
import java.util.List;
import java.util.Set;

/**
 * 任务执行记录表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
public interface ITaskRunService {

        /**
         * 分页
         *
         * @param queryDto
         * @return
         */
        PageInfo<TaskRunVO> page(TaskRunQueryDTO queryDto);

        /**
         * 列表
         *
         * @param sort
         * @param queryDto
         * @return
         */
        List<TaskRunVO> list(TaskRunQueryDTO queryDto);

        /**
         * 保存
         *
         * @param dto
         */
        void add(TaskRunDTO dto);

        /**
         * 更新
         *
         * @param dto
         */
        void update(TaskRunDTO dto);

        /**
         * 查看
         *
         * @param id
         * @return
         */
        TaskRunVO get(Integer id);
}