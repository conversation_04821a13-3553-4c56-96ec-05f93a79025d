package com.ai.application.app.auth.component;

import com.ai.framework.core.exception.BusinessException;
import com.ai.framework.web.component.JwtComponent;
import lombok.extern.slf4j.Slf4j;
import org.jose4j.lang.JoseException;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TokenComponent {
    public static String getToken(Integer userId, Integer tenantId, String userSn, String tenantSn, Integer appId, String privateKeyString) {
        String token = "";
        try {
            token = JwtComponent.sign(userId, tenantId, userSn, tenantSn, appId, privateKeyString);
        } catch (JoseException e) {
            log.error("JwtComponent.sign error:", e);
            throw new BusinessException(e.getMessage());
        }
        return token;
    }
}