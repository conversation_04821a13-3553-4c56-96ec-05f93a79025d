package com.ai.application.agent.base.api.mapstruct;
import com.ai.application.agent.base.api.entity.AgentRunNode;
import com.ai.application.agent.base.api.dto.AgentRunNodeDTO;
import com.ai.application.agent.base.api.vo.AgentRunNodeVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 工作流节点运行记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */

@Mapper(componentModel = "spring")
public interface AgentRunNodeMapstruct {

    AgentRunNode toEntity(AgentRunNodeDTO dto);
    List<AgentRunNode> toEntityList(List<AgentRunNodeDTO> dtolist);
    AgentRunNodeVO toVo(AgentRunNode entity);
    List<AgentRunNodeVO> toVoList(List<AgentRunNode> entities);
}
