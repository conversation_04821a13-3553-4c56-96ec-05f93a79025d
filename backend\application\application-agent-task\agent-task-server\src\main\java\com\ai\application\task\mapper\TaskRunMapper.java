package com.ai.application.task.mapper;

import com.ai.application.task.api.entity.TaskRun;
import com.ai.application.task.api.vo.TaskRunVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 任务执行记录表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Mapper
public interface TaskRunMapper extends BaseMapper<TaskRun> {
    /**
     * 查询任务执行记录表
     *
     * @return
     */
    List<TaskRunVO> selectTaskRunList();

    @Select("select * from task_run where task_id = #{taskId}")
    List<TaskRun> queryTaskRunByTaskId(@Param("taskId") Integer taskId);

    @Select("select * from task_run where task_id = #{taskId} order by create_time desc limit 1")
    TaskRun selectLastTaskRunByTaskId(@Param("taskId") Integer taskId);
}
