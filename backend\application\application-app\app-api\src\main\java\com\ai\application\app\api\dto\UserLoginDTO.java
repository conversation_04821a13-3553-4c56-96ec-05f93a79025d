package com.ai.application.app.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotBlank;

/**
 * 应用表
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Data
@Schema(name = "用户登录返参")
public class UserLoginDTO {
    /**
     * 用户账号
     */
    @Schema(description  = "用户账号")
    private String username;

    /**
     * 用户密码
     */
    @Schema(description  = "用户密码")
    private String password;

    /**
     * 应用ID
     */
    @Schema(description  = "应用ID")
    private Integer appId;

    @Schema(description = "租户编码")
    private String tenantSn;
}