package com.ai.application.base.file.api.mapstruct;


import com.ai.application.base.file.api.dto.DocFileDto;
import com.ai.application.base.file.api.entity.AppFile;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <p>
 * 工具资源表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */

@Mapper(componentModel = "spring")
public interface FileMapstruct {

    List<AppFile> toEntityList(List<DocFileDto> dtolist);


    List<DocFileDto> toDtoList(List<AppFile> appFiles);
}
