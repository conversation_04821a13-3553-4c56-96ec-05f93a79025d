package com.ai.application.agent.base.api.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 智能体MCP工具关联表
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Data
@Schema(name = "")
public class AgentUseMcpListVO {
    /**
     * 关联id
     */
    @Schema(description = "关联id")
    private Integer amcId;

    /**
     * 关联状态:1-启用,0-禁用,-1-删除
     */
    @Schema(description = "关联状态:1-启用,0-禁用,-1-删除")
    private Integer amcStatus;

    /**
     * 智能体id
     */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
     * 智能体版本id
     */
    @Schema(description = "智能体版本id")
    private Integer versionId;

    /**
     * MCP工具id
     */
    @Schema(description = "MCP工具id")
    private Integer mcpToolId;

    @Schema(description = "额外属性")
    private String mcpExtend;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}