package com.ai.application.app.api.feign;

import com.ai.application.app.api.dto.query.AppUserQueryDTO;
import com.ai.application.app.api.dto.query.AppUserVerifyPasswordDTO;
import com.ai.application.app.api.vo.AppUserDetailVO;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Tag(name = "用户Feign接口test", description = "用户基本操作test")
@FeignClient(
        value = ServiceConstant.APP,

        contextId = "IAppUserTestClient"
)
public interface IAppUserTestClient {
    String API_PREFIX = "/v1/feign/test";



    @GetMapping( "/getUserByIdTest/{userId}")
    ResultVo<AppUserVO> getUserByIdTest(@PathVariable("userId") Integer userId);

    @PostMapping(API_PREFIX + "/listTest")
    ResultVo<List<AppUserVO>> listTest(@RequestBody @Validated AppUserQueryDTO queryDto);

    @PostMapping("/getUsers/test")
    ResultVo<List<AppUserVO>> test(@RequestBody @Validated AppUserQueryDTO queryDto);
}
