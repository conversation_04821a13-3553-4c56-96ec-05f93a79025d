package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.dto.SessionCreateDTO;
import com.ai.application.agent.base.api.dto.SessionHistoryDTO;
import com.ai.application.agent.base.api.vo.SessionHistoryVO;
import com.github.pagehelper.PageInfo;

/**
 * 智能体运行会话表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface IAgentRunSessionService {
        /**
         * 历史会话列表
         * @param queryDto
         * @return
         */
        PageInfo<SessionHistoryVO> history(SessionHistoryDTO queryDto);

        /**
         * 创建会话
         *
         * @param dto
         */
        void create(SessionCreateDTO dto);
}