package com.ai.application.agent.base.api.feign;

import com.ai.application.agent.base.api.feign.fallback.IAgentRunLogFallback;
import com.ai.application.agent.base.api.vo.AgentRunMcpVO;
import com.ai.application.agent.base.api.vo.AgentRunToolVO;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import java.util.List;

@Tag(name = "智能体执行日志Feign接口", description = "智能体执行接口基本操作")
@FeignClient(
        value = ServiceConstant.AGENT_BASE,
        fallback = IAgentRunLogFallback.class,
        contextId = "IAgentRunClient"
)
public interface IAgentRunLogClient {
    String API_PREFIX = "/v1/feign/agent/run/log";

    @Operation(summary = "根据mcpId查看执行日志")
    @GetMapping(API_PREFIX + "/mcp/{mcpId}")
    ResultVo<List<AgentRunMcpVO>> mcpLogs(@PathVariable("mcpId") Integer mcpId);

    @Operation(summary = "根据toolId查看执行日志")
    @GetMapping(API_PREFIX + "/tool/{toolId}")
    ResultVo<List<AgentRunToolVO>> toolLogs(@PathVariable("toolId") Integer toolId);
}