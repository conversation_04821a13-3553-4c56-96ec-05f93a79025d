package com.ai.application.agent.run.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 文档文件DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DocFileDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 文件ID
     */
    private Long fileId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件编号
     */
    private String fileSn;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 文档标签
     */
    private String documentTag;

    /**
     * 文档摘要
     */
    private String documentSummary;

    /**
     * 文档后缀
     */
    private String documentSuffix;

    /**
     * 相似度分数
     */
    private Double score;

    /**
     * 创建时间
     */
    private Long createTime;
}
