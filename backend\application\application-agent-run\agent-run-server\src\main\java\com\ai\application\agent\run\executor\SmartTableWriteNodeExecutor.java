package com.ai.application.agent.run.executor;

import com.ai.application.agent.run.dto.SmartTableWriteRequestDTO;
import com.ai.application.agent.run.dto.SmartTableWriteResultDTO;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.service.ISmartTableWriteService;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import com.ai.framework.workflow.excutor.NodeExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 智能表格写入节点执行器
 * 用于在工作流中写入数据到智能表格
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SmartTableWriteNodeExecutor implements NodeExecutor {

    private final ISmartTableWriteService smartTableWriteService;

    @Override
    public void execute(WorkflowContext context) {
        String nodeKey = context.getCurrentNodeKey();
        NodeContext nodeCtx = context.getNodeContexts().get(nodeKey);
        Map<String, Object> nodeDef = nodeCtx.getNodeDefinition();

        log.info("SmartTableWriteNodeExecutor execute start, nodeKey: {}, nodeDef: {}", nodeKey, JsonUtils.toJsonString(nodeDef));

        try {
            // 设置节点状态为运行中
            nodeCtx.setStatus(NodeStatus.RUNNING);

            // 初始化节点输出
            if (nodeCtx.getOutput() == null) {
                nodeCtx.setOutput(new HashMap<>());
            }

            // 从节点定义中获取输入参数
            Map<String, Object> inputParameters = (Map<String, Object>) nodeDef.get("inputParameters");
            if (inputParameters == null) {
                throw new ServiceException(ExecutorError.NODE_DEFINITION_IS_NULL);
            }

            // 构建智能表格写入请求
            SmartTableWriteRequestDTO request = buildSmartTableWriteRequest(inputParameters, context);

            // 获取授权信息
            String authorization = (String) context.getGlobalVars().get("authorization");

            // 执行智能表格写入
            ResultVo<SmartTableWriteResultDTO> result = smartTableWriteService.executeSmartTableWrite(request, authorization);

            if (result.getCode() != 0 || result.getData() == null || !result.getData().getSuccess()) {
                String errorMsg = result.getData() != null ? result.getData().getErrorMessage() : result.getMessage();
                throw new ServiceException(result.getCode(), errorMsg);
            }

            SmartTableWriteResultDTO writeResult = result.getData();

            // 构建输出结果
            Map<String, Object> outputResult = buildOutputResult(writeResult);

            // 将结果写入输出参数
            writeOutputParameters(nodeDef, context, outputResult);

            // 设置节点输出
            nodeCtx.getOutput().putAll(outputResult);

            // 设置节点状态为成功
            nodeCtx.setStatus(NodeStatus.SUCCESS);
            nodeCtx.setEndTime(java.time.LocalDateTime.now());

            log.info("SmartTableWriteNodeExecutor execute success, result: {}", JsonUtils.toJsonString(outputResult));

        } catch (Exception e) {
            log.error("SmartTableWriteNodeExecutor execute error", e);
            nodeCtx.setStatus(NodeStatus.FAILED);
            nodeCtx.setErrorMsg("智能表格写入执行失败: " + e.getMessage());
            nodeCtx.setEndTime(java.time.LocalDateTime.now());
            throw e;
        }
    }

    /**
     * 构建智能表格写入请求
     */
    private SmartTableWriteRequestDTO buildSmartTableWriteRequest(Map<String, Object> inputParameters, WorkflowContext context) {
        // 获取参数值，支持变量替换
        String tableSn = getParameterValue(inputParameters, "tableSn", context);
        String typeStr = getParameterValue(inputParameters, "type", context);
        String overwriteStr = getParameterValue(inputParameters, "overwrite", context);
        String inputTypeStr = getParameterValue(inputParameters, "inputType", context);
        Object data = getParameterObject(inputParameters, "data", context);

        // 参数校验
        if (StringUtils.isBlank(tableSn)) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "表格编号不能为空");
        }
        if (data == null) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "写入数据不能为空");
        }

        // 构建请求
        return SmartTableWriteRequestDTO.builder()
                .tableSn(tableSn)
                .type(StringUtils.isNotBlank(typeStr) ? Integer.parseInt(typeStr) : 1)
                .overwrite(StringUtils.isNotBlank(overwriteStr) ? Integer.parseInt(overwriteStr) : 0)
                .inputType(StringUtils.isNotBlank(inputTypeStr) ? Integer.parseInt(inputTypeStr) : null)
                .data(data)
                .build();
    }

    /**
     * 构建输出结果
     */
    private Map<String, Object> buildOutputResult(SmartTableWriteResultDTO writeResult) {
        Map<String, Object> result = new HashMap<>();
        result.put("output", writeResult.getOutput() != null ? writeResult.getOutput() : "");
        result.put("success", writeResult.getSuccess());
        result.put("tableSn", writeResult.getTableSn());
        result.put("rowCount", writeResult.getRowCount());
        
        if (StringUtils.isNotBlank(writeResult.getErrorMessage())) {
            result.put("errorMessage", writeResult.getErrorMessage());
        }

        return result;
    }

    /**
     * 获取参数值，支持变量替换
     */
    private String getParameterValue(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        String strValue = value.toString();
        
        // 如果是变量引用（以$开头），从全局变量中获取
        if (strValue.startsWith("$")) {
            String varName = strValue.substring(1);
            Object varValue = context.getGlobalVars().get(varName);
            return varValue != null ? varValue.toString() : null;
        }
        
        return strValue;
    }

    /**
     * 获取参数对象，支持变量替换
     */
    private Object getParameterObject(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        if (value instanceof String) {
            String strValue = value.toString();
            // 如果是变量引用（以$开头），从全局变量中获取
            if (strValue.startsWith("$")) {
                String varName = strValue.substring(1);
                return context.getGlobalVars().get(varName);
            }
        }
        
        return value;
    }

    /**
     * 写入输出参数
     */
    private void writeOutputParameters(Map<String, Object> nodeDef, WorkflowContext context, Map<String, Object> result) {
        Map<String, Object> outputParameters = (Map<String, Object>) nodeDef.get("outputParameters");
        if (outputParameters != null) {
            for (Map.Entry<String, Object> entry : outputParameters.entrySet()) {
                String outputKey = entry.getKey();
                String variableName = entry.getValue().toString();

                Object resultValue = result.get(outputKey);
                if (resultValue != null) {
                    context.setVar(variableName, resultValue);
                    log.info("Set variable {} = {}", variableName, resultValue);
                }
            }
        }
    }

    @Override
    public String getType() {
        return "SMART_TABLE_WRITE";
    }
}
