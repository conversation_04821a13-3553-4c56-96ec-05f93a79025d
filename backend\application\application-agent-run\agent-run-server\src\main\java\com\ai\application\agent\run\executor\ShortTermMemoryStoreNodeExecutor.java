package com.ai.application.agent.run.executor;

import cn.hutool.json.JSONUtil;
import com.ai.application.agent.run.dto.ShortTermMemoryStoreRequestDTO;
import com.ai.application.agent.run.dto.ShortTermMemoryStoreResultDTO;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.service.IShortTermMemoryStoreService;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import com.ai.framework.workflow.excutor.NodeExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 短期记忆存储节点执行器
 * 用于在工作流中存储短期记忆到记忆服务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ShortTermMemoryStoreNodeExecutor implements NodeExecutor {

    private final IShortTermMemoryStoreService shortTermMemoryStoreService;

    @Override
    public void execute(WorkflowContext context) {
        String nodeKey = context.getCurrentNodeKey();
        NodeContext nodeCtx = context.getNodeContexts().get(nodeKey);
        Map<String, Object> nodeDef = nodeCtx.getNodeDefinition();

        log.info("ShortTermMemoryStoreNodeExecutor execute start, nodeKey: {}, nodeDef: {}", nodeKey, JsonUtils.toJsonString(nodeDef));

        try {
            // 设置节点状态为运行中
            nodeCtx.setStatus(NodeStatus.RUNNING);

            // 初始化节点输出
            if (nodeCtx.getOutput() == null) {
                nodeCtx.setOutput(new HashMap<>());
            }

            // 从节点定义中获取输入参数
            Map<String, Object> inputParameters = (Map<String, Object>) nodeDef.get("inputParameters");
            if (inputParameters == null) {
                throw new ServiceException(ExecutorError.NODE_DEFINITION_IS_NULL);
            }

            // 构建短期记忆存储请求
            ShortTermMemoryStoreRequestDTO request = buildShortTermMemoryStoreRequest(inputParameters, context);

            // 获取授权信息
            String authorization = (String) context.getGlobalVars().get("authorization");

            // 执行短期记忆存储
            ResultVo<ShortTermMemoryStoreResultDTO> result = shortTermMemoryStoreService.executeShortTermMemoryStore(request, authorization);

            if (result.getCode() != 0 || result.getData() == null || !result.getData().getSuccess()) {
                String errorMsg = result.getData() != null ? result.getData().getErrorMessage() : result.getMessage();
                throw new ServiceException(result.getCode(), errorMsg);
            }

            ShortTermMemoryStoreResultDTO storeResult = result.getData();

            // 构建输出结果
            Map<String, Object> outputResult = buildOutputResult(storeResult);

            // 将结果写入输出参数
            writeOutputParameters(nodeDef, context, outputResult);

            // 设置节点输出
            nodeCtx.getOutput().putAll(outputResult);

            // 设置节点状态为成功
            nodeCtx.setStatus(NodeStatus.SUCCESS);
            nodeCtx.setEndTime(java.time.LocalDateTime.now());

            log.info("ShortTermMemoryStoreNodeExecutor execute success, stored {} memories", storeResult.getMemoryCount());

        } catch (Exception e) {
            log.error("ShortTermMemoryStoreNodeExecutor execute error", e);
            nodeCtx.setStatus(NodeStatus.FAILED);
            nodeCtx.setErrorMsg("短期记忆存储执行失败: " + e.getMessage());
            nodeCtx.setEndTime(java.time.LocalDateTime.now());
            throw e;
        }
    }

    /**
     * 构建短期记忆存储请求
     */
    private ShortTermMemoryStoreRequestDTO buildShortTermMemoryStoreRequest(Map<String, Object> inputParameters, WorkflowContext context) {
        // 获取参数值，支持变量替换
        String agentId = getParameterValue(inputParameters, "agent_id", context);
        String userId = getParameterValue(inputParameters, "user_id", context);
        String bucketSn = getParameterValue(inputParameters, "bucketSn", context);
        String memoryContent = getParameterValue(inputParameters, "memoryContent", context);
        Object memoryContents = getParameterObject(inputParameters, "memoryContents", context);

        // 参数校验
        if (StringUtils.isBlank(agentId)) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "智能体ID不能为空");
        }
        if (StringUtils.isBlank(userId)) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "用户ID不能为空");
        }

        // 转换批量记忆内容
        List<ShortTermMemoryStoreRequestDTO.MemoryContentItem> memoryContentList = null;
        if (memoryContents instanceof List<?> contents) {
            memoryContentList = new ArrayList<>();
            for (Object content : contents) {
                ShortTermMemoryStoreRequestDTO.MemoryContentItem item = convertToMemoryContentItem(content);
                if (item != null) {
                    memoryContentList.add(item);
                }
            }
        }

        // 构建请求
        return ShortTermMemoryStoreRequestDTO.builder()
                .agentId(agentId)
                .userId(userId)
                .bucketSn(bucketSn)
                .memoryContent(memoryContent)
                .memoryContents(memoryContentList)
                .build();
    }

    /**
     * 转换为记忆内容项
     */
    private ShortTermMemoryStoreRequestDTO.MemoryContentItem convertToMemoryContentItem(Object content) {
        if (content == null) {
            return null;
        }

        try {
            if (content instanceof Map<?, ?> map) {
                String memoryContent = map.get("memoryContent") != null ? map.get("memoryContent").toString() : null;
                String bucketSn = map.get("bucketSn") != null ? map.get("bucketSn").toString() : null;
                
                if (StringUtils.isNotBlank(memoryContent)) {
                    return ShortTermMemoryStoreRequestDTO.MemoryContentItem.builder()
                            .memoryContent(memoryContent)
                            .bucketSn(bucketSn)
                            .build();
                }
            } else if (content instanceof String) {
                // 尝试解析JSON字符串
                Map<String, Object> contentMap = JSONUtil.toBean(content.toString(), Map.class);
                return convertToMemoryContentItem(contentMap);
            }
        } catch (Exception e) {
            log.warn("Failed to convert memory content item: {}", content, e);
        }

        return null;
    }

    /**
     * 构建输出结果
     */
    private Map<String, Object> buildOutputResult(ShortTermMemoryStoreResultDTO storeResult) {
        Map<String, Object> result = new HashMap<>();
        result.put("output", storeResult.getOutput());
        result.put("success", storeResult.getSuccess());
        result.put("memoryCount", storeResult.getMemoryCount());
        
        if (CollectionUtils.isNotEmpty(storeResult.getMemoryIds())) {
            result.put("memoryIds", storeResult.getMemoryIds());
        }
        
        if (StringUtils.isNotBlank(storeResult.getErrorMessage())) {
            result.put("errorMessage", storeResult.getErrorMessage());
        }

        return result;
    }

    /**
     * 获取参数值，支持变量替换
     */
    private String getParameterValue(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        String strValue = value.toString();
        
        // 如果是变量引用（以$开头），从全局变量中获取
        if (strValue.startsWith("$")) {
            String varName = strValue.substring(1);
            Object varValue = context.getGlobalVars().get(varName);
            return varValue != null ? varValue.toString() : null;
        }
        
        return strValue;
    }

    /**
     * 获取参数对象，支持变量替换
     */
    private Object getParameterObject(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        if (value instanceof String) {
            String strValue = value.toString();
            // 如果是变量引用（以$开头），从全局变量中获取
            if (strValue.startsWith("$")) {
                String varName = strValue.substring(1);
                return context.getGlobalVars().get(varName);
            }
        }
        
        return value;
    }

    /**
     * 写入输出参数
     */
    private void writeOutputParameters(Map<String, Object> nodeDef, WorkflowContext context, Map<String, Object> result) {
        Map<String, Object> outputParameters = (Map<String, Object>) nodeDef.get("outputParameters");
        if (outputParameters != null) {
            for (Map.Entry<String, Object> entry : outputParameters.entrySet()) {
                String outputKey = entry.getKey();
                String variableName = entry.getValue().toString();

                Object resultValue = result.get(outputKey);
                if (resultValue != null) {
                    context.setVar(variableName, resultValue);
                    log.info("Set variable {} = {}", variableName, resultValue);
                }
            }
        }
    }

    @Override
    public String getType() {
        return "SHORT_TERM_MEMORY_STORE";
    }
}
