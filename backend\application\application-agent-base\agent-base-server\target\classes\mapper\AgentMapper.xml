<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.agent.base.mapper.AgentMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.agent.base.api.entity.Agent">
                    <id column="agent_id" property="agentId" />
                    <result column="agent_sn" property="agentSn" />
                    <result column="agent_name" property="agentName" />
                    <result column="agent_desc" property="agentDesc" />
                    <result column="agent_type" property="agentType" />
                    <result column="agent_metadata" property="agentMetadata" />
                    <result column="agent_status" property="agentStatus" />
                    <result column="version_id" property="versionId" />
                    <result column="tenant_id" property="tenantId" />
                    <result column="user_id" property="userId" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        agent_id, agent_sn, agent_name, agent_desc, agent_type, agent_metadata, agent_status, version_id, tenant_id, user_id, create_time, update_time
    </sql>

    <select id="selectAgentList" resultType="com.ai.application.agent.base.api.vo.AgentVO">
        select
        <include refid="com.ai.application.agent.base.mapper.AgentMapper.Base_Column_List"></include>
        from agent
        order by create_time desc limit 10;
    </select>

    <select id="queryTokensFrequentAgent" resultType="com.ai.application.agent.base.api.vo.AgentTokensStatisticsDetailVO">
        SELECT
        B.agent_name stat_label,
        A.total_tokens stat_value
        FROM
        (SELECT agent_id,SUM(total_tokens) total_tokens FROM `agent_stats_daily`
        WHERE tenant_id=#{tenantId} and user_id=#{userId}
        <if test="days = 7">
            and stats_date>=DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        </if>
        <if test="days = 30">
            and stats_date>=DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        </if>
        GROUP BY agent_id) A
        LEFT JOIN agent B ON(A.agent_id=B.agent_id)
        WHERE B.agent_status=5
        ORDER BY A.total_tokens DESC LIMIT 5
    </select>

    <select id="queryLastTokensAgent" resultType="com.ai.application.agent.base.api.vo.AgentTokensStatisticsDetailVO">
        SELECT
            stats_date stat_label,
            SUM(total_tokens) stat_value
        FROM agent_stats_daily
        WHERE tenant_id=#{tenantId} and user_id=#{userId}
        <if test="days = 7">
            and stats_date>=DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        </if>
        <if test="days = 30">
            and stats_date>=DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        </if>
        GROUP BY stats_date
    </select>

    <select id="getAgentTotalTokens" resultType="java.lang.Integer">
        SELECT
        IFNULL(SUM(total_tokens),0)
        FROM agent_stats_daily
        WHERE tenant_id=#{tenantId} and user_id=#{userId}
        <if test="days!=null and days = 7">
            and stats_date>=DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        </if>
        <if test="days = 30">
            and stats_date>=DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        </if>
        <if test="startDate!=null and endDate !=null">
            and stats_date BETWEEN #{startDate} and #{endDate}
        </if>
    </select>
</mapper>