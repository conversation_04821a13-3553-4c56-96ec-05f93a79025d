package com.ai.application.agent.base.api.dto.query;

import com.ai.framework.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 智能体表-查询条件
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@Schema(name = "智能体列表AgentPageDTO")
public class AgentPageDTO extends PageParam {
    @Schema(description = "搜索关键词")
    private String keywords;

    @Schema(description = "智能体页面来源:10-管理,20-市场,30-工作区")
    private Integer pageSource;

    @Schema(description = "智能体名称")
    private String agentName;

    @Schema(description = "智能体类型: 10:对话流, 20:工作流, 30:master")
    private Integer agentType;

    @Schema(description = "状态 0停用 1开发 5发布")
    private Integer agentStatus;
}