package com.ai.application.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 租户扩展配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-28
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("tenant_extend")
public class TenantExtend implements Serializable {
    @Schema(description = "")
    @TableId(type = IdType.AUTO)
    private Integer itemId;

    /**
     * 参数name
     */
    @Schema(description = "参数name")
    private String itemName;

    /**
     * 参数值
     */
    @Schema(description = "参数值")
    private String itemValue;

    /**
     * 记录状态0:失效,1:生效
     */
    @Schema(description = "记录状态0:失效,1:生效")
    private Integer itemStatus;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Integer tenantId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}