# 智能体记忆模块配置文件
app:
  name: "AgentMemory"
  version: "1.0.0"
  host: "0.0.0.0"  # 注释后将使用动态获取的本机IP
  port: 6023
  debug: false
  log_level: "INFO"
  # workers: 2  #默认自动根据CPU核心数计算 (CPU核心数*2，最大8个)，debug模式下自动设为1

# Elasticsearch配置
elasticsearch:
  hosts:
    - "http://************:6200"
  index_name: "agent_memory"
  timeout: 30
  max_retries: 3
  retry_on_timeout: true
  # 认证配置 (如果ES启用了安全功能)
  username: "elastic"  # ES用户名，如：elastic
  password: "BWSyko0rZUiuTA30qAhF"  # ES密码
  use_ssl: false  # 是否使用SSL/TLS连接
  verify_certs: false  # 是否验证SSL证书
  ca_certs: ""  # CA证书路径
  client_cert: ""  # 客户端证书路径
  client_key: ""  # 客户端私钥路径

# 向量编码模型配置
embedding:
  model_name: "thenlper/gte-base-zh"
  model_path: "./models/gte-base-zh"
  device: "cpu"  # cpu 或 cuda
  dimensions: 768
  max_length: 512

# Nacos 3.0 兼容配置
nacos:
  # 服务器地址 (支持多个地址，逗号分隔)
  server_addresses: "************:7148"

  # 服务注册配置
  service_name: "agent-memory-service"
  ip: "************"  # 注释后将优先使用环境变量，及动态获取的本机IP
  port: 6023
  weight: 1.0
  cluster_name: "DEFAULT"
  group: "dev"

  # 命名空间配置
  namespace: "dev"  # 设置为具体的namespace ID

  # 认证配置 (Nacos 3.0增强安全性)
  username: "nacos"  # 如果启用认证，设置用户名
  password: "DUg1Yjewwu"  # 如果启用认证，设置密码

  # 配置中心
  data_id: "agent-memory-config"
  
  # gRPC配置 (Nacos 3.0推荐使用gRPC)
  grpc:
    enabled: true  # 优先使用gRPC协议
    timeout: 5000  # gRPC超时时间(毫秒)
    max_receive_message_length: 104857600  # 100MB
    max_keep_alive_ms: 60000  # 60秒
    initial_window_size: 10485760  # 10MB
    initial_conn_window_size: 10485760  # 10MB
  
  # 健康检查配置
  health_check:
    enabled: true
    interval: 5  # 心跳间隔(秒)
    timeout: 3   # 健康检查超时(秒)
    
  # 容错配置
  failover:
    enabled: true
    retry_times: 3
    retry_interval: 1  # 重试间隔(秒)

# 记忆检索配置
memory:
  default_similarity_threshold: 0.7
  max_search_results: 50
  default_search_results: 10
  memory_expire_days: 0
  vector_dimension: 768
  
# 日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  rotation: "1 day"
  retention: "7 days"
  
# 性能配置
performance:
  max_workers: 4
  connection_pool_size: 20
  request_timeout: 30

# 安全配置
security:
  api_key_enabled: false
  api_key: ""
  cors_enabled: true
  allowed_origins: ["*"]
  allowed_methods: ["GET", "POST", "PUT", "DELETE"]
  allowed_headers: ["*"] 