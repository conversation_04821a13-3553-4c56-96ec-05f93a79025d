package com.ai.application.task.api.mapstruct;

import com.ai.application.task.api.dto.TaskAddDTO;
import com.ai.application.task.api.dto.TaskDTO;
import com.ai.application.task.api.entity.Task;
import com.ai.application.task.api.vo.TaskDetailVO;
import com.ai.application.task.api.vo.TaskVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-14T11:01:00+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class TaskMapstructImpl implements TaskMapstruct {

    @Override
    public Task toEntity(TaskDTO dto) {
        if ( dto == null ) {
            return null;
        }

        Task task = new Task();

        task.setTaskId( dto.getTaskId() );
        task.setTaskSn( dto.getTaskSn() );
        task.setTaskName( dto.getTaskName() );
        task.setTaskDesc( dto.getTaskDesc() );
        task.setTaskType( dto.getTaskType() );
        task.setTaskStatus( dto.getTaskStatus() );
        task.setCronExpression( dto.getCronExpression() );
        task.setStartTime( dto.getStartTime() );
        task.setEndTime( dto.getEndTime() );
        task.setNextRunTime( dto.getNextRunTime() );
        task.setLastRunTime( dto.getLastRunTime() );
        task.setRunCount( dto.getRunCount() );
        task.setMaxRunCount( dto.getMaxRunCount() );
        task.setTimeoutSeconds( dto.getTimeoutSeconds() );
        task.setRetryCount( dto.getRetryCount() );
        task.setRetryInterval( dto.getRetryInterval() );
        task.setTaskInput( dto.getTaskInput() );
        task.setTaskConfig( dto.getTaskConfig() );
        task.setNotificationConfig( dto.getNotificationConfig() );
        task.setAgentId( dto.getAgentId() );
        task.setVersionId( dto.getVersionId() );
        task.setTenantId( dto.getTenantId() );
        task.setCreateUserId( dto.getCreateUserId() );
        task.setUpdateUserId( dto.getUpdateUserId() );
        task.setCreateTime( dto.getCreateTime() );
        task.setUpdateTime( dto.getUpdateTime() );

        return task;
    }

    @Override
    public Task toEntity(TaskAddDTO dto) {
        if ( dto == null ) {
            return null;
        }

        Task task = new Task();

        task.setTaskName( dto.getTaskName() );
        task.setTaskDesc( dto.getTaskDesc() );
        task.setTaskType( dto.getTaskType() );
        task.setTaskStatus( dto.getTaskStatus() );
        task.setCronExpression( dto.getCronExpression() );
        task.setStartTime( dto.getStartTime() );
        task.setEndTime( dto.getEndTime() );
        task.setNextRunTime( dto.getNextRunTime() );
        task.setLastRunTime( dto.getLastRunTime() );
        task.setRunCount( dto.getRunCount() );
        task.setMaxRunCount( dto.getMaxRunCount() );
        task.setTimeoutSeconds( dto.getTimeoutSeconds() );
        task.setRetryCount( dto.getRetryCount() );
        task.setRetryInterval( dto.getRetryInterval() );
        task.setTaskInput( dto.getTaskInput() );
        task.setTaskConfig( dto.getTaskConfig() );
        task.setNotificationConfig( dto.getNotificationConfig() );

        return task;
    }

    @Override
    public List<Task> toEntityList(List<TaskDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<Task> list = new ArrayList<Task>( dtolist.size() );
        for ( TaskDTO taskDTO : dtolist ) {
            list.add( toEntity( taskDTO ) );
        }

        return list;
    }

    @Override
    public TaskVO toVo(Task entity) {
        if ( entity == null ) {
            return null;
        }

        TaskVO taskVO = new TaskVO();

        taskVO.setTaskId( entity.getTaskId() );
        taskVO.setTaskSn( entity.getTaskSn() );
        taskVO.setTaskName( entity.getTaskName() );
        taskVO.setTaskDesc( entity.getTaskDesc() );
        taskVO.setTaskType( entity.getTaskType() );
        taskVO.setTaskStatus( entity.getTaskStatus() );
        taskVO.setCronExpression( entity.getCronExpression() );
        taskVO.setStartTime( entity.getStartTime() );
        taskVO.setEndTime( entity.getEndTime() );
        taskVO.setNextRunTime( entity.getNextRunTime() );
        taskVO.setLastRunTime( entity.getLastRunTime() );
        taskVO.setRunCount( entity.getRunCount() );
        taskVO.setMaxRunCount( entity.getMaxRunCount() );
        taskVO.setTimeoutSeconds( entity.getTimeoutSeconds() );
        taskVO.setRetryCount( entity.getRetryCount() );
        taskVO.setRetryInterval( entity.getRetryInterval() );
        taskVO.setTaskInput( entity.getTaskInput() );
        taskVO.setTaskConfig( entity.getTaskConfig() );
        taskVO.setNotificationConfig( entity.getNotificationConfig() );
        taskVO.setAgentId( entity.getAgentId() );
        taskVO.setVersionId( entity.getVersionId() );
        taskVO.setCreateTime( entity.getCreateTime() );
        taskVO.setUpdateTime( entity.getUpdateTime() );

        return taskVO;
    }

    @Override
    public TaskDetailVO toDetailVo(Task entity) {
        if ( entity == null ) {
            return null;
        }

        TaskDetailVO taskDetailVO = new TaskDetailVO();

        taskDetailVO.setTaskSn( entity.getTaskSn() );
        taskDetailVO.setTaskName( entity.getTaskName() );
        taskDetailVO.setTaskDesc( entity.getTaskDesc() );
        taskDetailVO.setTaskType( entity.getTaskType() );
        taskDetailVO.setTaskStatus( entity.getTaskStatus() );
        taskDetailVO.setCronExpression( entity.getCronExpression() );
        taskDetailVO.setStartTime( entity.getStartTime() );
        taskDetailVO.setEndTime( entity.getEndTime() );
        taskDetailVO.setNextRunTime( entity.getNextRunTime() );
        taskDetailVO.setLastRunTime( entity.getLastRunTime() );
        taskDetailVO.setRunCount( entity.getRunCount() );
        taskDetailVO.setMaxRunCount( entity.getMaxRunCount() );
        taskDetailVO.setTimeoutSeconds( entity.getTimeoutSeconds() );
        taskDetailVO.setRetryCount( entity.getRetryCount() );
        taskDetailVO.setRetryInterval( entity.getRetryInterval() );
        taskDetailVO.setTaskInput( entity.getTaskInput() );
        taskDetailVO.setTaskConfig( entity.getTaskConfig() );
        taskDetailVO.setNotificationConfig( entity.getNotificationConfig() );
        taskDetailVO.setAgentId( entity.getAgentId() );
        taskDetailVO.setVersionId( entity.getVersionId() );
        taskDetailVO.setTenantId( entity.getTenantId() );
        taskDetailVO.setCreateUserId( entity.getCreateUserId() );
        taskDetailVO.setUpdateUserId( entity.getUpdateUserId() );
        taskDetailVO.setCreateTime( entity.getCreateTime() );
        taskDetailVO.setUpdateTime( entity.getUpdateTime() );

        return taskDetailVO;
    }

    @Override
    public List<TaskVO> toVoList(List<Task> entities) {
        if ( entities == null ) {
            return null;
        }

        List<TaskVO> list = new ArrayList<TaskVO>( entities.size() );
        for ( Task task : entities ) {
            list.add( toVo( task ) );
        }

        return list;
    }
}
