package com.ai.application.task.api.mapstruct;

import com.ai.application.task.api.dto.TaskAddDTO;
import com.ai.application.task.api.dto.TaskBatchAddDTO;
import com.ai.application.task.api.dto.TaskCronAddDTO;
import com.ai.application.task.api.dto.TaskDTO;
import com.ai.application.task.api.entity.Task;
import com.ai.application.task.api.vo.TaskBatchDetailVO;
import com.ai.application.task.api.vo.TaskBatchPageVO;
import com.ai.application.task.api.vo.TaskCronDetailVO;
import com.ai.application.task.api.vo.TaskCronPageVO;
import com.ai.application.task.api.vo.TaskVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T09:54:05+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class TaskMapstructImpl implements TaskMapstruct {

    @Override
    public Task toEntity(TaskDTO dto) {
        if ( dto == null ) {
            return null;
        }

        Task task = new Task();

        task.setTaskId( dto.getTaskId() );
        task.setTaskSn( dto.getTaskSn() );
        task.setTaskName( dto.getTaskName() );
        task.setTaskDesc( dto.getTaskDesc() );
        task.setTaskType( dto.getTaskType() );
        task.setTaskStatus( dto.getTaskStatus() );
        task.setCronExpression( dto.getCronExpression() );
        task.setStartTime( dto.getStartTime() );
        task.setEndTime( dto.getEndTime() );
        task.setNextRunTime( dto.getNextRunTime() );
        task.setLastRunTime( dto.getLastRunTime() );
        task.setRunCount( dto.getRunCount() );
        task.setMaxRunCount( dto.getMaxRunCount() );
        task.setTimeoutSeconds( dto.getTimeoutSeconds() );
        task.setRetryCount( dto.getRetryCount() );
        task.setRetryInterval( dto.getRetryInterval() );
        task.setTaskInput( dto.getTaskInput() );
        task.setTaskConfig( dto.getTaskConfig() );
        task.setNotificationConfig( dto.getNotificationConfig() );
        task.setAgentId( dto.getAgentId() );
        task.setVersionId( dto.getVersionId() );
        task.setTenantId( dto.getTenantId() );
        task.setCreateUserId( dto.getCreateUserId() );
        task.setUpdateUserId( dto.getUpdateUserId() );
        task.setCreateTime( dto.getCreateTime() );
        task.setUpdateTime( dto.getUpdateTime() );

        return task;
    }

    @Override
    public Task toEntity(TaskAddDTO dto) {
        if ( dto == null ) {
            return null;
        }

        Task task = new Task();

        task.setTaskName( dto.getTaskName() );
        task.setTaskDesc( dto.getTaskDesc() );
        task.setTaskType( dto.getTaskType() );
        task.setTaskStatus( dto.getTaskStatus() );
        task.setCronExpression( dto.getCronExpression() );
        task.setStartTime( dto.getStartTime() );
        task.setEndTime( dto.getEndTime() );
        task.setNextRunTime( dto.getNextRunTime() );
        task.setLastRunTime( dto.getLastRunTime() );
        task.setRunCount( dto.getRunCount() );
        task.setMaxRunCount( dto.getMaxRunCount() );
        task.setTimeoutSeconds( dto.getTimeoutSeconds() );
        task.setRetryCount( dto.getRetryCount() );
        task.setRetryInterval( dto.getRetryInterval() );
        task.setTaskInput( dto.getTaskInput() );
        task.setTaskConfig( dto.getTaskConfig() );
        task.setNotificationConfig( dto.getNotificationConfig() );

        return task;
    }

    @Override
    public Task toEntity(TaskBatchAddDTO dto) {
        if ( dto == null ) {
            return null;
        }

        Task task = new Task();

        task.setTaskName( dto.getTaskName() );

        return task;
    }

    @Override
    public Task toEntity(TaskCronAddDTO dto) {
        if ( dto == null ) {
            return null;
        }

        Task task = new Task();

        task.setTaskName( dto.getTaskName() );
        task.setStartTime( dto.getStartTime() );
        task.setEndTime( dto.getEndTime() );
        task.setTaskInput( dto.getTaskInput() );

        return task;
    }

    @Override
    public List<Task> toEntityList(List<TaskDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<Task> list = new ArrayList<Task>( dtolist.size() );
        for ( TaskDTO taskDTO : dtolist ) {
            list.add( toEntity( taskDTO ) );
        }

        return list;
    }

    @Override
    public TaskVO toVo(Task entity) {
        if ( entity == null ) {
            return null;
        }

        TaskVO taskVO = new TaskVO();

        taskVO.setTaskId( entity.getTaskId() );
        taskVO.setTaskSn( entity.getTaskSn() );
        taskVO.setTaskName( entity.getTaskName() );
        taskVO.setTaskDesc( entity.getTaskDesc() );
        taskVO.setTaskType( entity.getTaskType() );
        taskVO.setTaskStatus( entity.getTaskStatus() );
        taskVO.setCronExpression( entity.getCronExpression() );
        taskVO.setStartTime( entity.getStartTime() );
        taskVO.setEndTime( entity.getEndTime() );
        taskVO.setNextRunTime( entity.getNextRunTime() );
        taskVO.setLastRunTime( entity.getLastRunTime() );
        taskVO.setRunCount( entity.getRunCount() );
        taskVO.setMaxRunCount( entity.getMaxRunCount() );
        taskVO.setTimeoutSeconds( entity.getTimeoutSeconds() );
        taskVO.setRetryCount( entity.getRetryCount() );
        taskVO.setRetryInterval( entity.getRetryInterval() );
        taskVO.setTaskInput( entity.getTaskInput() );
        taskVO.setTaskConfig( entity.getTaskConfig() );
        taskVO.setNotificationConfig( entity.getNotificationConfig() );
        taskVO.setAgentId( entity.getAgentId() );
        taskVO.setVersionId( entity.getVersionId() );
        taskVO.setCreateTime( entity.getCreateTime() );
        taskVO.setUpdateTime( entity.getUpdateTime() );

        return taskVO;
    }

    @Override
    public TaskBatchDetailVO toBatchDetailVo(Task entity) {
        if ( entity == null ) {
            return null;
        }

        TaskBatchDetailVO taskBatchDetailVO = new TaskBatchDetailVO();

        taskBatchDetailVO.setTaskSn( entity.getTaskSn() );
        taskBatchDetailVO.setTaskName( entity.getTaskName() );
        taskBatchDetailVO.setTaskDesc( entity.getTaskDesc() );
        taskBatchDetailVO.setTaskType( entity.getTaskType() );
        taskBatchDetailVO.setTaskStatus( entity.getTaskStatus() );
        taskBatchDetailVO.setCronExpression( entity.getCronExpression() );
        taskBatchDetailVO.setStartTime( entity.getStartTime() );
        taskBatchDetailVO.setEndTime( entity.getEndTime() );
        taskBatchDetailVO.setNextRunTime( entity.getNextRunTime() );
        taskBatchDetailVO.setLastRunTime( entity.getLastRunTime() );
        taskBatchDetailVO.setRunCount( entity.getRunCount() );
        taskBatchDetailVO.setMaxRunCount( entity.getMaxRunCount() );
        taskBatchDetailVO.setTimeoutSeconds( entity.getTimeoutSeconds() );
        taskBatchDetailVO.setRetryCount( entity.getRetryCount() );
        taskBatchDetailVO.setRetryInterval( entity.getRetryInterval() );
        taskBatchDetailVO.setTaskInput( entity.getTaskInput() );
        taskBatchDetailVO.setTaskConfig( entity.getTaskConfig() );
        taskBatchDetailVO.setNotificationConfig( entity.getNotificationConfig() );
        taskBatchDetailVO.setAgentId( entity.getAgentId() );
        taskBatchDetailVO.setVersionId( entity.getVersionId() );
        taskBatchDetailVO.setTenantId( entity.getTenantId() );
        taskBatchDetailVO.setCreateUserId( entity.getCreateUserId() );
        taskBatchDetailVO.setUpdateUserId( entity.getUpdateUserId() );
        taskBatchDetailVO.setCreateTime( entity.getCreateTime() );
        taskBatchDetailVO.setUpdateTime( entity.getUpdateTime() );

        return taskBatchDetailVO;
    }

    @Override
    public TaskCronDetailVO toCronDetailVo(Task entity) {
        if ( entity == null ) {
            return null;
        }

        TaskCronDetailVO taskCronDetailVO = new TaskCronDetailVO();

        taskCronDetailVO.setTaskSn( entity.getTaskSn() );
        taskCronDetailVO.setTaskName( entity.getTaskName() );
        taskCronDetailVO.setTaskDesc( entity.getTaskDesc() );
        taskCronDetailVO.setTaskType( entity.getTaskType() );
        taskCronDetailVO.setTaskStatus( entity.getTaskStatus() );
        taskCronDetailVO.setCronExpression( entity.getCronExpression() );
        taskCronDetailVO.setStartTime( entity.getStartTime() );
        taskCronDetailVO.setEndTime( entity.getEndTime() );
        taskCronDetailVO.setNextRunTime( entity.getNextRunTime() );
        taskCronDetailVO.setLastRunTime( entity.getLastRunTime() );
        taskCronDetailVO.setRunCount( entity.getRunCount() );
        taskCronDetailVO.setMaxRunCount( entity.getMaxRunCount() );
        taskCronDetailVO.setTimeoutSeconds( entity.getTimeoutSeconds() );
        taskCronDetailVO.setRetryCount( entity.getRetryCount() );
        taskCronDetailVO.setRetryInterval( entity.getRetryInterval() );
        taskCronDetailVO.setTaskInput( entity.getTaskInput() );
        taskCronDetailVO.setTaskConfig( entity.getTaskConfig() );
        taskCronDetailVO.setNotificationConfig( entity.getNotificationConfig() );
        taskCronDetailVO.setAgentId( entity.getAgentId() );
        taskCronDetailVO.setVersionId( entity.getVersionId() );
        taskCronDetailVO.setTenantId( entity.getTenantId() );
        taskCronDetailVO.setCreateUserId( entity.getCreateUserId() );
        taskCronDetailVO.setUpdateUserId( entity.getUpdateUserId() );
        taskCronDetailVO.setCreateTime( entity.getCreateTime() );
        taskCronDetailVO.setUpdateTime( entity.getUpdateTime() );

        return taskCronDetailVO;
    }

    @Override
    public List<TaskVO> toVoList(List<Task> entities) {
        if ( entities == null ) {
            return null;
        }

        List<TaskVO> list = new ArrayList<TaskVO>( entities.size() );
        for ( Task task : entities ) {
            list.add( toVo( task ) );
        }

        return list;
    }

    @Override
    public List<TaskBatchPageVO> toBatchPageVoList(List<Task> entities) {
        if ( entities == null ) {
            return null;
        }

        List<TaskBatchPageVO> list = new ArrayList<TaskBatchPageVO>( entities.size() );
        for ( Task task : entities ) {
            list.add( taskToTaskBatchPageVO( task ) );
        }

        return list;
    }

    @Override
    public List<TaskCronPageVO> toCronPageVoList(List<Task> entities) {
        if ( entities == null ) {
            return null;
        }

        List<TaskCronPageVO> list = new ArrayList<TaskCronPageVO>( entities.size() );
        for ( Task task : entities ) {
            list.add( taskToTaskCronPageVO( task ) );
        }

        return list;
    }

    protected TaskBatchPageVO taskToTaskBatchPageVO(Task task) {
        if ( task == null ) {
            return null;
        }

        TaskBatchPageVO taskBatchPageVO = new TaskBatchPageVO();

        taskBatchPageVO.setTaskSn( task.getTaskSn() );
        taskBatchPageVO.setTaskName( task.getTaskName() );
        taskBatchPageVO.setTaskDesc( task.getTaskDesc() );
        taskBatchPageVO.setTaskType( task.getTaskType() );
        taskBatchPageVO.setTaskStatus( task.getTaskStatus() );
        taskBatchPageVO.setStartTime( task.getStartTime() );
        taskBatchPageVO.setEndTime( task.getEndTime() );
        taskBatchPageVO.setNextRunTime( task.getNextRunTime() );
        taskBatchPageVO.setLastRunTime( task.getLastRunTime() );
        taskBatchPageVO.setRunCount( task.getRunCount() );
        taskBatchPageVO.setMaxRunCount( task.getMaxRunCount() );
        taskBatchPageVO.setTimeoutSeconds( task.getTimeoutSeconds() );
        taskBatchPageVO.setRetryCount( task.getRetryCount() );
        taskBatchPageVO.setRetryInterval( task.getRetryInterval() );
        taskBatchPageVO.setTaskInput( task.getTaskInput() );
        taskBatchPageVO.setTaskConfig( task.getTaskConfig() );
        taskBatchPageVO.setNotificationConfig( task.getNotificationConfig() );
        taskBatchPageVO.setAgentId( task.getAgentId() );
        taskBatchPageVO.setVersionId( task.getVersionId() );
        taskBatchPageVO.setCreateTime( task.getCreateTime() );
        taskBatchPageVO.setUpdateTime( task.getUpdateTime() );

        return taskBatchPageVO;
    }

    protected TaskCronPageVO taskToTaskCronPageVO(Task task) {
        if ( task == null ) {
            return null;
        }

        TaskCronPageVO taskCronPageVO = new TaskCronPageVO();

        taskCronPageVO.setTaskId( task.getTaskId() );
        taskCronPageVO.setTaskSn( task.getTaskSn() );
        taskCronPageVO.setTaskName( task.getTaskName() );
        taskCronPageVO.setTaskDesc( task.getTaskDesc() );
        taskCronPageVO.setTaskType( task.getTaskType() );
        taskCronPageVO.setTaskStatus( task.getTaskStatus() );
        taskCronPageVO.setCronExpression( task.getCronExpression() );
        taskCronPageVO.setStartTime( task.getStartTime() );
        taskCronPageVO.setEndTime( task.getEndTime() );
        taskCronPageVO.setNextRunTime( task.getNextRunTime() );
        taskCronPageVO.setLastRunTime( task.getLastRunTime() );
        taskCronPageVO.setRunCount( task.getRunCount() );
        taskCronPageVO.setMaxRunCount( task.getMaxRunCount() );
        taskCronPageVO.setTimeoutSeconds( task.getTimeoutSeconds() );
        taskCronPageVO.setRetryCount( task.getRetryCount() );
        taskCronPageVO.setRetryInterval( task.getRetryInterval() );
        taskCronPageVO.setTaskInput( task.getTaskInput() );
        taskCronPageVO.setTaskConfig( task.getTaskConfig() );
        taskCronPageVO.setNotificationConfig( task.getNotificationConfig() );
        if ( task.getCreateUserId() != null ) {
            taskCronPageVO.setCreateUserId( String.valueOf( task.getCreateUserId() ) );
        }
        taskCronPageVO.setCreateTime( task.getCreateTime() );

        return taskCronPageVO;
    }
}
