package com.ai.application.agent.base.controller;

import com.ai.application.agent.base.api.dto.*;
import com.ai.application.agent.base.api.vo.AgentDetailVO;
import com.ai.application.agent.base.api.vo.AgentVersionListVO;
import com.ai.application.agent.base.service.IAgentVersionService;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * 智能体版本
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Tag(name = "智能体版本", description = "智能体版本-相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1/version")
@AllArgsConstructor
public class AgentVersionController {
    private final IAgentVersionService agentVersionService;

    /**
     * 版本列表
     * @return
     */
    @Operation(summary = "版本列表")
    @GetMapping("/list")
    public ResultVo<List<AgentVersionListVO>> list(@Validated @RequestBody AgentVersionListDTO dto){
        return ResultVo.data(agentVersionService.list(dto));
    }

    /**
     * 根据版本查详情
     * @param sn
     * @return
     */
    @Operation(summary = "根据版本号获取智能体详情")
    @GetMapping("/detail/{versionSn}")
    public ResultVo<AgentDetailVO> detail(@PathVariable("versionSn") String sn){
        return ResultVo.data(agentVersionService.detail(sn));
    }

    /**
     * 创建智能体版本
     * @param dto
     * @return
     */
    @Operation(summary = "智能规划创建")
    @PostMapping(value = "/master/add")
    public ResultVo<String> masterAdd(@Validated @RequestBody MasterAddDTO dto){
        return ResultVo.data(agentVersionService.masterAdd(dto));
    }

    @Operation(summary = "工作流创建")
    @PostMapping(value = "/workflow/add")
    public ResultVo<String> workFlowAdd(@Validated @RequestBody WorkFlowAddDTO dto){
        return ResultVo.data(agentVersionService.workFlowAdd(dto));
    }

    /**
     * 智能体更新
     * @param dto
     * @return
     */
    @Operation(summary = "版本信息更新（不用）")
    @PostMapping(value = "/update")
    public ResultVo<String> update(@Validated @RequestBody MasterUpdateDTO dto){
        agentVersionService.update(dto);
        return ResultVo.success("更新成功");
    }

    /**
     * 发布
     * @param dto
     * @return
     */
    @Operation(summary = "智能体发布")
    @PostMapping("/publish")
    public ResultVo<Void> publish(@Validated @RequestBody AgentPublishDTO dto){
        agentVersionService.publish(dto);
        return ResultVo.success("发布成功");
    }

    /**
     * 草稿
     * @param dto
     * @return
     */
    @Operation(summary = "智能规划草稿保存")
    @PostMapping("/master/draft")
    public ResultVo<String> masterDraft(@Validated @RequestBody MasterDraftDTO dto) {
        return ResultVo.success(agentVersionService.masterDraft(dto));
    }

    @Operation(summary = "工作流草稿保存")
    @PostMapping("/workFlow/draft")
    public ResultVo<String> workFlowDraft(@Validated @RequestBody WorkFlowDraftDTO dto) {
        return ResultVo.success(agentVersionService.workFlowDraft(dto));
    }

    /**
     * 启用版本
     * @param dto
     * @return
     */
    @Operation(summary = "启用版本")
    @PostMapping("/enable")
    public ResultVo<Void> enable(@Validated @RequestBody VersionEnableDTO dto) {
        agentVersionService.enable(dto);
        return ResultVo.success("启用成功");
    }

    @Operation(summary = "版本上架")
    @PostMapping("/sale")
    public ResultVo<Void> sale(@Validated @RequestBody VersionSaleDTO dto) {
        agentVersionService.sale(dto);
        return ResultVo.success("渠道操作成功");
    }
}