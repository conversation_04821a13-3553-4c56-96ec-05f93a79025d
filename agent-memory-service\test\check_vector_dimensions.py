#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向量维度匹配性检查脚本

检查系统中各组件的向量维度配置一致性，提供：
- 配置文件中向量维度设置检查
- Elasticsearch索引映射维度验证
- Embedding模型实际输出维度测试
- 官方模型规格对比分析
- 维度不匹配问题诊断和修复建议

Usage:
    python test/check_vector_dimensions.py

Project: Agent Memory Service
Author: 张小龙 <<EMAIL>>
Created: 2025-06-01
Version: 1.0.0
License: private
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.config.settings import settings
from app.services.embedding_service import EmbeddingService


async def check_vector_dimensions():
    """检查向量维度匹配性"""
    print("=" * 60)
    print("[START] 检查向量维度匹配性")
    print("=" * 60)
    
    # 1. 获取配置中的向量维度
    config_vector_dim = settings.memory.vector_dimension
    print(f"[CONFIG] 配置文件中的向量维度: {config_vector_dim}")
    
    # 2. 检查ES映射中的向量维度
    from app.services.elasticsearch_service import ElasticsearchService
    es_service = ElasticsearchService()
    
    try:
        print("[CONNECT] 正在连接Elasticsearch...")
        if settings.elasticsearch.username:
            print(f"[INFO] 使用认证用户: {settings.elasticsearch.username}")
        
        await es_service.initialize()
        
        # 获取索引映射
        mapping = await es_service.client.indices.get_mapping(index=es_service.index_name)
        question_vector_mapping = mapping[es_service.index_name]['mappings']['properties']['question_vector']
        es_vector_dim = question_vector_mapping.get('dims', 'unknown')
        
        print(f"[ES] Elasticsearch中向量字段维度: {es_vector_dim}")
        
    except Exception as e:
        print(f"[ERROR] 获取ES映射信息失败: {e}")
        es_vector_dim = None
    finally:
        try:
            await es_service.close()
        except:
            pass
    
    # 3. 检查embedding模型实际生成的向量维度
    embedding_service = EmbeddingService()
    
    try:
        print("[INIT] 正在初始化Embedding模型...")
        await embedding_service.initialize()
        
        # 测试文本编码
        test_text = "这是一个测试文本"
        vector = await embedding_service.encode_text(test_text)
        actual_vector_dim = len(vector)
        
        print(f"[EMBED] Embedding模型实际生成的向量维度: {actual_vector_dim}")
        
        # 4. 检查GTE模型官方规格
        print(f"[SPEC] GTE-base-zh官方规格维度: 768")
        
    except Exception as e:
        print(f"[ERROR] 获取embedding模型向量维度失败: {e}")
        actual_vector_dim = None
    
    # 5. 匹配性分析
    print()
    print("=" * 60)
    print("[ANALYZE] 匹配性分析")
    print("=" * 60)
    
    # 收集所有维度信息
    dimensions = {
        "配置文件": config_vector_dim,
        "Elasticsearch": es_vector_dim,
        "Embedding模型实际": actual_vector_dim,
        "GTE-base-zh官方规格": 768
    }
    
    # 显示所有维度
    for source, dim in dimensions.items():
        if dim == 768:
            status = "[CORRECT]"
        elif dim is not None:
            status = "[MISMATCH]"
        else:
            status = "[UNKNOWN]"
        print(f"{status} {source}: {dim}")
    
    # 检查是否所有维度都一致
    valid_dims = [dim for dim in dimensions.values() if dim is not None]
    if len(set(valid_dims)) == 1:
        print()
        print("[SUCCESS] 所有向量维度配置匹配，系统正常")
        return True
    else:
        print()
        print("[ERROR] 向量维度配置不匹配！")
        print("[WARNING] 这可能导致以下问题：")
        print("  - 向量存储失败")
        print("  - 相似度搜索错误")
        print("  - 索引创建失败")
        
        print()
        print("[SOLUTION] 建议修复方案：")
        print("  1. 检查config.yaml中memory.vector_dimension配置")
        print("  2. 检查ES索引映射中question_vector字段的dims设置")
        print("  3. 确认使用的embedding模型是否为GTE-base-zh")
        print("  4. 如果更换了模型，需要同步更新维度配置")
        return False


if __name__ == "__main__":
    success = asyncio.run(check_vector_dimensions())
    if not success:
        sys.exit(1) 