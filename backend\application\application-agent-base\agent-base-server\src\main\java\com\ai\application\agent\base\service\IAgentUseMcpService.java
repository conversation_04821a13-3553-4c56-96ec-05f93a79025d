package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.bo.MasterSkillBO;
import com.ai.application.agent.base.api.dto.AgentUseMcpAddDTO;
import com.ai.application.agent.base.api.dto.AgentUseMcpListDTO;
import com.ai.application.agent.base.api.dto.AgentUseMcpUpdateDTO;
import com.ai.application.agent.base.api.vo.AgentUseMcpListVO;
import java.util.List;

public interface IAgentUseMcpService {
    /**
     * 列表
     *
     * @param queryDto
     * @return
     */
    List<AgentUseMcpListVO> list(AgentUseMcpListDTO queryDto);

    /**
     * 保存
     *
     * @param dto
     */
    void add(AgentUseMcpAddDTO dto);

    /**
     * 更新
     *
     * @param dto
     */
    void update(AgentUseMcpUpdateDTO dto);

    /**
     * 详情页
     * @param versionId
     * @return
     */
    List<MasterSkillBO> toDetail(Integer versionId);
}
