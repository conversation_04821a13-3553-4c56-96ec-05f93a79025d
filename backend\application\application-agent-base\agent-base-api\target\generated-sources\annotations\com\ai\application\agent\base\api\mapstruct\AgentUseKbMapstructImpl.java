package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.dto.AgentUseKbAddDTO;
import com.ai.application.agent.base.api.dto.AgentUseKbUpdateDTO;
import com.ai.application.agent.base.api.entity.AgentUseKb;
import com.ai.application.agent.base.api.vo.AgentUseKbListVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-13T10:32:23+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentUseKbMapstructImpl implements AgentUseKbMapstruct {

    @Override
    public AgentUseKb toAddEntity(AgentUseKbAddDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentUseKb agentUseKb = new AgentUseKb();

        agentUseKb.setAkbStatus( dto.getAkbStatus() );
        agentUseKb.setAgentId( dto.getAgentId() );
        agentUseKb.setVersionId( dto.getVersionId() );
        agentUseKb.setKbId( dto.getKbId() );
        agentUseKb.setKbExtend( dto.getKbExtend() );

        return agentUseKb;
    }

    @Override
    public AgentUseKb toUpdateEntity(AgentUseKbUpdateDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentUseKb agentUseKb = new AgentUseKb();

        agentUseKb.setAkbId( dto.getAkbId() );
        agentUseKb.setAkbStatus( dto.getAkbStatus() );
        agentUseKb.setAgentId( dto.getAgentId() );
        agentUseKb.setVersionId( dto.getVersionId() );
        agentUseKb.setKbId( dto.getKbId() );
        agentUseKb.setKbExtend( dto.getKbExtend() );

        return agentUseKb;
    }

    @Override
    public AgentUseKbListVO toVo(AgentUseKb entity) {
        if ( entity == null ) {
            return null;
        }

        AgentUseKbListVO agentUseKbListVO = new AgentUseKbListVO();

        agentUseKbListVO.setAkbId( entity.getAkbId() );
        agentUseKbListVO.setAkbStatus( entity.getAkbStatus() );
        agentUseKbListVO.setAgentId( entity.getAgentId() );
        agentUseKbListVO.setVersionId( entity.getVersionId() );
        agentUseKbListVO.setKbId( entity.getKbId() );
        agentUseKbListVO.setKbExtend( entity.getKbExtend() );
        agentUseKbListVO.setCreateTime( entity.getCreateTime() );
        agentUseKbListVO.setUpdateTime( entity.getUpdateTime() );

        return agentUseKbListVO;
    }

    @Override
    public List<AgentUseKbListVO> toVoList(List<AgentUseKb> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AgentUseKbListVO> list = new ArrayList<AgentUseKbListVO>( entities.size() );
        for ( AgentUseKb agentUseKb : entities ) {
            list.add( toVo( agentUseKb ) );
        }

        return list;
    }
}
