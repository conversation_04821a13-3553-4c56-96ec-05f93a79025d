package com.ai.application.agent.run.feign;

import com.ai.application.agent.run.dto.MemoryAddRequestDTO;
import com.ai.application.agent.run.dto.MemoryAddResponseDTO;
import com.ai.application.agent.run.dto.MemorySearchRequestDTO;
import com.ai.application.agent.run.dto.MemorySearchResponseDTO;
import com.ai.framework.core.constants.ServiceConstant;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 记忆服务FeignClient接口
 */
@Tag(name = "用户Feign接口", description = "用户基本操作")
@FeignClient(
        value = ServiceConstant.AGENT_MEMORY,

        contextId = "IAppUserClient"
)
public interface IMemoryFeignClient {

    /**
     * 添加记忆
     *
     * @param request 记忆添加请求
     * @return 记忆添加响应
     */
    @PostMapping("/agentmemory/v1/add")
    MemoryAddResponseDTO addMemory(@RequestBody MemoryAddRequestDTO request);

    /**
     * 搜索记忆
     *
     * @param request 记忆搜索请求
     * @return 记忆搜索响应
     */
    @PostMapping("/agentmemory/v1/search")
    MemorySearchResponseDTO searchMemory(@RequestBody MemorySearchRequestDTO request);
}
