package com.ai.application.agent.base.api.vo;

import com.ai.application.agent.base.api.common.serializer.AgentStatusSerializer;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 智能体运行会话表
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@Schema(name = "")
public class LastSessionAgentVO {
    /**
     * 智能体id
     */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
     * 智能体sn
     */
    @Schema(description = "智能体sn")
    private String agentSn;

    /**
     * 智能体名称
     */
    @Schema(description = "智能体名称")
    private String agentName;

    @Schema(description = "智能体图标")
    private String agentIcon;

    @Schema(description = "状态 0停用 1开发 5发布")
    //@JsonSerialize(using = AgentStatusSerializer.class)
    private Integer agentStatus;

    @Schema(description = "最后会话sessionSn")
    private String sessionSn;

    @Schema(description = "最后会话时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastSessionTime;
}