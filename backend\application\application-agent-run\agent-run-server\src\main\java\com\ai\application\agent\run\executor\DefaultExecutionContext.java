package com.ai.application.agent.run.executor;

import com.ai.framework.core.context.UserContext;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * 默认执行上下文实现
 */
@ToString
@Slf4j
@Getter
public class DefaultExecutionContext implements ExecutionContext {

    private final String processId;
    private final String processInstanceId;
    private final String nodeId;
    private final String nodeName;
    private final Map<String, Object> parameters;
    private final UserContext.UserContextItem userInfo;
    private final String authorization;
    private final boolean debugRun;

    public DefaultExecutionContext(
            String processId,
            String processInstanceId,
            String nodeId,
            String nodeName,
            Map<String, Object> parameters,
            UserContext.UserContextItem userInfo,
            String authorization,
            boolean debugRun) {
        this.processId = processId;
        this.processInstanceId = processInstanceId;
        this.nodeId = nodeId;
        this.nodeName = nodeName;
        this.parameters = parameters != null ? parameters : new HashMap<>();
        this.userInfo = userInfo;
        this.authorization = authorization;
        this.debugRun = debugRun;
    }

    @Override
    public String getProcessId() {
        return processId;
    }

    @Override
    public String getProcessInstanceId() {
        return processInstanceId;
    }

    @Override
    public String getNodeId() {
        return nodeId;
    }

    @Override
    public String getNodeName() {
        return nodeName;
    }

    @Override
    public Map<String, Object> getParameters() {
        return parameters;
    }

    @Override
    public String getParameterAsString(String parameter) {
        Object value = parameters.get(parameter);
        return value != null ? value.toString() : null;
    }

    @Override
    public UserContext.UserContextItem getUserInfo() {
        return userInfo;
    }

    @Override
    public String getAuthorization() {
        return authorization;
    }

    @Override
    public boolean isDebugRun() {
        return debugRun;
    }

    @Override
    public String getModule() {
        return "agent";
    }
}
