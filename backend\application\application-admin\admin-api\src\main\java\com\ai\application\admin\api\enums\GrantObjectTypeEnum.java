package com.ai.application.admin.api.enums;

/**
 * 授权对象类型:10-用户,20-角色,30-部门,40-租户,50-平台
 */
public enum GrantObjectTypeEnum {
    USER(10, "用户"),
    ROLE(20, "角色"),
    DEPARTMENT(30, "部门"),
    TENANT(40, "租户"),
    PLATFORM(50, "平台"),
    ;

    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    GrantObjectTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
