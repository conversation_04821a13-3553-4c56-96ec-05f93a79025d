package com.ai.application.app.api.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

/**
 * 应用功能表
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Data
@Schema(name = "应用功能表DTO")
public class AppFunctionDTO {
    @Schema(description = "")
    private Integer funId;
    /**
     * 类型，10目录 11菜单 12按钮，20接口
     */
    @Schema(description = "类型，10目录 11菜单 12按钮，20接口")
    private Integer funType;
    /**
     * 功能名称
     */
    @Schema(description = "功能名称")
    private String funName;
    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer funSort;
    /**
     * 关联资源code
     */
    @Schema(description = "关联资源code")
    private String resCode;
    /**
     * 功能状态:1启用,0停用,-1删除
     */
    @Schema(description = "功能状态:1启用,0停用,-1删除")
    private Integer funStatus;
    /**
     * 父级ID
     */
    @Schema(description = "父级ID")
    private Integer parentId;
    /**
     * 依赖节点ID
     */
    @Schema(description = "依赖节点ID")
    private String refIds;
    /**
     * 是否可关联租户角色
     */
    @Schema(description = "是否可关联租户角色")
    private Integer refTenant;
    /**
     * 应用ID
     */
    @Schema(description = "应用ID")
    private Integer appId;
    @Schema(description = "")
    private Date createTime;
    @Schema(description = "")
    private Date updateTime;
}