package com.ai.application.knowledge.table.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class TableManageDetailVo {

    @Schema(description = "字段sn")
    private String fieldSn;

    @Schema(description = "字段名称")
    private String fieldName;

    @Schema(description = "类型 KNOWLEDGE - 文档知识 FILE_BATCH - 批量文件 FILE - 文件 TEXT - 文本")
    private String fieldType;

    @Schema(description = "字段描述")
    private String fieldDesc;

    @Schema(description = "key标识 0否 1是")
    private Integer fieldKey;

    @Schema(description = "排序值")
    private Integer fieldSort;

    @Schema(description = "状态 0失效 1有效")
    private Integer fieldStatus;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @Schema(description = "创建用户")
    private String creator;

    @Schema(description = "更新用户")
    private String updater;

    @Schema(description = "创建用户id")
    private Integer createUserId;

    @Schema(description = "更新用户id")
    private Integer updateUserId;

}
