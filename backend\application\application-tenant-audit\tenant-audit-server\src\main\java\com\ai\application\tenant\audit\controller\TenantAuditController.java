package com.ai.application.tenant.audit.controller;

import com.ai.application.tenant.audit.api.dto.TenantAuditDTO;
import com.ai.application.tenant.audit.api.dto.query.TenantAddDTO;
import com.ai.application.tenant.audit.api.dto.query.TenantAuditQueryDTO;
import com.ai.application.tenant.audit.api.vo.TenantAuditVO;
import com.ai.application.tenant.audit.service.ITenantAuditService;
import com.github.pagehelper.PageInfo;

import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 租户审核表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Tag(name = "租户审核表", description = "租户审核表-相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1/operate")
public class TenantAuditController {

    @Resource
    private ITenantAuditService tenantAuditService;

    /**
     * 分页查询
     *
     * @param queryDto
     * @return
     */
    @Operation(summary = "租户审核表-分页查询", description = "查询所有租户审核表 信息")
    @PostMapping("/page")
    public ResultVo<PageInfo<TenantAuditVO>> page(@Validated @RequestBody TenantAuditQueryDTO queryDto){
        return ResultVo.data(tenantAuditService.page(queryDto));
    }

    /**
     * 保存
     *
     * @param dto
     * @return
     */
    @Operation(summary = "租户审核表-新增")
    @PostMapping("/add")
    public ResultVo<Void> add(@Validated @RequestBody TenantAddDTO dto){
        tenantAuditService.add(dto);
        return ResultVo.success("保存成功");
    }
    
    /**
     * 修改
     *
     * @param dto
     * @return
     */
    @Operation(summary = "租户审核表-修改")
    @PostMapping(value = "/update")
    public ResultVo<Void> update(@Validated @RequestBody TenantAuditDTO dto){
        tenantAuditService.update(dto);
        return ResultVo.success("修改成功");
    }
}