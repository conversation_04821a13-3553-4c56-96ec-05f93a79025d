package com.ai.application.agent.run.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 知识问答结果 DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(name = "KnowledgeQaResultDTO")
public class KnowledgeQaResultDTO {

    /**
     * 问答答案
     */
    @Schema(description = "问答答案")
    private String answer;

    /**
     * 参考页面/片段
     */
    @Schema(description = "参考页面/片段")
    private List<PageReferenceDTO> references;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 是否成功
     */
    @Schema(description = "是否成功")
    private Boolean success;

    /**
     * 页面引用 DTO
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @SuperBuilder
    @Schema(name = "PageReferenceDTO")
    public static class PageReferenceDTO {

        /**
         * 页面ID
         */
        @Schema(description = "页面ID")
        private String pageId;

        /**
         * 页面内容
         */
        @Schema(description = "页面内容")
        private String content;

        /**
         * 相关性分数
         */
        @Schema(description = "相关性分数")
        private Double score;

        /**
         * 文件名
         */
        @Schema(description = "文件名")
        private String fileName;

        /**
         * 页码
         */
        @Schema(description = "页码")
        private Integer pageNumber;

        /**
         * 知识库编号
         */
        @Schema(description = "知识库编号")
        private String knowledgeInventorySn;
    }
}
