export interface FlowVariable {
  id: string
  name: string
  isSystem?: boolean
  currentValue?: string | string[]
  defaultValue: string | string[]
  description?: string
  type: VariableType
  inputType?: VariableInputType
  required?: boolean
  collectionType: CollectionType
  originFile?: {
    sn?: string
    name?: string
    sizeInBytes?: number
  }
}

export interface FlowVariable {
  id: string  // 变量ID
  name: string  // 变量名称
  desc?: string // 变量描述
  isSystem?: boolean  // 是否系统变量
  required?: boolean // 是否必填
  type: VariableType // 变量类型
  isArray: boolean // 是否数据
  currentValue:{
  }
  defaultValue:{
  }
  fieldType:VariableInputType | null // 输入框类型，是

  // 超长文本转文件
  toFile: {
    fileSn: string
    fileName: string
    fileSize: number
    fileType: string
  } | null
}

// 自定义组件
export enum VariableInputType {
  VARIABLE_SELECT = 'VARIABLE_SELECT',
  TEXTAREA = 'TEXTAREA',
  SELECT = 'SELECT',
  MULTI_SELECT = 'MULTI_SELECT',
  LLM_SELECT = 'LLM_SELECT',
  DOC_SELECT = 'DOC_SELECT',
}

export enum CollectionType {
  NONE = 'NONE',
  ARRAY = 'ARRAY',
}

export enum VariableType {
  TEXT = 'TEXT',
  OBJECT = 'OBJECT',
  MD = 'MD',
  DOC = 'DOC',
  IMAGE = 'IMAGE',
  AUDIO = 'AUDIO',
  KNOWLEDGE = 'KNOWLEDGE',
  DATE = 'DATE',
  HYPERLINK = 'HYPERLINK',
  DOCUMENT_KNOWLEDGE = 'DOCUMENT_KNOWLEDGE',
  CHART = 'CHART',
  ARRAY_TEXT = 'ARRAY-TEXT',
  ARRAY_DOC = 'ARRAY-DOC',
  ARRAY_MD = 'ARRAY-MD',
  ARRAY_KNOWLEDGE = 'ARRAY-KNOWLEDGE',
  ARRAY_IMAGE = 'ARRAY-IMAGE',
  ARRAY_AUDIO = 'ARRAY-AUDIO',
  ARRAY_OBJECT = 'ARRAY-OBJECT',
  ARRAY_HYPERLINK = 'ARRAY-HYPERLINK',
  ARRAY_DOCUMENT_KNOWLEDGE = 'ARRAY-DOCUMENT_KNOWLEDGE',
  REFERENCE = 'REFERENCE',
  ARRAY_CHART = 'ARRAY-CHART',
}
//
// {
//   "id": "e3608606b4",
//     "name": "输入参数1",
//     "collectionType": "NONE",
//     "type": "TEXT",
//     "value": null,
//     "currentValue": null,
//     "defaultValue": null,
//     "required": true,
//     "inputType": "VARIABLE_SELECT",
//     "description": "",
//
//
//     "originFile": null,
//     "category": "USER",
//
//
//     "temporary": false,
//     "options": null,
//     "enums": null,
//     "outputToEndVariables": false,
//     "identifier": "e3608606b4"
// }