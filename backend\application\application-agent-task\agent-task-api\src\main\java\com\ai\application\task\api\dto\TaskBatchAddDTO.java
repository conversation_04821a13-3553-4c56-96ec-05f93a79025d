package com.ai.application.task.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 批任务表
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Data
@Schema(name = "批任务表创建DTO")
public class TaskBatchAddDTO {

    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "关联智能体Sn")
    @NotBlank(message = "关联智能体Sn不能为空")
    private String agentSn;

    @Schema(description = "智能体版本Sn")
    @NotBlank(message = "智能体版本Sn不能为空")
    private String versionSn;

    @Schema(description = "会话配置(0:每个输入独立对话,1:所有输入同一会话)")
    //@NotNull(message = "会话配置不能为空")
    private Integer sessionConfig = 0;

    @Schema(description = "数据集文件Sn")
    @NotNull(message = "数据集文件Sn不能为空")
    private String dataSetFileSn;

    @Schema(description = "数据集文件Url")
    private String dataSetFileUrl;
}