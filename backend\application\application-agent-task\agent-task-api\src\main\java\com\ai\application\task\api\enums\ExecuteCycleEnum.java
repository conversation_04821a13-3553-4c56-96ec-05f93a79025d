package com.ai.application.task.api.enums;

import lombok.Getter;

/**
 * 执行周期: 10：仅一次，20：每天重复，30：每周重复
 */
@Getter
public enum ExecuteCycleEnum {
    ONCE(10, "仅一次"),
    DAY_REPEAT(20, "每天重复"),
    WEEK_REPEAT(30, "每周重复")
    ;

    private final Integer code;
    private final String desc;

    ExecuteCycleEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
