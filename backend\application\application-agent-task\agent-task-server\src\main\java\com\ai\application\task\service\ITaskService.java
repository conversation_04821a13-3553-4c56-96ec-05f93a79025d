package com.ai.application.task.service;

import com.ai.application.task.api.dto.TaskAddDTO;
import com.ai.application.task.api.dto.TaskRunResultDTO;
import com.ai.application.task.api.vo.TaskDetailVO;
import com.github.pagehelper.PageInfo;
import com.ai.application.task.api.dto.TaskDTO;
import com.ai.application.task.api.dto.query.TaskQueryDTO;
import com.ai.application.task.api.vo.TaskVO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * 计划任务表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
public interface ITaskService {

        /**
         * 分页
         *
         * @param queryDto
         * @return
         */
        PageInfo<TaskVO> page(TaskQueryDTO queryDto);

        /**
         * 列表
         *
         * @param sort
         * @param queryDto
         * @return
         */
        List<TaskVO> list(TaskQueryDTO queryDto);

        /**
         * 保存
         *
         * @param dto
         */
        void add(TaskAddDTO dto);

        /**
         * 更新
         *
         * @param dto
         */
        void update(TaskDTO dto);

        @Transactional(rollbackFor = Exception.class)
        void delete(String taskSn);

        @Transactional(rollbackFor = Exception.class)
        void stop(String taskSn);

        /**
         * 查看
         *
         * @param id
         * @return
         */
        TaskVO get(Integer id);

        @Transactional(readOnly = true)
        TaskDetailVO detail(String taskSn);

        void getDataSetTemplate(String agentSn, String versionSn, HttpServletResponse response);

        void saveTaskRunResult(TaskRunResultDTO dto);
}