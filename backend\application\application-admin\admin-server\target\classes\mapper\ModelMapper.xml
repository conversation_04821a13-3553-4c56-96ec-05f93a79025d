<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.admin.mapper.ModelInfoMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.admin.api.entity.ModelInfo">
                    <id column="model_id" property="modelId" />
                    <result column="model_sn" property="modelSn" />
                    <result column="model_name" property="modelName" />
                    <result column="model_engine" property="modelEngine" />
                    <result column="model_type" property="modelType" />
                    <result column="model_toolcall" property="modelToolcall" />
                    <result column="model_desc" property="modelDesc" />
                    <result column="model_config" property="modelConfig" />
                    <result column="model_status" property="modelStatus" />
                    <result column="supplier_id" property="supplierId" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        model_id, model_sn, model_name, model_engine, model_type, model_toolcall, model_desc, model_config, model_status, supplier_id, create_time, update_time
    </sql>

    <select id="queryGrantTenantModelList" resultType="com.ai.application.admin.api.entity.ModelInfo">
        SELECT A.resource_id,B.* FROM `resource` A
        LEFT JOIN model_info B ON(A.resource_object_id=B.model_id)
        WHERE A.resource_status=1 AND A.resource_type=70
    </select>
</mapper>