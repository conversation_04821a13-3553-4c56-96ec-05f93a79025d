package com.ai.application.app.auth.component;

import com.ai.application.app.auth.api.constants.TokenConstant;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.redis.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class UserCacheComponent {
    /**
     * 设置用户缓存
     * @param userSn
     * @param accessToken
     * @param refreshToken
     * @param tokenExpiration
     */
    public static void UserLoginCache(String userSn, String accessToken, String refreshToken, Integer tokenExpiration) {
        // 用户token
        String userKey = TokenConstant.USER_KEY.concat(userSn);
        String userKeyCacheObject = (String) RedisUtils.getCacheObject(userKey);

        // 删除用户历史的token
        if (Objects.nonNull(userKeyCacheObject)) {
            log.info("userKeyCacheObject:{}", userKeyCacheObject);
            Map<String, String> userCache = JsonUtils.parseMapStr(userKeyCacheObject);
            RedisUtils.deleteObject(TokenConstant.USER_ACCESS_TOKEN.concat(userCache.get("accessToken")));
            RedisUtils.deleteObject(TokenConstant.USER_ACCESS_TOKEN.concat(userCache.get("accessToken")));
        }

        // 设置用户新token
        Map<String, String> userMap = new HashMap<>(2);
        userMap.put("accessToken", accessToken);
        userMap.put("refreshToken", refreshToken);
        String userCacheJson = JsonUtils.toJsonString(userMap);
        log.info("userCacheJson:{}", userCacheJson);
        RedisUtils.setCacheObject(userKey, userCacheJson, Duration.ofDays(tokenExpiration));

        // 设置refreshToken
        String refreshTokenKey = TokenConstant.USER_REFRESH_TOKEN.concat(refreshToken);
        RedisUtils.setCacheObject(refreshTokenKey, userSn, Duration.ofDays(tokenExpiration));

        // 设置accessToken
        String accessTokenKey = TokenConstant.USER_ACCESS_TOKEN.concat(accessToken);
        RedisUtils.setCacheObject(accessTokenKey, userCacheJson, Duration.ofDays(tokenExpiration));
    }
}
