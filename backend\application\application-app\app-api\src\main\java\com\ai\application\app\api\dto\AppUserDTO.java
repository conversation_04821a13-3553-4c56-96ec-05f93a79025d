package com.ai.application.app.api.dto;

import com.ai.framework.core.sensitive.Sensitive;
import com.ai.framework.core.sensitive.SensitiveType;
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 应用用户表
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(name = "应用用户表DTO")
public class AppUserDTO {
    /**
     * 用户id
     */
    @Schema(description = "用户id")
    private Integer userId;
    /**
     * 用户sn
     */
    @Schema(description = "用户sn")
    private String userSn;
    /**
     * 登录账号
     */
    @Schema(description = "登录账号")
    @NotBlank(message = "登录账号不能为空")
    @Length(max = 20,message ="登录账号长度不能超过20")
    private String userAccount;
    /**
     * 显示名
     */
    @Schema(description = "显示名")
    @Length(max = 20,message ="显示名长度不能超过20")
    private String userName;
    /**
     * 登录密码
     */
    @Schema(description = "登录密码")
    private String userPassword;
    /**
     * 用户手机号
     */
    @Schema(description = "用户手机号")
    @Sensitive(type = SensitiveType.MOBILE)
    private String userMobile;
    /**
     * 用户邮箱
     */
    @Schema(description = "用户邮箱")
    private String userEmail;
    /**
     * 头像链接
     */
    @Schema(description = "头像链接")
    private String userAvatar;
    /**
     * 工号
     */
    @Schema(description = "工号")
    private String userStaffSn;
    /**
     * 用户状态 1-启用 0-禁用 -1-删除
     */
    @Schema(description = "用户状态 1-启用 0-禁用 -1-删除")
    private Integer userStatus;
    /**
     * 应用id
     */
    @Schema(description = "应用id")
    private Integer appId;
    /**
     * 角色id
     */
    @Schema(description = "角色id")
    @NotNull(message = "角色不能为空")
    private Integer roleId;
    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Integer tenantId;
    /**
     * 部门id
     */
    @Schema(description = "部门id")
    private Integer deptId;
    @Schema(description = "")
    private Date createTime;
    @Schema(description = "")
    private Date updateTime;
}