package com.ai.application.admin.mapper;

import com.ai.application.admin.api.entity.Market;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 市场表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Mapper
public interface MarketMapper extends BaseMapper<Market> {
    @Select("SELECT * FROM `market` WHERE tenant_id=#{tenantId} and market_type=#{marketType} LIMIT 1")
    Market selectMarketByMarketType(@Param("tenantId") Integer tenantId,@Param("marketType") Integer marketType);
}
