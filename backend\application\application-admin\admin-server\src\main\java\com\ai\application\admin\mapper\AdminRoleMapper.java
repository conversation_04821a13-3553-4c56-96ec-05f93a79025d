package com.ai.application.admin.mapper;

import com.ai.application.admin.api.entity.AdminRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 应用角色表 Mapper接口
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Mapper
public interface AdminRoleMapper extends BaseMapper<AdminRole> {
    @Select("select * from app_role where tenant_id = 0 and role_status=1 limit 1")
    AdminRole getSaAdminRole();
}
