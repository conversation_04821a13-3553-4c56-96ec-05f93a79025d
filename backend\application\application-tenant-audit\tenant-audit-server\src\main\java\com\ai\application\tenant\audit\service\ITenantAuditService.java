package com.ai.application.tenant.audit.service;

import com.ai.application.tenant.audit.api.dto.TenantAuditDTO;
import com.ai.application.tenant.audit.api.dto.query.TenantAddDTO;
import com.ai.application.tenant.audit.api.dto.query.TenantAuditQueryDTO;
import com.ai.application.tenant.audit.api.vo.TenantAuditVO;
import com.github.pagehelper.PageInfo;

import java.util.List;
import java.util.Set;

/**
 * 租户审核表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface ITenantAuditService {

        /**
         * 分页
         *
         * @param queryDto
         * @return
         */
        PageInfo<TenantAuditVO> page(TenantAuditQueryDTO queryDto);

        /**
         * 列表
         *
         * @param
         * @param queryDto
         * @return
         */
        List<TenantAuditVO> list(TenantAuditQueryDTO queryDto);

        /**
         * 保存
         *
         * @param dto
         */
        void add(TenantAddDTO dto);

        /**
         * 更新
         *
         * @param dto
         */
        void update(TenantAuditDTO dto);

        /**
         * 查看
         *
         * @param id
         * @return
         */
        TenantAuditVO get(Integer id);
}