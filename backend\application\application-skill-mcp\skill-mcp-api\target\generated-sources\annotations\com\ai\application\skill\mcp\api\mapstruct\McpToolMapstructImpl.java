package com.ai.application.skill.mcp.api.mapstruct;

import com.ai.application.skill.mcp.api.dto.McpToolDTO;
import com.ai.application.skill.mcp.api.entity.McpTool;
import com.ai.application.skill.mcp.api.entity.McpTool.McpToolBuilder;
import com.ai.application.skill.mcp.api.vo.McpToolVO;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-14T11:01:15+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class McpToolMapstructImpl implements McpToolMapstruct {

    @Override
    public McpTool toEntity(McpToolDTO dto) {
        if ( dto == null ) {
            return null;
        }

        McpToolBuilder mcpTool = McpTool.builder();

        mcpTool.mcpToolId( dto.getMcpToolId() );
        mcpTool.toolName( dto.getToolName() );
        mcpTool.toolDesc( dto.getToolDesc() );
        mcpTool.toolCategory( dto.getToolCategory() );
        mcpTool.inputSchema( dto.getInputSchema() );
        mcpTool.outputSchema( dto.getOutputSchema() );
        mcpTool.toolConfig( dto.getToolConfig() );
        mcpTool.toolMetadata( dto.getToolMetadata() );
        mcpTool.toolStatus( dto.getToolStatus() );
        mcpTool.rateLimit( dto.getRateLimit() );
        mcpTool.timeoutSeconds( dto.getTimeoutSeconds() );
        mcpTool.lastCallTime( dto.getLastCallTime() );
        mcpTool.callCount( dto.getCallCount() );
        mcpTool.serverId( dto.getServerId() );
        if ( dto.getCreateTime() != null ) {
            mcpTool.createTime( new Timestamp( dto.getCreateTime().getTime() ) );
        }
        if ( dto.getUpdateTime() != null ) {
            mcpTool.updateTime( new Timestamp( dto.getUpdateTime().getTime() ) );
        }

        return mcpTool.build();
    }

    @Override
    public List<McpTool> toEntityList(List<McpToolDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<McpTool> list = new ArrayList<McpTool>( dtolist.size() );
        for ( McpToolDTO mcpToolDTO : dtolist ) {
            list.add( toEntity( mcpToolDTO ) );
        }

        return list;
    }

    @Override
    public McpToolVO toVo(McpTool entity) {
        if ( entity == null ) {
            return null;
        }

        McpToolVO mcpToolVO = new McpToolVO();

        mcpToolVO.setMcpToolId( entity.getMcpToolId() );
        mcpToolVO.setToolName( entity.getToolName() );
        mcpToolVO.setToolDesc( entity.getToolDesc() );
        mcpToolVO.setToolCategory( entity.getToolCategory() );
        mcpToolVO.setInputSchema( entity.getInputSchema() );
        mcpToolVO.setOutputSchema( entity.getOutputSchema() );
        mcpToolVO.setToolConfig( entity.getToolConfig() );
        mcpToolVO.setToolMetadata( entity.getToolMetadata() );
        mcpToolVO.setToolStatus( entity.getToolStatus() );
        mcpToolVO.setRateLimit( entity.getRateLimit() );
        mcpToolVO.setTimeoutSeconds( entity.getTimeoutSeconds() );
        mcpToolVO.setLastCallTime( entity.getLastCallTime() );
        mcpToolVO.setCallCount( entity.getCallCount() );
        mcpToolVO.setServerId( entity.getServerId() );
        mcpToolVO.setCreateTime( entity.getCreateTime() );
        mcpToolVO.setUpdateTime( entity.getUpdateTime() );

        return mcpToolVO;
    }

    @Override
    public List<McpToolVO> toVoList(List<McpTool> entities) {
        if ( entities == null ) {
            return null;
        }

        List<McpToolVO> list = new ArrayList<McpToolVO>( entities.size() );
        for ( McpTool mcpTool : entities ) {
            list.add( toVo( mcpTool ) );
        }

        return list;
    }
}
