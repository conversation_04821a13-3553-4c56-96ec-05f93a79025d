package com.ai.application.agent.run.executor;

import cn.hutool.json.JSONUtil;
import com.ai.application.agent.run.dto.LongTermMemoryRetrieveRequestDTO;
import com.ai.application.agent.run.dto.LongTermMemoryRetrieveResultDTO;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.service.ILongTermMemoryRetrieveService;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import com.ai.framework.workflow.excutor.NodeExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 长期记忆检索节点执行器
 * 用于在工作流中从记忆服务检索长期记忆
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LongTermMemoryRetrieveNodeExecutor implements NodeExecutor {

    private final ILongTermMemoryRetrieveService longTermMemoryRetrieveService;

    @Override
    public void execute(WorkflowContext context) {
        String nodeKey = context.getCurrentNodeKey();
        NodeContext nodeCtx = context.getNodeContexts().get(nodeKey);
        Map<String, Object> nodeDef = nodeCtx.getNodeDefinition();

        log.info("LongTermMemoryRetrieveNodeExecutor execute start, nodeKey: {}, nodeDef: {}", nodeKey, JsonUtils.toJsonString(nodeDef));

        try {
            // 设置节点状态为运行中
            nodeCtx.setStatus(NodeStatus.RUNNING);

            // 初始化节点输出
            if (nodeCtx.getOutput() == null) {
                nodeCtx.setOutput(new HashMap<>());
            }

            // 从节点定义中获取输入参数
            Map<String, Object> inputParameters = (Map<String, Object>) nodeDef.get("inputParameters");
            if (inputParameters == null) {
                throw new ServiceException(ExecutorError.NODE_DEFINITION_IS_NULL);
            }

            // 构建长期记忆检索请求
            LongTermMemoryRetrieveRequestDTO request = buildLongTermMemoryRetrieveRequest(inputParameters, context);

            // 获取授权信息
            String authorization = (String) context.getGlobalVars().get("authorization");

            // 执行长期记忆检索
            ResultVo<LongTermMemoryRetrieveResultDTO> result = longTermMemoryRetrieveService.executeLongTermMemoryRetrieve(request, authorization);

            if (result.getCode() != 0 || result.getData() == null || !result.getData().getSuccess()) {
                String errorMsg = result.getData() != null ? result.getData().getErrorMessage() : result.getMessage();
                throw new ServiceException(result.getCode(), errorMsg);
            }

            LongTermMemoryRetrieveResultDTO retrieveResult = result.getData();

            // 构建输出结果（匹配backend2的输出格式）
            Map<String, Object> outputResult = buildOutputResult(retrieveResult);

            // 将结果写入输出参数
            writeOutputParameters(nodeDef, context, outputResult);

            // 设置节点输出
            nodeCtx.getOutput().putAll(outputResult);

            // 设置节点状态为成功
            nodeCtx.setStatus(NodeStatus.SUCCESS);
            nodeCtx.setEndTime(java.time.LocalDateTime.now());

            log.info("LongTermMemoryRetrieveNodeExecutor execute success, retrieved {} memories", retrieveResult.getReturnCount());

        } catch (Exception e) {
            log.error("LongTermMemoryRetrieveNodeExecutor execute error", e);
            nodeCtx.setStatus(NodeStatus.FAILED);
            nodeCtx.setErrorMsg("长期记忆检索执行失败: " + e.getMessage());
            nodeCtx.setEndTime(java.time.LocalDateTime.now());
            throw e;
        }
    }

    /**
     * 构建长期记忆检索请求
     */
    private LongTermMemoryRetrieveRequestDTO buildLongTermMemoryRetrieveRequest(Map<String, Object> inputParameters, WorkflowContext context) {
        // 获取参数值，支持变量替换
        String agentId = getParameterValue(inputParameters, "agent_id", context);
        String userId = getParameterValue(inputParameters, "user_id", context);
        String queryContent = getParameterValue(inputParameters, "queryContent", context);
        Object strategiesObj = getParameterObject(inputParameters, "strategies", context);
        String startDaysBeforeStr = getParameterValue(inputParameters, "startDaysBefore", context);
        String limitStr = getParameterValue(inputParameters, "limit", context);
        String longTermMemoryEnabledStr = getParameterValue(inputParameters, "longTermMemoryEnabled", context);
        String sessionSn = getParameterValue(inputParameters, "sessionSn", context);
        String debugRunStr = getParameterValue(inputParameters, "debugRun", context);
        String category = getParameterValue(inputParameters, "category", context);
        String similarityThresholdStr = getParameterValue(inputParameters, "similarityThreshold", context);
        String sortBy = getParameterValue(inputParameters, "sortBy", context);

        // 参数校验
        if (StringUtils.isBlank(agentId)) {
            throw new ServiceException(ExecutorError.AGENT_ID_IS_BLANK);
        }
        if (StringUtils.isBlank(userId)) {
            throw new ServiceException(ExecutorError.USER_ID_IS_BLANK);
        }
        if (StringUtils.isBlank(queryContent)) {
            throw new ServiceException(ExecutorError.LONG_TERM_MEMORY_QUERY_CONTENT_IS_BLANK);
        }

        // 处理strategies参数
        List<String> strategies = null;
        if (strategiesObj instanceof List<?> list) {
            strategies = new ArrayList<>();
            for (Object item : list) {
                if (item != null) {
                    strategies.add(item.toString());
                }
            }
        } else if (strategiesObj instanceof String strategiesStr) {
            try {
                strategies = JsonUtils.parseArray(strategiesStr, String.class);
            } catch (Exception e) {
                log.warn("Failed to parse strategies as JSON array: {}", strategiesStr);
                strategies = List.of(strategiesStr);
            }
        }

        // 构建请求
        LongTermMemoryRetrieveRequestDTO.LongTermMemoryRetrieveRequestDTOBuilder builder = LongTermMemoryRetrieveRequestDTO.builder()
                .agentId(agentId)
                .userId(userId)
                .queryContent(queryContent)
                .strategies(strategies)
                .category(StringUtils.isNotBlank(category) ? ""+category : "long_term")
                .sortBy(StringUtils.isNotBlank(sortBy) ? sortBy : "similarity_desc");

        // 处理数值参数
        if (StringUtils.isNotBlank(startDaysBeforeStr)) {
            try {
                builder.startDaysBefore(Integer.parseInt(startDaysBeforeStr));
            } catch (NumberFormatException e) {
                log.warn("Invalid startDaysBefore: {}, using default 9999", startDaysBeforeStr);
                builder.startDaysBefore(9999);
            }
        } else {
            builder.startDaysBefore(9999); // 默认值
        }

        if (StringUtils.isNotBlank(limitStr)) {
            try {
                builder.limit(Integer.parseInt(limitStr));
            } catch (NumberFormatException e) {
                log.warn("Invalid limit: {}, using default 10", limitStr);
                builder.limit(10);
            }
        } else {
            builder.limit(10); // 默认值
        }

        if (StringUtils.isNotBlank(similarityThresholdStr)) {
            try {
                builder.similarityThreshold(Double.parseDouble(similarityThresholdStr));
            } catch (NumberFormatException e) {
                log.warn("Invalid similarity threshold: {}, using default 0.7", similarityThresholdStr);
                builder.similarityThreshold(0.7);
            }
        }

        // 处理布尔参数
        if (StringUtils.isNotBlank(longTermMemoryEnabledStr)) {
            builder.longTermMemoryEnabled(!"false".equals(longTermMemoryEnabledStr));
        } else {
            builder.longTermMemoryEnabled(true); // 默认启用
        }

        if (StringUtils.isNotBlank(debugRunStr)) {
            builder.debugRun("true".equals(debugRunStr));
        }

        if (StringUtils.isNotBlank(sessionSn)) {
            builder.sessionSn(sessionSn);
        }

        return builder.build();
    }

    /**
     * 构建输出结果（匹配backend2的输出格式）
     */
    private Map<String, Object> buildOutputResult(LongTermMemoryRetrieveResultDTO retrieveResult) {
        Map<String, Object> result = new HashMap<>();
        
        // 主要输出：jsonList（匹配backend2的输出参数名）
        result.put("jsonList", CollectionUtils.isNotEmpty(retrieveResult.getJsonList()) ? retrieveResult.getJsonList() : new ArrayList<>());
        
        // 兼容性输出：list（匹配组件定义的输出参数名）
        result.put("list", CollectionUtils.isNotEmpty(retrieveResult.getJsonList()) ? retrieveResult.getJsonList() : new ArrayList<>());
        
        // 添加元数据
        result.put("success", retrieveResult.getSuccess());
        result.put("totalCount", retrieveResult.getTotalCount());
        result.put("returnCount", retrieveResult.getReturnCount());
        
        if (StringUtils.isNotBlank(retrieveResult.getErrorMessage())) {
            result.put("errorMessage", retrieveResult.getErrorMessage());
        }

        return result;
    }

    /**
     * 获取参数值，支持变量替换
     */
    private String getParameterValue(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        String strValue = value.toString();
        
        // 如果是变量引用（以$开头），从全局变量中获取
        if (strValue.startsWith("$")) {
            String varName = strValue.substring(1);
            Object varValue = context.getGlobalVars().get(varName);
            return varValue != null ? varValue.toString() : null;
        }
        
        return strValue;
    }

    /**
     * 获取参数对象，支持变量替换
     */
    private Object getParameterObject(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        if (value instanceof String) {
            String strValue = value.toString();
            // 如果是变量引用（以$开头），从全局变量中获取
            if (strValue.startsWith("$")) {
                String varName = strValue.substring(1);
                return context.getGlobalVars().get(varName);
            }
        }
        
        return value;
    }

    /**
     * 写入输出参数
     */
    private void writeOutputParameters(Map<String, Object> nodeDef, WorkflowContext context, Map<String, Object> result) {
        Map<String, Object> outputParameters = (Map<String, Object>) nodeDef.get("outputParameters");
        if (outputParameters != null) {
            for (Map.Entry<String, Object> entry : outputParameters.entrySet()) {
                String outputKey = entry.getKey();
                String variableName = entry.getValue().toString();

                Object resultValue = result.get(outputKey);
                if (resultValue != null) {
                    context.setVar(variableName, resultValue);
                    log.info("Set variable {} = {}", variableName, resultValue);
                }
            }
        }
    }

    @Override
    public String getType() {
        return "LONG_TERM_MEMORY_SEARCH";
    }
}
