package com.ai.application.admin.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * 租户表
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(name = "租户表DTO-新增")
public class TenantAddDTO {
    /**
     * 租户名称
     */
    @Schema(description = "租户名称")
    @NotBlank(message = "租户名称不能为空")
    @Length(max = 50,message = "租户名称长度不能超过50个字符")
    private String tenantName;
    /**
     * 租户域名
     */
    @Schema(description = "租户编码")
    @NotBlank(message = "租户编码不能为空")
    @Length(max = 50,message = "租户编码长度不能超过50个字符")
    private String tenantDomain;
    /**
     * 租户描述
     */
    @Schema(description = "租户描述/备注")
    @Length(max = 200,message = "租户描述/备注长度不能超过200个字符")
    private String tenantDesc;
    /**
     * 有效截止时间
     */
    @Schema(description = "有效截止时间")
    private Date tenantExpireTime;
    /**
     * 租户状态 0:禁用,1:启用,-1:删除
     */
    @Schema(description = "租户状态 0:禁用,1:启用,-1:删除")
    private Integer tenantStatus;

    @Schema(description = "智能体限制数量")
    private Integer agentLimitCount=0;

    @Schema(description = "用户限制数量")
    private Integer userLimitCount=0;

    @Schema(description = "授权的智能体列表")
    private List<GrantResourceDTO> grantAgentList;

    @Schema(description = "授权的模型列表")
    private List<GrantResourceDTO> grantModelList;

    /**
     * 登录密码
     */
    @Schema(description = "登录密码")
    @Length(min = 8,max = 16, message = "密码长度8~16个字符")
    private String password;

    @Schema(description = "确认密码")
    @Length(min = 8,max = 16, message = "确认密码长度8~16个字符")
    private String confirmPassword;
}