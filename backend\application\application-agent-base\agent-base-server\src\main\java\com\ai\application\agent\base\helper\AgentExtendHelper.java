package com.ai.application.agent.base.helper;

import com.ai.application.agent.base.api.entity.AgentExtend;
import com.ai.application.agent.base.api.entity.AgentVersionExtend;
import com.ai.framework.core.util.list.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class AgentExtendHelper {
    // 获取Agent扩展
    public static Map<String, String> getAgentExtendItemValue(List<AgentExtend> agentExtends) {
        if (CollectionUtils.isEmpty(agentExtends)) {
            return Collections.emptyMap();
        }
        return agentExtends.stream().collect(Collectors.toMap(AgentExtend::getItemValue, AgentExtend::getItemName));
    }
}
