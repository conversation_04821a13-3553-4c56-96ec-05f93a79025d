package com.ai.application.admin.api.mapstruct;

import com.ai.application.admin.api.dto.ModelAddDTO;
import com.ai.application.admin.api.entity.ModelInfo;
import com.ai.application.admin.api.entity.ModelSupplier;
import com.ai.application.admin.api.vo.ModelBaseVO;
import com.ai.application.admin.api.vo.ModelInfoDetailVO;
import com.ai.application.admin.api.vo.ModelInfoVO;
import com.ai.application.admin.api.vo.ModelSupplierGroupVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-14T11:00:48+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class ModelMapstructImpl implements ModelMapstruct {

    @Override
    public ModelInfoVO toVo(ModelInfo entity) {
        if ( entity == null ) {
            return null;
        }

        ModelInfoVO modelInfoVO = new ModelInfoVO();

        modelInfoVO.setModelId( entity.getModelId() );
        modelInfoVO.setModelSn( entity.getModelSn() );
        modelInfoVO.setModelName( entity.getModelName() );
        modelInfoVO.setModelEngine( entity.getModelEngine() );
        modelInfoVO.setModelType( entity.getModelType() );
        modelInfoVO.setModelToolcall( entity.getModelToolcall() );
        modelInfoVO.setModelDesc( entity.getModelDesc() );
        modelInfoVO.setModelConfig( entity.getModelConfig() );
        modelInfoVO.setModelStatus( entity.getModelStatus() );
        modelInfoVO.setSupplierId( entity.getSupplierId() );
        modelInfoVO.setCreateTime( entity.getCreateTime() );
        modelInfoVO.setUpdateTime( entity.getUpdateTime() );

        return modelInfoVO;
    }

    @Override
    public ModelInfoDetailVO toDetailVo(ModelInfo entity) {
        if ( entity == null ) {
            return null;
        }

        ModelInfoDetailVO modelInfoDetailVO = new ModelInfoDetailVO();

        modelInfoDetailVO.setModelSn( entity.getModelSn() );
        modelInfoDetailVO.setModelName( entity.getModelName() );
        modelInfoDetailVO.setModelEngine( entity.getModelEngine() );
        modelInfoDetailVO.setModelType( entity.getModelType() );
        modelInfoDetailVO.setModelToolcall( entity.getModelToolcall() );
        modelInfoDetailVO.setModelDesc( entity.getModelDesc() );
        modelInfoDetailVO.setModelConfig( entity.getModelConfig() );
        modelInfoDetailVO.setModelStatus( entity.getModelStatus() );
        modelInfoDetailVO.setSupplierId( entity.getSupplierId() );

        return modelInfoDetailVO;
    }

    @Override
    public ModelInfo toEntity(ModelAddDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ModelInfo modelInfo = new ModelInfo();

        modelInfo.setModelName( dto.getModelName() );
        modelInfo.setModelEngine( dto.getModelEngine() );
        modelInfo.setModelType( dto.getModelType() );
        modelInfo.setModelToolcall( dto.getModelToolcall() );
        modelInfo.setModelDesc( dto.getModelDesc() );
        modelInfo.setModelConfig( dto.getModelConfig() );
        modelInfo.setSupplierId( dto.getSupplierId() );

        return modelInfo;
    }

    @Override
    public List<ModelInfoVO> toVoList(List<ModelInfo> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ModelInfoVO> list = new ArrayList<ModelInfoVO>( entities.size() );
        for ( ModelInfo modelInfo : entities ) {
            list.add( toVo( modelInfo ) );
        }

        return list;
    }

    @Override
    public List<ModelBaseVO> toBaseVoList(List<ModelInfo> voList) {
        if ( voList == null ) {
            return null;
        }

        List<ModelBaseVO> list = new ArrayList<ModelBaseVO>( voList.size() );
        for ( ModelInfo modelInfo : voList ) {
            list.add( modelInfoToModelBaseVO( modelInfo ) );
        }

        return list;
    }

    @Override
    public List<ModelSupplierGroupVO> toSupplierList(List<ModelSupplier> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ModelSupplierGroupVO> list = new ArrayList<ModelSupplierGroupVO>( entities.size() );
        for ( ModelSupplier modelSupplier : entities ) {
            list.add( modelSupplierToModelSupplierGroupVO( modelSupplier ) );
        }

        return list;
    }

    protected ModelBaseVO modelInfoToModelBaseVO(ModelInfo modelInfo) {
        if ( modelInfo == null ) {
            return null;
        }

        ModelBaseVO modelBaseVO = new ModelBaseVO();

        modelBaseVO.setModelSn( modelInfo.getModelSn() );
        modelBaseVO.setModelName( modelInfo.getModelName() );
        modelBaseVO.setModelEngine( modelInfo.getModelEngine() );
        modelBaseVO.setModelType( modelInfo.getModelType() );
        modelBaseVO.setModelToolcall( modelInfo.getModelToolcall() );
        modelBaseVO.setModelDesc( modelInfo.getModelDesc() );
        modelBaseVO.setModelConfig( modelInfo.getModelConfig() );
        modelBaseVO.setModelStatus( modelInfo.getModelStatus() );
        modelBaseVO.setSupplierId( modelInfo.getSupplierId() );
        modelBaseVO.setCreateTime( modelInfo.getCreateTime() );
        modelBaseVO.setUpdateTime( modelInfo.getUpdateTime() );

        return modelBaseVO;
    }

    protected ModelSupplierGroupVO modelSupplierToModelSupplierGroupVO(ModelSupplier modelSupplier) {
        if ( modelSupplier == null ) {
            return null;
        }

        ModelSupplierGroupVO modelSupplierGroupVO = new ModelSupplierGroupVO();

        modelSupplierGroupVO.setSupplierId( modelSupplier.getSupplierId() );
        modelSupplierGroupVO.setSupplierSn( modelSupplier.getSupplierSn() );
        modelSupplierGroupVO.setSupplierName( modelSupplier.getSupplierName() );

        return modelSupplierGroupVO;
    }
}
