package com.ai.application.agent.run.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 文档知识检索结果 DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(name = "DocumentSearchResultDTO")
public class DocumentSearchResultDTO {

    /**
     * 检索结果
     */
    @Schema(description = "检索结果")
    private String output;

    /**
     * 文档片段列表
     */
    @Schema(description = "文档片段列表")
    private List<DocumentFragmentDTO> fragments;

    /**
     * 是否成功
     */
    @Schema(description = "是否成功")
    private Boolean success;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 总数量
     */
    @Schema(description = "总数量")
    private Integer totalCount;

    /**
     * 文档片段 DTO
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @SuperBuilder
    @Schema(name = "DocumentFragmentDTO")
    public static class DocumentFragmentDTO {

        /**
         * 片段ID
         */
        @Schema(description = "片段ID")
        private String fragmentId;

        /**
         * 文档ID
         */
        @Schema(description = "文档ID")
        private String documentId;

        /**
         * 文件名
         */
        @Schema(description = "文件名")
        private String fileName;

        /**
         * 文件编号
         */
        @Schema(description = "文件编号")
        private String fileSn;

        /**
         * 内容
         */
        @Schema(description = "内容")
        private String content;

        /**
         * 相关性分数
         */
        @Schema(description = "相关性分数")
        private Double score;

        /**
         * 页码
         */
        @Schema(description = "页码")
        private Integer pageNumber;

        /**
         * 知识库编号
         */
        @Schema(description = "知识库编号")
        private String knowledgeInventorySn;

        /**
         * 检索方法
         */
        @Schema(description = "检索方法")
        private String method;

        /**
         * 元数据
         */
        @Schema(description = "元数据")
        private Object metadata;

        /**
         * 创建时间
         */
        @Schema(description = "创建时间")
        private Long createTime;

        /**
         * 更新时间
         */
        @Schema(description = "更新时间")
        private Long updateTime;
    }
}
