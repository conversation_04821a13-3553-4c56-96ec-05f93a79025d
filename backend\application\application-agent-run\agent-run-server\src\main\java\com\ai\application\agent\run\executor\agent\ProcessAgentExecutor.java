package com.ai.application.agent.run.executor.agent;

import com.ai.application.agent.base.api.dto.AgentChatDTO;
import com.ai.application.agent.base.api.feign.IAgentClient;
import com.ai.application.agent.base.api.vo.AgentChatVO;
import com.ai.application.agent.run.errors.AgentNodeExecutorError;
import com.ai.application.agent.run.executor.AgentExecutionContext;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 流程型智能体执行器
 */
@Slf4j
@Component
public class ProcessAgentExecutor extends BaseAgentExecutor {

    @Autowired
    private IAgentClient agentClient;

    @Override
    public Map<String, Object> execute(AgentExecutionContext executionContext) {
        String processInstanceId = executionContext.getProcessInstanceId();
        String agentSn = executionContext.getParameterAsString("agentSn");
        String agentType = executionContext.getParameterAsString("agentType");
        String sessionSn = executionContext.getParameterAsString("sessionSn");
        String msgContent = executionContext.getParameterAsString("msgContent");
        String auth = executionContext.getAuthorization();
        
        // 会话管理
        boolean contextMemorised = Objects.equals("true", executionContext.getParameterAsString("contextMemorised"));
        if (sessionSn == null || !contextMemorised) {
            String modelSn = executionContext.getParameterAsString("model");
            String prompt = executionContext.getParameterAsString("sysPrompt");
            String temperature = executionContext.getParameterAsString("temperature");
            
            if (StringUtils.isNotBlank(prompt) || StringUtils.isNotBlank(modelSn) || StringUtils.isNotBlank(temperature)) {
                sessionSn = newDebugSessionSn(auth, agentSn, modelSn, prompt, 
                    temperature != null ? Double.parseDouble(temperature) : 0.6);
            } else {
                sessionSn = newSessionSn(auth, agentSn);
            }
        }

        // 构建智能体请求
        AgentChatDTO chatDto = AgentChatDTO.builder()
                .agentSn(agentSn)
                .agentType(agentType)
                .msgContent(msgContent)
                .msgType("text")
                .delayInMs(20L)
                .fromCode("Workflow")
                .processId(executionContext.getProcessId())
                .sessionSn(sessionSn)
                .debug(executionContext.isDebugRun())
                .build();

        log.info("[{}] {} agent process request body {}", processInstanceId, agentType, JsonUtils.toJsonString(chatDto));

        // 调用智能体服务
        ResultVo<AgentChatVO> result = agentClient.chat(chatDto);

        if (result == null || !Objects.equals(result.getCode(), 0)) {
            String errorMsg = result != null ? result.getMessage() : "调用文档智能体失败";
            log.error("[{}] {} agent doc error {}", processInstanceId, agentType, errorMsg);
            throw new ServiceException(51004,"调用文档智能体失败: " + errorMsg);
        }

        AgentChatVO agentChatVo = result.getData();
        if (agentChatVo == null) {
            throw new ServiceException(AgentNodeExecutorError.AGENT_RESPONSE_IS_NULL);
        }

        // 解析返回结果
        String message = parseMsgContent(agentChatVo);
        
        // 流程智能体特有的逻辑：解析流程节点信息
        List<Object> processNodes = parseProcessNodes(agentChatVo);
        Map<String, Object> processDebug = parseProcessDebug(agentChatVo);
        
        log.info("[{}] {} agent process response, message: {}, nodes: {}", 
                processInstanceId, agentType, message, processNodes.size());

        // 构建返回结果
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("message", message);
        resultMap.put("sessionSn", sessionSn);
        resultMap.put("success", agentChatVo.getSuccess());
        resultMap.put("processNodes", processNodes);
        resultMap.put("processDebug", processDebug);
        
        return resultMap;
    }

    /**
     * 解析消息内容
     */
    private String parseMsgContent(AgentChatVO agentChatVo) {
        if (agentChatVo.getContent() != null) {
            JsonNode answerNode = agentChatVo.getContent().get("answer");
            if (answerNode != null && answerNode.isTextual()) {
                return answerNode.asText();
            }
        }
        
        return StringUtils.defaultIfBlank(agentChatVo.getReply(), "");
    }

    /**
     * 解析流程节点信息
     */
    private List<Object> parseProcessNodes(AgentChatVO agentChatVo) {
        List<Object> nodesList = new ArrayList<>();
        
        if (agentChatVo.getContent() != null) {
            JsonNode nodesNode = agentChatVo.getContent().get("nodes");
            if (nodesNode != null && nodesNode.isArray()) {
                for (JsonNode nodeNode : nodesNode) {
                    Map<String, Object> nodeInfo = new HashMap<>();
                    
                    if (nodeNode.has("id")) {
                        nodeInfo.put("id", nodeNode.get("id").asText());
                    }
                    if (nodeNode.has("name")) {
                        nodeInfo.put("name", nodeNode.get("name").asText());
                    }
                    if (nodeNode.has("type")) {
                        nodeInfo.put("type", nodeNode.get("type").asText());
                    }
                    if (nodeNode.has("status")) {
                        nodeInfo.put("status", nodeNode.get("status").asText());
                    }
                    if (nodeNode.has("output")) {
                        nodeInfo.put("output", nodeNode.get("output"));
                    }
                    
                    if (!nodeInfo.isEmpty()) {
                        nodesList.add(nodeInfo);
                    }
                }
            }
        }
        
        return nodesList;
    }

    /**
     * 解析流程调试信息
     */
    private Map<String, Object> parseProcessDebug(AgentChatVO agentChatVo) {
        Map<String, Object> debugInfo = new HashMap<>();
        
        if (agentChatVo.getContent() != null) {
            JsonNode debugNode = agentChatVo.getContent().get("processDebug");
            if (debugNode != null && debugNode.isObject()) {
                if (debugNode.has("processId")) {
                    debugInfo.put("processId", debugNode.get("processId").asText());
                }
                if (debugNode.has("instanceId")) {
                    debugInfo.put("instanceId", debugNode.get("instanceId").asText());
                }
                if (debugNode.has("status")) {
                    debugInfo.put("status", debugNode.get("status").asText());
                }
                if (debugNode.has("startTime")) {
                    debugInfo.put("startTime", debugNode.get("startTime").asText());
                }
                if (debugNode.has("endTime")) {
                    debugInfo.put("endTime", debugNode.get("endTime").asText());
                }
            }
        }
        
        return debugInfo;
    }

    @Override
    public String getAgentType() {
        return "process";
    }
}
