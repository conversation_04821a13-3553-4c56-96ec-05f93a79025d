package com.ai.application.app.auth.granter;

import com.ai.application.app.api.dto.query.AppUserVerifyPasswordDTO;
import com.ai.application.app.api.feign.IAppUserClient;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.application.app.auth.api.vo.GrantVO;
import com.ai.application.app.auth.component.TokenComponent;
import com.ai.application.app.auth.component.UserCacheComponent;
import com.ai.application.tenant.api.entity.Tenant;
import com.ai.application.tenant.api.feign.ITenantClient;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.util.validator.AssertUtil;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.web.properties.AuthProperties;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jose4j.json.JsonUtil;
import org.springframework.stereotype.Component;

/**
 * PasswordTokenGranter
 * <AUTHOR>
 */
@Slf4j
@Component
public class PasswordTokenGranter implements ITokenGranter {
    @Resource
    private AuthProperties authProperties;

    // 模式
    public static final Integer GRANT_TYPE = 1;

    @Resource
    private IAppUserClient appUserClient;

    @Resource
    private ITenantClient tenantClient;

    /**
     * 密码验证
     * @param tokenParameter 授权参数
     * @return
     */
    @Override
    public GrantVO grant(TokenParameter tokenParameter) {

        // 登录验证
        AppUserVO appUserVO = this.loginValidate(tokenParameter);

        Integer userId = appUserVO.getUserId();
        String userSn = appUserVO.getUserSn();
        String userName = appUserVO.getUserName();
        Integer appId = tokenParameter.getArgs().getInt("appId");

        ResultVo<Tenant> tenantByIdRes = tenantClient.getTenantById(appUserVO.getTenantId());
        AssertUtil.isTrue(tenantByIdRes.isSuccess(), "获取租户信息异常");
        Tenant tenantData = tenantByIdRes.getData();
        String tenantSn = tenantData.getTenantSn();
        Integer tenantId = tenantData.getTenantId();

        // 获取密钥
        String accessTokenPrivateKey = authProperties.getAccessTokenPrivateKey();
        Integer tokenExpiration = authProperties.getTokenExpiration();

        // 获取accessToken
        String accessToken = TokenComponent.getToken(userId, tenantId, userSn, tenantSn, appId, accessTokenPrivateKey);

        // 生成刷新token
        String refreshToken = UUIDUtil.genRandomSn("refresh-token");

        // 设置用户登录缓存
        UserCacheComponent.UserLoginCache(userSn, accessToken, refreshToken, tokenExpiration);

        // 设置返回值
        GrantVO grantVo = new GrantVO();
        grantVo.setAccessToken(accessToken);
        grantVo.setRefreshToken(refreshToken);
        grantVo.setUserName(userName);
        grantVo.setUserSn(userSn);
        grantVo.setTenantSn(tenantData.getTenantSn());
        grantVo.setTenantDomain(tenantData.getTenantDomain());
        grantVo.setTenantName(tenantData.getTenantName());
        return grantVo;
    }

    AppUserVO loginValidate(TokenParameter tokenParameter) {
        Integer appId = tokenParameter.getArgs().getInt("appId");
        String loginName = tokenParameter.getArgs().getStr("loginName");
        String password = tokenParameter.getArgs().getStr("password");
        String tenantSn = tokenParameter.getArgs().getStr("tenantSn");

        // 登录
        AppUserVerifyPasswordDTO appUserVerifyPasswordDTO = new AppUserVerifyPasswordDTO();
        appUserVerifyPasswordDTO.setLoginName(loginName);
        appUserVerifyPasswordDTO.setPassword(password);
        appUserVerifyPasswordDTO.setTenantSn(tenantSn);
        appUserVerifyPasswordDTO.setAppId(appId);
        ResultVo<AppUserVO> appUserVOResultVo = appUserClient.verifyPassword(appUserVerifyPasswordDTO);
        AssertUtil.isTrue(appUserVOResultVo.isSuccess(), appUserVOResultVo.getMessage());

        return appUserVOResultVo.getData();
    }
}
