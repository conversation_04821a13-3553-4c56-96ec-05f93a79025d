package com.ai.application.agent.run.executor;

import org.apache.commons.lang3.NotImplementedException;
import reactor.core.publisher.Flux;

import java.util.Map;

/**
 * 执行器基础接口
 */
public interface BaseExecutor {

    /**
     * 同步执行
     *
     * @param executionContext 上下文信息
     * @return 输出结果
     */
    Map<String, Object> execute(ExecutionContext executionContext);

    /**
     * 异步调用 基于回调消息 需要自己返回结果
     *
     * @param executionContext 上下文信息
     */
    default void executeAsync(ExecutionContext executionContext) {
        // 默认实现为空
    }

    /**
     * 响应式执行
     *
     * @param executionContext executionContext
     * @return 响应式输出结果
     */
    default Flux<Map<String, Object>> executeReactive(ExecutionContext executionContext) {
        throw new NotImplementedException("executeReactive Not implemented");
    }

    /**
     * 组件id
     *
     * @return string
     */
    String getId();

    /**
     * 预校验
     * 异常则直接报错
     *
     * @param executionContext 上下文
     */
    default void preValidate(ExecutionContext executionContext) {
        // 默认实现为空
    }
}
