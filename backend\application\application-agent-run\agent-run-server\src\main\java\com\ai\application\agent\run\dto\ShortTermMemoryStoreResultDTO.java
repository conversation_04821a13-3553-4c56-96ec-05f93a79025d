package com.ai.application.agent.run.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 短期记忆存储结果DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "ShortTermMemoryStoreResultDTO")
public class ShortTermMemoryStoreResultDTO {

    /**
     * 是否成功
     */
    @Schema(description = "是否成功")
    private Boolean success;

    /**
     * 输出结果
     */
    @Schema(description = "输出结果")
    private String output;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 存储的记忆ID列表
     */
    @Schema(description = "存储的记忆ID列表")
    private List<String> memoryIds;

    /**
     * 存储的记忆数量
     */
    @Schema(description = "存储的记忆数量")
    private Integer memoryCount;
}
