package com.ai.application.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 应用角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("app_role")
public class AdminRole implements Serializable {
    @Schema(description = "")
    @TableId(type = IdType.AUTO)
    private Integer roleId;

    /**
     * 角色code:SA,ADMIN,IT,USER
     */
    @Schema(description = "角色code:SA,ADMIN,IT,USER")
    private String roleCode;

    /**
     * 角色名称
     */
    @Schema(description = "角色名称")
    private String roleName;

    /**
     * 角色描述
     */
    @Schema(description = "角色描述")
    private String roleDesc;

    /**
     * 角色状态:1启用,0停用,-1删除
     */
    @Schema(description = "角色状态:1启用,0停用,-1删除")
    private Integer roleStatus;

    /**
     * 应用ID
     */
    @Schema(description = "应用ID")
    private Integer appId;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Integer tenantId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}