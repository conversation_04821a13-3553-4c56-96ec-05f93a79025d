package com.ai.application.agent.base.api.dto.query;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import com.ai.framework.core.vo.PageParam;

/**
 * 智能体表-查询条件
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Data
@Schema(name = "智能体表QueryDTO")
public class AgentQueryDTO extends PageParam {
    @Schema(description = "智能体id集合")
    private List<Integer> agentIds;

    @Schema(description = "智能体名称")
    private String agentName;

    @Schema(description = "智能体类型: 10:对话流, 20:工作流, 30:master")
    private Integer agentType;

    @Schema(description = "状态 0停用 1开发 5发布")
    private Integer agentStatus;
}