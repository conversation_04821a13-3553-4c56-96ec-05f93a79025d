package com.ai.application.admin.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 应用用户表
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(name = "管理后台-用户表编辑用户DTO")
public class AdminUserUpdateDTO {
    /**
     * 显示名
     */
    @Schema(description = "显示名")
    @Length(max = 20,message ="显示名长度不能超过20")
    private String userName;
}