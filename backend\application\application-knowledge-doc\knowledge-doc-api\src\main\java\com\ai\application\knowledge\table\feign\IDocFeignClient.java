package com.ai.application.knowledge.table.feign;

import com.ai.application.knowledge.table.dto.DeleteBySnFeignDto;
import com.ai.application.knowledge.table.dto.EmbeddingFeignDto;
import com.ai.application.knowledge.table.dto.FileByIdFeignDto;
import com.ai.application.knowledge.table.vo.BaseDetailVo;
import com.ai.application.knowledge.table.vo.FileDetailVo;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

@FeignClient(value = "knowledge-doc-service", contextId = "IDocFeignClient")
public interface IDocFeignClient {

    @GetMapping("/detailById/{kbId}")
    ResultVo<BaseDetailVo> detailById(@PathVariable("kbId") Integer kbId);

    @GetMapping("/detailById/{kbSn}")
     ResultVo<BaseDetailVo> detailBySn(@PathVariable("kbSn") String kbSn);

    @PostMapping("/deleteById")
    ResultVo<String> deleteById(@Valid @RequestBody DeleteBySnFeignDto dto);

    @PostMapping("/embedding")
     ResultVo<String> embedding(@Valid @RequestBody EmbeddingFeignDto dto);

    @PostMapping("/fileDetailById")
    ResultVo<FileDetailVo> fileDetailById(@Valid @RequestBody FileByIdFeignDto dto);


}
