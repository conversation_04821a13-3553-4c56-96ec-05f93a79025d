package com.ai.application.admin.mapper;

import com.ai.application.admin.api.entity.AdminUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 应用用户表 Mapper接口
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Mapper
public interface AdminUserMapper extends BaseMapper<AdminUser> {
    @Select("select * from app_user where user_sn = #{userSn} and user_status >= 0")
    AdminUser findByUserSn(@Param("userSn") String userSn);

    @Select("select * from app_user where app_id=#{appId} and tenant_id=#{tenantId} and user_account = #{userAccount} and user_status >= 0 limit 1")
    AdminUser getByUserAccount(@Param("appId") Integer appId, @Param("tenantId") Integer tenantId, @Param("userAccount") String userAccount);
}
