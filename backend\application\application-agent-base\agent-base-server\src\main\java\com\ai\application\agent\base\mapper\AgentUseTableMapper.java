package com.ai.application.agent.base.mapper;

import com.ai.application.agent.base.api.dto.query.AgentUseKnowledgeTableQueryDTO;
import com.ai.application.agent.base.api.entity.AgentUseTable;
import com.ai.application.agent.base.api.vo.AgentUseKnowledgeTableQueryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 智能体关联智能表格表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface AgentUseTableMapper extends BaseMapper<AgentUseTable> {
    IPage<AgentUseKnowledgeTableQueryVO> selectUseTableByPage(IPage<AgentUseKnowledgeTableQueryVO> page, @Param("params") AgentUseKnowledgeTableQueryDTO dto);
}