package com.ai.application.agent.base.service;

import com.ai.application.agent.base.api.dto.*;
import com.ai.application.agent.base.api.vo.AgentDetailVO;
import com.ai.application.agent.base.api.vo.AgentVersionListVO;
import com.ai.application.agent.base.api.vo.AgentVersionVO;

import java.util.List;

/**
 * 智能体表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
public interface IAgentVersionService {
    /**
     * 根据版本查详情
     * @param versionSn
     * @return
     */
    AgentDetailVO detail(String versionSn);

    /**
     * 版本发布
     * @param dto
     */
    void publish(AgentPublishDTO dto);

    /**
     * 发布为草稿
     * @param dto
     */
    String masterDraft(MasterDraftDTO dto);

    String workFlowDraft(WorkFlowDraftDTO dto);

    /**
     * 版本列表
     */
    List<AgentVersionListVO> list(AgentVersionListDTO dto);

    /**
     * 版本更新
     * @param dto
     */
    void update(MasterUpdateDTO dto);

    /**
     * 智能体版本创建
     * @param dto
     */
    String masterAdd(MasterAddDTO dto);

    String workFlowAdd(WorkFlowAddDTO dto);

    /**
     * 启用版本
     * @param dto
     */
    void enable(VersionEnableDTO dto);

    /**
     * 版本上下架
     * @param dto
     */
    void sale(VersionSaleDTO dto);

    AgentVersionVO get(Integer versionId);

    AgentVersionVO getBySn(String versionSn);
}