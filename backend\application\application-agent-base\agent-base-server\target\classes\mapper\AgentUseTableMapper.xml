<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.agent.base.mapper.AgentUseTableMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.agent.base.api.entity.AgentUseTable">
                    <id column="at_id" property="atId" />
                    <result column="at_status" property="atStatus" />
                    <result column="agent_id" property="agentId" />
                    <result column="version_id" property="versionId" />
                    <result column="table_id" property="tableId" />
                    <result column="table_extend" property="tableExtend" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        at_id, at_status, agent_id, version_id, table_id, table_extend, create_time, update_time
    </sql>

    <select id="selectUseTableByPage" resultType="com.ai.application.agent.base.api.vo.AgentUseKnowledgeTableQueryVO">
        select
        <include refid="com.ai.application.agent.base.mapper.AgentUseTableMapper.Base_Column_List"></include>
        from agent_use_table
        order by create_time desc;
    </select>
</mapper>