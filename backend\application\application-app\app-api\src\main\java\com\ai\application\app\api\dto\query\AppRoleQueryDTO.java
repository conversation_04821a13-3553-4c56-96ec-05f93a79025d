package com.ai.application.app.api.dto.query;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import com.ai.framework.core.vo.PageParam;

import java.util.List;

/**
 * 应用角色表 查询条件
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(name = "应用角色表QueryDTO")
public class AppRoleQueryDTO extends PageParam {
    @Schema(description = "角色id集合")
    private List<Integer> roleIds;

    @Schema(description = "角色code")
    private String roleCode;
}