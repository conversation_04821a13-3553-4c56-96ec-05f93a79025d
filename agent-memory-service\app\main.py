# -*- coding: utf-8 -*-
"""
FastAPI应用主入口

智能体记忆模块的主应用程序文件，负责：
- FastAPI应用初始化和配置
- 中间件设置（CORS、日志记录）
- 异常处理和错误响应
- API路由注册
- 应用生命周期管理

Project: Agent Memory Service
Author: 张小龙 <<EMAIL>>
Created: 2025-06-01
Version: 1.0.0
License: private
"""

from datetime import datetime
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from loguru import logger
import uvicorn

# 导入配置和服务
from app.config.settings import settings
from app.config.nacos_client import nacos_manager
from app.services.memory_service import memory_service
from app.api.memory import router as memory_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("智能体记忆模块启动中...")
    
    try:
        # 初始化记忆服务
        await memory_service.initialize()
        logger.info("记忆服务初始化完成")
        
        # 初始化Nacos（如果配置了）
        try:
            await nacos_manager.init_nacos()
            logger.info("Nacos服务初始化完成")
        except Exception as e:
            logger.warning(f"Nacos初始化失败，将在无服务注册模式下运行: {e}")
        
        logger.info("智能体记忆模块启动完成")
        
        yield
        
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        raise
    finally:
        # 应用关闭时的清理工作
        logger.info("智能体记忆模块关闭中...")
        
        try:
            await memory_service.close()
            logger.info("记忆服务已关闭")
        except Exception as e:
            logger.error(f"关闭记忆服务失败: {e}")
        
        try:
            await nacos_manager.close()
            logger.info("Nacos服务已关闭")
        except Exception as e:
            logger.error(f"关闭Nacos服务失败: {e}")
        
        logger.info("智能体记忆模块已关闭")


# 创建FastAPI应用
app = FastAPI(
    title="智能体记忆模块",
    description="""
    智能体记忆模块提供以下功能：
    
    - **记忆存储**: 支持智能体记忆的结构化存储，包括智能体ID、用户ID、记忆类别、用户问题、问题回复等
    - **向量检索**: 使用 thenlper/gte-base-zh 模型进行文本向量化，支持基于语义相似度的记忆检索
    - **混合检索**: 支持条件过滤与向量相似度检索的组合，包括时间范围、记忆类别等维度
    - **记忆管理**: 提供记忆的增删查改功能，支持批量操作和统计分析
    - **服务集成**: 集成Elasticsearch存储、Nacos服务注册与配置管理
    """,
    version=settings.app.version,
    debug=settings.app.debug,
    lifespan=lifespan
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 全局异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理"""
    logger.error(f"HTTP异常: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": "请求执行失败",
            "detail": exc.detail,
            "timestamp": datetime.now().isoformat()
        }
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理"""
    logger.error(f"请求验证失败: {exc}")
    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "message": "请求参数验证失败",
            "detail": exc.errors(),
            "timestamp": datetime.now().isoformat()
        }
    )


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    logger.error(f"全局异常: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "服务内部错误",
            "detail": str(exc),
            "timestamp": datetime.now().isoformat()
        }
    )


# 请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录请求日志"""
    start_time = datetime.now()
    
    # 记录请求信息
    logger.info(
        f"收到请求: {request.method} {request.url.path} "
        f"from {request.client.host if request.client else 'unknown'}"
    )
    
    # 处理请求
    response = await call_next(request)
    
    # 计算处理时间
    process_time = (datetime.now() - start_time).total_seconds()
    
    # 记录响应信息
    logger.info(
        f"请求完成: {request.method} {request.url.path} "
        f"状态码={response.status_code} 耗时={process_time:.3f}s"
    )
    
    # 添加响应头
    response.headers["X-Process-Time"] = str(process_time)
    
    return response


# 注册路由
app.include_router(memory_router, prefix="/agentmemory/v1")


# 根路径
@app.get("/", summary="根路径", description="返回应用基本信息")
async def root():
    """根路径"""
    return {
        "name": settings.app.name,
        "version": settings.app.version,
        "description": "智能体记忆模块 - 提供智能体记忆存储、检索和管理功能",
        "timestamp": datetime.now(),
        "docs_url": "/docs",
        "health_url": "/agentmemory/v1/health"
    }


# 应用信息
@app.get("/info", summary="应用信息", description="获取应用详细信息")
async def get_app_info():
    """获取应用信息"""
    return {
        "app": {
            "name": settings.app.name,
            "version": settings.app.version,
            "debug": settings.app.debug
        },
        "services": {
            "elasticsearch": {
                "hosts": settings.elasticsearch.hosts,
                "index_name": settings.elasticsearch.index_name
            },
            "embedding": {
                "model_name": settings.embedding.model_name,
                "device": settings.embedding.device
            },
            "nacos": {
                "server_addresses": settings.nacos.server_addresses,
                "service_name": settings.nacos.service_name
            }
        },
        "timestamp": datetime.now()
    }


# 健康检查
@app.get("/health", summary="健康检查", description="检查应用及各服务组件的健康状态")
async def health_check():
    """健康检查接口"""
    try:
        # 获取记忆服务的健康检查信息
        health_info = await memory_service.health_check()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "app": {
                "name": settings.app.name,
                "version": settings.app.version
            },
            **health_info
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(
            status_code=503, 
            detail={
                "status": "unhealthy",
                "timestamp": datetime.now().isoformat(),
                "app": {
                    "name": settings.app.name,
                    "version": settings.app.version
                },
                "error": str(e)
            }
        )


if __name__ == "__main__":
    # 运行应用
    uvicorn.run(
        "app.main:app",
        host=settings.app.host,
        port=settings.app.port,
        log_level=settings.app.log_level.lower(),
        reload=settings.app.debug
    ) 