package com.ai.application.base.file.api.vo;

import lombok.Data;

import java.util.List;

@Data
public class DocListVo {
    private List<DocFileVo> list;
    private Integer pageNum;
    private Integer pageSize;
    private Long total;
    private Integer uploaded = 0;
    private Integer learning = 0;
    private Integer complete = 0;
    private Integer failed = 0;

    @Data
    public static class DocFileVo{
        private String datasetId;
        private String fileSn;
        private String fileName;
        private Integer status;
        private Long createTime;
        private String errorReason;
        private Integer estimatedTime;
        private String downloadUrl;
    }
}
