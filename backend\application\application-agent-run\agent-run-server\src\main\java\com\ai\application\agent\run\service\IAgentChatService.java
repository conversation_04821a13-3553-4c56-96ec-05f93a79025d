package com.ai.application.agent.run.service;

import com.ai.application.agent.base.api.dto.AgentChatDTO;
import com.ai.application.agent.base.api.vo.AgentChatVO;
import com.ai.framework.core.vo.ResultVo;

/**
 * Agent Chat 服务接口
 */
public interface IAgentChatService {

    /**
     * 调用智能体进行对话
     *
     * @param chatDto 对话请求
     * @return 对话结果
     */
    ResultVo<AgentChatVO> requestLLMWithAgent(AgentChatDTO chatDto);

    /**
     * 检查智能体是否可用
     *
     * @param agentSn 智能体编号
     * @return 可用性结果
     */
    ResultVo<Boolean> checkAgentAvailable(String agentSn);
}
