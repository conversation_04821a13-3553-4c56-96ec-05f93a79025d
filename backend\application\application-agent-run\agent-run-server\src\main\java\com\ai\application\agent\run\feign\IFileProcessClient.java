package com.ai.application.agent.run.feign;

import com.ai.application.base.file.api.dto.FileInfoDto;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.vo.ResultVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文件处理客户端接口
 */
@FeignClient(
        value = ServiceConstant.BASE_FILE,
        contextId = "IFileProcessClient",url = "http://localhost:6042"
)
public interface IFileProcessClient {
    String API_PREFIX = "/v1/feign/file";

    /**
     * 根据文件编号查询流程文件
     *
     * @param fileSn 文件编号
     * @return 文件信息列表
     */
    @GetMapping(value = API_PREFIX + "/process/findByFileSn")
    ResultVo<List<ProcessFileInfo>> findProcessFileByFileSn(@RequestParam("fileSn") String fileSn);

    /**
     * 流程文件上传
     *
     * @param content 文件内容
     * @param fileSn 文件编号
     * @param fileType 文件类型
     * @param fileName 文件名
     * @param source 来源
     * @return 上传结果
     */
    @PostMapping(value = API_PREFIX + "/process/upload")
    ResultVo<String> processFileUpload(@RequestBody byte[] content,
                                      @RequestParam("fileSn") String fileSn,
                                      @RequestParam("fileType") String fileType,
                                      @RequestParam("fileName") String fileName,
                                      @RequestParam("source") String source);

    /**
     * 流程文件信息
     */
    class ProcessFileInfo {
        private String fileSn;
        private String fileName;
        private String fileType;
        private String dataMd5;

        // Getters and Setters
        public String getFileSn() { return fileSn; }
        public void setFileSn(String fileSn) { this.fileSn = fileSn; }
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        public String getFileType() { return fileType; }
        public void setFileType(String fileType) { this.fileType = fileType; }
        public String getDataMd5() { return dataMd5; }
        public void setDataMd5(String dataMd5) { this.dataMd5 = dataMd5; }
    }
}
