package com.ai.application.agent.base.api.dto;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;

/**
 * 工作流节点运行记录表
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@Schema(name = "工作流节点运行记录表DTO")
public class AgentRunNodeDTO {
    /**
     * 节点运行id
     */
    @Schema(description = "节点运行id")
    private Integer nodeRunId;

    /**
     * 节点ID
     */
    @Schema(description = "节点ID")
    private String nodeId;

    /**
     * 节点名称
     */
    @Schema(description = "节点名称")
    private String nodeName;

    /**
     * 节点类型:10-开始,20-LLM,30-工具,40-条件,50-循环,60-并行,70-子流程,80-结束
     */
    @Schema(description = "节点类型:10-开始,20-LLM,30-工具,40-条件,50-循环,60-并行,70-子流程,80-结束")
    private Integer nodeType;

    /**
     * 节点执行顺序
     */
    @Schema(description = "节点执行顺序")
    private Integer nodeOrder;

    /**
     * 节点状态:1-等待,2-执行中,3-完成,4-失败,5-跳过,6-中断
     */
    @Schema(description = "节点状态:1-等待,2-执行中,3-完成,4-失败,5-跳过,6-中断")
    private Integer nodeStatus;

    /**
     * 节点输入数据
     */
    @Schema(description = "节点输入数据")
    private String nodeInput;

    /**
     * 节点输出数据
     */
    @Schema(description = "节点输出数据")
    private String nodeOutput;

    /**
     * 节点变量状态
     */
    @Schema(description = "节点变量状态")
    private String nodeVariables;

    /**
     * 节点配置快照
     */
    @Schema(description = "节点配置快照")
    private String nodeConfig;

    /**
     * 节点错误信息
     */
    @Schema(description = "节点错误信息")
    private String nodeError;

    /**
     * 节点进度描述
     */
    @Schema(description = "节点进度描述")
    private String nodeProgress;

    /**
     * 节点执行时长(毫秒)
     */
    @Schema(description = "节点执行时长(毫秒)")
    private Integer nodeDuration;

    /**
     * 节点消耗tokens
     */
    @Schema(description = "节点消耗tokens")
    private Integer nodeTokens;

    /**
     * 重试次数
     */
    @Schema(description = "重试次数")
    private Integer nodeRetryCount;

    /**
     * 节点开始时间
     */
    @Schema(description = "节点开始时间")
    private Date nodeStartTime;

    /**
     * 节点结束时间
     */
    @Schema(description = "节点结束时间")
    private Date nodeEndTime;

    /**
     * 父节点ID
     */
    @Schema(description = "父节点ID")
    private String parentNodeId;

    /**
     * 下一个节点IDs
     */
    @Schema(description = "下一个节点IDs")
    private String nextNodeIds;

    /**
     * 条件判断结果:0-false,1-true
     */
    @Schema(description = "条件判断结果:0-false,1-true")
    private Integer conditionResult;

    /**
     * 循环当前次数
     */
    @Schema(description = "循环当前次数")
    private Integer loopCurrent;

    /**
     * 循环总次数
     */
    @Schema(description = "循环总次数")
    private Integer loopTotal;

    /**
     * 运行记录id
     */
    @Schema(description = "运行记录id")
    private Integer runId;

    /**
     * 工作流id
     */
    @Schema(description = "工作流id")
    private Integer flowId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}