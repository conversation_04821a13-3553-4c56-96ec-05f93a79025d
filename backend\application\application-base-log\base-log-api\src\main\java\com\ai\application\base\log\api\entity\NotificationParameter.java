package com.ai.application.base.log.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 通知参数配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("notification_parameter")
public class NotificationParameter implements Serializable {
        /**
    * 通知参数id
    */
    @Schema(description = "通知参数id")
    @TableId(type = IdType.AUTO)
private Integer ntprId;

    /**
    * 适用分类:10-异常,20-一般通知,30-成功
    */
    @Schema(description = "适用分类:10-异常,20-一般通知,30-成功")
    private Integer ntprType;

    /**
    * 参数代码
    */
    @Schema(description = "参数代码")
    private String ntprCode;

    /**
    * 参数名称
    */
    @Schema(description = "参数名称")
    private String ntprName;

    /**
    * 参数描述
    */
    @Schema(description = "参数描述")
    private String ntprDescription;

    /**
    * 参数状态:1-启用,0-禁用,-1-删除
    */
    @Schema(description = "参数状态:1-启用,0-禁用,-1-删除")
    private Integer ntprStatus;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}