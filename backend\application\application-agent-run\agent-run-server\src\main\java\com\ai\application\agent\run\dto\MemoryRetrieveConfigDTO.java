package com.ai.application.agent.run.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 记忆提取配置DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "MemoryRetrieveConfigDTO")
public class MemoryRetrieveConfigDTO {

    /**
     * 提取类型：ALL-全部，COUNT-指定数量
     */
    @Schema(description = "提取类型：ALL-全部，COUNT-指定数量")
    private String extractType;

    /**
     * 提取数量
     */
    @Schema(description = "提取数量")
    private String extractCount;

    /**
     * 提取内容类型：ALL-全部信息，CONTENT-仅内容
     */
    @Schema(description = "提取内容类型：ALL-全部信息，CONTENT-仅内容")
    private String extractContent;

    /**
     * 存储桶编号/记忆类别
     */
    @Schema(description = "存储桶编号/记忆类别")
    private String bucketSn;

    /**
     * 输出索引键
     */
    @Schema(description = "输出索引键")
    private String index;

    /**
     * 检索内容（用于向量检索）
     */
    @Schema(description = "检索内容（用于向量检索）")
    private String retrieveContent;

    /**
     * 相似度阈值
     */
    @Schema(description = "相似度阈值")
    private Double similarityThreshold;
}
