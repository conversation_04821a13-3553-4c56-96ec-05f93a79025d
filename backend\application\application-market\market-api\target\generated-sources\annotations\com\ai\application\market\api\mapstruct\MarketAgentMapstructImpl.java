package com.ai.application.market.api.mapstruct;

import com.ai.application.market.api.dto.MarketAgentAddDTO;
import com.ai.application.market.api.dto.MarketAgentDTO;
import com.ai.application.market.api.entity.MarketAgent;
import com.ai.application.market.api.vo.MarketAgentVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-13T10:32:23+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class MarketAgentMapstructImpl implements MarketAgentMapstruct {

    @Override
    public MarketAgent toEntity(MarketAgentAddDTO dto) {
        if ( dto == null ) {
            return null;
        }

        MarketAgent marketAgent = new MarketAgent();

        marketAgent.setAppWeight( dto.getAppWeight() );
        marketAgent.setAppClone( dto.getAppClone() );
        marketAgent.setAppSnapshot( dto.getAppSnapshot() );
        marketAgent.setAppStatus( dto.getAppStatus() );
        marketAgent.setAgentId( dto.getAgentId() );
        marketAgent.setVersionId( dto.getVersionId() );

        return marketAgent;
    }

    @Override
    public MarketAgent toEntity(MarketAgentDTO dto) {
        if ( dto == null ) {
            return null;
        }

        MarketAgent marketAgent = new MarketAgent();

        marketAgent.setAppId( dto.getAppId() );
        marketAgent.setAppWeight( dto.getAppWeight() );
        marketAgent.setAppClone( dto.getAppClone() );
        marketAgent.setAppSnapshot( dto.getAppSnapshot() );
        marketAgent.setAppStatus( dto.getAppStatus() );
        marketAgent.setMarketId( dto.getMarketId() );
        marketAgent.setAgentId( dto.getAgentId() );
        marketAgent.setVersionId( dto.getVersionId() );
        marketAgent.setCateId( dto.getCateId() );
        marketAgent.setCreateTime( dto.getCreateTime() );
        marketAgent.setUpdateTime( dto.getUpdateTime() );

        return marketAgent;
    }

    @Override
    public MarketAgent toEntity(MarketAgentVO vo) {
        if ( vo == null ) {
            return null;
        }

        MarketAgent marketAgent = new MarketAgent();

        marketAgent.setAppId( vo.getAppId() );
        marketAgent.setAppWeight( vo.getAppWeight() );
        marketAgent.setAppClone( vo.getAppClone() );
        marketAgent.setAppSnapshot( vo.getAppSnapshot() );
        marketAgent.setAppStatus( vo.getAppStatus() );
        marketAgent.setMarketId( vo.getMarketId() );
        marketAgent.setAgentId( vo.getAgentId() );
        marketAgent.setVersionId( vo.getVersionId() );
        marketAgent.setCateId( vo.getCateId() );
        marketAgent.setCreateTime( vo.getCreateTime() );
        marketAgent.setUpdateTime( vo.getUpdateTime() );

        return marketAgent;
    }

    @Override
    public List<MarketAgent> toEntityList(List<MarketAgentDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<MarketAgent> list = new ArrayList<MarketAgent>( dtolist.size() );
        for ( MarketAgentDTO marketAgentDTO : dtolist ) {
            list.add( toEntity( marketAgentDTO ) );
        }

        return list;
    }

    @Override
    public MarketAgentVO toVo(MarketAgent entity) {
        if ( entity == null ) {
            return null;
        }

        MarketAgentVO marketAgentVO = new MarketAgentVO();

        marketAgentVO.setAppId( entity.getAppId() );
        marketAgentVO.setAppWeight( entity.getAppWeight() );
        marketAgentVO.setAppClone( entity.getAppClone() );
        marketAgentVO.setAppSnapshot( entity.getAppSnapshot() );
        marketAgentVO.setAppStatus( entity.getAppStatus() );
        marketAgentVO.setMarketId( entity.getMarketId() );
        marketAgentVO.setAgentId( entity.getAgentId() );
        marketAgentVO.setVersionId( entity.getVersionId() );
        marketAgentVO.setCateId( entity.getCateId() );
        marketAgentVO.setCreateTime( entity.getCreateTime() );
        marketAgentVO.setUpdateTime( entity.getUpdateTime() );

        return marketAgentVO;
    }

    @Override
    public List<MarketAgentVO> toVoList(List<MarketAgent> entities) {
        if ( entities == null ) {
            return null;
        }

        List<MarketAgentVO> list = new ArrayList<MarketAgentVO>( entities.size() );
        for ( MarketAgent marketAgent : entities ) {
            list.add( toVo( marketAgent ) );
        }

        return list;
    }
}
