package com.ai.application.task.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class TaskConfigBO {
    @Schema(description = "数据集文件Sn")
    private String dataSetFileSn;

    @Schema(description = "数据集文件Url")
    private String dataSetFileUrl;

    @Schema(description = "会话配置(0:每个输入独立对话,1:所有输入同一会话)")
    private Integer sessionConfig;

    @Schema(description = "执行结果文件Sn")
    private String runResultFileSn;
}
