package com.ai.application.admin.mapper;

import com.ai.application.admin.api.entity.Tenant;
import com.ai.application.admin.api.vo.TenantVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 租户表 Mapper接口
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Mapper
public interface TenantMapper extends BaseMapper<Tenant> {

    @Select("select * from tenant where tenant_sn = #{tenantSn} and user_status >= 0")
    Tenant findByTenantSn(@Param("tenantSn") String tenantSn);

    @Select("select * from tenant where tenant_domain = #{tenantDomain} and user_status >= 0")
    Tenant findByTenantDomain(@Param("tenantDomain") String tenantDomain);
}
