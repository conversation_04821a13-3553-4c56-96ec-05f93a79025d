package com.ai.application.agent.run.service;

import com.ai.application.agent.run.dto.DocumentSearchRequestDTO;
import com.ai.application.agent.run.dto.DocumentSearchResultDTO;
import com.ai.application.agent.run.dto.MultiRetrieverRequestDTO;
import com.ai.framework.core.vo.ResultVo;

import java.util.List;

/**
 * 知识片段检检索服务接口
 */
public interface IKnowledgeSearchService {

    /**
     * 执行知识片段检检索
     *
     * @param request 检索请求
     * @param authorization 授权信息
     * @return 检索结果
     */
    ResultVo<DocumentSearchResultDTO> executeDocumentSearch(DocumentSearchRequestDTO request, String authorization);

    /**
     * 多检索器检索
     *
     * @param request 多检索器请求
     * @param authorization 授权信息
     * @return 检索结果
     */
    ResultVo<List<DocumentSearchResultDTO.DocumentFragmentDTO>> multiRetrieverSearch(MultiRetrieverRequestDTO request, String authorization);

    /**
     * 扩展检索
     *
     * @param expandParams 扩展参数
     * @param authorization 授权信息
     * @return 检索结果
     */
    ResultVo<List<DocumentSearchResultDTO.DocumentFragmentDTO>> expandSearch(DocumentSearchRequestDTO.ExpandSearchParamsDTO expandParams, String authorization);

    /**
     * 手动检索
     *
     * @param searchKnowledge 检索知识
     * @param authorization 授权信息
     * @return 检索结果
     */
    ResultVo<List<DocumentSearchResultDTO.DocumentFragmentDTO>> manualRetriever(Object searchKnowledge, String authorization);

    /**
     * 准备检索请求
     *
     * @param type 检索类型
     * @param knowledgeInventorySn 知识库编号
     * @param knowledgeSn 知识编号
     * @return 检索请求
     */
    DocumentSearchRequestDTO prepareSearchRequest(String type, String knowledgeInventorySn, Object knowledgeSn);

    /**
     * 准备多级知识库检索请求
     *
     * @param type 检索类型
     * @param knowledgeInventorySnList 知识库编号列表
     * @param knowledgeSn 知识编号
     * @return 检索请求
     */
    DocumentSearchRequestDTO prepareSearchRequestForKnowledgeTree(String type, List<String> knowledgeInventorySnList, Object knowledgeSn);

    /**
     * 获取知识树并添加到知识列表
     *
     * @param knowledgeSn 知识编号
     * @param knowledgeList 知识列表
     * @return 知识库编号列表
     */
    List<String> getKnowledgeTreeAndAddKnowList(Object knowledgeSn, List<String> knowledgeList);

    /**
     * 根据文件ID获取文件编号
     *
     * @param fileIds 文件ID列表
     * @return 文件编号列表
     */
    List<String> getFileSnByFileId(List<String> fileIds);

    /**
     * 检查知识库信息
     *
     * @param knowledgeInventorySn 知识库编号
     * @return 知识库信息
     */
    KnowledgeInventoryInfo checkKnowledgeInventory(String knowledgeInventorySn);

    /**
     * 知识库信息
     */
    class KnowledgeInventoryInfo {
        private String inventorySn;
        private String modelSn;
        private String name;
        private Integer status;

        // Getters and Setters
        public String getInventorySn() {
            return inventorySn;
        }

        public void setInventorySn(String inventorySn) {
            this.inventorySn = inventorySn;
        }

        public String getModelSn() {
            return modelSn;
        }

        public void setModelSn(String modelSn) {
            this.modelSn = modelSn;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }
    }
}
