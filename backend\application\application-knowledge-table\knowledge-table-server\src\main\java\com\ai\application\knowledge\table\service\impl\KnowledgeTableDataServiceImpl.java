package com.ai.application.knowledge.table.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.ai.application.app.api.feign.IAppUserClient;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.application.base.file.api.dto.DocFileDto;
import com.ai.application.base.file.api.feign.IFileFeignClient;
import com.ai.application.knowledge.table.dto.*;
import com.ai.application.knowledge.table.entity.KnowledgeTable;
import com.ai.application.knowledge.table.entity.KnowledgeTableData;
import com.ai.application.knowledge.table.entity.KnowledgeTableRow;
import com.ai.application.knowledge.table.entity.KnowledgeTableSchema;
import com.ai.application.knowledge.table.enums.RowFromEnum;
import com.ai.application.knowledge.table.enums.RowStatusEnum;
import com.ai.application.knowledge.table.enums.SchemaKeyEnum;
import com.ai.application.knowledge.table.enums.SchemaStatusEnum;
import com.ai.application.knowledge.table.errors.AgentDocError;
import com.ai.application.knowledge.table.feign.TableArithmeticFeignClient;
import com.ai.application.knowledge.table.mapper.KnowledgeTableDataMapper;
import com.ai.application.knowledge.table.service.IKnowledgeTableDataService;
import com.ai.application.knowledge.table.service.IKnowledgeTableRowService;
import com.ai.application.knowledge.table.service.IKnowledgeTableSchemaService;
import com.ai.application.knowledge.table.service.IKnowledgeTableService;
import com.ai.application.knowledge.table.vo.*;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.list.CollectionUtils;
import com.ai.framework.core.util.string.StringUtil;
import com.ai.framework.core.vo.ResultVo;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 智能表格数据表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
@Slf4j
public class KnowledgeTableDataServiceImpl extends ServiceImpl<KnowledgeTableDataMapper, KnowledgeTableData> implements IKnowledgeTableDataService {

    @Autowired
    private IKnowledgeTableService knowledgeTableService;

    @Autowired
    private IKnowledgeTableSchemaService knowledgeTableSchemaService;

    @Autowired
    private IKnowledgeTableRowService knowledgeTableRowService;

    @Autowired
    private IFileFeignClient fileFeignClient;

    @Autowired
    private TableArithmeticFeignClient tableArithmeticFeignClient;

    @Autowired
    private IAppUserClient userClient;

    @Value("${dataTable.data.maxRow}")
    private Integer maxRow;
    @Value("${dataTable.data.maxSize}")
    private Integer maxSize;
    @Value("${dataTable.data.maxContent}")
    private Integer maxContent;
    @Value("${dataTable.data.exportMaxRow}")
    private Integer exportMaxRow;

    @Override
    public ResultVo<String> create(TableDataCreateDto dto) {
        log.info("创建智能表格数据开始, 入参: {}", JsonUtils.toJsonString(dto));
        // 初始化数据
        InitializationResult initResult = initializeData(dto);
        Map<String, KnowledgeTableSchema> schemaMap = initResult.getSchemaMap();
        Map<Integer, String> keyMap = initResult.getKeyMap();
        KnowledgeTable knowledgeTable = initResult.getKnowledgeTable();
        Date now = new Date();

        int rowId = 0;
        String rowCode = "";
        KnowledgeTableRow row = new KnowledgeTableRow();
        // 如果存在关键字段，则进行唯一性校验 并尝试获取已存在的行数据
        if (!keyMap.isEmpty()) {
            TreeMap<Integer, String> treeMap = new TreeMap<>(keyMap);
            rowCode = StringUtil.getMd5Hash(treeMap.toString());
            // 查询是否已存在匹配的行数据
            row = knowledgeTableRowService.getOne(Wrappers.lambdaQuery(KnowledgeTableRow.class).eq(KnowledgeTableRow::getRowCode, rowCode).eq(KnowledgeTableRow::getTableId, knowledgeTable.getTableId()).eq(KnowledgeTableRow::getRowStatus, RowStatusEnum.VALID.getCode()).last("limit 1"));

            if (ObjectUtil.isNotEmpty(row)) {
                // 更新行表数据
                row.setUpdateTime(now);
                row.setUpdateUserId(UserContext.getUserId());
                knowledgeTableRowService.updateById(row);
            } else {
                // 插入新的行记录
                rowId = UUID.randomUUID().hashCode() & Integer.MAX_VALUE;
                knowledgeTableRowService.save(KnowledgeTableRow.builder().rowCode(rowCode).tableId(knowledgeTable.getTableId()).rowId(rowId).rowFrom(RowFromEnum.INPUT.getCode()).rowStatus(RowStatusEnum.VALID.getCode()).createUserId(UserContext.getUserId()).updateUserId(UserContext.getUserId()).build());
            }
        }
        Integer finalRowId = ObjectUtil.isNotEmpty(rowId) ? rowId : row.getRowId();
        // 处理单元格数据（存在行对应字段则跳过插入）
        List<KnowledgeTableData> dataList = dto.getDatas().stream().map(data -> {
                    KnowledgeTableSchema schema = schemaMap.get(data.getFieldSn());
                    ServiceException.throwIf(schema == null, AgentDocError.INTELLIGENT_TABLE_DATA_FIELD_IS_NOT_EXISTS, data.getFieldSn());

                    // 查询是否已存在该行和字段的数据
                    LambdaQueryWrapper<KnowledgeTableData> queryWrapper = Wrappers.lambdaQuery();
                    queryWrapper.eq(KnowledgeTableData::getRowId, finalRowId).eq(KnowledgeTableData::getFieldId, schema.getFieldId());

                    boolean exists = this.count(queryWrapper) > 0;

                    // 如果存在对应的行字段数据，则跳过插入
                    if (exists) {
                        return null;
                    }

                    // 创建新的单元格数据
                    KnowledgeTableData tableData = new KnowledgeTableData();
                    tableData.setTableId(knowledgeTable.getTableId());
                    tableData.setRowId(finalRowId);
                    tableData.setFieldId(schema.getFieldId());
                    tableData.setFieldValue(data.getData());
                    tableData.setUpdateTime(now);
                    tableData.setCreateTime(now);

                    return tableData;
                }).filter(Objects::nonNull) // 过滤掉null值
                .collect(Collectors.toList());

        this.saveBatch(dataList);
        // 调用算法添加行数据
        arithmeticAdd(dataList, knowledgeTable, finalRowId);
        // 更新智能表格更新时间
        updateKnowledgeTable(knowledgeTable, now);

        log.info("创建智能表格数据结束");
        return ResultVo.success("操作成功");
    }

    @Override
    public ResultVo<String> update(TableDataCreateDto dto) {
        log.info("更新智能表格数据开始, 入参: {}", JsonUtils.toJsonString(dto));
        // 初始化数据
        InitializationResult initResult = initializeData(dto);
        Map<String, KnowledgeTableSchema> schemaMap = initResult.getSchemaMap();
        Map<Integer, String> keyMap = initResult.getKeyMap();
        KnowledgeTable knowledgeTable = initResult.getKnowledgeTable();
        Date now = new Date();

        // 如果存在关键字段，则进行唯一性校验 并尝试获取已存在的行数据
        if (!keyMap.isEmpty()) {
            // 查询是否已存在匹配的行数据
            KnowledgeTableRow row = knowledgeTableRowService.getOne(Wrappers.lambdaQuery(KnowledgeTableRow.class).eq(KnowledgeTableRow::getTableId, knowledgeTable.getTableId()).eq(KnowledgeTableRow::getRowStatus, RowStatusEnum.VALID.getCode()).last("limit 1"));

            ServiceException.throwIf(row == null, AgentDocError.INTELLIGENT_TABLE_DATA_ROW_IS_NOT_EXISTS);

            // 更新行表数据
            row.setUpdateTime(now);
            row.setUpdateUserId(UserContext.getUserId());
            knowledgeTableRowService.updateById(row);

            // 批量更新单元格数据
            dto.getDatas().stream().map(data -> {
                KnowledgeTableSchema schema = schemaMap.get(data.getFieldSn());
                ServiceException.throwIf(schema == null, AgentDocError.INTELLIGENT_TABLE_DATA_FIELD_IS_NOT_EXISTS, data.getFieldSn());

                // 查询原来的行字段数据
                KnowledgeTableData tableData = this.getOne(Wrappers.lambdaQuery(KnowledgeTableData.class).eq(KnowledgeTableData::getRowId, row.getRowId()).eq(KnowledgeTableData::getFieldId, schema.getFieldId()).last(" limit 1 "));
                LambdaQueryWrapper<KnowledgeTableData> updateWrapper = Wrappers.lambdaQuery();
                updateWrapper.eq(KnowledgeTableData::getRowId, row.getRowId()).eq(KnowledgeTableData::getFieldId, schema.getFieldId());

                tableData.setUpdateTime(now);
                tableData.setFieldValue(data.getData());

                // 使用MyBatis Plus的update方法进行更新操作
                this.update(tableData, updateWrapper);

                //调用算法编辑行数据
                arithmeticUpdate(Collections.singletonList(tableData), knowledgeTable, row.getRowId());
                return tableData;
            }).toList();

            // 更新智能表格更新时间
            updateKnowledgeTable(knowledgeTable, now);

            log.info("更新智能表格数据结束");
            return ResultVo.success("操作成功");
        } else {
            log.info("更新智能表格数据结束");
            return ResultVo.fail(AgentDocError.INTELLIGENT_TABLE_DATA_ROW_IS_NOT_EXISTS);
        }
    }

    @Override
    public ResultVo<PageInfo<TableDataListVo>> list(TableDataListDto dto) {
        log.info("查询智能表格数据列表开始, 入参: {}", JsonUtils.toJsonString(dto));
        // 参数校验
        ServiceException.throwIf(StringUtil.isEmpty(dto.getTableSn()), AgentDocError.VER_SN_NOT_EXIST);

        // 检查智能表格是否存在
        KnowledgeTable knowledgeTable = knowledgeTableService.checkTable(dto.getTableSn());

        // 获取智能表格字段定义
        List<KnowledgeTableSchema> schemaList = knowledgeTableSchemaService.list(Wrappers.lambdaQuery(KnowledgeTableSchema.class).eq(KnowledgeTableSchema::getTableId, knowledgeTable.getTableId()));
        if (ObjectUtil.isEmpty(schemaList)) {
            log.info("查询智能表格数据列表结束, 出参: {}");
            return ResultVo.data(new PageInfo<>(new ArrayList<>()));
        }

        // 构建字段定义映射
        Map<String, KnowledgeTableSchema> schemaMap = CollectionUtils.convertMap(schemaList, KnowledgeTableSchema::getFieldSn);

        // 获取行记录（算法返回全量数据）
        List<KnowledgeTableRow> rowList = arithmeticQuery(dto, knowledgeTable);
        if (CollectionUtils.isEmpty(rowList)) {
            log.info("查询智能表格数据列表结束");
            return ResultVo.data(new PageInfo<>(new ArrayList<>()));
        }

        // 提取所有行ID
        List<Integer> rowIds = rowList.stream().map(KnowledgeTableRow::getRowId).distinct().collect(Collectors.toList());

        // 查询所有相关数据
        List<KnowledgeTableData> dataList = this.list(Wrappers.lambdaQuery(KnowledgeTableData.class).in(KnowledgeTableData::getRowId, rowIds));

        // 转换为VO对象
        List<TableDataListVo> voList = convertToVoList(dataList, rowList, schemaMap);

        // 分页处理
        int total = voList.size();
        int start = (dto.getPageNo() - 1) * dto.getPageSize();
        int end = Math.min(start + dto.getPageSize(), total);
        List<TableDataListVo> pagedList = voList.subList(start, end);

        PageInfo<TableDataListVo> pageInfo = new PageInfo<>();
        pageInfo.setList(pagedList);
        pageInfo.setTotal(total);
        pageInfo.setPageNum(dto.getPageNo());
        pageInfo.setPageSize(dto.getPageSize());

        log.info("查询智能表格数据列表结束, 出参");
        return ResultVo.data(pageInfo);
    }

    @Override
    public ResultVo<String> delete(String tableSn, Integer rowId) {
        log.info("删除智能表格数据开始, 入参: {}, {}", tableSn, rowId);
        // 检查智能表格是否存在
        KnowledgeTable knowledgeTable = knowledgeTableService.checkTable(tableSn);

        // 查询行记录
        KnowledgeTableRow row = knowledgeTableRowService.getById(rowId);

        if (ObjectUtil.isNotEmpty(row)) {
            ServiceException.throwIf(row == null || !row.getRowStatus().equals(RowStatusEnum.VALID.getCode()), AgentDocError.INTELLIGENT_TABLE_DATA_ROW_IS_NOT_EXISTS);

            // 更新行状态为已删除
            row.setRowStatus(RowStatusEnum.DELETED.getCode());

            // 更新人信息处理
            row.setUpdateTime(new Date());
            row.setUpdateUserId(UserContext.getUserId());

            // 更新智能表格更新时间
            updateKnowledgeTable(knowledgeTable, new Date());
        }

        //调用算法删除行数据
        arithmeticDelete(knowledgeTable, rowId);

        log.info("删除智能表格数据结束");
        return ResultVo.success("删除成功");
    }

    @Override
    public ResponseEntity<byte[]> downloadTemplate(String tableSn) {
        // 检查智能表格是否存在
        KnowledgeTable knowledgeTable = knowledgeTableService.checkTable(tableSn);

        LambdaQueryWrapper<KnowledgeTableSchema> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(KnowledgeTableSchema::getTableId, knowledgeTable.getTableId()).eq(KnowledgeTableSchema::getFieldStatus, SchemaStatusEnum.VALID.getCode()).eq(KnowledgeTableSchema::getFieldType, "TEXT").orderByAsc(KnowledgeTableSchema::getFieldSort);
        List<KnowledgeTableSchema> repeatFields = knowledgeTableSchemaService.getBaseMapper().selectList(queryWrapper);

        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        // 创建工作表
        Sheet sheet = workbook.createSheet("表格内容");

        // 设置字段名
        List<String> fieldNames = repeatFields.stream().map(KnowledgeTableSchema::getFieldName).toList();

        // 设置表头
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < fieldNames.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(fieldNames.get(i));
        }

        // 设置样式
        settingStyle(workbook, headerRow, fieldNames, sheet);

        // 将 Excel 写入字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            workbook.write(outputStream);
        } catch (IOException e) {
            log.error("智能表格内容导入模版下载失败", e);
            throw new ServiceException(AgentDocError.INTELLIGENT_TABLE_DATA_TEMPLATE_DOWNLOAD_ERROR);
        }

        byte[] excelTemplate = outputStream.toByteArray();

        // 设置响应头信息
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", URLEncoder.encode("智能表格内容导入模版.xlsx", StandardCharsets.UTF_8));

        return ResponseEntity.ok().headers(headers).body(excelTemplate);
    }

    @Override
    public ResponseEntity<byte[]> export(TableDataListDto dto) {
        // 检查智能表格是否存在
        KnowledgeTable knowledgeTable = knowledgeTableService.checkTable(dto.getTableSn());

        dto.setPageNo(1);
        dto.setPageSize(exportMaxRow);
        List<KnowledgeTableSchema> definitions = knowledgeTableSchemaService.getBaseMapper().selectList(new LambdaQueryWrapper<KnowledgeTableSchema>().eq(KnowledgeTableSchema::getTableId, knowledgeTable.getTableId()).eq(KnowledgeTableSchema::getFieldStatus, SchemaStatusEnum.VALID.getCode()).orderByAsc(KnowledgeTableSchema::getFieldSort));

        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        // 创建工作表
        Sheet sheet = workbook.createSheet("表格内容");

        // 设置字段名
        List<String> fieldNames = definitions.stream().map(KnowledgeTableSchema::getFieldName).toList();

        List<String> definitionSn = definitions.stream().map(KnowledgeTableSchema::getFieldSn).toList();

        Map<Integer, String> fieldNameMap = new HashMap<>();
        // 设置表头
        Row headerRow = settingHeader(sheet, fieldNames, fieldNameMap, definitionSn);
        // 设置表头样式
        this.settingStyle(workbook, headerRow, fieldNames, sheet);

        List<TableDataListVo> list = this.list(dto).getData().getList();
        this.buildExportData(list, sheet, fieldNames, fieldNameMap);
        // 将 Excel 写入字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            workbook.write(outputStream);
        } catch (IOException e) {
            log.error("导出表格异常", e);
            throw new ServiceException(AgentDocError.INTELLIGENT_TABLE_DATA_EXPORT_ERROR);
        }

        byte[] excelTemplate = outputStream.toByteArray();

        // 设置响应头信息
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", URLEncoder.encode("智能表格内容.xlsx", StandardCharsets.UTF_8));

        return ResponseEntity.ok().headers(headers).body(excelTemplate);
    }

    /**
     * 导入Excel数据到智能表格
     *
     * @param file      要导入的Excel文件
     * @param tableSn   表格的唯一标识符
     * @param overwrite 是否覆盖现有数据的标志 1-覆盖，2-跳过
     * @return 返回导入结果的Vo对象
     * @throws ServiceException 如果导入过程中发生错误
     */
    @Override
    public ResultVo<TableDataImportVo> importExcel(MultipartFile file, String tableSn, Integer overwrite) {
        // 检查智能表格是否存在
        KnowledgeTable knowledgeTable = knowledgeTableService.checkTable(tableSn);

        // 获取表格的有效字段定义
        List<KnowledgeTableSchema> definitions = knowledgeTableSchemaService.getBaseMapper().selectList(new LambdaQueryWrapper<KnowledgeTableSchema>().eq(KnowledgeTableSchema::getTableId, knowledgeTable.getTableId()).eq(KnowledgeTableSchema::getFieldStatus, SchemaStatusEnum.VALID.getCode()));

        // 检查是否存在非文本类型的键字段，如果存在则抛出异常
        boolean isContainNoTextKey = definitions.stream().anyMatch(e -> e.getFieldKey() == SchemaKeyEnum.IS_KEY.getCode() && !StringUtils.equals(e.getFieldType(), "TEXT"));
        if (isContainNoTextKey) {
            throw new ServiceException(AgentDocError.INTELLIGENT_TABLE_DATA_IMPORT_ERROR_CONTAIN_NO_TEXT_KEY);
        }

        // 获取所有键字段的名称
        List<String> keyDefinitions = definitions.stream().filter(e -> e.getFieldKey() == SchemaKeyEnum.IS_KEY.getCode()).map(KnowledgeTableSchema::getFieldName).toList();

        // 根据是否有键字段，决定是新增还是更新行数据
        Map<String, Integer> tableRowMap;
        if (CollectionUtils.isNotEmpty(keyDefinitions)) {
            // 如果有键字段，获取所有有效行的ID
            List<KnowledgeTableRow> tableRows = knowledgeTableRowService.getBaseMapper().selectList(new LambdaQueryWrapper<KnowledgeTableRow>().select(KnowledgeTableRow::getRowCode, KnowledgeTableRow::getRowId).eq(KnowledgeTableRow::getTableId, knowledgeTable.getTableId()).eq(KnowledgeTableRow::getRowStatus, RowStatusEnum.VALID.getCode()));
            tableRowMap = tableRows.stream().collect(Collectors.toMap(KnowledgeTableRow::getRowCode, KnowledgeTableRow::getRowId, (k1, k2) -> k1));
        } else {
            // 如果没有键字段，初始化为空列表，准备全部新增
            tableRowMap = new HashMap<>();
        }

        // 初始化计数器
        AtomicInteger skip = new AtomicInteger(0);
        AtomicInteger update = new AtomicInteger(0);
        AtomicInteger successful = new AtomicInteger(0);
        AtomicInteger fail = new AtomicInteger(0);
        AtomicInteger total = new AtomicInteger(0);

        try {
            // 解析excel 组装表格内容数据
            List<TableDataExcelAssembleDto> list = this.getIntelligentTableData(file, knowledgeTable.getTableId(), overwrite, definitions, fail, tableRowMap, skip, total, keyDefinitions);
            // 保存行数据
            this.saveKnowledgeTableRow(knowledgeTable.getTableId(), list);
            // 保存内容数据
            this.saveKnowledgeTableData(list, update, successful, knowledgeTable);
            // 更新智能表格更新时间
            updateKnowledgeTable(knowledgeTable, new Date());
        } catch (Exception e) {
            log.error("批量导入数据失败", e);
            if (e instanceof ServiceException serviceException) {
                throw new ServiceException(serviceException.getCode(), e.getMessage());
            } else {
                throw new ServiceException(AgentDocError.INTELLIGENT_TABLE_DATA_IMPORT_ERROR1);
            }
        }

        // 返回导入结果
        return ResultVo.data(TableDataImportVo.builder().totalCount(total.get()).skip(skip.get()).successful(successful.get()).update(update.get()).fail(fail.get()).build());
    }


    private void saveKnowledgeTableData(List<TableDataExcelAssembleDto> list, AtomicInteger update, AtomicInteger successful, KnowledgeTable knowledgeTable) {
        if (list == null || list.isEmpty()) {
            return;
        }

        List<KnowledgeTableData> insertList = new ArrayList<>();
        List<KnowledgeTableData> updateList = new ArrayList<>();

        list.forEach(dto -> {
            if ("INSERT".equalsIgnoreCase(dto.getOperation())) {
                // 插入操作
                dto.getDatas().forEach(data -> {
                    KnowledgeTableData tableData = new KnowledgeTableData();
                    tableData.setCellId(UUID.randomUUID().hashCode() & Integer.MAX_VALUE);
                    tableData.setTableId(dto.getTableId());
                    tableData.setRowId(dto.getRowId());
                    tableData.setFieldId(data.getFieldId());
                    tableData.setFieldValue(data.getFieldValue());
                    tableData.setCreateTime(new Date());
                    tableData.setUpdateTime(new Date());
                    insertList.add(tableData);
                });
            } else if ("UPDATE".equalsIgnoreCase(dto.getOperation())) {
                // 更新操作
                dto.getDatas().forEach(data -> {
                    // 查询原先数据 cellId
                    KnowledgeTableData oldData = this.getBaseMapper().selectOne(new LambdaQueryWrapper<KnowledgeTableData>().eq(KnowledgeTableData::getTableId, dto.getTableId()).eq(KnowledgeTableData::getRowId, dto.getRowId()).eq(KnowledgeTableData::getFieldId, data.getFieldId()));

                    // 创建更新对象
                    KnowledgeTableData tableData = new KnowledgeTableData();
                    tableData.setCellId(oldData.getCellId());
                    tableData.setTableId(oldData.getTableId());
                    tableData.setRowId(oldData.getRowId());
                    tableData.setFieldId(oldData.getFieldId());
                    tableData.setCreateTime(oldData.getCreateTime());
                    tableData.setUpdateTime(new Date());
                    tableData.setFieldValue(data.getFieldValue());

                    updateList.add(tableData);
                });
            }
        });

        // 统计新增行数据数量并赋值给successful
        long insertRowCount = list.stream().filter(dto -> "INSERT".equalsIgnoreCase(dto.getOperation())).count();
        if (insertRowCount > 0) {
            successful.addAndGet((int) insertRowCount);
        }

        // 统计更新行数据数量并赋值给update
        long updateRowCount = list.stream().filter(dto -> "UPDATE".equalsIgnoreCase(dto.getOperation())).count();
        if (updateRowCount > 0) {
            update.addAndGet((int) updateRowCount);
        }
        // 批量插入
        if (!insertList.isEmpty()) {
            // 调用算法批量添加行数据
            arithmeticBatchAdd(insertList, knowledgeTable);
            this.saveBatch(insertList);
        }

        // 统计更新数量
        if (!updateList.isEmpty()) {
            // 调用算法批量添加行数据
            arithmeticBatchUpdate(updateList, knowledgeTable);
            this.updateBatchById(updateList);
        }
    }

    @NotNull
    private List<TableDataExcelAssembleDto> getIntelligentTableData(MultipartFile file, Integer tableId, Integer overwrite, List<KnowledgeTableSchema> definitions, AtomicInteger fail, Map<String, Integer> tableRowMap, AtomicInteger skip, AtomicInteger total, List<String> keyDefinitions) throws IOException {
        //文件名
        String originalFilename = file.getOriginalFilename();
        //文件后缀
        String suffix = StringUtils.substringAfterLast(originalFilename, ".");
        ServiceException.throwIf(!StringUtils.equals(suffix, "xlsx"), AgentDocError.INTELLIGENT_TABLE_FIELD_IMPORT_ERROR1);
        Workbook workbook = new XSSFWorkbook(file.getInputStream());
        Sheet sheetAt = workbook.getSheetAt(0);
        long size = file.getSize();
        long maxSizeInBytes = maxSize * 1024 * 1024;
        if (size > maxSizeInBytes) {
            throw new ServiceException(AgentDocError.INTELLIGENT_TABLE_DATA_IMPORT_ERROR_SIZE, maxSize + "M");
        }
        //Excel中首行必须为列名
        Row firstRow = sheetAt.getRow(0);
        if (firstRow == null) {
            throw new ServiceException(AgentDocError.INTELLIGENT_TABLE_DATA_IMPORT_TEMPLATE_ERROR);
        }
        // excel表头
        Map<Integer, String> header = new HashMap<>();
        firstRow.forEach(cell -> {
            header.put(cell.getColumnIndex(), cell.getStringCellValue());
        });
        Map<String, KnowledgeTableSchema> collect = definitions.stream().collect(Collectors.toMap(KnowledgeTableSchema::getFieldName, value -> value));
        // excel排序不需要的列记录导入列
        Map<Integer, String> importHeader = new HashMap<>();
        List<Integer> keyIndex = new ArrayList<>();
        List<String> keyFields = new ArrayList<>();
        header.forEach((k, v) -> {
            /**
             * 仅支持导入文本类型的字段
             * 列名与表格字段名称相同的才会被导入
             * 列名重复时仅导入首个列名的值
             */
            if (collect.containsKey(v) && !importHeader.containsValue(v) && StringUtils.equals(collect.get(v).getFieldType(), "TEXT")) {
                importHeader.put(k, v);
                if (collect.get(v).getFieldKey() == 1) {
                    keyIndex.add(k);
                    keyFields.add(v);
                }
            }
        });
        //若导入列是空说明没有可导入的字段，不需要继续继续excel
        if (importHeader.isEmpty()) {
            throw new ServiceException(AgentDocError.INTELLIGENT_TABLE_DATA_IMPORT_TEMPLATE_ERROR);
        }
        Set<String> set1 = new HashSet<>(keyFields);
        Set<String> set2 = new HashSet<>(keyDefinitions);
        set1.retainAll(set2);
        boolean allMatchKey = set1.size() == keyFields.size() && set1.size() == set2.size();
        // 验证key字段是否完整
        if (!allMatchKey) {
            throw new ServiceException(AgentDocError.INTELLIGENT_TABLE_DATA_IMPORT_ERROR_CONTAIN_NO_ALL_KEY);
        }
        List<TableDataExcelAssembleDto> list = new ArrayList<>();
        Set<String> md5s = new HashSet<>();
        sheetAt.forEach(row -> {
            // 跳过空行
            if (row.getPhysicalNumberOfCells() == 0) {
                return;
            }
            AtomicBoolean isKeySkip = new AtomicBoolean(false);
            Map<Integer, String> keyCellValue = new TreeMap<>();
            // 跳过表头
            if (row.getRowNum() == 0) {
                return;
            }
            if (row.getRowNum() > maxRow) {
                return;
            }
            // 跳过key字段为空的行
            if (CollectionUtils.isNotEmpty(keyIndex)) {
                boolean b = keyIndex.stream().allMatch(e -> row.getCell(e) == null);
                if (b) {
                    fail.set(fail.get() + 1);
                    return;
                }
            }

            TableDataExcelAssembleDto lineRow = new TableDataExcelAssembleDto();
            List<TableDataExcelAssembleDto.TableData> cells = new ArrayList<>();
            row.forEach(cell -> {
                if (!importHeader.containsKey(cell.getColumnIndex())) {
                    return;
                }
                // key字段为空 整行跳过标记
                if (isKeySkip.get()) {
                    return;
                }
                KnowledgeTableSchema definition = collect.get(importHeader.get(cell.getColumnIndex()));
                if (definition == null) {
                    return;
                }
                String cellValue = "";
                switch (cell.getCellType()) {
                    case STRING:  //字符串
                        cellValue = cell.getStringCellValue();
                        break;
                    case NUMERIC: //数字
                        double numericCellValue = cell.getNumericCellValue();
                        cellValue = this.numericString(numericCellValue);
                        break;
                    case BOOLEAN: //布尔
                        boolean booleanCellValue = cell.getBooleanCellValue();
                        cellValue = Boolean.toString(booleanCellValue);
                        break;
                    case BLANK: //空值
                        break;
                    case FORMULA: //公式
                        try {
                            cellValue = String.valueOf(this.numericString(cell.getNumericCellValue()));
                        } catch (IllegalStateException e) {
                            try {
                                cellValue = String.valueOf(cell.getRichStringCellValue());
                            } catch (IllegalStateException e1) {
                                cellValue = "";
                            }
                        }
                        break;
                    case ERROR: //故障
                        break;
                    default:
                        break;
                }
                if (StringUtils.isBlank(cellValue)) {
                    return;
                }
                Integer key = definition.getFieldKey();
                // 是key字段的数据不能为空，整行跳过标记 失败+1
                if (key == 1 && StringUtils.isBlank(cellValue)) {
                    isKeySkip.set(true);
                    return;
                }
                if (cellValue.length() > maxContent) {
                    return;
                }
                if (key == 1) {
                    keyCellValue.put(definition.getFieldSort(), cellValue);
                }
                TableDataExcelAssembleDto.TableData data = new TableDataExcelAssembleDto.TableData();
                data.setFieldValue(cellValue);
                data.setFieldId(definition.getFieldId());
                data.setTableId(tableId);
                data.setFieldSn(definition.getFieldSn());
                data.setFieldName(definition.getFieldName());
                data.setFieldType(definition.getFieldType());
                data.setFieldDesc(definition.getFieldDesc());
                data.setFieldKey(definition.getFieldKey());
                data.setFieldSort(definition.getFieldSort());
                data.setFieldStatus(definition.getFieldStatus());
                cells.add(data);
            });
            if (isKeySkip.get()) {
                fail.set(fail.get() + 1);
                return;
            }
            if (cells.isEmpty()) {
                return;
            }
            // rowCode校验
            String rowCode;
            if (!keyCellValue.isEmpty()) {
                rowCode = StringUtil.getMd5Hash(keyCellValue.toString());
            } else {
                rowCode = "";
            }
            if (!tableRowMap.isEmpty() && tableRowMap.containsKey(rowCode)) {
                if (overwrite == 2) {
                    skip.set(skip.get() + 1);
                    return;
                } else {
                    lineRow.setRowId(tableRowMap.get(rowCode));
                    lineRow.setOperation("UPDATE");
                }
            } else {
                lineRow.setRowId(UUID.randomUUID().hashCode() & Integer.MAX_VALUE);
                lineRow.setOperation("INSERT");
            }
            // 存在相同md5 移除旧的数据
            if (StringUtils.isNotBlank(rowCode) && !md5s.add(rowCode)) {
                skip.set(skip.get() + 1);
                list.removeIf(e -> e.getRowCode().equals(rowCode));
            }
            lineRow.setRowCode(rowCode);
            lineRow.setDatas(cells);
            lineRow.setTableId(tableId);
            list.add(lineRow);
            total.set(total.get() + 1);
        });
        return list;
    }


    private void saveKnowledgeTableRow(Integer tableId, List<TableDataExcelAssembleDto> list) {
        //获取新增的行
        List<KnowledgeTableRow> insertList = list.stream().filter(row -> StringUtils.equals(row.getOperation(), "INSERT")).map(row -> {
            KnowledgeTableRow tableDataRow = new KnowledgeTableRow();
            tableDataRow.setTableId(tableId);
            tableDataRow.setRowId(row.getRowId());
            tableDataRow.setCreateUserId(UserContext.getUserId());
            tableDataRow.setUpdateUserId(UserContext.getUserId());
            tableDataRow.setRowCode(row.getRowCode());
            tableDataRow.setCreateTime(new Date());
            tableDataRow.setUpdateTime(new Date());
            return tableDataRow;
        }).toList();
        // 获取更新的行
        List<KnowledgeTableRow> updateList = list.stream().filter(row -> StringUtils.equals(row.getOperation(), "UPDATE")).map(row -> {
            KnowledgeTableRow tableDataRow = new KnowledgeTableRow();
            tableDataRow.setRowId(row.getRowId());
            tableDataRow.setUpdateUserId(UserContext.getUserId());
            tableDataRow.setTableId(tableId);
            tableDataRow.setRowCode(row.getRowCode());
            tableDataRow.setUpdateTime(new Date());
            return tableDataRow;
        }).toList();
        // 批量插入
        if (CollectionUtils.isNotEmpty(insertList)) {
            knowledgeTableRowService.saveBatch(insertList);
        }
        // 批量更新
        if (CollectionUtils.isNotEmpty(updateList)) {
            knowledgeTableRowService.saveOrUpdateBatch(updateList);
        }
    }


    private void settingStyle(Workbook workbook, Row headerRow, List<String> fieldNames, Sheet sheet) {
        // 设置样式
        CellStyle headerCellStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerCellStyle.setFont(headerFont);
        headerRow.setRowStyle(headerCellStyle);

        // 设置列宽
        for (int i = 0; i < fieldNames.size(); i++) {
            sheet.setColumnWidth(i, 4000);
        }
    }

    /**
     * 将实体数据转换为VO列表
     *
     * @param dataList  数据库中的数据列表
     * @param rowList   行记录列表
     * @param schemaMap 字段定义映射
     * @return VO列表
     */
    private List<TableDataListVo> convertToVoList(List<KnowledgeTableData> dataList, List<KnowledgeTableRow> rowList, Map<String, KnowledgeTableSchema> schemaMap) {
        // 构建行ID到行记录的映射
        Map<Integer, KnowledgeTableRow> rowMap = rowList.stream().collect(Collectors.toMap(KnowledgeTableRow::getRowId, row -> row));

        // 按行ID分组数据
        Map<Integer, List<KnowledgeTableData>> dataGroup = dataList.stream().collect(Collectors.groupingBy(KnowledgeTableData::getRowId));

        // map转成list
        List<KnowledgeTableSchema> schemaList = schemaMap.values().stream().toList();

        // 转换为VO对象
        return dataGroup.entrySet().stream().map(entry -> {
            Integer rowId = entry.getKey();
            List<KnowledgeTableData> rowData = entry.getValue();

            TableDataListVo vo = new TableDataListVo();
            vo.setRowId(rowId.toString());

            Map<String, TableDataListVo.Definition> fieldSnMap = new HashMap<>();
            rowData.forEach(data -> {
                KnowledgeTableSchema schema = schemaList.stream().filter(f -> f.getFieldId().equals(data.getFieldId())).findFirst().orElse(null);
                if (schema != null) {
                    TableDataListVo.Definition definition = new TableDataListVo.Definition();
                    if (schema.getFieldType().equals("FILE")) {
                        String[] fileParts = data.getFieldValue().split("::");
                        if (fileParts.length > 1) {
                            definition.setFileName(fileParts[1]);
                            definition.setFileSn(fileParts[0]);
                        } else {
                            definition.setFileName(data.getFieldValue());
                            definition.setFileSn(data.getFieldValue());
                        }
                    }
                    definition.setData(data.getFieldValue()).setFieldType(schema.getFieldType());
                    fieldSnMap.put(schema.getFieldSn(), definition);
                }
            });

            vo.setFieldSnMap(fieldSnMap);

            KnowledgeTableRow row = rowMap.get(rowId);
            if (row != null) {
                vo.setCreateTime(row.getCreateTime());
                vo.setUpdateTime(row.getUpdateTime());
                //创建人 更新人
                AppUserVO createUser = userClient.getUserById(vo.getCreateUserId()).getData();
                vo.setCreator(createUser.getUserName());
                AppUserVO updateUser = userClient.getUserById(vo.getUpdateUserId()).getData();
                vo.setUpdater(updateUser.getUserName());
            }

            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 公共初始化操作：参数校验、字段定义构建、文件处理、key字段获取
     *
     * @param dto 表格数据传输对象
     * @return 包含 schemaMap, docFileMap, keyDefinitions 的封装对象
     */
    private InitializationResult initializeData(TableDataCreateDto dto) {
        // 参数校验
        ServiceException.throwIf(StringUtil.isEmpty(dto.getTableSn()), AgentDocError.VER_SN_NOT_EXIST);
        ServiceException.throwIf(CollectionUtils.isEmpty(dto.getDatas()), AgentDocError.INTELLIGENT_TABLE_DATA_IMPORT_ERROR1);

        // 检查智能表格是否存在
        KnowledgeTable knowledgeTable = knowledgeTableService.checkTable(dto.getTableSn());
        ServiceException.throwIf(knowledgeTable == null, AgentDocError.INTELLIGENT_TABLE_IS_EXISTS);

        // 内容长度检测
        dto.getDatas().forEach(e -> {
            ServiceException.throwIf(e.getData() != null && e.getData().length() > maxContent, AgentDocError.INTELLIGENT_TABLE_DATA_CONTENT_MAX_LENGTH, String.valueOf(maxContent));
        });

        // 获取智能表格字段定义
        List<KnowledgeTableSchema> schemaList = knowledgeTableSchemaService.list(Wrappers.lambdaQuery(KnowledgeTableSchema.class).eq(KnowledgeTableSchema::getTableId, knowledgeTable.getTableId()));

        ServiceException.throwIf(CollectionUtils.isEmpty(schemaList), AgentDocError.INTELLIGENT_TABLE_DEFINITION_IS_EXISTS, knowledgeTable.getTableSn());

        // 构建字段定义映射
        Map<String, KnowledgeTableSchema> schemaMap = CollectionUtils.convertMap(schemaList, KnowledgeTableSchema::getFieldSn);

        // 文件处理  字段 - 文件
        Map<String, DocFileDto> docFileMap = this.getFileMap(dto, schemaList);

        // 获取 key 字段定义
        List<String> keyDefinitions = schemaList.stream().filter(e -> SchemaKeyEnum.IS_KEY.getCode() == e.getFieldKey()).map(KnowledgeTableSchema::getFieldSn).toList();

        // 校验必填字段是否存在
        List<String> requiredFields = schemaList.stream().filter(schema -> "REQUIRED".equals(schema.getFieldDesc())).map(KnowledgeTableSchema::getFieldSn).toList();

        if (!requiredFields.isEmpty()) {
            boolean hasMissingRequired = requiredFields.stream().noneMatch(fieldSn -> dto.getDatas().stream().anyMatch(data -> data.getFieldSn().equals(fieldSn)));

            ServiceException.throwIf(hasMissingRequired, AgentDocError.INTELLIGENT_TABLE_FIELD_IMPORT_TEMPLATE_ERROR, "缺少必填字段");
        }

        // key 字段 排序 - 值
        Map<Integer, String> keyMap = this.getKeyMap(dto, keyDefinitions, schemaMap, docFileMap);

        return new InitializationResult(schemaMap, docFileMap, keyDefinitions, keyMap, knowledgeTable);
    }

    /**
     * 更新智能表格的通用操作
     *
     * @param knowledgeTable 智能表格对象
     * @param now            当前时间
     */
    private void updateKnowledgeTable(KnowledgeTable knowledgeTable, Date now) {
        // 更新智能表格更新时间
        knowledgeTableService.update(Wrappers.lambdaUpdate(KnowledgeTable.class).set(KnowledgeTable::getUpdateTime, now).set(KnowledgeTable::getUpdateUserId, UserContext.getUserId()).eq(KnowledgeTable::getTableSn, knowledgeTable.getTableSn()));
    }

    /**
     * 获取文件映射
     * 该方法用于从给定的数据传输对象和架构列表中提取文件相关信息，并将其映射到一个文件DTO的Map中
     *
     * @param dto        数据传输对象，包含表格数据创建信息
     * @param schemaList 架构列表，包含知识表格的架构信息
     * @return 返回一个不为空的Map，键为文件编号，值为文件DTO对象
     */
    @NotNull
    private Map<String, DocFileDto> getFileMap(TableDataCreateDto dto, List<KnowledgeTableSchema> schemaList) {
        // 提取文件类型字段sn
        List<String> fileFieldSn = schemaList.stream().filter(e -> StringUtil.equals("FILE", e.getFieldType())).map(KnowledgeTableSchema::getFieldSn).toList();

        // 从请求数据中提取包含文件类型字段sn的数据
        List<String> fileSnList = new ArrayList<>(dto.getDatas().stream().filter(e -> fileFieldSn.contains(e.getFieldSn())).map(e -> e.getData().split("::")[0]).toList());

        // 去重处理
        List<String> uniqueFileSnList = fileSnList.stream().distinct().toList();

        // 获取文件信息
        if (CollectionUtils.isNotEmpty(uniqueFileSnList)) {
            ResultVo<List<DocFileDto>> fileResultVo = fileFeignClient.getBatchFileByFileSn(uniqueFileSnList);
            if (CollectionUtils.isNotEmpty(fileResultVo.getData())) {
                return fileResultVo.getData().stream().collect(Collectors.toMap(DocFileDto::getFileSn, value -> value));
            }
        }

        return new HashMap<>();
    }

    /**
     * 生成键值对映射
     * 该方法根据提供的参数，过滤出与keyDefinitions匹配的数据，并根据数据类型进一步处理，最终生成一个键值对映射
     *
     * @param dto            智能表格数据保存DTO，包含需要处理的数据
     * @param keyDefinitions 键定义列表，用于过滤数据
     * @param schemaMap      定义映射，包含每个定义的详细信息
     * @param docFileMap     文档文件映射，用于获取文件数据MD5
     * @return 返回一个不可空的映射，键为定义的排序号，值为对应的数据或文件MD5
     */
    @NotNull
    private Map<Integer, String> getKeyMap(TableDataCreateDto dto, List<String> keyDefinitions, Map<String, KnowledgeTableSchema> schemaMap, Map<String, DocFileDto> docFileMap) {
        // 过滤出与key字段匹配的数据
        List<TableDataCreateDto.TableData> keyList = dto.getDatas().stream().filter(e -> keyDefinitions.contains(e.getFieldSn())).toList();

        // 根据定义的排序号和数据类型，收集数据生成映射
        return keyList.stream().collect(Collectors.toMap(e -> schemaMap.get(e.getFieldSn()).getFieldSort(), e -> {
            KnowledgeTableSchema schema = schemaMap.get(e.getFieldSn());

            // 如果数据类型为FILE，尝试获取文件的MD5
            if ("FILE".equals(schema.getFieldType())) {
                DocFileDto docFile = docFileMap.get(e.getData().split("::")[0]);
                if (docFile != null) {
                    return docFile.getFileHash();
                }
            }

            // 对于非文件类型，直接返回数据
            return e.getData();
        }));
    }

    /**
     * 初始化结果封装类
     */
    @Getter
    private static class InitializationResult {
        private final Map<String, KnowledgeTableSchema> schemaMap;
        private final Map<String, DocFileDto> docFileMap;
        private final List<String> keyDefinitions;
        private final Map<Integer, String> keyMap;
        private final KnowledgeTable knowledgeTable;

        public InitializationResult(Map<String, KnowledgeTableSchema> schemaMap, Map<String, DocFileDto> docFileMap, List<String> keyDefinitions, Map<Integer, String> keyMap, KnowledgeTable knowledgeTable) {
            this.schemaMap = schemaMap;
            this.docFileMap = docFileMap;
            this.keyDefinitions = keyDefinitions;
            this.keyMap = keyMap;
            this.knowledgeTable = knowledgeTable;
        }
    }

    private Row settingHeader(Sheet sheet, List<String> fieldNames, Map<Integer, String> fieldNameMap, List<String> definitionSn) {
        // 设置表头
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < fieldNames.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(fieldNames.get(i));
            fieldNameMap.put(i, definitionSn.get(i));
        }
        return headerRow;
    }

    private void buildExportData(List<TableDataListVo> list, Sheet sheet, List<String> fieldNames, Map<Integer, String> fieldNameMap) {
        if (list != null && !list.isEmpty()) {
            list.forEach(item -> {
                Map<String, TableDataListVo.Definition> definitionSnMap = item.getFieldSnMap();
                Row row = sheet.createRow(sheet.getLastRowNum() + 1);
                for (int i = 0; i < fieldNames.size(); i++) {
                    Cell cell = row.createCell(i);
                    TableDataListVo.Definition definition = definitionSnMap.get(fieldNameMap.get(i));
                    if (definition != null) {
                        String data = definition.getData();
                        if ("FILE".equals(definition.getFieldType())) {
                            if (StringUtils.isNotBlank(data)) {
                                String[] split = data.split("::");
                                cell.setCellValue(split.length > 1 ? split[1] : "");
                            }
                        } else if ("FILE_BATCH".equals(definition.getFieldType())) {
                            this.exportFileBatch(data, cell);
                        } else {
                            cell.setCellValue(data);
                        }
                    } else {
                        cell.setCellValue("");
                    }
                }
            });
        }
    }


    private void exportFileBatch(String data, Cell cell) {
        if (StringUtils.isNotBlank(data)) {
            try {
                List<String> fileList = JSONArray.parseArray(data, String.class);
                String value = fileList.stream().map(e -> {
                    String[] split = e.split("::");
                    return split.length > 1 ? split[1] : "";
                }).collect(Collectors.joining("|"));
                cell.setCellValue(value);
            } catch (Exception e) {
                cell.setCellValue("");
            }
        }
    }

    private String numericString(double numericCellValue) {
        String cellValue;
        boolean isInteger = this.isInteger(numericCellValue);
        if (isInteger) {
            DecimalFormat df = new DecimalFormat("0");
            cellValue = df.format(numericCellValue);
        } else {
            cellValue = Double.toString(numericCellValue);
        }
        return cellValue;
    }

    private boolean isInteger(Double num) {
        double eqs = 1e-10; //精度范围
        return num - Math.floor(num) < eqs;
    }

    private void arithmeticUpdate(List<KnowledgeTableData> dataList, KnowledgeTable knowledgeTable, Integer finalRowId) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }
        executeArithmeticOperation(dataList, knowledgeTable, finalRowId, "update");
        log.info("调用算法更新行数据结束");
    }

    private void arithmeticBatchAdd(List<KnowledgeTableData> list, KnowledgeTable knowledgeTable) {
        if (list == null || list.isEmpty()) {
            return;
        }
        executeArithmeticBatchOperation(list, knowledgeTable, "batchAdd");
        log.info("调用算法批量添加行数据结束");
    }

    @Override
    public void arithmeticBatchUpdate(List<KnowledgeTableData> list, KnowledgeTable knowledgeTable) {
        if (list == null || list.isEmpty()) {
            return;
        }
        executeArithmeticBatchOperation(list, knowledgeTable, "batchUpdate");
        log.info("调用算法批量更新行数据结束");
    }

    private void executeArithmeticBatchOperation(List<KnowledgeTableData> list, KnowledgeTable knowledgeTable, String operationType) {
        if (list == null || list.isEmpty()) {
            return;
        }

        // 构建请求参数
        TableDataArithmeticCreateBatchDto dto = new TableDataArithmeticCreateBatchDto();
        dto.setTableId(Long.valueOf(knowledgeTable.getTableId()));

        List<TableDataArithmeticCreateBatchDto.RowData> rowDataList = new ArrayList<>();

        // 按行ID分组数据，并过滤掉 rowId 为 null 的情况
        Map<Integer, List<KnowledgeTableData>> dataGroup = list.stream().filter(data -> data.getRowId() != null).collect(Collectors.groupingBy(KnowledgeTableData::getRowId));

        // 批量获取字段定义
        List<Integer> fieldIds = list.stream().map(KnowledgeTableData::getFieldId).distinct().collect(Collectors.toList());
        Map<Integer, KnowledgeTableSchema> schemaMap = knowledgeTableSchemaService.listByIds(fieldIds).stream().collect(Collectors.toMap(KnowledgeTableSchema::getFieldId, s -> s));

        // 构建行数据
        for (Map.Entry<Integer, List<KnowledgeTableData>> entry : dataGroup.entrySet()) {
            Integer rowId = entry.getKey();
            List<KnowledgeTableData> rowData = entry.getValue();

            TableDataArithmeticCreateBatchDto.RowData rowDataItem = new TableDataArithmeticCreateBatchDto.RowData();
            rowDataItem.setRowId(Long.valueOf(rowId));

            List<TableDataArithmeticCreateBatchDto.RowData.ColumnData> columnDataList = new ArrayList<>();

            for (KnowledgeTableData data : rowData) {
                KnowledgeTableSchema schema = schemaMap.get(data.getFieldId());
                if (schema == null) {
                    continue; // 跳过没有对应字段定义的数据
                }

                TableDataArithmeticCreateBatchDto.RowData.ColumnData columnData = new TableDataArithmeticCreateBatchDto.RowData.ColumnData();
                columnData.setColId(data.getCellId());
                columnData.setColType(schema.getFieldType().toLowerCase());
                columnData.setColValue(data.getFieldValue());
                columnDataList.add(columnData);
            }

            rowDataItem.setData(columnDataList);
            rowDataList.add(rowDataItem);
        }

        dto.setRows(rowDataList);

        // 调用算法服务
        ResultVo<TableDataArithmeticCreateBatchVo> result;
        if ("batchUpdate".equals(operationType)) {
            log.info("调用算法服务批量更新行数据，入参: {}", JsonUtils.toJsonString(dto));
            result = tableArithmeticFeignClient.batchUpdateRows(dto);
            log.info("调用算法服务批量更新行数据，出参: {}", JsonUtils.toJsonString(result));
        } else {
            log.info("调用算法服务批量新增行数据，入参: {}", JsonUtils.toJsonString(dto));
            result = tableArithmeticFeignClient.batchAddRows(dto);
            log.info("调用算法服务批量更新行数据，出参: {}", JsonUtils.toJsonString(result));
        }

        if (!ResultVo.isSuccess(result)) {
            String operationName = "batchUpdate".equals(operationType) ? "批量更新" : "批量添加";
            log.info("调用算法服务{}行数据失败，入参: {}, 错误信息: {}", operationName, JsonUtils.toJsonString(dto), result.getMessage());
        }
    }

    private void arithmeticAdd(List<KnowledgeTableData> dataList, KnowledgeTable knowledgeTable, Integer finalRowId) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        executeArithmeticOperation(dataList, knowledgeTable, finalRowId, "add");
    }

    private void executeArithmeticOperation(List<KnowledgeTableData> dataList, KnowledgeTable knowledgeTable, Integer finalRowId, String operationType) {
        TableDataArithmeticCreateDto dto = new TableDataArithmeticCreateDto();
        dto.setTableId(Long.valueOf(knowledgeTable.getTableId()));
        dto.setRowId(Long.valueOf(finalRowId));

        List<TableDataArithmeticCreateDto.ColumnData> columnDataList = new ArrayList<>(dataList.size());

        // 批量获取字段定义
        List<Integer> fieldIds = dataList.stream().map(KnowledgeTableData::getFieldId).distinct().collect(Collectors.toList());
        Map<Integer, KnowledgeTableSchema> schemaMap = knowledgeTableSchemaService.listByIds(fieldIds).stream().collect(Collectors.toMap(KnowledgeTableSchema::getFieldId, s -> s));

        // 构建列数据
        for (KnowledgeTableData data : dataList) {
            KnowledgeTableSchema schema = schemaMap.get(data.getFieldId());
            if (schema == null) {
                continue; // 跳过没有对应字段定义的数据
            }

            TableDataArithmeticCreateDto.ColumnData columnData = new TableDataArithmeticCreateDto.ColumnData();
            columnData.setColId(data.getCellId());
            columnData.setColType(schema.getFieldType().toLowerCase());
            columnData.setColValue(data.getFieldValue());
            columnDataList.add(columnData);
        }

        dto.setData(columnDataList);

        // 调用算法服务
        ResultVo<TableDataArithmeticCreateVo> result;
        if ("update".equals(operationType)) {
            log.info("调用算法服务更新行数据，入参: {}", JsonUtils.toJsonString(dto));
            result = tableArithmeticFeignClient.updateRow(dto);
            log.info("调用算法服务更新行数据，出参: {}", JsonUtils.toJsonString(result));
        } else {
            log.info("调用算法服务添加行数据，入参: {}", JsonUtils.toJsonString(dto));
            result = tableArithmeticFeignClient.addRow(dto);
            log.info("调用算法服务添加行数据，出参: {}", JsonUtils.toJsonString(result));
        }

        if (!ResultVo.isSuccess(result)) {
            String operationName = "update".equals(operationType) ? "更新" : "添加";
            log.info("调用算法服务{}行数据失败，入参: {}, 错误信息: {}", operationName, JsonUtils.toJsonString(dto), result.getMessage());
        }
    }

    private void arithmeticDelete(KnowledgeTable knowledgeTable, Integer rowId) {
        TableDataArithmeticDeleteDto dto = new TableDataArithmeticDeleteDto();
        dto.setTableId(Long.valueOf(knowledgeTable.getTableId()));
        dto.setRowId(Long.valueOf(rowId));
        log.info("调用算法服务删除行数据，入参: {}", JsonUtils.toJsonString(dto));
        ResultVo<TableDataArithmeticDeleteVo> result = tableArithmeticFeignClient.deleteRow(dto);
        log.info("调用算法服务删除行数据，出参: {}", JsonUtils.toJsonString(result));
        if (!ResultVo.isSuccess(result)) {
            log.info("调用算法服务删除行数据失败，入参: {}, 错误信息: {}", JsonUtils.toJsonString(dto), result.getMessage());
        }
    }

    private List<KnowledgeTableRow> arithmeticQuery(TableDataListDto dto, KnowledgeTable knowledgeTable) {
        TableDataArithmeticSearchDto searchDto = new TableDataArithmeticSearchDto();
        searchDto.setTableId(Long.valueOf(knowledgeTable.getTableId()));

        // 构建查询参数
        TableDataArithmeticSearchDto.QueryParam queryParam = new TableDataArithmeticSearchDto.QueryParam();
        queryParam.setMaxRows(dto.getPageSize());

        // 处理过滤条件
        if (dto.getFilter() != null && !dto.getFilter().isEmpty()) {
            List<TableDataArithmeticSearchDto.FilterCondition> filterConditions = new ArrayList<>();
            for (TableDataListDto.Filter filter : dto.getFilter()) {
                if (filter != null && filter.getFieldSn() != null) {
                    //通过字段SN获取字段详情
                    KnowledgeTableSchema schema = knowledgeTableSchemaService.getOne(Wrappers.lambdaQuery(KnowledgeTableSchema.class).eq(KnowledgeTableSchema::getFieldSn, filter.getFieldSn()).eq(KnowledgeTableSchema::getFieldStatus, SchemaStatusEnum.VALID.getCode()).eq(KnowledgeTableSchema::getTableId, knowledgeTable.getTableId()));
                    if (schema != null) {
                        TableDataArithmeticSearchDto.FilterCondition condition = new TableDataArithmeticSearchDto.FilterCondition();
                        condition.setColId(schema.getFieldId());
                        condition.setFilterType(filter.getExpression());
                        condition.setFilterObject("value");
                        condition.setFilterContent(filter.getValue());
                        filterConditions.add(condition);
                    }
                }
            }
            queryParam.setFilter(filterConditions);
        }

        // 设置查询参数到搜索DTO
        searchDto.setQuery(queryParam);

        // 调用算法服务查询数据
        log.info("调用算法服务查询行数据，入参: {}", JsonUtils.toJsonString(searchDto));
        ResultVo<TableDataArithmeticSearchVo> result = tableArithmeticFeignClient.searchRows(searchDto);
        log.info("调用算法服务查询行数据，出参: {}", JsonUtils.toJsonString(result));
        if (ResultVo.isSuccess(result) && result.getData() != null) {
            List<Integer> rowIds = result.getData().getRows();
            if (rowIds != null && !rowIds.isEmpty()) {
                return knowledgeTableRowService.listByIds(rowIds);
            }
        }

        return new ArrayList<>();
    }
}
