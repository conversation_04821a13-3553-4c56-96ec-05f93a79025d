<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.knowledge.table.mapper.KnowledgeFileMapper">

    <select id="allList" resultType="com.ai.application.knowledge.table.vo.FileListVo"
            parameterType="com.ai.application.knowledge.table.dto.FileListDto">
        select kf.file_id as fileId,
        af.file_sn as fileSn,
        af.file_name as fileName,
        af.file_ext as fileExt,
        af.file_status as fileStatus,
        af.app_id as appId,
        af.file_mime as fileMime,
        af.file_path as filePath,
        af.file_type as fileType,
        af.file_from as fileFrom,
        af.file_size as fileSize,
        af.file_width as fileWidth,
        af.file_height as fileHeight,
        af.file_duration as fileDuration,
        af.file_hash as fileHash,
        af.file_source as fileSource,
        af.app_id as appId,
        af.user_id as userId,
        kf.kb_id as kbId,
        kb.kb_sn as kbSn,
        kb.tenant_id as tenantId,
        kf.kf_parser as kfParser,
        kf.kf_summary as kfSummary,
        kf.create_time as createTime,
        kf.update_time as updateTime,
        kf.create_user_id as createUserId,
        kf.update_user_id as updateUserId,
        kf.kf_status as kfStatus
        from knowledge_file kf left join app_file af on kf.file_id = af.file_id
        left join knowledge_base kb on kb.kb_id = kf.kb_id
        where kb.kb_sn = #{kbSn}
        and kf.kf_status != 0
        <if test="keyword != null and keyword != ''">
            and af.file_name like concat('%', #{keyword}, '%')
        </if>
    </select>
</mapper>
