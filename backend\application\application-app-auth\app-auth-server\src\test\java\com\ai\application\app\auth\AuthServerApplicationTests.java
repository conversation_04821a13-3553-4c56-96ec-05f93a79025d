package com.ai.application.app.auth;

import org.jose4j.jwk.JsonWebKey;
import org.jose4j.jwk.RsaJsonWebKey;
import org.jose4j.jwk.RsaJwkGenerator;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class AuthServerApplicationTests {

    @Test
    void contextLoads() throws Exception {
    }

    /**
     * {"kty":"RSA","n":"4-7sGix1zm0lWZFnlsM8JPRESmWiVtT8D5lRK__ANuzClhWIRPXxqx11xVmwNl7l_hfzAHmv0iHEeHqN6ZAlyaCydeI9rc2soGrTgd4UxZk7wZydCyxV21fNR8TgjCtSZT3Tin1MsNsF965bxOK3bFstfcSUVKVGLQsikauyBuRejE8ia2J3u0uR8h3MXUlxg5nHWANVxAIjILeGxn9ogK7OJhou4oMkhQfmQI6D0WLjU1sAmMMuMqXwVIbaWfNYAEXmSptznxo9c3ApzIpX1mg8TT-bbbtcEl_lstllvcUdaBycu8tXvz2MRxKfvIHMh5CI0rSP51u46wBhQnnXZQ","e":"AQAB"}
     * {"kty":"RSA","n":"4-7sGix1zm0lWZFnlsM8JPRESmWiVtT8D5lRK__ANuzClhWIRPXxqx11xVmwNl7l_hfzAHmv0iHEeHqN6ZAlyaCydeI9rc2soGrTgd4UxZk7wZydCyxV21fNR8TgjCtSZT3Tin1MsNsF965bxOK3bFstfcSUVKVGLQsikauyBuRejE8ia2J3u0uR8h3MXUlxg5nHWANVxAIjILeGxn9ogK7OJhou4oMkhQfmQI6D0WLjU1sAmMMuMqXwVIbaWfNYAEXmSptznxo9c3ApzIpX1mg8TT-bbbtcEl_lstllvcUdaBycu8tXvz2MRxKfvIHMh5CI0rSP51u46wBhQnnXZQ","e":"AQAB","d":"AXqA6RobXbmkCb8AYqaqf1sXtQ91JRjnfjr93gnEllH5SGY0vjrp63_SpsnPCxSKpL1GP7uc7yag4RBOkEViUgl3Y4knHOoiGyXITi_kCMVaKPa6kxDUO_VUDx3CMIOJ1ypwZdULIs1S7CONt1egQKgQrSHI3R3M8B7Beh0eW61uptmhSg34-9TIw1jU0xnchxjjzLfmkplrmM-LtXuh3HrADmra-nEty2ia40ms-gF3zjcXOTfmZ_6jjVspuclkKeTB0rQmYLw8Ft03uXCfQp6krnW-MAa36j9YY1odysSJXpzYfNwy8vBaYpZ5-r6U8zi2QzSWlnkE0AwE6nmR","p":"_4YDtnrXMRw-Ywblm4ntGrL3zetMBjHT0Y0mU8M2D2UbrZJNbX4uoE3q_YwubRpBBxKRn5AfSKc0o5qoMzXFvgFL0Pk-M7MEU62HLkONCwbgM7bPGn2oxbg9L9SGF9kERLCWtkrW8FrEmlauYRsEvz9zs8XEz5jbD5kcLol7XvU","q":"5Fu8gih74cRn3CKP590kGTK54Nb5vijmanJSFYB7BXlxlH1nZL3lu6qX01UnJG5hTtNgJ_PoDVUNw6w3xxggu7R9ywMAhxmOkTEuI4CgRP3LAtmisoluXtA4KUN3AJBZpTEyVmxbKppbLPA_4Sbv1T0WJvUR9Gy4ktny6ybacLE","dp":"yHCG3xETVvnR0nppiI40STpUetp1o3JYt0ui4hgCgycMBCb8x8QwrR0SFdxt2rW6huLBCsrST3qiJyg9gxZOTJLVt1KcwY9O-Jjprpfcvuag3tArO5uQ1bCFhsyOd4yL9ZoUT2D7pEV_bZZLae741VDf4QhlL3e3d0_itHqyJTU","dq":"OD-8hFcShL8LUjcVOWjIboiHLrHlXIjYkNnODEvUgbmzV384S6FXCE3yDfVIqfmicHcylUTIIRiVRmCScLEpxvKjlbEMnTqXWn5Bvi79y2C0c-RF5jJiBGrHmqpqkrH70uv2WUFhh3H0CXK4WW5s3xqn7TqqjImHQWz8IB426lE","qi":"axQlFz0VfIb9hS871Kva8sgWifgFgoWJ0ThjBBZoxk980cGNYe6mD110kWMbLwf_kLSONdwSKW4_StGkVJjYsf5a1rA2U8CS2XrFZ9Uf9HGzHO-6GWyBH1XMyT839S0_On20dj0PTnQHTP3jgleolUHXVlmRXYkqTe7OZ6QPoSs"}
     * @param args
     */
    public static void main(String[] args) {
        try {
            RsaJsonWebKey rsaJsonWebKey = RsaJwkGenerator.generateJwk(2048);

            //生成公钥
            final String publicKey = rsaJsonWebKey.toJson(JsonWebKey.OutputControlLevel.PUBLIC_ONLY);
            System.out.println(publicKey);

            //生成私钥
            final String privateKey = rsaJsonWebKey.toJson(JsonWebKey.OutputControlLevel.INCLUDE_PRIVATE);
            System.out.println(privateKey);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}