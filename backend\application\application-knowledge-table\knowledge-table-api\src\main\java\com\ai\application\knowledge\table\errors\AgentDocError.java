package com.ai.application.knowledge.table.errors;

import com.ai.framework.core.enums.IErrorCode;

public enum AgentDocError implements IErrorCode {

    VER_SN_NOT_EXIST(31300, "verSn not exist error"),

    INTELLIGENT_TABLE_IS_EXISTS(31309, "智能表格不存在"),

    INTELLIGENT_TABLE_NAME_HAS_EXISTS(31310, "智能表格名称已存在"),

    INTELLIGENT_TABLE_FIELD_NAME_HAS_EXISTS(31311, "字段已存在"),

    INTELLIGENT_TABLE_DEFINITION_IS_EXISTS(31312, "智能表格定义不存在"),

    INTELLIGENT_TABLE_DEFINITION_FIELD_KEY_CONFIG_ERROR(31313, "当且仅当表格内容数据为空时，可更改字段key配置"),

    INTELLIGENT_TABLE_DEFINITION_FIELD_KEY_ERROR(31314, "请先清空表格内容数据后再删除key字段"),

    INTELLIGENT_TABLE_FIELD_IMPORT_ERROR1(31315, "智能表格字段批量导入仅支持xlsx格式"),

    INTELLIGENT_TABLE_FIELD_IMPORT_TEMPLATE_ERROR(31316, "智能表格字段批量导入模版不正确"),

    INTELLIGENT_TABLE_FIELD_IMPORT_ERROR2(31317, "智能表格字段批量导入失败"),

    INTELLIGENT_TABLE_FILE_ERROR(31318, "获取文件失败"),

    INTELLIGENT_TABLE_KNOWLEDGE_ERROR(31319, "获取知识失败"),
    //不存在此行数据
    INTELLIGENT_TABLE_DATA_ROW_IS_NOT_EXISTS(31320, "不存在此行数据"),

    INTELLIGENT_TABLE_DATA_IMPORT_ERROR1(31321, "智能表格数据批量导入失败"),

    INTELLIGENT_TABLE_ADD_FAIL(31322, "调用算法失败"),

    INTELLIGENT_TABLE_SEARCH_FAIL(31323, "智能表格语义检索失败"),

    INTELLIGENT_TABLE_DATA_IMPORT_TEMPLATE_ERROR(31324, "智能表格内容批量导入模版不正确，不存在智能表格定义的列"),

    INTELLIGENT_TABLE_DATA_IMPORT_ERROR_SIZE(31325, "文件大小超过{}"),

    INTELLIGENT_TABLE_DATA_CONTENT_MAX_LENGTH(31326, "内容长度超过{}"),

    INTELLIGENT_TABLE_DATA_IMPORT_ERROR_CONTAIN_NO_TEXT_KEY(31327, "智能表格数据批量导入失败,智能表格包含非文本字段key"),

    INTELLIGENT_TABLE_DATA_IMPORT_ERROR_CONTAIN_NO_ALL_KEY(31328, "智能表格内容批量导入失败，智能表格定义的key字段缺失"),

    //不存在此字段
    INTELLIGENT_TABLE_DATA_FIELD_IS_NOT_EXISTS(31329, "检索字段不存在"),

    INTELLIGENT_TABLE_DEFINITION_FILE_ARRAY_ERROR(31330, "智能表格定义批量文件不能设置为key"),

    //智能表格导出异常
    INTELLIGENT_TABLE_DATA_EXPORT_ERROR(31331, "智能表格数据导出失败"),

    //规则检索数组文件参数错误
    INTELLIGENT_TABLE_RULE_PARAM_ERROR(31332, "规则检索数组文件参数错误"),

    //智能表格内容导入模版下载失败
    INTELLIGENT_TABLE_DATA_TEMPLATE_DOWNLOAD_ERROR(31333, "智能表格内容导入模版下载失败"),

    INTELLIGENT_TABLE_RELATIVE_AGENT(31334,"表格已关联启用的智能体，请先解除关联");
    ;

    private Integer code;
    private String message;

    AgentDocError(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getMessage() {
        return this.message;
    }

    public Integer getCode() {
        return this.code;
    }
}
