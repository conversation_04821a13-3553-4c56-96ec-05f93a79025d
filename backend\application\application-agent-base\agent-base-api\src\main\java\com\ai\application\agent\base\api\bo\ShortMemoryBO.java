package com.ai.application.agent.base.api.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class ShortMemoryBO {
    /** 短期记忆库SN */
    @NotBlank(message = "短期记忆库SN不能为空")
    @Schema(description = "知期记忆编码")
    private String bucketSn;

    /** 短期记忆库名称 */
    @NotBlank(message = "短期记忆库名称不能为空")
    @Schema(description = "知期记忆名称")
    private String bucketName;

    /** 短期记忆库描述 */
    @Schema(description = "知期记忆描述")
    private String description;
}
