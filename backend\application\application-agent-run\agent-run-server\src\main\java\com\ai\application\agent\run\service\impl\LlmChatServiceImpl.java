package com.ai.application.agent.run.service.impl;

import com.ai.application.agent.run.dto.ProcessChatDTO;
import com.ai.application.agent.run.service.ILlmChatService;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * LLM 聊天服务实现类
 */
@Slf4j
@Service
public class LlmChatServiceImpl implements ILlmChatService {

    @Override
    public ResultVo<String> chat(ProcessChatDTO dto) {
        log.info("LlmChatService chat start, request: {}", JsonUtils.toJsonString(dto));

        try {
            // 参数校验
            if (StringUtils.isBlank(dto.getPrompt())) {
                return ResultVo.fail("prompt 不能为空");
            }
            if (StringUtils.isBlank(dto.getModel())) {
                return ResultVo.fail("model 不能为空");
            }

            // 模拟调用大模型（实际应该调用真实的 LLM 服务）
            String reply = simulateLlmChat(dto);

            log.info("LlmChatService chat success, reply: {}", reply);
            return ResultVo.data(reply);

        } catch (Exception e) {
            log.error("LlmChatService chat error", e);
            return ResultVo.fail("调用大模型失败: " + e.getMessage());
        }
    }

    /**
     * 模拟大模型对话（实际应该调用真实的 LLM 服务）
     */
    private String simulateLlmChat(ProcessChatDTO dto) {
        StringBuilder reply = new StringBuilder();
        
        // 模拟根据系统提示词和用户提示词生成回复
        if (StringUtils.isNotBlank(dto.getSysPrompt())) {
            reply.append("基于系统提示词：").append(dto.getSysPrompt()).append("\n");
        }
        
        reply.append("针对您的问题：").append(dto.getPrompt()).append("\n");
        reply.append("这是大模型 ").append(dto.getModel()).append(" 的回复。");
        
        if (StringUtils.isNotBlank(dto.getTemperature())) {
            reply.append("（温度参数：").append(dto.getTemperature()).append("）");
        }
        
        return reply.toString();
    }
}
