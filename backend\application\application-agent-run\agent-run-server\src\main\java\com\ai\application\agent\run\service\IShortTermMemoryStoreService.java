package com.ai.application.agent.run.service;

import com.ai.application.agent.run.dto.ShortTermMemoryStoreRequestDTO;
import com.ai.application.agent.run.dto.ShortTermMemoryStoreResultDTO;
import com.ai.framework.core.vo.ResultVo;

/**
 * 短期记忆存储服务接口
 */
public interface IShortTermMemoryStoreService {

    /**
     * 执行短期记忆存储
     *
     * @param request 存储请求
     * @param authorization 授权信息
     * @return 存储结果
     */
    ResultVo<ShortTermMemoryStoreResultDTO> executeShortTermMemoryStore(ShortTermMemoryStoreRequestDTO request, String authorization);

    /**
     * 验证记忆存储请求
     *
     * @param request 存储请求
     * @return 是否有效
     */
    boolean validateMemoryStoreRequest(ShortTermMemoryStoreRequestDTO request);

    /**
     * 存储单个记忆
     *
     * @param agentId 智能体ID
     * @param userId 用户ID
     * @param bucketSn 存储桶编号
     * @param memoryContent 记忆内容
     * @return 记忆ID
     */
    String storeMemory(String agentId, String userId, String bucketSn, String memoryContent);
}
