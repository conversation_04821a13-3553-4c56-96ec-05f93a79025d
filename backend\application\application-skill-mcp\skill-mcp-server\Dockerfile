FROM  harbor.idc7x24.cn/devops/openjdk:17-alpine-shanghai
COPY ./target/skill-mcp-server-0.0.1.jar /app.jar
RUN apt-get update && apt-get install -y curl gnupg && \
    curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs && \
    node -v && npm -v && npx -v && \
    apt-get clean

# 可选：安装 pnpm 或其他 node 工具
RUN npm install -g pnpm
ENTRYPOINT ["java","-Dspring.profiles.active=dev","-jar","/app.jar"]
