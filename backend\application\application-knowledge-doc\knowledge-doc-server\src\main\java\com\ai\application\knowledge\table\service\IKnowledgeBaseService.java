package com.ai.application.knowledge.table.service;

import com.ai.application.knowledge.table.dto.AgentByBbDto;
import com.ai.application.knowledge.table.dto.BaseCreateDto;
import com.ai.application.knowledge.table.dto.BaseListDto;
import com.ai.application.knowledge.table.dto.DeleteBySnFeignDto;
import com.ai.application.knowledge.table.entity.KnowledgeBase;
import com.ai.application.knowledge.table.vo.AgentByBbVo;
import com.ai.application.knowledge.table.vo.BaseDetailVo;
import com.ai.application.knowledge.table.vo.BaseListVo;
import com.ai.framework.core.vo.ResultVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * <p>
 * 知识库表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public interface IKnowledgeBaseService extends IService<KnowledgeBase> {

    /**
     * 知识库列表
     */
    ResultVo<PageInfo<BaseListVo>> list(BaseListDto dto);

    /**
     * 知识库列表 非分页
     */
    List<BaseListVo> allList(BaseListDto dto);

    /**
     * 知识库详情
     */
    ResultVo<BaseDetailVo> detail(String kbSn);

    /**
     * 知识库详情
     */
    ResultVo<BaseDetailVo> detailById(Integer kbId);

    /**
     * 新增知识库
     */
    ResultVo<String> create(BaseCreateDto dto);

    /**
     * 更新知识库
     */
    ResultVo<String> update(BaseCreateDto dto);

    /**
     * 删除知识库
     */
    ResultVo<String> delete(String kbSn);


    /**
     * 检查知识库是否存在
     */
    KnowledgeBase checkBase(String kbSn);

    /**
     * 根据编码获取知识库
     */
    ResultVo<List<AgentByBbVo>> agentByBb(AgentByBbDto dto);

}
