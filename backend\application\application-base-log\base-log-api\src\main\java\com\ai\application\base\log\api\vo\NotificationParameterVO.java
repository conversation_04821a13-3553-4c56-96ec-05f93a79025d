package com.ai.application.base.log.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 通知参数配置表
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@Schema(name = "")
public class NotificationParameterVO {
    /**
     * 通知参数id
     */
    @Schema(description = "通知参数id")
    private Integer ntprId;

    /**
     * 适用分类:10-异常,20-一般通知,30-成功
     */
    @Schema(description = "适用分类:10-异常,20-一般通知,30-成功")
    private Integer ntprType;

    /**
     * 参数代码
     */
    @Schema(description = "参数代码")
    private String ntprCode;

    /**
     * 参数名称
     */
    @Schema(description = "参数名称")
    private String ntprName;

    /**
     * 参数描述
     */
    @Schema(description = "参数描述")
    private String ntprDescription;

}