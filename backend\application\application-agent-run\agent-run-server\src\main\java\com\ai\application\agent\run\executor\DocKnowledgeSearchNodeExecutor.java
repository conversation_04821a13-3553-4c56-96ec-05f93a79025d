package com.ai.application.agent.run.executor;

import com.ai.application.agent.run.dto.DocKnowledgeSearchRequestDTO;
import com.ai.application.agent.run.dto.DocKnowledgeSearchResultDTO;
import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.service.IDocKnowledgeSearchService;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import com.ai.framework.workflow.excutor.NodeExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * 文档知识检索节点执行器
 * 用于在工作流中执行文档知识检索功能
 */
@Slf4j
public class DocKnowledgeSearchNodeExecutor implements NodeExecutor {

    private IDocKnowledgeSearchService docKnowledgeSearchService;

    @Override
    public void execute(WorkflowContext context) {
        String nodeKey = context.getCurrentNodeKey();
        NodeContext nodeCtx = context.getNodeContexts().get(nodeKey);
        Map<String, Object> nodeDef = nodeCtx.getNodeDefinition();

        log.info("DocKnowledgeSearchNodeExecutor execute start, nodeKey: {}, nodeDef: {}", nodeKey, JsonUtils.toJsonString(nodeDef));

        try {
            // 设置节点状态为运行中
            nodeCtx.setStatus(NodeStatus.RUNNING);

            // 初始化节点输出
            if (nodeCtx.getOutput() == null) {
                nodeCtx.setOutput(new HashMap<>());
            }

            // 从节点定义中获取输入参数
            Map<String, Object> inputParameters = (Map<String, Object>) nodeDef.get("inputParameters");
            if (inputParameters == null) {
                throw new ServiceException(ExecutorError.NODE_DEFINITION_IS_NULL);
            }

            // 构建文档知识检索请求
            DocKnowledgeSearchRequestDTO request = buildDocKnowledgeSearchRequest(inputParameters, context);

            // 执行文档知识检索
            String authorization = (String) context.getGlobalVars().get("authorization");
            ResultVo<DocKnowledgeSearchResultDTO> result = docKnowledgeSearchService.executeDocKnowledgeSearch(request, authorization);

            if (result.getCode() != 0) {
                throw new ServiceException(result.getCode(), result.getMessage());
            }

            DocKnowledgeSearchResultDTO searchResult = result.getData();
            if (searchResult == null) {
                throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "文档知识检索结果为空");
            }

            // 构建输出结果
            Map<String, Object> outputResult = buildOutputResult(searchResult);

            // 将结果写入输出参数
            writeOutputParameters(nodeDef, context, outputResult);

            // 设置节点输出
            nodeCtx.getOutput().putAll(outputResult);

            // 设置节点状态为成功
            nodeCtx.setStatus(NodeStatus.SUCCESS);
            nodeCtx.setEndTime(java.time.LocalDateTime.now());

            log.info("DocKnowledgeSearchNodeExecutor execute success, result: {}", JsonUtils.toJsonString(outputResult));

        } catch (Exception e) {
            log.error("DocKnowledgeSearchNodeExecutor execute error", e);
            nodeCtx.setStatus(NodeStatus.FAILED);
            nodeCtx.setErrorMsg("文档知识检索执行失败: " + e.getMessage());
            nodeCtx.setEndTime(java.time.LocalDateTime.now());
            throw e;
        }
    }

    /**
     * 构建文档知识检索请求
     */
    private DocKnowledgeSearchRequestDTO buildDocKnowledgeSearchRequest(Map<String, Object> inputParameters, WorkflowContext context) {
        // 获取参数值，支持变量替换
        String knowledgeInventorySn = getParameterValue(inputParameters, "knowledgeInventorySn", context);
        String type = getParameterValue(inputParameters, "type", context);
        String searchContent = getParameterValue(inputParameters, "searchContent", context);
        String searchKnowledgeContent = getParameterValue(inputParameters, "searchKnowledgeContent", context);
        String logic = getParameterValue(inputParameters, "logic", context);
        String topK = getParameterValue(inputParameters, "topK", context);
        String knowledgeType = getParameterValue(inputParameters, "knowledgeType", context);

        // 获取检索条件
        Object conditions = getParameterObject(inputParameters, "conditions", context);

        // 参数校验
        validateDocKnowledgeSearchParameters(knowledgeInventorySn, type, searchContent, searchKnowledgeContent, conditions);

        // 处理知识库编号（支持单个和列表）
        List<String> knowledgeInventorySnList = new ArrayList<>();
        if (StringUtils.isNotBlank(knowledgeInventorySn)) {
            knowledgeInventorySnList.add(knowledgeInventorySn);
        }

        // 设置默认值
        if (StringUtils.isBlank(topK)) {
            topK = "999"; // 根据backend2逻辑，默认为999
        }

        return DocKnowledgeSearchRequestDTO.builder()
                .knowledgeInventorySn(knowledgeInventorySn)
                .knowledgeInventorySnList(knowledgeInventorySnList)
                .type(type)
                .conditions(conditions)
                .searchContent(searchContent)
                .searchKnowledgeContent(searchKnowledgeContent)
                .logic(logic)
                .topK(topK)
                .knowledgeType(knowledgeType)
                .build();
    }

    /**
     * 参数校验
     */
    private void validateDocKnowledgeSearchParameters(String knowledgeInventorySn, String type, 
                                                    String searchContent, String searchKnowledgeContent, 
                                                    Object conditions) {
        if (StringUtils.isBlank(knowledgeInventorySn)) {
            throw new ServiceException(ExecutorError.KNOWLEDGE_INVENTORY_SN_IS_NULL);
        }
        
        if (StringUtils.isBlank(type)) {
            throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "检索类型不能为空");
        }

        // 根据检索类型校验相应参数
        if ("embedding".equals(type) || "keywords".equals(type)) {
            if (StringUtils.isBlank(searchKnowledgeContent)) {
                throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "检索内容不能为空");
            }
        } else if ("rule".equals(type)) {
            if (conditions == null) {
                throw new ServiceException(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), "规则检索条件不能为空");
            }
        }
    }

    /**
     * 获取参数值，支持变量替换
     */
    private String getParameterValue(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        String strValue = value.toString();
        
        // 如果是变量引用（以$开头），从全局变量中获取
        if (strValue.startsWith("$")) {
            String varName = strValue.substring(1);
            Object varValue = context.getGlobalVars().get(varName);
            return varValue != null ? varValue.toString() : null;
        }
        
        return strValue;
    }

    /**
     * 获取参数对象，支持变量替换
     */
    private Object getParameterObject(Map<String, Object> inputParameters, String paramName, WorkflowContext context) {
        Object value = inputParameters.get(paramName);
        if (value == null) {
            return null;
        }

        if (value instanceof String) {
            String strValue = value.toString();
            // 如果是变量引用（以$开头），从全局变量中获取
            if (strValue.startsWith("$")) {
                String varName = strValue.substring(1);
                return context.getGlobalVars().get(varName);
            }
        }
        
        return value;
    }

    /**
     * 构建输出结果
     */
    private Map<String, Object> buildOutputResult(DocKnowledgeSearchResultDTO searchResult) {
        Map<String, Object> result = new HashMap<>();
        
        // 设置主要输出
        result.put("docFile", searchResult.getDocFile() != null ? searchResult.getDocFile() : Collections.emptyList());
        result.put("target", searchResult.getTarget());
        result.put("matchResult", searchResult.getMatchResult() != null ? searchResult.getMatchResult() : "False");
        
        // 设置执行状态
        result.put("success", searchResult.getSuccess() != null ? searchResult.getSuccess() : false);
        result.put("errorMessage", searchResult.getErrorMessage());
        result.put("totalCount", searchResult.getTotalCount() != null ? searchResult.getTotalCount() : 0);

        return result;
    }

    /**
     * 写入输出参数
     */
    private void writeOutputParameters(Map<String, Object> nodeDef, WorkflowContext context, Map<String, Object> result) {
        Map<String, Object> outputParameters = (Map<String, Object>) nodeDef.get("outputParameters");
        if (outputParameters != null) {
            for (Map.Entry<String, Object> entry : outputParameters.entrySet()) {
                String outputKey = entry.getKey();
                String variableName = entry.getValue().toString();
                
                Object resultValue = result.get(outputKey);
                if (resultValue != null) {
                    context.setVar(variableName, resultValue);
                    log.info("Set variable {} = {}", variableName, resultValue);
                }
            }
        }
    }

    @Override
    public String getType() {
        return "DOCUMENT_KNOWLEDGE_SEARCH";
    }
}
