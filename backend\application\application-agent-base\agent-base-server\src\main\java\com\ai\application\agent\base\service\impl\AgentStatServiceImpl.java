package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.bo.AgentMetadataBO;
import com.ai.application.agent.base.api.common.enums.AgentStatusEnum;
import com.ai.application.agent.base.api.dto.AgentStatDTO;
import com.ai.application.agent.base.api.dto.query.AgentQueryDTO;
import com.ai.application.agent.base.api.entity.AgentRunSession;
import com.ai.application.agent.base.api.enums.AgentPageSourceEnum;
import com.ai.application.agent.base.api.mapstruct.AgentMapstruct;
import com.ai.application.agent.base.api.vo.*;
import com.ai.application.agent.base.mapper.AgentMapper;
import com.ai.application.agent.base.mapper.AgentRunSessionMapper;
import com.ai.application.agent.base.mapper.AgentRunWorkflowMapper;
import com.ai.application.agent.base.service.IAgentCommService;
import com.ai.application.agent.base.service.IAgentService;
import com.ai.application.agent.base.service.IAgentStatService;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.validator.AssertUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@Service
@AllArgsConstructor
@Slf4j
public class AgentStatServiceImpl implements IAgentStatService {
    private final AgentRunSessionMapper agentRunSessionMapper;
    private final IAgentService agentService;
    private final AgentMapper agentMapper;
    private final IAgentCommService agentCommService;
    private final AgentMapstruct agentMapstruct;
    private final AgentRunWorkflowMapper agentRunWorkflowMapper;

    /**
     * 首页统计-查询当前用户最近会话智能体列表
     * @return
     */
    @Transactional(readOnly = true)
    @Override
    public List<LastSessionAgentVO> queryLastSessionAgent() {
        List<LastSessionAgentVO> agentRunSessions = agentRunSessionMapper.queryLastSessionAgentByUserId(UserContext.getTenantId(), UserContext.getUserId());
        if(CollectionUtils.isEmpty(agentRunSessions)){
            return Lists.newArrayList();
        }

        //去重智能体
        List<LastSessionAgentVO> resultList = new ArrayList<>();
        agentRunSessions.forEach(session -> {
            if(!resultList.contains(session)){
                resultList.add(session);
            }
        });


        List<Integer> listAgentId = resultList.stream().map(LastSessionAgentVO::getAgentId).toList();

        AgentQueryDTO agentQueryDTO = new AgentQueryDTO();
        agentQueryDTO.setAgentIds(listAgentId);

        List<AgentVO> listAgent = agentService.list(agentQueryDTO);
        if(CollectionUtils.isEmpty(listAgent)){
            return Lists.newArrayList();
        }

        resultList.forEach(lastSessionAgentVO -> {
            Optional<AgentVO> first = listAgent.stream().filter(a -> a.getAgentId().equals(lastSessionAgentVO.getAgentId())).findFirst();
            first.ifPresent(agentVO -> {
                lastSessionAgentVO.setAgentName(agentVO.getAgentName());
                lastSessionAgentVO.setAgentSn(agentVO.getAgentSn());
                lastSessionAgentVO.setAgentStatus(agentVO.getAgentStatus());
                AgentMetadataBO agentMetadataBO = JsonUtils.parseObject(agentVO.getAgentMetadata(), AgentMetadataBO.class);
                lastSessionAgentVO.setAgentIcon(agentMetadataBO.getIcon());
            });

        });
        return agentRunSessions;
    }

    /**
     * 首页统计-移除当前用户最近会话智能体列表
     * @param agentSn
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeSessionByAgentSn(String agentSn) {
        log.info("会话移除,agentSn={}", agentSn);
        AgentVO agent = agentService.getAgentBySn(agentSn);
        AssertUtil.isNotNull(agent, "agent is null");
        AssertUtil.isNotNull(agent.getVersionId(), "版本不存在");

        LambdaQueryWrapper<AgentRunSession> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentRunSession::getAgentId, agent.getAgentId());
        queryWrapper.eq(AgentRunSession::getTenantId, UserContext.getTenantId());
        queryWrapper.eq(AgentRunSession::getUserId, UserContext.getUserId());
        List<AgentRunSession> agentRunSessions = agentRunSessionMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(agentRunSessions)){
            return;
        }

        agentRunSessions.forEach(agentRunSession -> {
            agentRunSession.setSessionStatus(-1);
            agentRunSessionMapper.updateById(agentRunSession);
        });
    }

    /**
     * 中台看板-数量统计
     * @param dto
     * @return
     */
    @Override
    public AgentStatResultVO statCountMetrics(AgentStatDTO dto) {
        log.info("数量统计,dto={}", JsonUtils.toJsonString(dto));
        AgentStatResultVO res = new AgentStatResultVO();

        //===活跃应用数量===
        //智能体总数量
        List<Integer> agentIds = agentCommService.queryGrantAgentIdList(AgentPageSourceEnum.USER.getCode());
        AgentStatDTO agentStatDTO = new AgentStatDTO();
        agentStatDTO.setDays(dto.getDays());
        agentStatDTO.setSessionStatus(1);
        Integer lastActiveAgents = Optional.ofNullable(agentRunSessionMapper.getAgentTotalSessions(dto)).orElse(0);
        //更早7/30统计
        agentStatDTO = new AgentStatDTO();
        agentStatDTO.setSessionStatus(1);
        agentStatDTO.setStartDate(LocalDate.now().minusDays(dto.getDays()*2));
        agentStatDTO.setEndDate(LocalDate.now().minusDays(dto.getDays()));
        Integer preActiveAgents = Optional.ofNullable(agentRunSessionMapper.getAgentTotalSessions(agentStatDTO)).orElse(0);
        //计算增长率
        int diff = lastActiveAgents - preActiveAgents;
        String rate = "0%";
        if(diff > 0){
             rate = new BigDecimal(diff).divide(new BigDecimal(preActiveAgents), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).intValue() + "%";
        }
        res.setAgentMetrics(AgentStatIndexVO.builder()
                .totalCount(agentIds.size())
                .count(lastActiveAgents)
                .rate(rate).build());

        //===总会话量===
        //最近7/30统计
        Integer lastAgentSessions = Optional.ofNullable(agentRunSessionMapper.getAgentTotalSessions(dto)).orElse(0);
        //更早7/30统计
        agentStatDTO = new AgentStatDTO();
        agentStatDTO.setStartDate(LocalDate.now().minusDays(dto.getDays()*2));
        agentStatDTO.setEndDate(LocalDate.now().minusDays(dto.getDays()));
        Integer preAgentSessions = Optional.ofNullable(agentRunSessionMapper.getAgentTotalSessions(agentStatDTO)).orElse(0);
        //计算增长率
        diff = lastAgentSessions - preAgentSessions;
        rate = "0%";
        if(diff > 0) {
            rate = new BigDecimal(diff).divide(new BigDecimal(preAgentSessions), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).intValue() + "%";
        }
        res.setSessionMetrics(AgentStatIndexVO.builder().count(lastAgentSessions).rate(rate).build());

        //===工作流调用次数===
        Integer lastWorkflows = Optional.ofNullable(agentRunWorkflowMapper.getWorkflowCount(dto)).orElse(0);
        //更早7/30统计
        agentStatDTO = new AgentStatDTO();
        agentStatDTO.setStartDate(LocalDate.now().minusDays(dto.getDays()*2));
        agentStatDTO.setEndDate(LocalDate.now().minusDays(dto.getDays()));
        Integer preWorkflows = Optional.ofNullable(agentRunWorkflowMapper.getWorkflowCount(agentStatDTO)).orElse(0);
        //计算增长率
        diff = lastWorkflows - preWorkflows;
        rate = "0%";
        if(diff > 0) {
            rate = new BigDecimal(diff).divide(new BigDecimal(preWorkflows), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).intValue() + "%";
        }
        res.setWorkFlowMetrics(AgentStatIndexVO.builder().count(lastWorkflows).rate(rate).build());

        //===Token总消耗量统计===
        //最近7/30统计
        Integer lastAgentTokens = Optional.ofNullable(agentMapper.getAgentTotalTokens(dto)).orElse(0);
        //更早7/30统计
        agentStatDTO = new AgentStatDTO();
        agentStatDTO.setStartDate(LocalDate.now().minusDays(dto.getDays()*2));
        agentStatDTO.setEndDate(LocalDate.now().minusDays(dto.getDays()));
        Integer preAgentTokens = Optional.ofNullable(agentMapper.getAgentTotalTokens(agentStatDTO)).orElse(0);
        //计算增长率
        diff = lastAgentTokens - preAgentTokens;
        rate = "0%";
        if(diff > 0) {
            rate = new BigDecimal(diff).divide(new BigDecimal(preAgentTokens), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).intValue() + "%";

        }res.setTokenMetrics(AgentStatIndexVO.builder().count(lastAgentTokens).rate(rate).build());

        return res;
    }

    /**
     * 高频智能体tokens消耗
     * @param dto
     * @return
     */
    @Override
    public List<AgentTokensStatisticsDetailVO> queryTokensFrequentAgent(AgentStatDTO dto) {
        log.info("高频tokens统计,dto={}", JsonUtils.toJsonString(dto));
        return agentMapper.queryTokensFrequentAgent(dto);
    }

    /**
     * 智能体最近消耗tokens消耗趋势
     * @param dto
     * @return
     */
    @Override
    public List<AgentTokensStatisticsDetailVO> queryLastTokensAgent(AgentStatDTO dto) {
        log.info("最近tokens统计,dto={}", JsonUtils.toJsonString(dto));
        return agentMapper.queryLastTokensAgent(dto);
    }

    /**
     * 最新智能体：当前用户有使用权限的，最新创建&&启用的智能体top3
     * 用户动态：用户创建、发布了什么信息/工具/智能体
     *
     * @return
     */
    @Override
    public List<AgentStatVO> queryLastCreateAgent() {
        AgentQueryDTO agentQueryDTO = new AgentQueryDTO();
        agentQueryDTO.setAgentStatus(AgentStatusEnum.RELEASE.getCode());
        List<AgentVO> listAgent = agentService.list(agentQueryDTO);
        if(CollectionUtils.isEmpty(listAgent)){
            return Lists.newArrayList();
        }
        List<AgentVO> list = listAgent.stream().sorted(Comparator.comparing(AgentVO::getCreateTime).reversed()).toList();
        return agentMapstruct.toStatVoList(list.subList(0,3));
    }

    /**
     * 常用智能体：当前用户有使用权限的，组织内使用会话量最多的智能体top3，可直接进入会话
     * @return
     */
    @Override
    public List<AgentStatVO> queryFrequentAgent() {
        List<AgentStatVO> agentStatVOS = agentRunSessionMapper.querySessionCountAgentByUserId(UserContext.getTenantId(), UserContext.getUserId());
        if(CollectionUtils.isEmpty(agentStatVOS)){
            return Lists.newArrayList();
        }
        List<AgentStatVO> list = agentStatVOS.stream().sorted(Comparator.comparing(AgentStatVO::getSessionCount).reversed()).toList();
        return list.subList(0, 3);
    }

    /**
     * Agent使用统计（agent详情页面）
     * @param date
     * @return
     */
    @Override
    public AgentUseTotalVO agentUserTotal(Integer date) {
        AgentUseTotalVO agentUseTotalVO = new AgentUseTotalVO();

        AgentUseTotalVO.UseTotal useTotal = new AgentUseTotalVO.UseTotal();
        useTotal.setLabel("测试");
        useTotal.setValue(new BigDecimal("1.1"));
        List<AgentUseTotalVO.UseTotal> useTotalList = List.of(useTotal);

        agentUseTotalVO.setMessage(useTotalList);
        agentUseTotalVO.setToken(useTotalList);
        agentUseTotalVO.setSession(useTotalList);

        return agentUseTotalVO;
    }
}
