package com.ai.application.agent.base.api.enums;

import lombok.Getter;

@Getter
public enum ToolExtendNameEnum {
    REPLY_TYPE("replyType", "回复后总结"),
    ;

    private final String code;
    private final String desc;

    ToolExtendNameEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ToolExtendNameEnum ofCode(String value) {
        for (ToolExtendNameEnum enums : ToolExtendNameEnum.values()) {
            if (enums.code.equals(value)) {
                return enums;
            }
        }
        return null;
    }
}
