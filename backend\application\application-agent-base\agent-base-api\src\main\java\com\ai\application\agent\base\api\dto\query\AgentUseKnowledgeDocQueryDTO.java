package com.ai.application.agent.base.api.dto.query;

import com.ai.framework.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Schema(name = "AgentUseDocQueryDTO")
@Data
public class AgentUseKnowledgeDocQueryDTO extends PageParam {
    @Schema(description = "知识库ID")
    private String docId;
}
