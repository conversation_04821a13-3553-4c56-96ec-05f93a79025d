package com.ai.application.base.log.api.enums;

import lombok.Data;
import lombok.Getter;

import java.util.Objects;

/**
 * 错误级别:10-致命,20-严重,30-警告,40-信息
 */
@Getter
public enum ErrorLevelEnum {
    FATAL(10, "致命"),
    SERIOUS(20, "严重"),
    WARN(30, "警告"),
    INFORMATION(40, "信息"),
    ;

    private Integer code;
    private String title;

    ErrorLevelEnum(Integer code, String title) {
        this.code = code;
        this.title = title;
    }

    public static String getTitle(Integer code) {
        for(ErrorLevelEnum vo :values() ) {
            if (Objects.equals(vo.code, code)) {
                return vo.title;
            }
        }
        return null;
    }
}
