package ${package.Entity};

import com.baomidou.mybatisplus.annotation.TableName;
#foreach($pkg in ${table.importPackages})
import ${pkg};
#end
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * $!{table.comment}
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("${table.name}")
public class ${entity} implements Serializable {
    ## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})
    #if("$!field.comment" != "")
    /**
    * ${field.comment}
    */
    #end
    @Schema(description = "$!{field.comment}")
    ## 主键
    #if(${field.keyFlag})
@TableId(type = IdType.AUTO)
    #end
private ${field.propertyType} ${field.propertyName};

#end
}