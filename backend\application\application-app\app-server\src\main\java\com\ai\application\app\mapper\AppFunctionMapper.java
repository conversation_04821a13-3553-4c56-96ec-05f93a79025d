package com.ai.application.app.mapper;

import com.ai.application.app.api.entity.AppFunction;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ai.application.app.api.vo.AppFunctionVO;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 应用功能表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-05-30
 */
@Mapper
public interface AppFunctionMapper extends BaseMapper<AppFunction> {
    /**
     * 查询应用功能表
     *
     * @return
     */
    List<AppFunctionVO> selectAppFunctionList();
}
