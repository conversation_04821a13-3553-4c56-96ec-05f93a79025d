package com.ai.application.agent.base.api.bo;

import com.ai.application.agent.base.api.enums.FlowDataTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class FlowVariableBO {
    @Schema(description = "变量ID")
    private String id;

    @Schema(description = "变量名称")
    private String name;

    @Schema(description = "变量描述")
    private String desc;

    @Schema(description = "是否必填")
    private Boolean required;

    @Schema(description = "是否系统变量")
    private Boolean isSystem = false;

    @Schema(description = "是否数组")
    private Boolean isArray = false;

    @Schema(description = "变量类型")
    private FlowDataTypeEnum type;

    @Schema(description = "值")
    private Object value;

    @Schema(description = "当前值")
    private Object currentValue;

    @Schema(description = "默认值")
    private Object defaultValue;

    @Schema(description = "数入框类型")
    private Object fieldType;
}
