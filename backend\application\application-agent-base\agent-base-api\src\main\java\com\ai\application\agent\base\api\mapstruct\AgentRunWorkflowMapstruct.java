package com.ai.application.agent.base.api.mapstruct;
import com.ai.application.agent.base.api.entity.AgentRunWorkflow;
import com.ai.application.agent.base.api.dto.AgentRunWorkflowDTO;
import com.ai.application.agent.base.api.vo.AgentRunWorkflowVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 智能体工作流执行记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */

@Mapper(componentModel = "spring")
public interface AgentRunWorkflowMapstruct {

    AgentRunWorkflow toEntity(AgentRunWorkflowDTO dto);
    List<AgentRunWorkflow> toEntityList(List<AgentRunWorkflowDTO> dtolist);
    AgentRunWorkflowVO toVo(AgentRunWorkflow entity);
    List<AgentRunWorkflowVO> toVoList(List<AgentRunWorkflow> entities);
}
