package com.ai.application.base.log.api.mapstruct;

import com.ai.application.base.log.api.dto.NotificationTypeAddDTO;
import com.ai.application.base.log.api.dto.NotificationTypeDTO;
import com.ai.application.base.log.api.dto.NotificationTypeUpdateDTO;
import com.ai.application.base.log.api.entity.NotificationType;
import com.ai.application.base.log.api.vo.NotificationTypeDetailVO;
import com.ai.application.base.log.api.vo.NotificationTypePageVO;
import com.ai.application.base.log.api.vo.NotificationTypeVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-14T11:00:52+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class NotificationTypeMapstructImpl implements NotificationTypeMapstruct {

    @Override
    public NotificationType toEntity(NotificationTypeDTO dto) {
        if ( dto == null ) {
            return null;
        }

        NotificationType notificationType = new NotificationType();

        notificationType.setNtypeId( dto.getNtypeId() );
        notificationType.setNtypeType( dto.getNtypeType() );
        notificationType.setNtypeName( dto.getNtypeName() );
        notificationType.setNtypeDesc( dto.getNtypeDesc() );
        notificationType.setNtypeStatus( dto.getNtypeStatus() );
        notificationType.setNtypeConfig( dto.getNtypeConfig() );
        notificationType.setTenantId( dto.getTenantId() );
        notificationType.setCreateTime( dto.getCreateTime() );
        notificationType.setUpdateTime( dto.getUpdateTime() );

        return notificationType;
    }

    @Override
    public NotificationType toEntity(NotificationTypeAddDTO dto) {
        if ( dto == null ) {
            return null;
        }

        NotificationType notificationType = new NotificationType();

        notificationType.setNtypeType( dto.getNtypeType() );
        notificationType.setNtypeName( dto.getNtypeName() );

        return notificationType;
    }

    @Override
    public NotificationType toEntity(NotificationTypeUpdateDTO dto) {
        if ( dto == null ) {
            return null;
        }

        NotificationType notificationType = new NotificationType();

        notificationType.setNtypeId( dto.getNtypeId() );
        notificationType.setNtypeType( dto.getNtypeType() );
        notificationType.setNtypeName( dto.getNtypeName() );

        return notificationType;
    }

    @Override
    public List<NotificationType> toEntityList(List<NotificationTypeDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<NotificationType> list = new ArrayList<NotificationType>( dtolist.size() );
        for ( NotificationTypeDTO notificationTypeDTO : dtolist ) {
            list.add( toEntity( notificationTypeDTO ) );
        }

        return list;
    }

    @Override
    public NotificationTypeVO toVo(NotificationType entity) {
        if ( entity == null ) {
            return null;
        }

        NotificationTypeVO notificationTypeVO = new NotificationTypeVO();

        notificationTypeVO.setNtypeId( entity.getNtypeId() );
        notificationTypeVO.setNtypeType( entity.getNtypeType() );
        notificationTypeVO.setNtypeName( entity.getNtypeName() );
        notificationTypeVO.setNtypeDesc( entity.getNtypeDesc() );
        notificationTypeVO.setNtypeStatus( entity.getNtypeStatus() );
        notificationTypeVO.setNtypeConfig( entity.getNtypeConfig() );

        return notificationTypeVO;
    }

    @Override
    public NotificationTypeDetailVO toDetailVo(NotificationType entity) {
        if ( entity == null ) {
            return null;
        }

        NotificationTypeDetailVO notificationTypeDetailVO = new NotificationTypeDetailVO();

        notificationTypeDetailVO.setNtypeId( entity.getNtypeId() );
        notificationTypeDetailVO.setNtypeType( entity.getNtypeType() );
        notificationTypeDetailVO.setNtypeName( entity.getNtypeName() );
        notificationTypeDetailVO.setNtypeDesc( entity.getNtypeDesc() );
        notificationTypeDetailVO.setNtypeStatus( entity.getNtypeStatus() );
        notificationTypeDetailVO.setNtypeConfig( entity.getNtypeConfig() );
        notificationTypeDetailVO.setCreateTime( entity.getCreateTime() );
        notificationTypeDetailVO.setUpdateTime( entity.getUpdateTime() );

        return notificationTypeDetailVO;
    }

    @Override
    public List<NotificationTypeVO> toVoList(List<NotificationType> entities) {
        if ( entities == null ) {
            return null;
        }

        List<NotificationTypeVO> list = new ArrayList<NotificationTypeVO>( entities.size() );
        for ( NotificationType notificationType : entities ) {
            list.add( toVo( notificationType ) );
        }

        return list;
    }

    @Override
    public List<NotificationTypePageVO> toPageVoList(List<NotificationType> entities) {
        if ( entities == null ) {
            return null;
        }

        List<NotificationTypePageVO> list = new ArrayList<NotificationTypePageVO>( entities.size() );
        for ( NotificationType notificationType : entities ) {
            list.add( notificationTypeToNotificationTypePageVO( notificationType ) );
        }

        return list;
    }

    protected NotificationTypePageVO notificationTypeToNotificationTypePageVO(NotificationType notificationType) {
        if ( notificationType == null ) {
            return null;
        }

        NotificationTypePageVO notificationTypePageVO = new NotificationTypePageVO();

        notificationTypePageVO.setNtypeId( notificationType.getNtypeId() );
        notificationTypePageVO.setNtypeType( notificationType.getNtypeType() );
        notificationTypePageVO.setNtypeName( notificationType.getNtypeName() );
        notificationTypePageVO.setNtypeDesc( notificationType.getNtypeDesc() );
        notificationTypePageVO.setNtypeStatus( notificationType.getNtypeStatus() );
        notificationTypePageVO.setNtypeConfig( notificationType.getNtypeConfig() );
        notificationTypePageVO.setCreateTime( notificationType.getCreateTime() );
        notificationTypePageVO.setUpdateTime( notificationType.getUpdateTime() );

        return notificationTypePageVO;
    }
}
