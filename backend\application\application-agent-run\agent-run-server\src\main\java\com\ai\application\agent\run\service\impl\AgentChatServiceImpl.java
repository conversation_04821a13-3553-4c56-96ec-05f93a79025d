package com.ai.application.agent.run.service.impl;

import com.ai.application.agent.base.api.dto.AgentChatDTO;
import com.ai.application.agent.base.api.vo.AgentChatVO;
import com.ai.application.agent.run.service.IAgentChatService;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.ai.framework.core.vo.ResultVo;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Agent Chat 服务实现类
 */
@Slf4j
@Service
public class AgentChatServiceImpl implements IAgentChatService {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public ResultVo<AgentChatVO> requestLLMWithAgent(AgentChatDTO chatDto) {
        log.info("AgentChatService requestLLMWithAgent start, request: {}", JsonUtils.toJsonString(chatDto));

        try {
            // 参数校验
            if (StringUtils.isBlank(chatDto.getAgentSn())) {
                return ResultVo.fail("agentSn 不能为空");
            }
            if (StringUtils.isBlank(chatDto.getAgentType())) {
                return ResultVo.fail("agentType 不能为空");
            }
            if (StringUtils.isBlank(chatDto.getMsgContent())) {
                return ResultVo.fail("msgContent 不能为空");
            }

            // 模拟调用智能体（实际应该调用真实的智能体服务）
            AgentChatVO result = simulateAgentChat(chatDto);

            log.info("AgentChatService requestLLMWithAgent success, result: {}", JsonUtils.toJsonString(result));
            return ResultVo.data(result);

        } catch (Exception e) {
            log.error("AgentChatService requestLLMWithAgent error", e);
            return ResultVo.fail("调用智能体失败: " + e.getMessage());
        }
    }

    @Override
    public ResultVo<Boolean> checkAgentAvailable(String agentSn) {
        log.info("AgentChatService checkAgentAvailable, agentSn: {}", agentSn);

        if (StringUtils.isBlank(agentSn)) {
            return ResultVo.fail("agentSn 不能为空");
        }

        // 模拟检查智能体可用性（实际应该查询数据库或调用相关服务）
        boolean available = !agentSn.contains("invalid");
        
        return ResultVo.data(available);
    }

    /**
     * 模拟智能体对话（实际应该调用真实的智能体服务）
     */
    private AgentChatVO simulateAgentChat(AgentChatDTO chatDto) {
        try {
            // 生成模拟回复
            String reply = generateMockReply(chatDto);
            
            // 构建内容JSON
            JsonNode content = objectMapper.createObjectNode()
                    .put("answer", reply)
                    .put("type", "text");

            return AgentChatVO.builder()
                    .sessionSn(StringUtils.defaultIfBlank(chatDto.getSessionSn(), UUIDUtil.genRandomSn("session")))
                    .msgSn(UUIDUtil.genRandomSn("msg"))
                    .msgType("text")
                    .source("agent")
                    .msgStatus("success")
                    .msgTime(System.currentTimeMillis())
                    .success(true)
                    .time(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                    .messageStatusStr("成功")
                    .content(content)
                    .reply(reply)
                    .build();

        } catch (Exception e) {
            log.error("模拟智能体对话失败", e);
            throw new RuntimeException("模拟智能体对话失败", e);
        }
    }

    /**
     * 生成模拟回复
     */
    private String generateMockReply(AgentChatDTO chatDto) {
        String agentType = chatDto.getAgentType();
        String msgContent = chatDto.getMsgContent();
        
        switch (agentType) {
            case "chat":
                return "这是对话型智能体的回复：" + msgContent;
            case "doc":
                return "这是文档型智能体的回复，基于知识库回答：" + msgContent;
            case "form":
                return "这是表单型智能体的回复：" + msgContent;
            case "master":
                return "这是主控型智能体的回复：" + msgContent;
            default:
                return "智能体回复：" + msgContent;
        }
    }
}
