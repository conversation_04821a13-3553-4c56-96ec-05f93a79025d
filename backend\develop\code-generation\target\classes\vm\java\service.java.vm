package ${package.Service};

import com.github.pagehelper.PageInfo;
import ${cfg.dtoPackage}.${entity}DTO;
import ${cfg.queryDtoPackage}.${entity}QueryDTO;
import ${cfg.voPackage}.${entity}VO;
import java.util.List;
import java.util.Set;

/**
 * $!{table.comment}-前端控制器
 *
 * <AUTHOR>
 * @since ${date}
 */
public interface ${table.serviceName} {

        /**
         * 分页
         *
         * @param queryDto
         * @return
         */
        PageInfo<${entity}VO> page(${entity}QueryDTO queryDto);

        /**
         * 列表
         *
         * @param sort
         * @param queryDto
         * @return
         */
        List<${entity}VO> list(${entity}QueryDTO queryDto);

        /**
         * 保存
         *
         * @param dto
         */
        void add(${entity}DTO dto);

        /**
         * 更新
         *
         * @param dto
         */
        void update(${entity}DTO dto);

        /**
         * 查看
         *
         * @param id
         * @return
         */
        ${entity}VO get(Integer id);
}