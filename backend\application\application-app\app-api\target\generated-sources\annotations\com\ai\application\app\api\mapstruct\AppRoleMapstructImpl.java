package com.ai.application.app.api.mapstruct;

import com.ai.application.app.api.dto.AppRoleDTO;
import com.ai.application.app.api.entity.AppRole;
import com.ai.application.app.api.vo.AppRoleListVO;
import com.ai.application.app.api.vo.AppRoleSimpleVO;
import com.ai.application.app.api.vo.AppRoleVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-14T11:00:29+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AppRoleMapstructImpl implements AppRoleMapstruct {

    @Override
    public AppRole toEntity(AppRoleDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AppRole appRole = new AppRole();

        appRole.setRoleId( dto.getRoleId() );
        appRole.setRoleCode( dto.getRoleCode() );
        appRole.setRoleName( dto.getRoleName() );
        appRole.setRoleDesc( dto.getRoleDesc() );
        appRole.setRoleStatus( dto.getRoleStatus() );
        appRole.setAppId( dto.getAppId() );
        appRole.setTenantId( dto.getTenantId() );
        appRole.setCreateTime( dto.getCreateTime() );
        appRole.setUpdateTime( dto.getUpdateTime() );

        return appRole;
    }

    @Override
    public List<AppRole> toEntityList(List<AppRoleDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<AppRole> list = new ArrayList<AppRole>( dtolist.size() );
        for ( AppRoleDTO appRoleDTO : dtolist ) {
            list.add( toEntity( appRoleDTO ) );
        }

        return list;
    }

    @Override
    public AppRoleVO toVo(AppRole entity) {
        if ( entity == null ) {
            return null;
        }

        AppRoleVO appRoleVO = new AppRoleVO();

        appRoleVO.setRoleId( entity.getRoleId() );
        appRoleVO.setRoleCode( entity.getRoleCode() );
        appRoleVO.setRoleName( entity.getRoleName() );
        appRoleVO.setRoleDesc( entity.getRoleDesc() );
        appRoleVO.setRoleStatus( entity.getRoleStatus() );
        appRoleVO.setAppId( entity.getAppId() );
        appRoleVO.setTenantId( entity.getTenantId() );
        appRoleVO.setCreateTime( entity.getCreateTime() );
        appRoleVO.setUpdateTime( entity.getUpdateTime() );

        return appRoleVO;
    }

    @Override
    public List<AppRoleVO> toVoList(List<AppRole> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AppRoleVO> list = new ArrayList<AppRoleVO>( entities.size() );
        for ( AppRole appRole : entities ) {
            list.add( toVo( appRole ) );
        }

        return list;
    }

    @Override
    public List<AppRoleSimpleVO> toSimpleVoList(List<AppRole> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AppRoleSimpleVO> list = new ArrayList<AppRoleSimpleVO>( entities.size() );
        for ( AppRole appRole : entities ) {
            list.add( appRoleToAppRoleSimpleVO( appRole ) );
        }

        return list;
    }

    @Override
    public List<AppRoleListVO> toRoleVoList(List<AppRole> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AppRoleListVO> list = new ArrayList<AppRoleListVO>( entities.size() );
        for ( AppRole appRole : entities ) {
            list.add( appRoleToAppRoleListVO( appRole ) );
        }

        return list;
    }

    protected AppRoleSimpleVO appRoleToAppRoleSimpleVO(AppRole appRole) {
        if ( appRole == null ) {
            return null;
        }

        AppRoleSimpleVO appRoleSimpleVO = new AppRoleSimpleVO();

        appRoleSimpleVO.setRoleId( appRole.getRoleId() );
        appRoleSimpleVO.setRoleCode( appRole.getRoleCode() );
        appRoleSimpleVO.setRoleName( appRole.getRoleName() );

        return appRoleSimpleVO;
    }

    protected AppRoleListVO appRoleToAppRoleListVO(AppRole appRole) {
        if ( appRole == null ) {
            return null;
        }

        AppRoleListVO appRoleListVO = new AppRoleListVO();

        appRoleListVO.setRoleId( appRole.getRoleId() );
        appRoleListVO.setRoleCode( appRole.getRoleCode() );
        appRoleListVO.setRoleName( appRole.getRoleName() );
        appRoleListVO.setRoleDesc( appRole.getRoleDesc() );

        return appRoleListVO;
    }
}
