package com.ai.application.agent.base.api.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * Agent Chat VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(name = "AgentChatVO")
public class AgentChatVO {
    /**
     * session sn
     */
    @Schema(description = "会话编号")
    private String sessionSn;

    /**
     * message sn
     */
    @Schema(description = "消息编号")
    private String msgSn;

    /**
     * 消息类型, msgType新增chatDoc
     */
    @Schema(description = "消息类型")
    private String msgType;

    /**
     * 消息来源
     */
    @Schema(description = "消息来源")
    private String source;

    /**
     * 消息状态
     */
    @Schema(description = "消息状态")
    private String msgStatus;

    /**
     * 消失时间
     */
    @Schema(description = "消息时间戳")
    private Long msgTime;

    /**
     * 推荐
     */
    @Schema(description = "推荐评分")
    private Long rate;

    /**
     * 消息的状态,正常true, 异常为false
     */
    @Schema(description = "是否成功")
    private Boolean success = true;

    /**
     * 消息时间
     */
    @Schema(description = "消息时间字符串")
    private String time;

    /**
     * 消息状态字符串
     */
    @Schema(description = "消息状态描述")
    private String messageStatusStr;

    /**
     * 内容
     */
    @Schema(description = "消息内容")
    private JsonNode content;

    /**
     * 回复内容
     */
    @Schema(description = "回复内容")
    private String reply;

    /**
     * 引用信息
     */
    @Schema(description = "引用信息")
    private JsonNode reference;
}
