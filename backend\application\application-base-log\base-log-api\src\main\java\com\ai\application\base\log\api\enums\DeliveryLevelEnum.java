package com.ai.application.base.log.api.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 告警错误级别:1-致命,2-严重,3-警告,4-信息,0-全部
 */
@Getter
public enum DeliveryLevelEnum {
    ALL(0, "全部"),
    FATAL(1, "致命"),
    SERIOUS(2, "严重"),
    WARN(3, "警告"),
    INFORMATION(4, "信息")
    ;

    private Integer code;
    private String title;

    DeliveryLevelEnum(Integer code, String title) {
        this.code = code;
        this.title = title;
    }

    public static String getTitle(Integer code) {
        for(DeliveryLevelEnum vo :values() ) {
            if (Objects.equals(vo.code, code)) {
                return vo.title;
            }
        }
        return null;
    }
}
