package com.ai.application.agent.base.mapper;

import com.ai.application.agent.base.api.entity.AgentExtend;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;


/**
 * agent扩展信息-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface AgentExtendMapper extends BaseMapper<AgentExtend> {
    @Update("update agent_extend set item_status = #{status} where agent_id = #{agentId} and item_name = #{itemName}")
    void updateItemStatus(@Param("agentId") Integer agentId, @Param("itemName") String itemName, @Param("status") Integer status);

    @Select("select * from agent_extend where agent_id = #{agentId}")
    List<AgentExtend> findByAgentId(@Param("agentId") Integer agentId);
}