package com.ai.application.app.api.vo;

import com.ai.application.app.api.dto.AppUserBatchImportData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class AppUserBatchImportResultVO {
    @Schema(description = "导入成功数据")
    private List<AppUserBatchImportData> listSuccessData;
    @Schema(description = "导入失败数据")
    private List<AppUserBatchImportData> listFailData;
}
