package com.ai.application.admin.api.mapstruct;

import com.ai.application.admin.api.dto.TenantDepartmentDTO;
import com.ai.application.admin.api.entity.TenantDepartment;
import com.ai.application.admin.api.vo.TenantDepartmentTreeVO;
import com.ai.application.admin.api.vo.TenantDepartmentVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-13T10:32:30+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class TenantDepartmentMapstructImpl implements TenantDepartmentMapstruct {

    @Override
    public TenantDepartment toEntity(TenantDepartmentDTO dto) {
        if ( dto == null ) {
            return null;
        }

        TenantDepartment tenantDepartment = new TenantDepartment();

        tenantDepartment.setDeptId( dto.getDeptId() );
        tenantDepartment.setDeptName( dto.getDeptName() );
        tenantDepartment.setDeptUpName( dto.getDeptUpName() );
        tenantDepartment.setDeptUpPath( dto.getDeptUpPath() );
        tenantDepartment.setDeptSort( dto.getDeptSort() );
        tenantDepartment.setDeptStatus( dto.getDeptStatus() );
        tenantDepartment.setParentId( dto.getParentId() );
        tenantDepartment.setTenantId( dto.getTenantId() );
        tenantDepartment.setCreateTime( dto.getCreateTime() );
        tenantDepartment.setUpdateTime( dto.getUpdateTime() );

        return tenantDepartment;
    }

    @Override
    public List<TenantDepartment> toEntityList(List<TenantDepartmentDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<TenantDepartment> list = new ArrayList<TenantDepartment>( dtolist.size() );
        for ( TenantDepartmentDTO tenantDepartmentDTO : dtolist ) {
            list.add( toEntity( tenantDepartmentDTO ) );
        }

        return list;
    }

    @Override
    public TenantDepartmentVO toVo(TenantDepartment entity) {
        if ( entity == null ) {
            return null;
        }

        TenantDepartmentVO tenantDepartmentVO = new TenantDepartmentVO();

        tenantDepartmentVO.setDeptId( entity.getDeptId() );
        tenantDepartmentVO.setDeptName( entity.getDeptName() );
        tenantDepartmentVO.setDeptUpName( entity.getDeptUpName() );
        tenantDepartmentVO.setDeptUpPath( entity.getDeptUpPath() );
        tenantDepartmentVO.setDeptSort( entity.getDeptSort() );
        tenantDepartmentVO.setDeptStatus( entity.getDeptStatus() );
        tenantDepartmentVO.setParentId( entity.getParentId() );
        tenantDepartmentVO.setTenantId( entity.getTenantId() );
        tenantDepartmentVO.setCreateTime( entity.getCreateTime() );
        tenantDepartmentVO.setUpdateTime( entity.getUpdateTime() );

        return tenantDepartmentVO;
    }

    @Override
    public List<TenantDepartmentVO> toVoList(List<TenantDepartment> entities) {
        if ( entities == null ) {
            return null;
        }

        List<TenantDepartmentVO> list = new ArrayList<TenantDepartmentVO>( entities.size() );
        for ( TenantDepartment tenantDepartment : entities ) {
            list.add( toVo( tenantDepartment ) );
        }

        return list;
    }

    @Override
    public List<TenantDepartmentTreeVO> toTreeVoList(List<TenantDepartment> entities) {
        if ( entities == null ) {
            return null;
        }

        List<TenantDepartmentTreeVO> list = new ArrayList<TenantDepartmentTreeVO>( entities.size() );
        for ( TenantDepartment tenantDepartment : entities ) {
            list.add( tenantDepartmentToTenantDepartmentTreeVO( tenantDepartment ) );
        }

        return list;
    }

    protected TenantDepartmentTreeVO tenantDepartmentToTenantDepartmentTreeVO(TenantDepartment tenantDepartment) {
        if ( tenantDepartment == null ) {
            return null;
        }

        TenantDepartmentTreeVO tenantDepartmentTreeVO = new TenantDepartmentTreeVO();

        tenantDepartmentTreeVO.setDeptId( tenantDepartment.getDeptId() );
        tenantDepartmentTreeVO.setDeptName( tenantDepartment.getDeptName() );
        tenantDepartmentTreeVO.setDeptSort( tenantDepartment.getDeptSort() );
        tenantDepartmentTreeVO.setParentId( tenantDepartment.getParentId() );

        return tenantDepartmentTreeVO;
    }
}
