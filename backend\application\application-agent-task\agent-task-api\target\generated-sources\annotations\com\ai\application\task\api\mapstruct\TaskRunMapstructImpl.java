package com.ai.application.task.api.mapstruct;

import com.ai.application.task.api.dto.TaskRunDTO;
import com.ai.application.task.api.entity.TaskRun;
import com.ai.application.task.api.vo.TaskRunVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T09:54:05+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class TaskRunMapstructImpl implements TaskRunMapstruct {

    @Override
    public TaskRun toEntity(TaskRunDTO dto) {
        if ( dto == null ) {
            return null;
        }

        TaskRun taskRun = new TaskRun();

        taskRun.setTaskRunId( dto.getTaskRunId() );
        taskRun.setRunType( dto.getRunType() );
        taskRun.setRunStatus( dto.getRunStatus() );
        taskRun.setScheduledTime( dto.getScheduledTime() );
        taskRun.setActualStartTime( dto.getActualStartTime() );
        taskRun.setActualEndTime( dto.getActualEndTime() );
        taskRun.setDuration( dto.getDuration() );
        taskRun.setRetryAttempt( dto.getRetryAttempt() );
        taskRun.setRunInput( dto.getRunInput() );
        taskRun.setRunOutput( dto.getRunOutput() );
        taskRun.setRunError( dto.getRunError() );
        taskRun.setRunMetadata( dto.getRunMetadata() );
        taskRun.setTokensUsed( dto.getTokensUsed() );
        taskRun.setNextRetryTime( dto.getNextRetryTime() );
        taskRun.setTaskId( dto.getTaskId() );
        taskRun.setAgentRunId( dto.getAgentRunId() );
        taskRun.setTriggerUserId( dto.getTriggerUserId() );
        taskRun.setCreateTime( dto.getCreateTime() );
        taskRun.setUpdateTime( dto.getUpdateTime() );

        return taskRun;
    }

    @Override
    public List<TaskRun> toEntityList(List<TaskRunDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<TaskRun> list = new ArrayList<TaskRun>( dtolist.size() );
        for ( TaskRunDTO taskRunDTO : dtolist ) {
            list.add( toEntity( taskRunDTO ) );
        }

        return list;
    }

    @Override
    public TaskRunVO toVo(TaskRun entity) {
        if ( entity == null ) {
            return null;
        }

        TaskRunVO taskRunVO = new TaskRunVO();

        taskRunVO.setTaskRunId( entity.getTaskRunId() );
        taskRunVO.setRunType( entity.getRunType() );
        taskRunVO.setRunStatus( entity.getRunStatus() );
        taskRunVO.setScheduledTime( entity.getScheduledTime() );
        taskRunVO.setActualStartTime( entity.getActualStartTime() );
        taskRunVO.setActualEndTime( entity.getActualEndTime() );
        taskRunVO.setDuration( entity.getDuration() );
        taskRunVO.setRetryAttempt( entity.getRetryAttempt() );
        taskRunVO.setRunInput( entity.getRunInput() );
        taskRunVO.setRunOutput( entity.getRunOutput() );
        taskRunVO.setRunError( entity.getRunError() );
        taskRunVO.setRunMetadata( entity.getRunMetadata() );
        taskRunVO.setTokensUsed( entity.getTokensUsed() );
        taskRunVO.setNextRetryTime( entity.getNextRetryTime() );
        taskRunVO.setTaskId( entity.getTaskId() );
        taskRunVO.setAgentRunId( entity.getAgentRunId() );
        taskRunVO.setTriggerUserId( entity.getTriggerUserId() );
        taskRunVO.setCreateTime( entity.getCreateTime() );
        taskRunVO.setUpdateTime( entity.getUpdateTime() );

        return taskRunVO;
    }

    @Override
    public List<TaskRunVO> toVoList(List<TaskRun> entities) {
        if ( entities == null ) {
            return null;
        }

        List<TaskRunVO> list = new ArrayList<TaskRunVO>( entities.size() );
        for ( TaskRun taskRun : entities ) {
            list.add( toVo( taskRun ) );
        }

        return list;
    }
}
