# 智能体记忆模块 (Agent Memory)

这是一个基于FastAPI开发的智能体记忆管理系统，提供智能体记忆的存储、检索和管理功能。系统支持向量化存储、语义相似度检索、混合查询过滤等高级功能。


## 🌟 主要功能

### 核心功能
- **记忆存储**: 支持智能体记忆的结构化存储，包括智能体ID、用户ID、记忆类别、用户问题、问题回复等
- **向量检索**: 使用thenlper/gte-base-zh模型进行文本向量化，支持基于语义相似度的记忆检索
- **混合检索**: 支持条件过滤与向量相似度检索的组合，包括时间范围、记忆类别等维度
- **记忆管理**: 提供记忆的增删查改功能，支持批量操作和统计分析

### 技术特性
- **高性能**: 基于FastAPI异步框架，支持高并发请求处理
- **可扩展**: 采用微服务架构，支持水平扩展
- **服务发现**: 集成Nacos 3.0服务注册与发现，支持gRPC和HTTP双协议，动态配置更新
- **容器化**: 支持Docker容器化部署，便于运维管理
- **监控**: 内置健康检查和日志记录功能

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI App   │    │  Elasticsearch  │    │   Nacos 3.0     │
│                 │    │                 │    │                 │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │API Routes │  │    │  │   Index   │  │    │  │  Service  │  │
│  └───────────┘  │    │  │Management │  │    │  │Discovery  │  │
│  ┌───────────┐  │────┤  └───────────┘  │    │  │  (gRPC)   │  │
│  │  Memory   │  │    │  ┌───────────┐  │    │  └───────────┘  │
│  │ Service   │  │    │  │  Vector   │  │    │  ┌───────────┐  │
│  └───────────┘  │    │  │  Search   │  │    │  │  Config   │  │
│  ┌───────────┐  │    │  └───────────┘  │    │  │Management │  │
│  │Embedding  │  │    └─────────────────┘    │  │  (gRPC)   │  │
│  │ Service   │  │───────────────────────────│  └───────────┘  │
│  └───────────┘  │                           └─────────────────┘
└─────────────────┘
```

## 📋 环境要求

### 基础环境
- Python 3.10+
- Docker & Docker Compose (可选)

### 依赖服务
- Elasticsearch 8.11+
- **Nacos 3.0+** (推荐，支持gRPC协议) 或 Nacos 2.x (兼容HTTP协议)

### 📌 地址占位符说明

本文档中使用以下占位符表示实际部署时需要替换的地址信息：

- `{your-host}:{your-port}` - 应用服务地址（默认: `localhost:6023`）
- `{es-host}:{es-port}` - Elasticsearch服务地址（默认: `localhost:9200`）
- `{nacos-host}:{nacos-port}` - Nacos服务地址（默认: `localhost:8848`）
- `{nacos-grpc-port}` - Nacos gRPC端口（默认: `9848`）
- `{kibana-host}:{kibana-port}` - Kibana服务地址（默认: `localhost:5601`）

> **重要提示**: 请根据实际部署环境替换这些占位符为真实的服务器地址和端口号。

## 🚀 快速开始

### 方式一：Docker Compose 部署（推荐）

1. **克隆项目**
```bash
git clone <repository-url>
cd agentmemory
```

2. **配置端口（可选）**
```bash
# 如果需要使用非默认端口(6023)，请直接修改 config/config.yaml 中的 app.port 配置
```

3. **启动服务**
```bash
# 启动所有服务 (包含 Nacos 3.0)
docker-compose up -d

# 启动包含Kibana的完整服务
docker-compose --profile optional up -d

# 使用自定义环境变量启动
docker-compose --env-file docker.env up -d
```

4. **验证服务**
```bash
# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs agentmemory

# 运行Nacos 3.0兼容性测试
python test_nacos_v3_compatibility.py
```

### 方式二：本地开发部署

1. **安装依赖**
```bash
pip install -r requirements.txt
```

2. **配置环境**
   - 确保Elasticsearch运行在指定地址（例如: `{es-host}:{es-port}`）
   - 确保Nacos 3.0运行在指定地址（例如: `{nacos-host}:{nacos-port}` (HTTP) 和 `{nacos-host}:{nacos-grpc-port}` (gRPC)）
   - 修改 `config/config.yaml` 中的相关配置

3. **启动应用**
```bash
# 使用启动脚本 (推荐)
python start.py

# 或直接启动
python -m uvicorn app.main:app --host 0.0.0.0 --port 6023 --reload
```

### 访问服务

- **API文档**: http://{your-host}:{your-port}/docs
- **应用信息**: http://{your-host}:{your-port}/info
- **健康检查**: http://{your-host}:{your-port}/health
- **Elasticsearch**: http://{es-host}:{es-port}
- **Nacos控制台**: http://{nacos-host}:{nacos-port}/nacos (用户名/密码: nacos/nacos)
- **Kibana** (可选): http://{kibana-host}:{kibana-port}

## 📖 API 使用说明

### 接口基础信息

- **基础URL**: `http://{your-host}:{your-port}/agentmemory/v1`
- **Content-Type**: `application/json`
- **响应格式**: JSON

> **注意**: 请将 `{your-host}` 和 `{your-port}` 替换为实际的服务器地址和端口号

### 1. 添加记忆 - POST /agentmemory/v1/add

**功能描述**: 向智能体记忆库中添加新的记忆记录

**请求参数**:
```json
{
  "agent_id": "string",           // 必填，智能体ID，1-100字符
  "user_id": "string",            // 必填，用户ID，1-100字符
  "category": "string",           // 必填，记忆类别，1-50字符
  "user_question": "string",      // 必填，用户问题，1-2000字符
  "question_reply": "string",     // 可选，问题回复，最多5000字符
  "question_time": "ISO8601"      // 可选，问题时间，默认为当前时间
}
```

**请求示例**:
```bash
curl -X POST "http://{your-host}:{your-port}/agentmemory/v1/add" \
  -H "Content-Type: application/json" \
  -d '{
    "agent_id": "agent_001",
    "user_id": "user_001",
    "category": "conversation",
    "user_question": "今天天气怎么样？",
    "question_reply": "今天天气晴朗，温度适宜。",
    "question_time": "2025-06-15T10:30:00"
  }'
```

**返回参数**:
```json
{
  "success": true,                // 操作是否成功
  "message": "记忆添加成功",        // 响应消息
  "timestamp": "2025-06-15T10:30:00", // 响应时间
  "data": {
    "memory_id": "string",        // 生成的记忆ID
    "agent_id": "agent_001",      // 智能体ID
    "user_id": "user_001",        // 用户ID
    "category": "conversation"    // 记忆类别
  }
}
```

### 2. 搜索记忆 - POST /agentmemory/v1/search

**功能描述**: 根据条件搜索智能体记忆，支持向量相似度检索和混合过滤

**请求参数**:
```json
{
  "agent_id": "string",           // 可选，智能体ID
  "user_id": "string",            // 可选，用户ID
  "category": "string",           // 可选，记忆类别
  "user_question": "string",      // 可选，用户问题（用于向量检索）
  "similarity_threshold": 0.7,    // 可选，相似度阈值，0.0-1.0，默认0.7
  "start_time": "ISO8601",        // 可选，开始时间
  "end_time": "ISO8601",          // 可选，结束时间
  "sort_by": "similarity_desc",   // 可选，排序方式
  "limit": 10                     // 可选，返回数量，1-100，默认10
}
```

**排序方式枚举**:
- `similarity_desc`: 相似度降序 (默认)
- `similarity_asc`: 相似度升序
- `time_desc`: 时间降序
- `time_asc`: 时间升序

**请求示例**:
```bash
curl -X POST "http://{your-host}:{your-port}/agentmemory/v1/search" \
  -H "Content-Type: application/json" \
  -d '{
    "agent_id": "agent_001",
    "user_id": "user_001",
    "user_question": "天气情况",
    "similarity_threshold": 0.7,
    "sort_by": "similarity_desc",
    "limit": 10
  }'
```

**返回参数**:
```json
{
  "success": true,
  "message": "记忆搜索成功",
  "timestamp": "2025-06-15T10:30:00",
  "data": {
    "total": 5,                     // 总记录数
    "memories": [                   // 记忆列表
      {
        "id": "memory_id_1",        // 记忆ID
        "agent_id": "agent_001",    // 智能体ID
        "user_id": "user_001",      // 用户ID
        "category": "conversation", // 记忆类别
        "user_question": "今天天气怎么样？", // 用户问题
        "question_reply": "今天天气晴朗", // 问题回复
        "question_time": "2025-06-15T10:30:00", // 问题时间
        "similarity_score": 0.85,   // 相似度分数（0.0-1.0）
        "created_at": "2025-06-15T10:30:00", // 创建时间
        "updated_at": "2025-06-15T10:30:00"  // 更新时间
      }
    ]
  }
}
```

### 3. 清除记忆 - DELETE /clear

**功能描述**: 根据条件清除智能体记忆记录（至少需要指定一个过滤条件）

**请求参数**:
```json
{
  "agent_id": "string",           // 可选，智能体ID
  "user_id": "string",            // 可选，用户ID
  "category": "string",           // 可选，记忆类别
  "start_time": "ISO8601",        // 可选，开始时间
  "end_time": "ISO8601"           // 可选，结束时间
}
```

**请求示例**:
```bash
curl -X DELETE "http://{your-host}:{your-port}/agentmemory/v1/clear" \
  -H "Content-Type: application/json" \
  -d '{
    "agent_id": "agent_001",
    "user_id": "user_001",
    "category": "conversation"
  }'
```

**返回参数**:
```json
{
  "success": true,
  "message": "记忆清除成功，删除了3条记录",
  "timestamp": "2025-06-15T10:30:00",
  "data": {
    "deleted_count": 3              // 删除的记录数
  }
}
```

### 4. 获取记忆详情 - GET /agentmemory/v1/{memory_id}

**功能描述**: 根据记忆ID获取记忆详细信息

**路径参数**:
- `memory_id`: 记忆ID (必填)

**请求示例**:
```bash
curl "http://{your-host}:{your-port}/agentmemory/v1/abc123def456"
```

**返回参数**:
```json
{
  "success": true,
  "message": "找到记忆",
  "timestamp": "2025-06-15T10:30:00",
  "data": {
    "memory_id": "abc123def456",
    "memory": {
      "id": "abc123def456",           // 记忆ID
      "agent_id": "agent_001",        // 智能体ID
      "user_id": "user_001",          // 用户ID
      "category": "conversation",     // 记忆类别
      "user_question": "今天天气怎么样？", // 用户问题
      "question_reply": "今天天气晴朗", // 问题回复
      "question_time": "2025-06-15T10:30:00", // 问题时间
      "similarity_score": null,       // 相似度分数（仅搜索时有值）
      "created_at": "2025-06-15T10:30:00", // 创建时间
      "updated_at": "2025-06-15T10:30:00"  // 更新时间
    }
  }
}
```

### 5. 搜索相似记忆 - GET /agentmemory/v1/similar/{agent_id}/{user_id}

**功能描述**: 根据问题搜索相似的记忆记录（简化版语义检索接口）

**路径参数**:
- `agent_id`: 智能体ID (必填)
- `user_id`: 用户ID (必填)

**查询参数**:
- `question`: 查询问题 (必填)
- `similarity_threshold`: 相似度阈值，0.0-1.0 (可选)
- `limit`: 返回数量限制，1-100，默认10 (可选)
- `category`: 记忆类别 (可选)

**请求示例**:
```bash
curl "http://{your-host}:{your-port}/agentmemory/v1/similar/agent_001/user_001?question=天气&similarity_threshold=0.8&limit=5&category=conversation"
```

**返回参数**:
```json
{
  "success": true,
  "message": "找到相似记忆",
  "timestamp": "2025-06-15T10:30:00",
  "data": {
    "total": 1,
    "memories": [
      {
        "id": "memory_id_1",
        "agent_id": "agent_001",
        "user_id": "user_001",
        "category": "conversation",
        "user_question": "今天天气怎么样？",
        "question_reply": "今天天气晴朗",
        "question_time": "2025-06-15T10:30:00",
        "similarity_score": 0.85,
        "created_at": "2025-06-15T10:30:00",
        "updated_at": "2025-06-15T10:30:00"
      }
    ]
  }
}
```

### 6. 获取最近记忆 - GET /agentmemory/v1/recent/{agent_id}/{user_id}

**功能描述**: 获取指定时间范围内的最近记忆

**路径参数**:
- `agent_id`: 智能体ID (必填)
- `user_id`: 用户ID (必填)

**查询参数**:
- `days`: 天数，1-365，默认7 (可选)
- `limit`: 返回数量限制，1-100，默认10 (可选)
- `category`: 记忆类别 (可选)

**请求示例**:
```bash
curl "http://{your-host}:{your-port}/agentmemory/v1/recent/agent_001/user_001?days=7&limit=10&category=conversation"
```

**返回参数**:
```json
{
  "success": true,
  "message": "找到最近记忆",
  "timestamp": "2025-06-15T10:30:00",
  "data": {
    "total": 1,
    "memories": [
      {
        "id": "memory_id_1",
        "agent_id": "agent_001",
        "user_id": "user_001",
        "category": "conversation",
        "user_question": "今天天气怎么样？",
        "question_reply": "今天天气晴朗",
        "question_time": "2025-06-15T10:30:00",
        "similarity_score": null,
        "created_at": "2025-06-15T10:30:00",
        "updated_at": "2025-06-15T10:30:00"
      }
    ]
  }
}
```

### 7. 获取记忆统计 - GET /agentmemory/v1/statistics/{agent_id}/{user_id}

**功能描述**: 获取智能体和用户的记忆统计信息

**路径参数**:
- `agent_id`: 智能体ID (必填)
- `user_id`: 用户ID (必填)

**请求示例**:
```bash
curl "http://{your-host}:{your-port}/agentmemory/v1/statistics/agent_001/user_001"
```

**返回参数**:
```json
{
  "success": true,
  "message": "统计信息获取成功",
  "timestamp": "2025-06-15T10:30:00",
  "data": {
    "total_memories": 150,        // 总记忆数
    "category_stats": {           // 各类别统计
      "conversation": 80,
      "knowledge": 45,
      "experience": 25
    },
    "recent_7days": 12,          // 最近7天记忆数
    "recent_30days": 45          // 最近30天记忆数
  }
}
```

### 8. 健康检查 - GET /health

**功能描述**: 检查应用及记忆服务的健康状态

**请求示例**:
```bash
curl "http://{your-host}:{your-port}/health"
```

**返回参数**:
```json
{
  "status": "healthy",
  "timestamp": "2025-06-01T12:00:00",
  "app": {
    "name": "智能体记忆模块",
    "version": "1.0.0"
  },
  "elasticsearch": {
    "status": "connected",
    "cluster_health": "green"
  },
  "embedding": {
    "status": "ready",
    "model_name": "thenlper/gte-base-zh"
  }
}
```

**错误状态** (HTTP 503):
```json
{
  "status": "unhealthy",
  "timestamp": "2025-06-15T10:30:00",
  "app": {
    "name": "智能体记忆模块",
    "version": "1.0.0"
  },
  "error": "具体错误信息"
}
```

### 错误响应格式

所有接口在发生错误时返回统一格式：

```json
{
  "success": false,
  "message": "错误类型描述（如：请求执行失败、请求参数验证失败、服务内部错误）",
  "detail": "详细异常或错误信息",
  "timestamp": "2025-06-15T10:30:00"
}
```

**常见错误码**:
- `400`: 请求参数错误
- `404`: 资源不存在
- `422`: 请求参数验证失败
- `500`: 服务内部错误

### 使用建议

1. **智能体对话场景**: 推荐使用 `GET /agentmemory/v1/similar/{agent_id}/{user_id}` 快速检索相关记忆
2. **复杂查询场景**: 使用 `POST /agentmemory/v1/search` 进行多条件组合查询
3. **记忆管理场景**: 使用 `POST /agentmemory/v1/add` 和 `DELETE /clear` 管理记忆
4. **监控统计场景**: 使用 `GET /agentmemory/v1/statistics/{agent_id}/{user_id}` 获取统计信息

## ⚙️ 配置说明

### 主要配置文件: `config/config.yaml`

```yaml
# 应用配置
app:
  name: "AgentMemory"
  version: "1.0.0"
  host: "0.0.0.0"
  port: 6023
  debug: false
  # workers: 自动根据CPU核心数计算 (CPU核心数*2，最大8个)，如需自定义可取消注释并设置数值

# Elasticsearch配置
elasticsearch:
  hosts:
    - "http://{es-host}:{es-port}"
  index_name: "agent_memory"
  timeout: 30
  
  # 认证配置 (如果ES启用了安全功能)
  username: ""  # ES用户名，如：elastic
  password: ""  # ES密码
  use_ssl: false  # 是否使用SSL/TLS连接
  verify_certs: true  # 是否验证SSL证书
  ca_certs: ""  # CA证书路径
  client_cert: ""  # 客户端证书路径
  client_key: ""  # 客户端私钥路径

# 向量编码配置
embedding:
  model_name: "thenlper/gte-base-zh"
  device: "cpu"  # 或 "cuda"
  max_length: 512
  dimensions: 768  # gte-base-zh模型输出768维向量

# Nacos 3.0 兼容配置
nacos:
  # 服务器地址 (支持多个地址，逗号分隔)
  server_addresses: "{nacos-host}:{nacos-port}"
  
  # 服务注册配置
  service_name: "agentmemory"
  ip: "127.0.0.1"
  port: 6023
  weight: 1.0
  cluster_name: "DEFAULT"
  group: "DEFAULT_GROUP"
  
  # 命名空间配置
  namespace: ""  # 默认命名空间
  
  # 认证配置 (Nacos 3.0增强安全性)
  username: ""
  password: ""
  
  # 配置中心
  data_id: "agentmemory-config"
  
  # gRPC配置 (Nacos 3.0推荐使用gRPC)
  grpc:
    enabled: true  # 优先使用gRPC协议
    timeout: 5000  # gRPC超时时间(毫秒)
    max_receive_message_length: 104857600  # 100MB
    max_keep_alive_ms: 60000  # 60秒
    initial_window_size: 10485760  # 10MB
    initial_conn_window_size: 10485760  # 10MB
  
  # 健康检查配置
  health_check:
    enabled: true
    interval: 5  # 心跳间隔(秒)
    timeout: 3   # 健康检查超时(秒)
    
  # 容错配置
  failover:
    enabled: true
    retry_times: 3
    retry_interval: 1  # 重试间隔(秒)
```

### 环境变量覆盖

可以通过环境变量覆盖配置文件中的设置：

```bash
# 应用配置
export APP_HOST="0.0.0.0"  # 应用主机

# Elasticsearch配置
export ELASTICSEARCH_HOSTS="http://es-host:9200"
export ELASTICSEARCH_USERNAME="elastic"
export ELASTICSEARCH_PASSWORD="your_password"

# Nacos配置
export NACOS_SERVER_ADDRESSES="nacos-host:8848"

# 向量模型配置
export EMBEDDING_DEVICE="cuda"
```

**重要提示**：如果在Docker环境中修改端口，需要同时更新：
1. `config/config.yaml` 中的 `app.port`
2. `docker-compose.yml` 中的端口映射

确保两处配置保持一致，否则可能导致健康检查失败或服务无法访问。

### Elasticsearch认证配置

如果您的Elasticsearch启用了安全功能，需要配置认证信息：

#### 基础认证 (用户名密码)
```yaml
elasticsearch:
  hosts:
    - "http://es-host:9200"  # 或 "https://es-host:9200" (如使用SSL)
  username: "elastic"
  password: "your_password"
```

#### SSL/TLS配置
```yaml
elasticsearch:
  hosts:
    - "https://es-host:9200"
  username: "elastic"
  password: "your_password"
  use_ssl: true
  verify_certs: true
  ca_certs: "/path/to/ca.crt"  # 可选：CA证书路径
```

#### 客户端证书认证
```yaml
elasticsearch:
  hosts:
    - "https://es-host:9200"
  use_ssl: true
  verify_certs: true
  ca_certs: "/path/to/ca.crt"
  client_cert: "/path/to/client.crt"
  client_key: "/path/to/client.key"
```

#### 环境变量方式
```bash
export ELASTICSEARCH_USERNAME="elastic"
export ELASTICSEARCH_PASSWORD="your_password"
export ELASTICSEARCH_USE_SSL="true"
export ELASTICSEARCH_VERIFY_CERTS="true"
export ELASTICSEARCH_CA_CERTS="/path/to/ca.crt"
```

### 🚀 性能配置优化

#### Workers进程数配置
应用支持多进程部署以提高并发性能，通过 `app.workers` 参数控制：

**配置原则：**
- **CPU密集型负载**：建议设置为CPU核心数
- **I/O密集型负载**：建议设置为CPU核心数的1.5-2倍
- **内存限制环境**：适当减少workers数量以控制内存使用
- **开发调试模式**：自动设置为1，确保热重载功能正常

**配置示例：**
```yaml
app:
  workers: 8  # 根据服务器CPU核心数调整
  debug: false  # 生产环境关闭debug模式
```

**性能测试建议：**
```bash
# 使用不同workers数量进行压测对比
ab -n 1000 -c 50 http://{your-host}:{your-port}/agentmemory/v1/health
```

**监控指标：**
- CPU使用率应保持在70-80%
- 内存使用量应在可控范围内
- 响应时间应满足业务需求

**注意事项：**
- workers数量过多可能导致内存占用过高
- workers数量过少可能无法充分利用CPU资源
- 每个worker进程会独立加载向量模型，需考虑内存容量

## 🗂️ 数据模型

### 记忆类别 (category)
记忆类别现在支持任意字符串类型，支持完全匹配过滤筛选。常用类别示例：
- `conversation`: 对话记忆
- `knowledge`: 知识记忆
- `experience`: 经验记忆
- `preference`: 偏好记忆
- `context`: 上下文记忆

您可以根据业务需求定义自己的类别字符串。

### 记忆文档结构
```json
{
  "agent_id": "智能体ID",
  "user_id": "用户ID",
  "category": "记忆类别",
  "user_question": "用户问题",
  "question_reply": "问题回复",
  "question_time": "问题时间",
  "question_vector": [向量数组],
  "created_at": "创建时间",
  "updated_at": "更新时间"
}
```

## 🔧 运维管理

### 日志管理

日志文件位置：
- 应用日志: `logs/agentmemory.log`
- 错误日志: `logs/error.log`

日志级别配置：
```yaml
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  rotation: "1 day"
  retention: "30 days"
```

### 健康检查

```bash
# 检查服务健康状态
curl "http://{your-host}:{your-port}/health"
```

返回示例：
```json
{
  "status": "healthy",
  "timestamp": "2025-06-01T12:00:00",
  "app": {
    "name": "智能体记忆模块",
    "version": "1.0.0"
  },
  "elasticsearch": {
    "status": "connected",
    "cluster_health": "green"
  },
  "embedding": {
    "status": "ready",
    "model_name": "thenlper/gte-base-zh"
  }
}
```

### 监控指标

应用内置以下监控指标：
- 请求计数和响应时间
- 服务健康状态
- 记忆存储统计
- 向量检索性能

## 🛠️ 开发指南

### 项目结构
```
agentmemory/
├── app/                    # 应用主目录
│   ├── config/            # 配置管理
│   ├── models/            # 数据模型
│   ├── services/          # 业务服务
│   ├── api/               # API接口
│   ├── utils/             # 工具模块
│   └── main.py           # 应用入口
├── config/                # 配置文件
├── docker/               # Docker相关
├── requirements.txt      # Python依赖
└── README.md            # 项目文档
```

### 本地开发

1. **设置开发环境**
```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装开发依赖
pip install -r requirements.txt
```

2. **代码规范**
```bash
# 代码格式化
black app/

# 类型检查
mypy app/

# 代码质量检查
flake8 app/
```

3. **运行测试**
```bash
pytest tests/
```

### 添加新功能

1. 在 `app/models/schemas.py` 中定义数据模型
2. 在 `app/services/` 中实现业务逻辑
3. 在 `app/api/` 中添加API接口
4. 更新配置和文档

## 🚨 故障排除

### 常见问题

1. **向量模型下载失败**
   - 检查网络连接
   - 使用镜像源：`export HF_ENDPOINT=https://hf-mirror.com`
   - 手动下载模型到 `models/` 目录

2. **Elasticsearch连接失败**
   - 检查ES服务是否启动：`curl http://{es-host}:{es-port}`
   - 检查配置文件中的ES地址
   - 查看ES容器日志：`docker logs elasticsearch`

3. **Nacos注册失败**
   - 检查Nacos服务状态
   - 验证网络连通性
   - 检查用户名密码配置

4. **内存不足**
   - 调整Docker内存限制
   - 使用CPU模式而非GPU：`device: "cpu"`
   - 减少批处理大小：`batch_size: 16`

### 日志调试

启用调试模式：
```yaml
app:
  debug: true
  log_level: "DEBUG"
```

查看详细日志：
```bash
tail -f logs/agentmemory.log
```

## 📊 性能优化

### 推荐配置

**生产环境配置**：
```yaml
elasticsearch:
  timeout: 60
  max_retries: 5

embedding:
  batch_size: 64
  device: "cuda"  # 如果有GPU

memory:
  max_search_results: 100
```

**硬件要求**：
- CPU: 4核以上
- 内存: 8GB以上
- 存储: SSD推荐
- GPU: 可选，用于加速向量计算

## 📊 更新日志

### v1.0.0 (2025-06-01)
- 初始版本发布
- 支持基础记忆存储和检索功能
- 集成thenlper/gte-base-zh向量模型（768维）
- 支持Elasticsearch存储
- 支持Nacos服务注册
- 提供Docker容器化部署

### v1.0.2 (2025-06-09)
- 更新Dockerfile，增加环境变量
- 新建打包脚本
- 优化 health 接口


## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送到分支：`git push origin feature/new-feature`
5. 创建Pull Request

### 代码贡献规范

- 遵循PEP 8代码风格
- 添加必要的单元测试
- 更新相关文档
- 确保所有测试通过

## 👨‍💻 开发者信息

**项目作者**: 张小龙 <<EMAIL>>  
**创建时间**: 2025-06-01  
**当前版本**: v1.0.0  
**许可证**: Private License  
**技术栈**: Python 3.10+ | FastAPI | Elasticsearch | Nacos 3.0 | Docker  

## 📄 许可证

本项目采用私有许可证，仅供内部使用。未经授权不得用于其他用途。

---

**© 2025 MarketingForce. All rights reserved.**