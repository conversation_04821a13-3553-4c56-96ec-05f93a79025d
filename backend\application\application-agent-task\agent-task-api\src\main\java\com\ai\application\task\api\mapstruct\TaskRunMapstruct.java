package com.ai.application.task.api.mapstruct;
import com.ai.application.task.api.entity.TaskRun;
import com.ai.application.task.api.dto.TaskRunDTO;
import com.ai.application.task.api.vo.TaskRunVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 任务执行记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-07
 */

@Mapper(componentModel = "spring")
public interface TaskRunMapstruct {

    TaskRun toEntity(TaskRunDTO dto);
    List<TaskRun> toEntityList(List<TaskRunDTO> dtolist);
    TaskRunVO toVo(TaskRun entity);
    List<TaskRunVO> toVoList(List<TaskRun> entities);
}
