package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.dto.AgentRunWorkflowListDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ai.application.agent.base.mapper.AgentRunWorkflowMapper;
import com.ai.application.agent.base.api.entity.AgentRunWorkflow;
import com.ai.application.agent.base.service.IAgentRunWorkflowService;
import com.ai.application.agent.base.api.dto.AgentRunWorkflowDTO;
import com.ai.application.agent.base.api.vo.AgentRunWorkflowVO;
import com.ai.application.agent.base.api.mapstruct.AgentRunWorkflowMapstruct;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Date;

/**
 * 智能体工作流执行记录表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
public class AgentRunWorkflowServiceImpl implements IAgentRunWorkflowService {

    @Resource
    private AgentRunWorkflowMapper agentRunWorkflowMapper;

    @Resource
    private AgentRunWorkflowMapstruct agentRunWorkflowMapstruct;

    @Transactional(readOnly = true)
    @Override
    public List<AgentRunWorkflowVO> list(AgentRunWorkflowListDTO queryDto) {
        LambdaQueryWrapper<AgentRunWorkflow> queryWrapper = this.buildQuery(queryDto);
        return agentRunWorkflowMapstruct.toVoList(this.agentRunWorkflowMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(AgentRunWorkflowDTO dto) {
        dto.setRunId(null);
        AgentRunWorkflow entity = agentRunWorkflowMapstruct.toEntity(dto);
        entity.setCreateTime(new Date());

        agentRunWorkflowMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AgentRunWorkflowDTO dto) {
        BusinessAssertUtil.notNull(dto.getWorkflowRunId(), "RunId不能为空");

        AgentRunWorkflow entity = agentRunWorkflowMapper.selectById(dto.getWorkflowRunId());
        BusinessAssertUtil.notNull(entity, "找不到ID为 " + dto.getWorkflowRunId() + " 的记录");

        AgentRunWorkflow entityList = agentRunWorkflowMapstruct.toEntity(dto);
        entityList.setUpdateTime(new Date());
        agentRunWorkflowMapper.updateById(entityList);
    }

    @Transactional(readOnly = true)
    @Override
    public AgentRunWorkflowVO get(Integer id) {
        BusinessAssertUtil.notNull(id, "ID不能为空");

        AgentRunWorkflow entity = agentRunWorkflowMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到ID为 " + id + " 的记录");

        return agentRunWorkflowMapstruct.toVo(entity);
    }

    private LambdaQueryWrapper<AgentRunWorkflow> buildQuery(AgentRunWorkflowListDTO queryDto) {
        LambdaQueryWrapper<AgentRunWorkflow> queryWrapper = new LambdaQueryWrapper<>();
        return queryWrapper;
    }
}