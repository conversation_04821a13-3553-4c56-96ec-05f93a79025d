package com.ai.application.agent.base.api.feign.fallback;

import com.ai.application.agent.base.api.dto.query.AgentPageDTO;
import com.ai.application.agent.base.api.dto.query.AgentStatDetailQueryDTO;
import com.ai.application.agent.base.api.feign.IAgentClient;
import com.ai.application.agent.base.api.vo.AgentDetailVO;
import com.ai.application.agent.base.api.vo.AgentPageVO;
import com.ai.application.agent.base.api.vo.AgentStatDetailVO;
import com.ai.application.agent.base.api.vo.AgentVO;
import com.ai.framework.core.vo.ResultVo;
import com.github.pagehelper.PageInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public class IAgentClientFallback implements IAgentClient {

    @Override
    public ResultVo<List<AgentStatDetailVO>> queryAgentByModelId(@RequestBody @Validated AgentStatDetailQueryDTO dto){
        return ResultVo.fail("查询失败");
    }

    @Override
    public ResultVo<AgentVO> getAgentBySn(String agentSn) {
        return ResultVo.fail("查询失败");
    }

    @Override
    public ResultVo<List<Integer>> queryGrantAgentIdList(Integer pageSource) {
        return ResultVo.fail("查询失败");
    }

    @Override
    public ResultVo<PageInfo<AgentPageVO>> page(AgentPageDTO dto) {
        return ResultVo.fail("查询失败");
    }
}