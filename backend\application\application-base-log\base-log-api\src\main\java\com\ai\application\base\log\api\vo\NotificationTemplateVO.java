package com.ai.application.base.log.api.vo;

import com.ai.application.base.log.api.bo.TemplateConfigBO;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 通知模板配置表
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@Schema(name = "")
public class NotificationTemplateVO {
    /**
     * 通知模板id
     */
    @Schema(description = "通知模板id")
    private Integer ntplId;

    /**
     * 分类:10-异常,20-一般通知,30-成功
     */
    @Schema(description = "分类:10-异常,20-一般通知,30-成功")
    private Integer ntplType;

    /**
     * 模板名称
     */
    @Schema(description = "模板名称")
    private String ntplName;

    /**
     * 模板内容
     */
    @Schema(description = "模板内容")
    private String ntplContent;

    /**
     * 模板状态:1-启用,0-禁用,-1-删除
     */
    @Schema(description = "模板状态:1-启用,0-禁用,-1-删除")
    private Integer ntplStatus;

    /**
     * 模板配置参数
     */
    @Schema(description = "模板配置参数")
    private String ntplConfig;

    @Schema(description = "模板配置参数-对象")
    private TemplateConfigBO templateConfig;
}