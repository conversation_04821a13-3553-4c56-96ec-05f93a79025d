package com.ai.application.app.controller;

import com.github.pagehelper.PageInfo;
import com.ai.application.app.service.IAppService;
import com.ai.application.app.api.dto.AppDTO;
import com.ai.application.app.api.dto.query.AppQueryDTO;
import com.ai.application.app.api.vo.AppVO;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 应用表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
@Tag(name = "应用表", description = "应用表相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1")
public class AppController {

    @Resource
    private IAppService  appService;

    /**
     * 分页查询
     *
     * @param queryDto
     * @return
     */
    @Operation(summary = "应用表 分页查询", description = "查询所有应用表 信息")
    @RequestMapping(value = "/page", method = {RequestMethod.GET})
    public ResultVo<PageInfo<AppVO>> page(AppQueryDTO queryDto){
        return ResultVo.data(appService.page(queryDto));
    }

    /**
     * 保存
     *
     * @param dto
     * @return
     */
    @Operation(summary = "应用表新增")
    @PostMapping("/save")
    public ResultVo<Void> save(@Validated @RequestBody AppDTO dto){
        appService.save(dto);
        return ResultVo.success("保存成功");
    }

    /**
     * 修改
     *
     * @param dto
     * @return
     */
    @Operation(summary = "应用表修改")
    @PostMapping(value = "/update")
    public ResultVo<Void> update(@Validated @RequestBody AppDTO dto){
        appService.update(dto);
        return ResultVo.success("修改成功");
    }

    /**
     * 查询详情
     * @param appId
     * @return
     */
    @Operation(summary = "应用表 查询详情", description = "根据ID应用表 信息")
    @GetMapping("/get/{appId}")
    public ResultVo<AppVO> get(@PathVariable("appId") Integer appId){
        return ResultVo.data(appService.get(appId));
    }
}