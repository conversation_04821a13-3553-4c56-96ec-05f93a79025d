package com.ai.application.admin.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 应用用户表
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(name = "用户详情VO")
public class AdminUserDetailVO {
    /**
     * 用户sn
     */
    @Schema(description = "用户sn")
    private String userSn;
    /**
     * 登录账号
     */
    @Schema(description = "登录账号")
    private String userAccount;
    /**
     * 用户名称
     */
    @Schema(description = "用户名称")
    private String userName;

    /**
     * 用户状态 1-启用 0-禁用 -1-删除
     */
    @Schema(description = "用户状态 1-启用 0-禁用 -1-删除")
    private Integer userStatus;
}