package com.ai.application.base.log.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 通知模板配置表
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@Schema(name = "通知模板配置表DTO")
public class NotificationTemplateAddDTO {

    /**
     * 模板名称
     */
    @Schema(description = "模板名称")
    @NotBlank(message = "模板名称不能为空")
    @Length(max = 20, message = "模板名称长度不能超过20个字符")
    private String ntplName;

    /**
     * 模板内容
     */
    @Schema(description = "模板内容")
    @NotBlank(message = "模板内容不能为空")
    @Length(max = 200, message = "模板内容长度不能超过200个字符")
    private String ntplContent;

    /**
     * 模板配置参数
     */
    @Schema(description = "模板配置参数")
    @Valid
    private TemplateConfigDTO templateConfig;


}