package com.ai.application.agent.run.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 智能表格写入结果DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "SmartTableWriteResultDTO")
public class SmartTableWriteResultDTO {

    /**
     * 是否成功
     */
    @Schema(description = "是否成功")
    private Boolean success;

    /**
     * 输出结果
     */
    @Schema(description = "输出结果")
    private List<String> output;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 写入的表格编号
     */
    @Schema(description = "写入的表格编号")
    private String tableSn;

    /**
     * 写入的行数
     */
    @Schema(description = "写入的行数")
    private Integer rowCount;
}
