package com.ai.application.agent.base.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.experimental.Accessors;

/**
 * <p>
 * 智能体关联字典表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("agent_use_dict")
public class AgentUseDict implements Serializable {
    @Schema(description = "")
    @TableId(type = IdType.AUTO)
    private Integer adId;

    /**
    * 状态 0失效 1有效
    */
    @Schema(description = "状态 0失效 1有效")
    private Integer adStatus;

    /**
    * 智能体id
    */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
    * 智能体版本id
    */
    @Schema(description = "智能体版本id")
    private Integer versionId;

    /**
    * 字典id
    */
    @Schema(description = "字典id")
    private Integer dictId;

    /**
    * 额外属性
    */
    @Schema(description = "额外属性")
    private String dictExtend;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}