package com.ai.application.agent.run.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 文档知识检索请求 DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
@Schema(name = "DocumentSearchRequestDTO")
public class DocumentSearchRequestDTO {

    /**
     * 检索类型 embedding/keyword/text/expand/all
     */
    @Schema(description = "检索类型")
    private String type;

    /**
     * 检索模式 content/expand/all
     */
    @Schema(description = "检索模式")
    private String searchModel;

    /**
     * 知识库编号
     */
    @Schema(description = "知识库编号")
    private String knowledgeInventorySn;

    /**
     * 知识库编号列表
     */
    @Schema(description = "知识库编号列表")
    private List<String> knowledgeInventorySnList;

    /**
     * 知识编号列表
     */
    @Schema(description = "知识编号列表")
    private List<String> knowledgeSn;

    /**
     * 知识类型
     */
    @Schema(description = "知识类型")
    private String knowledgeType;

    /**
     * 检索内容
     */
    @Schema(description = "检索内容")
    private String searchContent;

    /**
     * 检索知识内容
     */
    @Schema(description = "检索知识内容")
    private String searchKnowledgeContent;

    /**
     * TopK 数量
     */
    @Schema(description = "TopK 数量")
    private Integer topK;

    /**
     * 语义检索数量
     */
    @Schema(description = "语义检索数量")
    private Integer embedding;

    /**
     * 关键词检索数量
     */
    @Schema(description = "关键词检索数量")
    private Integer keyword;

    /**
     * 文本检索数量
     */
    @Schema(description = "文本检索数量")
    private Integer text;

    /**
     * 检索条件
     */
    @Schema(description = "检索条件")
    private List<SearchConditionDTO> conditions;

    /**
     * 扩展检索参数
     */
    @Schema(description = "扩展检索参数")
    private ExpandSearchParamsDTO expandParams;

    /**
     * 手动检索知识
     */
    @Schema(description = "手动检索知识")
    private Object searchKnowledge;

    /**
     * 检索条件 DTO
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @SuperBuilder
    @Schema(name = "SearchConditionDTO")
    public static class SearchConditionDTO {

        /**
         * 目标
         */
        @Schema(description = "目标")
        private String target;

        /**
         * 条件值
         */
        @Schema(description = "条件值")
        private String value;

        /**
         * 操作符
         */
        @Schema(description = "操作符")
        private String operator;
    }

    /**
     * 扩展检索参数 DTO
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @SuperBuilder
    @Schema(name = "ExpandSearchParamsDTO")
    public static class ExpandSearchParamsDTO {

        /**
         * 片段类型
         */
        @Schema(description = "片段类型")
        private String fragmentType;

        /**
         * 长度
         */
        @Schema(description = "长度")
        private Integer length;

        /**
         * 向前扩展
         */
        @Schema(description = "向前扩展")
        private Integer forward;

        /**
         * 向后扩展
         */
        @Schema(description = "向后扩展")
        private Integer backward;

        /**
         * 扩展内容
         */
        @Schema(description = "扩展内容")
        private Object expand;
    }
}
