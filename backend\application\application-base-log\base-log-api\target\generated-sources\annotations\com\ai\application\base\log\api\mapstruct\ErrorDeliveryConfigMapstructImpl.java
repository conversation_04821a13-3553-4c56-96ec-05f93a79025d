package com.ai.application.base.log.api.mapstruct;

import com.ai.application.base.log.api.dto.ErrorDeliveryConfigDTO;
import com.ai.application.base.log.api.dto.ErrorDeliveryConfigSaveDTO;
import com.ai.application.base.log.api.entity.ErrorDeliveryConfig;
import com.ai.application.base.log.api.vo.ErrorDeliveryConfigVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-13T10:32:29+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class ErrorDeliveryConfigMapstructImpl implements ErrorDeliveryConfigMapstruct {

    @Override
    public ErrorDeliveryConfig toEntity(ErrorDeliveryConfigDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ErrorDeliveryConfig errorDeliveryConfig = new ErrorDeliveryConfig();

        errorDeliveryConfig.setConfigId( dto.getConfigId() );
        errorDeliveryConfig.setConfigStatus( dto.getConfigStatus() );
        errorDeliveryConfig.setDeliveryLevel( dto.getDeliveryLevel() );
        errorDeliveryConfig.setDeliveryType( dto.getDeliveryType() );
        errorDeliveryConfig.setDeliveryEnabled( dto.getDeliveryEnabled() );
        errorDeliveryConfig.setDeliveryRecipient( dto.getDeliveryRecipient() );
        errorDeliveryConfig.setDeliveryTemplate( dto.getDeliveryTemplate() );
        errorDeliveryConfig.setRetryEnabled( dto.getRetryEnabled() );
        errorDeliveryConfig.setMaxRetryCount( dto.getMaxRetryCount() );
        errorDeliveryConfig.setRetryInterval( dto.getRetryInterval() );
        errorDeliveryConfig.setTenantId( dto.getTenantId() );
        errorDeliveryConfig.setCreateTime( dto.getCreateTime() );
        errorDeliveryConfig.setUpdateTime( dto.getUpdateTime() );

        return errorDeliveryConfig;
    }

    @Override
    public ErrorDeliveryConfig toEntity(ErrorDeliveryConfigSaveDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ErrorDeliveryConfig errorDeliveryConfig = new ErrorDeliveryConfig();

        errorDeliveryConfig.setDeliveryLevel( dto.getDeliveryLevel() );
        errorDeliveryConfig.setDeliveryType( dto.getDeliveryType() );
        errorDeliveryConfig.setDeliveryRecipient( dto.getDeliveryRecipient() );
        errorDeliveryConfig.setDeliveryTemplate( dto.getDeliveryTemplate() );

        return errorDeliveryConfig;
    }

    @Override
    public List<ErrorDeliveryConfig> toEntityList(List<ErrorDeliveryConfigDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<ErrorDeliveryConfig> list = new ArrayList<ErrorDeliveryConfig>( dtolist.size() );
        for ( ErrorDeliveryConfigDTO errorDeliveryConfigDTO : dtolist ) {
            list.add( toEntity( errorDeliveryConfigDTO ) );
        }

        return list;
    }

    @Override
    public ErrorDeliveryConfigVO toVo(ErrorDeliveryConfig entity) {
        if ( entity == null ) {
            return null;
        }

        ErrorDeliveryConfigVO errorDeliveryConfigVO = new ErrorDeliveryConfigVO();

        errorDeliveryConfigVO.setConfigId( entity.getConfigId() );
        errorDeliveryConfigVO.setConfigStatus( entity.getConfigStatus() );
        errorDeliveryConfigVO.setDeliveryLevel( entity.getDeliveryLevel() );
        errorDeliveryConfigVO.setDeliveryType( entity.getDeliveryType() );
        errorDeliveryConfigVO.setDeliveryEnabled( entity.getDeliveryEnabled() );
        errorDeliveryConfigVO.setDeliveryRecipient( entity.getDeliveryRecipient() );
        errorDeliveryConfigVO.setDeliveryTemplate( entity.getDeliveryTemplate() );
        errorDeliveryConfigVO.setRetryEnabled( entity.getRetryEnabled() );
        errorDeliveryConfigVO.setMaxRetryCount( entity.getMaxRetryCount() );
        errorDeliveryConfigVO.setRetryInterval( entity.getRetryInterval() );
        errorDeliveryConfigVO.setTenantId( entity.getTenantId() );
        errorDeliveryConfigVO.setCreateTime( entity.getCreateTime() );
        errorDeliveryConfigVO.setUpdateTime( entity.getUpdateTime() );

        return errorDeliveryConfigVO;
    }

    @Override
    public List<ErrorDeliveryConfigVO> toVoList(List<ErrorDeliveryConfig> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ErrorDeliveryConfigVO> list = new ArrayList<ErrorDeliveryConfigVO>( entities.size() );
        for ( ErrorDeliveryConfig errorDeliveryConfig : entities ) {
            list.add( toVo( errorDeliveryConfig ) );
        }

        return list;
    }
}
