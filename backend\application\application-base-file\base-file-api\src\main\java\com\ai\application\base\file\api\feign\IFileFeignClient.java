package com.ai.application.base.file.api.feign;

import com.ai.application.base.file.api.dto.*;
import com.ai.application.base.file.api.vo.DownloadBytesVo;
import com.ai.application.base.file.api.vo.DownloadUrlVo;
import com.ai.application.base.file.api.vo.FileMd5Vo;
import com.ai.application.base.file.api.vo.FileUpGtIdVo;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.vo.ResultVo;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@FeignClient(value = ServiceConstant.BASE_FILE, contextId = "IFileFeignClient")
@Component
public interface IFileFeignClient {
    String API_PREFIX = "/v1/operate/feign";


    @PostMapping(API_PREFIX+"/feign/process/files/upload")
    ResultVo<String> processFileUpload(@RequestBody byte[] content,
                                       @RequestParam("fileSn") String fileSn,
                                       @RequestParam("fileType") String fileType,
                                       @RequestParam("fileName") String fileName,
                                       @RequestParam(value = "source",required = false)  String source);


    @PostMapping(API_PREFIX+"/feign/process/files/processFileUploadCompa")
    ResultVo<String> processFileUploadCompa(@RequestBody byte[] content,
                                       @RequestParam("fileSn") String fileSn,
                                       @RequestParam("fileType") String fileType,
                                       @RequestParam("fileName") String fileName,
                                       @RequestParam(value = "source",required = false)  String source,@RequestParam(value = "type",required = false)  String type);




    @PostMapping(API_PREFIX+"/feign/process/files/uploadGtId")
     ResultVo<FileUpGtIdVo> uploadGtId(@RequestBody FileUploadGtIdDto dt);

    @GetMapping(API_PREFIX+"/feign/process/files/downloadBytes")
     ResultVo<DownloadBytesVo> downloadAsBytes(@RequestParam("fileSn") String fileSn);

    @PostMapping(API_PREFIX+"/upload")
     ResultVo<DownloadUrlVo> upload(@RequestParam("file") MultipartFile file, @RequestParam("fileSn") String fileSn,
                                          @RequestParam("fileType") String fileType) throws IOException;

    /**
     * 获取文件下载url
     */
    @GetMapping(API_PREFIX+"/download/url")
     ResultVo<String> getUrl(@RequestParam("fileSn") String fileSn, @RequestParam(value ="type",required = false) String type, @RequestParam(value ="fileName",required = false) String fileName);

    /**
     * 获取当前用户历史上传文件的MD5
     *
     * @return
     */
    @GetMapping(API_PREFIX+"/history/md5")
     ResultVo<FileMd5Vo> historyMd5();


    @PostMapping(API_PREFIX+"/upload/notice")
     ResultVo<String> notice(@RequestBody @Valid FileUploadNoticeDto uploadInfo);

    @DeleteMapping(API_PREFIX+"/deleteFile")
     ResultVo<String> deleteFile(@RequestBody @Valid FileDeleteDto fileDeleteDto);

    @GetMapping(API_PREFIX+"getDocFileByFileSn/{fileSn}")
     ResultVo<DocFileDto> getDocFileByFileSn(@PathVariable("fileSn") String fileSn);

    @GetMapping(API_PREFIX+"getDocFileById/{fileId}")
     ResultVo<DocFileDto> getDocFileById(@PathVariable("fileId") Integer fileId);

    @PostMapping(API_PREFIX+"/batchFileSave")
    void batchFileSave(@RequestBody AppFileBatchDto dto);

    @PostMapping(API_PREFIX+"/getDocFileByFileIds")
    ResultVo<List<DocFileDto>>   getDocFileByFileIds(@RequestBody List<Integer> fileIds);

    @PostMapping(API_PREFIX+"/getBatchFileByFileSn")
    ResultVo<List<DocFileDto>>   getBatchFileByFileSn(@RequestBody List<String> fileSns);


    @DeleteMapping(API_PREFIX+"/batchDelete")
    ResultVo<String> deleteFiles(@RequestBody List<Integer> fileIds);

}