package com.ai.application.agent.base.mapper;

import com.ai.application.agent.base.api.dto.AgentStatDTO;
import com.ai.application.agent.base.api.entity.AgentRunWorkflow;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 智能体工作流执行记录表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Mapper
public interface AgentRunWorkflowMapper extends BaseMapper<AgentRunWorkflow> {

    Integer getWorkflowCount(AgentStatDTO dto);

}
