package com.ai.application.agent.run.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 文档检索器DTO
 */
@Data
public class DocumentRetrieverDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 8708564122394889998L;

    private String originQuery;

    private List<String> datasetIds;

    private String retrieveMode;

    private String modelSn;

    private Integer topK;
}
