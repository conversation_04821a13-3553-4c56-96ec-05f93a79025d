package com.ai.application.agent.base.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Schema(name = "使用统计VO")
@Data
public class AgentUseTotalVO {
    @Schema(description = "消息总数趋势")
    private List<UseTotal> message;

    @Schema(description = "平均会话深度趋势")
    private List<UseTotal> session;

    @Schema(description = "token消耗趋势")
    private List<UseTotal> token;

    @Schema(description = "X轴与Y轴数据")
    @Data
    public static class UseTotal {
        @Schema(description = "X轴数据")
        private String label;

        @Schema(description = "Y轴数据")
        private BigDecimal value;
    }
}
