package com.ai.application.knowledge.table.feign;

import com.ai.application.knowledge.table.dto.DeleteBySnFeignDto;
import com.ai.application.knowledge.table.dto.EmbeddingFeignDto;
import com.ai.application.knowledge.table.dto.FileByIdFeignDto;
import com.ai.application.knowledge.table.service.IKnowledgeBaseService;
import com.ai.application.knowledge.table.service.IKnowledgeFileService;
import com.ai.application.knowledge.table.vo.BaseDetailVo;
import com.ai.application.knowledge.table.vo.FileDetailVo;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@Slf4j
public class DocFeignClient implements IDocFeignClient {

    private final IKnowledgeBaseService knowledgeBaseService;

    private final IKnowledgeFileService knowledgeFileService;

    public DocFeignClient(IKnowledgeBaseService knowledgeBaseService, IKnowledgeFileService knowledgeFileService) {
        this.knowledgeBaseService = knowledgeBaseService;
        this.knowledgeFileService = knowledgeFileService;
    }

    @Operation(summary = "根据知识库ID获取详情")
    @GetMapping("/detailById/{kbId}")
    public ResultVo<BaseDetailVo> detailById(@PathVariable("kbId") Integer kbId) {
        return knowledgeBaseService.detailById(kbId);
    }

    @Operation(summary = "根据知识库sn查询知识库是否存在")
    @GetMapping("/detailById/{kbSn}")
    public ResultVo<BaseDetailVo> detailBySn(@PathVariable("kbSn") String kbSn) {
        return knowledgeBaseService.detail(kbSn);
    }


    @Operation(summary = "根据知识id+知识库sn删除知识")
    @PostMapping("/deleteById")
    public ResultVo<String> deleteById(@Valid @RequestBody DeleteBySnFeignDto dto) {
        return knowledgeFileService.deleteById(dto);
    }

    @Operation(summary = "embedding")
    @PostMapping("/embedding")
    public ResultVo<String> embedding(@Valid @RequestBody EmbeddingFeignDto dto) {
        return knowledgeFileService.embedding(dto);
    }

    @Operation(summary = "根据知识库sn + 知识id")
    @PostMapping("/fileDetailById")
    public ResultVo<FileDetailVo> fileDetailById(@Valid @RequestBody FileByIdFeignDto dto) {
        return knowledgeFileService.fileDetailById(dto);
    }


}
