package com.ai.application.agent.run.executor;

import com.ai.application.agent.run.dto.ProcessChatDTO;
import com.ai.application.agent.run.errors.LlmNodeExecutorError;
import com.ai.application.agent.run.feign.ILlmChatClient;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * LLM 节点执行器单元测试
 */
@ExtendWith(MockitoExtension.class)
class LlmNodeExecutorTest {

    @Mock
    private ILlmChatClient llmChatClient;

    @InjectMocks
    private LlmNodeExecutor llmNodeExecutor;

    private WorkflowContext workflowContext;
    private NodeContext nodeContext;
    private Map<String, Object> nodeDefinition;
    private Map<String, Object> inputParameters;
    private Map<String, Object> outputParameters;

    @BeforeEach
    void setUp() {
        // 初始化工作流上下文
        workflowContext = new WorkflowContext();
        workflowContext.setWorkflowInstanceId(12345L);
        workflowContext.setCurrentNodeKey("llm_node_001");
        workflowContext.setGlobalVars(new HashMap<>());
        workflowContext.getGlobalVars().put("authorization", "Bearer test-token");

        // 初始化节点上下文
        nodeContext = new NodeContext();
        nodeContext.setNodeKey("llm_node_001");
        nodeContext.setStatus(NodeStatus.INIT);
        nodeContext.setOutput(new HashMap<>());

        // 初始化输入参数
        inputParameters = new HashMap<>();
        inputParameters.put("model", "localModel");
        inputParameters.put("prompt", "s z d f g h j");
        inputParameters.put("sysPrompt", "");
        inputParameters.put("temperature", "0.6");

        // 初始化输出参数
        outputParameters = new HashMap<>();
        outputParameters.put("message", "d3ef893c5c");

        // 初始化节点定义
        nodeDefinition = new HashMap<>();
        nodeDefinition.put("inputParameters", inputParameters);
        nodeDefinition.put("outputParameters", outputParameters);
        nodeContext.setNodeDefinition(nodeDefinition);

        // 设置节点上下文
        workflowContext.getNodeContexts().put("llm_node_001", nodeContext);
    }

    @Test
    void testExecuteSuccess() {
        // 准备测试数据
        String expectedReply = "这是大模型的回复：s z d f g h j";
        ResultVo<String> mockResult = ResultVo.data(expectedReply);

        // 模拟 LLM 客户端调用
        when(llmChatClient.chat(any(ProcessChatDTO.class), anyString())).thenReturn(mockResult);

        // 执行测试
        assertDoesNotThrow(() -> llmNodeExecutor.execute(workflowContext));

        // 验证结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        assertEquals(expectedReply, nodeContext.getOutput().get("message"));
        assertEquals(true, nodeContext.getOutput().get("success"));
        assertEquals(expectedReply, workflowContext.getGlobalVars().get("d3ef893c5c"));
        assertNotNull(nodeContext.getEndTime());

        // 验证 LLM 客户端被调用
        verify(llmChatClient, times(1)).chat(any(ProcessChatDTO.class), eq("Bearer test-token"));
    }

    @Test
    void testExecuteWithVariableReplacement() {
        // 准备测试数据 - 使用变量替换
        workflowContext.getGlobalVars().put("userPrompt", "Hello, AI!");
        inputParameters.put("prompt", "$userPrompt");

        String expectedReply = "Hello, this is AI response!";
        ResultVo<String> mockResult = ResultVo.data(expectedReply);

        // 模拟 LLM 客户端调用
        when(llmChatClient.chat(any(ProcessChatDTO.class), anyString())).thenReturn(mockResult);

        // 执行测试
        assertDoesNotThrow(() -> llmNodeExecutor.execute(workflowContext));

        // 验证结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        assertEquals(expectedReply, nodeContext.getOutput().get("message"));

        // 验证参数替换
        verify(llmChatClient).chat(argThat(dto -> 
            "Hello, AI!".equals(dto.getPrompt())), anyString());
    }

    @Test
    void testExecuteWithSystemPrompt() {
        // 准备测试数据 - 包含系统提示词
        inputParameters.put("sysPrompt", "你是一个专业的AI助手");

        String expectedReply = "基于系统提示词的回复";
        ResultVo<String> mockResult = ResultVo.data(expectedReply);

        // 模拟 LLM 客户端调用
        when(llmChatClient.chat(any(ProcessChatDTO.class), anyString())).thenReturn(mockResult);

        // 执行测试
        assertDoesNotThrow(() -> llmNodeExecutor.execute(workflowContext));

        // 验证结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());

        // 验证系统提示词被转换为 JSON 格式
        verify(llmChatClient).chat(argThat(dto -> 
            dto.getSysPrompt().contains("你是一个专业的AI助手")), anyString());
    }

    @Test
    void testExecuteMissingInputParameters() {
        // 准备测试数据 - 缺少输入参数
        nodeDefinition.remove("inputParameters");

        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, 
            () -> llmNodeExecutor.execute(workflowContext));

        assertEquals(LlmNodeExecutorError.LLM_NODE_MISSING_INPUT_PARAMETERS.getCode(), exception.getCode());
        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
        assertNotNull(nodeContext.getErrorMsg());
        assertNotNull(nodeContext.getEndTime());
    }

    @Test
    void testExecuteBlankPrompt() {
        // 准备测试数据 - 空的 prompt
        inputParameters.put("prompt", "");

        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, 
            () -> llmNodeExecutor.execute(workflowContext));

        assertEquals(LlmNodeExecutorError.LLM_PROMPT_IS_BLANK.getCode(), exception.getCode());
        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
    }

    @Test
    void testExecuteBlankModel() {
        // 准备测试数据 - 空的 model
        inputParameters.put("model", "");

        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, 
            () -> llmNodeExecutor.execute(workflowContext));

        assertEquals(LlmNodeExecutorError.LLM_MODEL_IS_BLANK.getCode(), exception.getCode());
        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
    }

    @Test
    void testExecuteLlmCallFailed() {
        // 准备测试数据 - LLM 调用失败
        ResultVo<String> mockResult = ResultVo.fail("LLM service unavailable");

        // 模拟 LLM 客户端调用失败
        when(llmChatClient.chat(any(ProcessChatDTO.class), anyString())).thenReturn(mockResult);

        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, 
            () -> llmNodeExecutor.execute(workflowContext));

        assertEquals(LlmNodeExecutorError.LLM_CALL_FAILED.getCode(), exception.getCode());
        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
    }

    @Test
    void testExecuteLlmResponseNull() {
        // 准备测试数据 - LLM 返回空结果
        ResultVo<String> mockResult = ResultVo.data(null);

        // 模拟 LLM 客户端返回空结果
        when(llmChatClient.chat(any(ProcessChatDTO.class), anyString())).thenReturn(mockResult);

        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, 
            () -> llmNodeExecutor.execute(workflowContext));

        assertEquals(LlmNodeExecutorError.LLM_RESPONSE_IS_NULL.getCode(), exception.getCode());
        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
    }

    @Test
    void testGetType() {
        assertEquals("LLM", llmNodeExecutor.getType());
    }

    // 注意：getParameterValue 是私有方法，这里通过集成测试来验证其功能
    // 在 testExecuteWithVariableReplacement 测试中已经验证了变量替换功能
}
