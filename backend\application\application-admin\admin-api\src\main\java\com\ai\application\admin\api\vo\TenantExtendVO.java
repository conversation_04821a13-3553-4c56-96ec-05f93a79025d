package com.ai.application.admin.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 租户扩展配置表
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@Schema(name = "")
public class TenantExtendVO {
    @Schema(description = "")
    private Integer itemId;
    /**
     * 参数name
     */
    @Schema(description = "参数name")
    private String itemName;
    /**
     * 参数值
     */
    @Schema(description = "参数值")
    private String itemValue;
    /**
     * 记录状态0:失效,1:生效
     */
    @Schema(description = "记录状态0:失效,1:生效")
    private Integer itemStatus;
    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Integer tenantId;
    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}