package com.ai.application.knowledge.table.service;

import com.ai.application.knowledge.table.dto.*;
import com.ai.application.knowledge.table.entity.KnowledgeFile;
import com.ai.application.knowledge.table.vo.FileChunksDetailVo;
import com.ai.application.knowledge.table.vo.FileChunksListVo;
import com.ai.application.knowledge.table.vo.FileDetailVo;
import com.ai.application.knowledge.table.vo.FileListVo;
import com.ai.framework.core.vo.ResultVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * <p>
 * 知识库文件表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public interface IKnowledgeFileService extends IService<KnowledgeFile> {

    ResultVo<String> batchCreate(FileCreateDto dto);

    ResultVo<String> update(FileUpdateDto dto);

    ResultVo<String> batchDelete(FileBatchDeleteDto dto);

    ResultVo<FileDetailVo> detail(FileDetailDto dto);

    ResultVo<PageInfo<FileListVo>> list(FileListDto dto);

    ResultVo<String> retryEmbedding(FileRetryEmbeddingDto dto);

    ResultVo<List<KnowledgeFile>> getStatus(FileStatusDto dto);

    ResultVo<FileChunksListVo> listChunks(FileChunksListDto dto);

    ResultVo<String> updateChunks(FileChunksUpdateDto dto);

    ResultVo<FileChunksListVo.ContentNode> detailChunks(FileChunksDetailVo dto);

    void exportChunks(FileChunksListDto dto, HttpServletResponse response);

    ResultVo<String> deleteById(DeleteBySnFeignDto dto);

    ResultVo<FileDetailVo> fileDetailById(FileByIdFeignDto dto);

    ResultVo<String> embedding(EmbeddingFeignDto dto);
}
