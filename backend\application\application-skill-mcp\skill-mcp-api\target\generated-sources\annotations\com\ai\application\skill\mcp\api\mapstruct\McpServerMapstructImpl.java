package com.ai.application.skill.mcp.api.mapstruct;

import com.ai.application.skill.mcp.api.dto.McpServerDTO;
import com.ai.application.skill.mcp.api.entity.McpServer;
import com.ai.application.skill.mcp.api.vo.McpServerVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-14T11:01:16+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class McpServerMapstructImpl implements McpServerMapstruct {

    @Override
    public McpServer toEntity(McpServerDTO dto) {
        if ( dto == null ) {
            return null;
        }

        McpServer mcpServer = new McpServer();

        mcpServer.setServerId( dto.getServerId() );
        mcpServer.setServerSn( dto.getServerSn() );
        mcpServer.setServerName( dto.getServerName() );
        mcpServer.setServerDesc( dto.getServerDesc() );
        mcpServer.setServerType( dto.getServerType() );
        mcpServer.setConnectionConfig( dto.getConnectionConfig() );
        mcpServer.setServerMetadata( dto.getServerMetadata() );
        mcpServer.setServerStatus( dto.getServerStatus() );

        return mcpServer;
    }

    @Override
    public List<McpServer> toEntityList(List<McpServerDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<McpServer> list = new ArrayList<McpServer>( dtolist.size() );
        for ( McpServerDTO mcpServerDTO : dtolist ) {
            list.add( toEntity( mcpServerDTO ) );
        }

        return list;
    }

    @Override
    public McpServerVO toVo(McpServer entity) {
        if ( entity == null ) {
            return null;
        }

        McpServerVO mcpServerVO = new McpServerVO();

        mcpServerVO.setServerId( entity.getServerId() );
        mcpServerVO.setServerSn( entity.getServerSn() );
        mcpServerVO.setServerName( entity.getServerName() );
        mcpServerVO.setServerDesc( entity.getServerDesc() );
        mcpServerVO.setServerType( entity.getServerType() );
        mcpServerVO.setServerVersion( entity.getServerVersion() );
        mcpServerVO.setServerUrl( entity.getServerUrl() );
        mcpServerVO.setServerPort( entity.getServerPort() );
        mcpServerVO.setTransportType( entity.getTransportType() );
        mcpServerVO.setAuthType( entity.getAuthType() );
        mcpServerVO.setAuthConfig( entity.getAuthConfig() );
        mcpServerVO.setConnectionConfig( entity.getConnectionConfig() );
        mcpServerVO.setServerCapabilities( entity.getServerCapabilities() );
        mcpServerVO.setServerMetadata( entity.getServerMetadata() );
        mcpServerVO.setServerStatus( entity.getServerStatus() );
        mcpServerVO.setHealthCheckUrl( entity.getHealthCheckUrl() );
        mcpServerVO.setLastCheckTime( entity.getLastCheckTime() );
        mcpServerVO.setCheckStatus( entity.getCheckStatus() );
        mcpServerVO.setTenantId( entity.getTenantId() );
        mcpServerVO.setCreateUserId( entity.getCreateUserId() );
        mcpServerVO.setUpdateUserId( entity.getUpdateUserId() );
        mcpServerVO.setCreateTime( entity.getCreateTime() );
        mcpServerVO.setUpdateTime( entity.getUpdateTime() );
        mcpServerVO.setSupplierId( entity.getSupplierId() );

        return mcpServerVO;
    }

    @Override
    public List<McpServerVO> toVoList(List<McpServer> entities) {
        if ( entities == null ) {
            return null;
        }

        List<McpServerVO> list = new ArrayList<McpServerVO>( entities.size() );
        for ( McpServer mcpServer : entities ) {
            list.add( toVo( mcpServer ) );
        }

        return list;
    }
}
