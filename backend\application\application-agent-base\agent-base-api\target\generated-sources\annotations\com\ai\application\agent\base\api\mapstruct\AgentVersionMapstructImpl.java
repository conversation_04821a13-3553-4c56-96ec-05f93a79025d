package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.entity.AgentVersion;
import com.ai.application.agent.base.api.vo.AgentVersionListVO;
import com.ai.application.agent.base.api.vo.AgentVersionVO;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-12T18:39:57+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentVersionMapstructImpl implements AgentVersionMapstruct {

    @Override
    public AgentVersionListVO toListVo(AgentVersion agentVersion) {
        if ( agentVersion == null ) {
            return null;
        }

        AgentVersionListVO agentVersionListVO = new AgentVersionListVO();

        agentVersionListVO.setVersionSn( agentVersion.getVersionSn() );
        agentVersionListVO.setVersionNumber( agentVersion.getVersionNumber() );
        agentVersionListVO.setCreateTime( agentVersion.getCreateTime() );
        agentVersionListVO.setUpdateTime( agentVersion.getUpdateTime() );

        return agentVersionListVO;
    }

    @Override
    public AgentVersionVO toVo(AgentVersion agentVersion) {
        if ( agentVersion == null ) {
            return null;
        }

        AgentVersionVO agentVersionVO = new AgentVersionVO();

        agentVersionVO.setVersionId( agentVersion.getVersionId() );
        agentVersionVO.setVersionSn( agentVersion.getVersionSn() );
        agentVersionVO.setVersionNumber( agentVersion.getVersionNumber() );
        agentVersionVO.setVersionMetadata( agentVersion.getVersionMetadata() );
        agentVersionVO.setVersionSnapshot( agentVersion.getVersionSnapshot() );
        agentVersionVO.setVersionStatus( agentVersion.getVersionStatus() );
        agentVersionVO.setVersionOnsale( agentVersion.getVersionOnsale() );
        agentVersionVO.setAgentId( agentVersion.getAgentId() );
        agentVersionVO.setCreateTime( agentVersion.getCreateTime() );
        agentVersionVO.setUpdateTime( agentVersion.getUpdateTime() );

        return agentVersionVO;
    }
}
