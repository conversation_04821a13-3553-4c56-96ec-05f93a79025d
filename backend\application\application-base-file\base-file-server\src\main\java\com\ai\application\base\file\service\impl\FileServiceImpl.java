package com.ai.application.base.file.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.ai.application.base.file.api.dto.AppFileBatchDto;
import com.ai.application.base.file.api.dto.DocFileDto;
import com.ai.application.base.file.api.dto.FileUploadNoticeDto;
import com.ai.application.base.file.api.dto.QueryByFileIdsDto;
import com.ai.application.base.file.api.entity.AppFile;
import com.ai.application.base.file.api.mapstruct.FileMapstruct;
import com.ai.application.base.file.api.vo.DownloadBytesVo;
import com.ai.application.base.file.enums.SkillErrorCodeEnum;
import com.ai.application.base.file.mapper.AppFileMapper;
import com.ai.application.base.file.properties.FileProperties;
import com.ai.application.base.file.service.IFileService;
import com.ai.application.base.file.service.IOssService;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.enums.ErrorCodeEnum;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.util.list.CollectionUtils;
import com.ai.framework.core.util.string.StringUtil;
import com.ai.framework.core.util.uuid.UUIDUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.minio.PutObjectArgs;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.sql.Timestamp;
import java.util.*;

@Service
@Slf4j
public class FileServiceImpl extends ServiceImpl<AppFileMapper, AppFile> implements IFileService {
    public static final String PUBLIC = "public";
    @Resource
    private AppFileMapper appFileMapper;
    @Resource
    private IOssService ossService;

    @Resource
    private FileMapstruct fileMapstruct;
    @Value("${oss.bucketPublicName:public-agent}")
    private String ossPublicBucketName;


    @Value("${oss.bucketPublicUrl:http://************:8001}")
    String ossPublicBucketUrl;

    //    @Override
    public List<AppFile> queryByFileIds(QueryByFileIdsDto dto) {
        List<Long> fileIds = dto.getFileIds();
        if (CollectionUtils.isEmpty(fileIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AppFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AppFile::getFileId, fileIds);
        queryWrapper.orderByDesc(AppFile::getFileId);

        return appFileMapper.selectList(queryWrapper);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void upload(MultipartFile file, String fileSn, String fileType) {
        // 1、调用文件上传
        try {
            byte[] bytes = file.getBytes();
            ossService.uploadFile(file, bytes.length, fileSn, fileType);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new ServiceException(ErrorCodeEnum.OSS_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String uploadFileReturnUrl(MultipartFile file, String fileSn, String fileType) {
        // 1、调用文件上传
        try {
            byte[] bytes = file.getBytes();
            return ossService.uploadFileReturnUrl(file, bytes.length, fileSn, fileType);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new ServiceException(ErrorCodeEnum.OSS_ERROR);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String processFileUploadCompa(byte[] content, String fileSn, String fileType, String fileName, String source,String type) {
        // 1、调用文件上传
        try {

        return     ossService.uploadFileCompa(content, content.length, fileSn, fileType,type);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new ServiceException(ErrorCodeEnum.OSS_ERROR);
        }

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processFileUpload(byte[] content, String fileSn, String fileType, String fileName, String source) {
        // 1、调用文件上传
        try {

            ossService.uploadFile(content, content.length, fileSn, fileType);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new ServiceException(ErrorCodeEnum.OSS_ERROR);
        }
        //数据入库
        Timestamp now = new Timestamp(System.currentTimeMillis());
        AppFile file = new AppFile();
        file.setFileSn(fileSn);
        file.setFileName(fileName);
        file.setFileMime(fileType);
        file.setCreateTime(now);
//        file.setUserId(UserContext.getUserId());
        file.setUpdateTime(now);
//        file.setTenantId(UserContext.getUserId());
        file.setFileHash(DigestUtils.md5Hex(content));
        file.setFileSource(source);
        appFileMapper.insert(file);

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public DownloadBytesVo downloadAsBytes(String objectName) {
        // 1、调用文件上传
        try {
            LambdaQueryWrapper<AppFile> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AppFile::getFileSn, objectName);
            AppFile processFile = appFileMapper.selectOne(queryWrapper);
            DownloadBytesVo downloadBytesVo = new DownloadBytesVo();
            downloadBytesVo.setFileName(processFile.getFileName());
            downloadBytesVo.setData(ossService.downloadAsBytes(objectName));
            return downloadBytesVo;
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new ServiceException(ErrorCodeEnum.OSS_ERROR);
        }


    }


    /**
     * 根据租户FileSn查询信息（用户查询需要注意重复）
     *
     * @param fileSns
     * @return
     */

    @Transactional(readOnly = true)
    public List<AppFile> queryFileSns(List<String> fileSns) {
        Integer tenantId = UserContext.getTenantId();
        LambdaQueryWrapper<AppFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppFile::getTenantId, tenantId).in(AppFile::getFileSn, fileSns);
        return appFileMapper.selectList(queryWrapper);
    }


    @Transactional(readOnly = true)
    public AppFile queryOne(String datasetId, String fileSn) {
        LambdaQueryWrapper<AppFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppFile::getFileSn, fileSn);
        AppFile docFile = appFileMapper.selectOne(queryWrapper);
        if (null != docFile) {
            docFile.setFileSource(ossService.getDownloadUrlByFileName(
                    docFile.getFileSn(), "", docFile.getFileName()));
        }
        return docFile;
    }


    public List<AppFile> findDocFileByDataSetIdAndMd5(String datasetId, String dataMd5) {
        LambdaQueryWrapper<AppFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppFile::getFileHash, dataMd5);
        return appFileMapper.selectList(queryWrapper);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFile(com.ai.application.base.file.api.dto.FileDeleteDto fileDeleteDto) {
        String fileSn = fileDeleteDto.getFileSn();
        LambdaQueryWrapper<AppFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppFile::getFileSn, fileSn);
        AppFile docFile = appFileMapper.selectOne(queryWrapper);
        if (docFile == null) {
            return;
        }
        Integer fileId = docFile.getFileId();
        appFileMapper.deleteById(fileId);
        ossService.batchDelete(Arrays.asList(fileSn));
//        }
    }


    @Override
    @Transactional(readOnly = true)
    public String getUrl(String fileSn, String type, String fileName) {
        String fileNameNew = fileName;
        if( StringUtil.isNotEmpty(type)|| PUBLIC.equals(type)){

            String publicUrl = String.format("%s/%s/%s", ossPublicBucketUrl, ossPublicBucketName, fileSn);
            return publicUrl;
        }

        return ossService.getDownloadUrlByFileName(fileSn, type, fileNameNew);
    }


    @Transactional(readOnly = true)
    public String getUrl(Long fileId, String type, String fileName) {
        AppFile docFile = appFileMapper.selectById(fileId);
        if (docFile == null) {
            throw new ServiceException(SkillErrorCodeEnum.FILE_IS_NOT_EXISTS);
        }

        return ossService.getDownloadUrlByFileName(docFile.getFileSn(), type, fileName);
    }


    private com.ai.application.base.file.api.dto.FileInfoDto getFileInfoFromDocFile(AppFile docFile) {
        com.ai.application.base.file.api.dto.FileInfoDto fileInfoDto = new com.ai.application.base.file.api.dto.FileInfoDto();
        BeanUtil.copyProperties(docFile, fileInfoDto);
        fileInfoDto.setFileMd5(docFile.getFileHash());
        return fileInfoDto;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer uploadFile(com.ai.application.base.file.api.dto.FileUploadNoticeDto uploadInfo, boolean allowExists) {
        // 1、文件数据入库
        String fileSn = uploadInfo.getFileSn();
        LambdaQueryWrapper<AppFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppFile::getFileSn, fileSn);
        AppFile docFile = appFileMapper.selectOne(queryWrapper);
        if (docFile != null) {
            if (!allowExists) {
                throw new ServiceException(SkillErrorCodeEnum.FILE_HAS_EXISTS);
            } else {
//                docFile.setSummary("");
//                docFile.setStatus(1);
                docFile.setUpdateTime(new Timestamp(System.currentTimeMillis()));

                appFileMapper.updateById(docFile);
            }

        } else {
            AppFile file = buildDocFile(uploadInfo);
            appFileMapper.insert(file);
        }
        return  docFile.getFileId();
    }

    @Override
    @Transactional(readOnly = true)
    public com.ai.application.base.file.api.vo.FileMd5Vo historyMd5() {
        Integer tenantId = UserContext.getTenantId() == null ? 0 : UserContext.getTenantId();
        QueryWrapper<AppFile> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct file_sn,file_hash").lambda().eq(AppFile::getTenantId, tenantId);
        List<AppFile> list = appFileMapper.selectList(queryWrapper);
        List<com.ai.application.base.file.api.vo.FileMd5Vo.FileMd5> fileMd5List = list.stream().map(docFile -> {
            com.ai.application.base.file.api.vo.FileMd5Vo.FileMd5 vo = new com.ai.application.base.file.api.vo.FileMd5Vo.FileMd5();
            vo.setFileSn(docFile.getFileSn());
            vo.setDataMd5(docFile.getFileHash());
            return vo;
        }).toList();

        return com.ai.application.base.file.api.vo.FileMd5Vo.builder().list(fileMd5List).build();
    }


    private AppFile buildDocFile(FileUploadNoticeDto uploadInfo) {
        long now = System.currentTimeMillis();
        Timestamp time = new Timestamp(now);

        UserContext.UserContextItem user = UserContext.getCurrentUser();
        // 构建文件信息
        AppFile file = new AppFile();
        BeanUtils.copyProperties(uploadInfo, file);
        return file;
    }


    @Override
    public Optional<com.ai.application.base.file.api.dto.DocFileDto> getDocFileById(Long fileId) {
        AppFile docFile = appFileMapper.selectById(fileId);
        return Optional.ofNullable(DocFileDto.toDto(docFile));
    }

    @Override
    public Optional<com.ai.application.base.file.api.dto.DocFileDto> getDocFileByFileSn(String fileSn) {
        LambdaQueryWrapper<AppFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppFile::getFileSn, fileSn);
        AppFile docFile = appFileMapper.selectOne(queryWrapper);
        return Optional.ofNullable(com.ai.application.base.file.api.dto.DocFileDto.toDto(docFile));
    }


    public Optional<com.ai.application.base.file.api.dto.DocFileDto> getDocFileByFileId(Integer fileId) {
        LambdaQueryWrapper<AppFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AppFile::getFileId, fileId);
        AppFile docFile = appFileMapper.selectOne(queryWrapper);
        return Optional.ofNullable(com.ai.application.base.file.api.dto.DocFileDto.toDto(docFile));
    }
    @Override
    public List<DocFileDto> getDocFileByFileIds(List<Integer> fileIds) {
        LambdaQueryWrapper<AppFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(AppFile::getFileStatus,-1);
        queryWrapper.in(AppFile::getFileId, fileIds);
        List<AppFile> appFiles = appFileMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(appFiles)) {
            return Collections.emptyList();
        }
        List<DocFileDto> dtoList = fileMapstruct.toDtoList(appFiles);
        return dtoList;
    }

   @Override
    public List<DocFileDto> getDocFileByFileSns(List<String> fileSns) {
        LambdaQueryWrapper<AppFile> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(AppFile::getFileStatus,-1);
        queryWrapper.in(AppFile::getFileSn, fileSns);
        List<AppFile> appFiles = appFileMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(appFiles)) {
            return Collections.emptyList();
        }
        List<DocFileDto> dtoList = fileMapstruct.toDtoList(appFiles);
       dtoList.forEach(dto -> {
           dto.setFileUrl(getUrl(dto.getFileSn(), null, null));
       });
        return dtoList;
    }
@Transactional(rollbackFor = Exception.class)
public Boolean batchUpdateStatus(List<Integer> fileIds) {
    if (CollectionUtils.isEmpty(fileIds)) {
        return false;
    }
    // 使用 LambdaUpdateWrapper 构建更新条件
    LambdaUpdateWrapper<AppFile> updateWrapper = new LambdaUpdateWrapper<>();
    updateWrapper
        .in(AppFile::getFileId, fileIds)  // 限定要更新的ID范围
        .ne(AppFile::getFileStatus, -1)  // 排除已删除的文件（可选）
        .set(AppFile::getFileStatus, -1)  // 设置新状态
        .set(AppFile::getUpdateTime, new Timestamp(System.currentTimeMillis()));  // 更新时间

    // 执行批量更新
    int affectedRows = appFileMapper.update(null, updateWrapper);
    return affectedRows > 0;
}

    @Transactional(rollbackFor = Exception.class)
    public void batchFileSave(AppFileBatchDto dto) {
        //文件存储
        List<AppFile> entityList = fileMapstruct.toEntityList(dto.getDocFileList());

        saveBatch(entityList);
    }


    public List<AppFile> findDocFilesByMd5sOrMd5(Object md5Input) {
        LambdaQueryWrapper<AppFile> queryWrapper = new LambdaQueryWrapper<>();
        ;

        if (md5Input instanceof List) {
            List<String> md5s = (List<String>) md5Input;
            if (CollectionUtils.isEmpty(md5s)) {
                return Collections.emptyList();
            }
            queryWrapper.in(AppFile::getFileHash, md5s);
        } else if (md5Input instanceof String dataMd5) {
            queryWrapper.eq(AppFile::getFileHash, dataMd5);
        } else {
            return Collections.emptyList();
        }
        return appFileMapper.selectList(queryWrapper);
    }


}
