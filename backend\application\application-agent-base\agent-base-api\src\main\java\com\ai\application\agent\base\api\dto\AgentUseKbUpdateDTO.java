package com.ai.application.agent.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 智能体关联知识库表
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Data
@Schema(name = "智能体关联知识库表DTO")
public class AgentUseKbUpdateDTO {
    @Schema(description = "")
    private Integer akbId;

    /**
     * 状态 0失效 1有效
     */
    @Schema(description = "状态 0失效 1有效")
    private Integer akbStatus;

    /**
     * 智能体id
     */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
     * 智能体版本id
     */
    @Schema(description = "智能体版本id")
    private Integer versionId;

    /**
     * 知识库id
     */
    @Schema(description = "知识库id")
    private Integer kbId;

    /**
     * 额外属性
     */
    @Schema(description = "额外属性")
    private String kbExtend;
}