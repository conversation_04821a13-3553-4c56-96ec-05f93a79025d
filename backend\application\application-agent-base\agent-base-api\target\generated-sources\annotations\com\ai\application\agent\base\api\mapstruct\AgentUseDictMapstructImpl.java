package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.dto.AgentUseDictAddDTO;
import com.ai.application.agent.base.api.dto.AgentUseDictUpdateDTO;
import com.ai.application.agent.base.api.entity.AgentUseDict;
import com.ai.application.agent.base.api.vo.AgentUseDictListVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-14T11:00:33+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentUseDictMapstructImpl implements AgentUseDictMapstruct {

    @Override
    public AgentUseDict toAddEntity(AgentUseDictAddDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentUseDict agentUseDict = new AgentUseDict();

        agentUseDict.setAdStatus( dto.getAdStatus() );
        agentUseDict.setAgentId( dto.getAgentId() );
        agentUseDict.setVersionId( dto.getVersionId() );
        agentUseDict.setDictId( dto.getDictId() );
        agentUseDict.setDictExtend( dto.getDictExtend() );

        return agentUseDict;
    }

    @Override
    public AgentUseDict toUpdateEntity(AgentUseDictUpdateDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentUseDict agentUseDict = new AgentUseDict();

        agentUseDict.setAdId( dto.getAdId() );
        agentUseDict.setAdStatus( dto.getAdStatus() );
        agentUseDict.setAgentId( dto.getAgentId() );
        agentUseDict.setVersionId( dto.getVersionId() );
        agentUseDict.setDictId( dto.getDictId() );
        agentUseDict.setDictExtend( dto.getDictExtend() );

        return agentUseDict;
    }

    @Override
    public AgentUseDictListVO toVo(AgentUseDict entity) {
        if ( entity == null ) {
            return null;
        }

        AgentUseDictListVO agentUseDictListVO = new AgentUseDictListVO();

        agentUseDictListVO.setAdId( entity.getAdId() );
        agentUseDictListVO.setAdStatus( entity.getAdStatus() );
        agentUseDictListVO.setAgentId( entity.getAgentId() );
        agentUseDictListVO.setVersionId( entity.getVersionId() );
        agentUseDictListVO.setDictId( entity.getDictId() );
        agentUseDictListVO.setDictExtend( entity.getDictExtend() );
        agentUseDictListVO.setCreateTime( entity.getCreateTime() );
        agentUseDictListVO.setUpdateTime( entity.getUpdateTime() );

        return agentUseDictListVO;
    }

    @Override
    public List<AgentUseDictListVO> toVoList(List<AgentUseDict> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AgentUseDictListVO> list = new ArrayList<AgentUseDictListVO>( entities.size() );
        for ( AgentUseDict agentUseDict : entities ) {
            list.add( toVo( agentUseDict ) );
        }

        return list;
    }
}
