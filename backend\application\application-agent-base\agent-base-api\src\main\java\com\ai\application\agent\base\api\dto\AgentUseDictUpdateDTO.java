package com.ai.application.agent.base.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 智能体关联字典表
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Data
@Schema(name = "智能体关联字典表DTO")
public class AgentUseDictUpdateDTO {
    @Schema(description = "")
    private Integer adId;

    /**
     * 状态 0失效 1有效
     */
    @Schema(description = "状态 0失效 1有效")
    private Integer adStatus;

    /**
     * 智能体id
     */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
     * 智能体版本id
     */
    @Schema(description = "智能体版本id")
    private Integer versionId;

    /**
     * 字典id
     */
    @Schema(description = "字典id")
    private Integer dictId;

    /**
     * 额外属性
     */
    @Schema(description = "额外属性")
    private String dictExtend;
}