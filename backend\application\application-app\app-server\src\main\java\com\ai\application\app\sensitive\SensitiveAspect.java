package com.ai.application.app.sensitive;

import com.ai.framework.core.sensitive.DesensitizationUtil;
import com.ai.framework.core.sensitive.EncryptUtil;
import com.ai.framework.core.sensitive.Sensitive;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Objects;

@Aspect
@Component
public class SensitiveAspect {
    @Value("${config.userSensitiveSaltKey:''}")
    private String saltKey;

    /**
     * 在保存操作前进行加密
     */
    @Before("@annotation(com.ai.framework.core.sensitive.SensitiveEncrypt)")
    public void encryptFields(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        for (Object arg : args) {
            if (arg == null) continue;

            // 获取所有字段
            ReflectionUtils.doWithFields(arg.getClass(), field -> {
                processSensitiveField(field, arg, true);
            });
        }
    }

    /**
     * 在查询操作后进行解密和脱敏
     */
    @AfterReturning(pointcut = "@annotation(com.ai.framework.core.sensitive.SensitiveDecrypt)", returning = "result")
    public void decryptAndDesensitize(Object result) {
        if (result == null) return;
        // 获取所有字段
        ReflectionUtils.doWithFields(result.getClass(), field -> {
            processSensitiveField(field, result, false);
        });
    }

    private void processSensitiveField(Field field, Object target, boolean isEncrypt) {
        // 获取敏感注解
        Sensitive sensitive = field.getAnnotation(Sensitive.class);
        if (sensitive == null) return;

        // 设置字段可访问
        field.setAccessible(true);

        try {
            String value = (String) field.get(target);
            if (value == null) return;
            if (value.contains("*")) return;

            if (isEncrypt) {
                // 加密
                field.set(target, EncryptUtil.encrypt(value,saltKey));
            } else {
                // 解密并脱敏
                String decrypted = EncryptUtil.decrypt(value,saltKey);
                String desensitized = DesensitizationUtil.desensitize(
                        decrypted,
                        sensitive.type(),
                        sensitive.prefixLength(),
                        sensitive.suffixLength(),
                        sensitive.mask()
                );
                field.set(target, desensitized);
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException("处理敏感字段失败", e);
        }
    }
}
