package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.dto.AgentRunStepListDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ai.application.agent.base.mapper.AgentRunStepMapper;
import com.ai.application.agent.base.api.entity.AgentRunStep;
import com.ai.application.agent.base.service.IAgentRunStepService;
import com.ai.application.agent.base.api.dto.AgentRunStepDTO;
import com.ai.application.agent.base.api.vo.AgentRunStepVO;
import com.ai.application.agent.base.api.mapstruct.AgentRunStepMapstruct;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Date;

/**
 * 智能体运行步骤表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
public class AgentRunStepServiceImpl implements IAgentRunStepService{

    @Resource
    private AgentRunStepMapper agentRunStepMapper;

    @Resource
    private AgentRunStepMapstruct agentRunStepMapstruct;

    @Transactional(readOnly = true)
    @Override
    public List<AgentRunStepVO> list(AgentRunStepListDTO queryDto) {
        LambdaQueryWrapper<AgentRunStep> queryWrapper = this.buildQuery(queryDto);
        return agentRunStepMapstruct.toVoList(this.agentRunStepMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(AgentRunStepDTO dto) {
        dto.setRunId(null);
        AgentRunStep entity = agentRunStepMapstruct.toEntity(dto);
        entity.setCreateTime(new Date());

        agentRunStepMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AgentRunStepDTO dto) {
        BusinessAssertUtil.notNull(dto.getRunId(), "RunId不能为空");

        AgentRunStep entity = agentRunStepMapper.selectById(dto.getStepId());
        BusinessAssertUtil.notNull(entity, "找不到ID为 " + dto.getStepId() + " 的记录");

        AgentRunStep entityList = agentRunStepMapstruct.toEntity(dto);
        entityList.setUpdateTime(new Date());
        agentRunStepMapper.updateById(entityList);
    }

    @Transactional(readOnly = true)
    @Override
    public AgentRunStepVO get(Integer id) {
        BusinessAssertUtil.notNull(id, "ID不能为空");

        AgentRunStep entity = agentRunStepMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到ID为 " + id + " 的记录");

        return agentRunStepMapstruct.toVo(entity);
    }

    private LambdaQueryWrapper<AgentRunStep> buildQuery(AgentRunStepListDTO queryDto) {
        LambdaQueryWrapper<AgentRunStep> queryWrapper = new LambdaQueryWrapper<>();
        return queryWrapper;
    }
}