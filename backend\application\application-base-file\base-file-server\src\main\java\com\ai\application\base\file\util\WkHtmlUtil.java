package com.ai.application.base.file.util;

import cn.hutool.core.io.FileUtil;
import com.ai.framework.core.exception.ServiceException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.CountDownLatch;

/**
 * html 转 图片或pdf 工具类
 */
@Slf4j
public class WkHtmlUtil {
    /**
     * 工具根目录
     */
    private static final String TOOL_WIN_ROOT_DIRECTORY = "";

    /**
     * html转图片
     *
     * @param cmd            工具操作指令
     * @param sourceFilePath html源资源
     * @param targetFilePath 生成目标资源
     * @return 图片字节码
     */
    public static byte[] html2ImageBytes(String cmd, String sourceFilePath, String targetFilePath) {
        return baseTool("wkhtmltoimage", cmd, sourceFilePath, targetFilePath);
    }

    /**
     * html转pdf
     *
     * @param cmd            工具操作指令
     * @param sourceFilePath html源资源
     * @param targetFilePath 生成目标资源
     * @return pdf字节码
     */
    public static byte[] html2PdfBytes(String cmd, String sourceFilePath, String targetFilePath) {
        return baseTool("wkhtmltopdf", cmd, sourceFilePath, targetFilePath);
    }

    /**
     * html转pdf
     *
     * @param cmd            工具操作指令
     * @param sourceFilePath html源资源
     * @param targetFilePath 生成目标资源
     * @return pdf字节码
     */
    public static String html2PdfFilePath(String cmd, String sourceFilePath, String targetFilePath) {
        return  baseToolForPath("wkhtmltopdf", cmd, sourceFilePath, targetFilePath);
    }

    /**
     * 工具基础操作
     *
     * @param tool           工具
     * @param cmd            工具操作指令
     * @param sourceFilePath html源资源
     * @param targetFilePath 生成目标资源
     * @return 字节码
     */
    @SneakyThrows({Exception.class})
    private static byte[] baseTool(String tool, String cmd, String sourceFilePath, String targetFilePath) {
        String filePath = baseToolForPath(tool, cmd, sourceFilePath, targetFilePath);
        return FileUtil.readBytes(filePath);
    }

    /**
     * 基本工具路径
     * 工具基础操作
     *
     * @param command            工具操作指令
     * @return 生成资源路径
     */
    @SneakyThrows({Exception.class})
    public static String baseToolForPath(String command) {
        // 先创建父目录
        Process process = null;
        ProcessBuilder pb = new ProcessBuilder(
                command
        );
        try {
            process = pb.start();
            // 等待当前命令执行完，再往下执行
            process.waitFor();
        } catch (IOException e) {
            e.printStackTrace();
            throw new Exception("工具丢失，请联系系统管理员！");
        }finally {
            if (null != process) {
                process.destroy();
            }
        }
        log.info("=============== FINISH: [{}] ===============", command);
        return command;
    }

    /**
     * 基本工具路径
     * 工具基础操作
     *
     * @param tool           工具
     * @param cmd            工具操作指令
     * @param sourceFilePath html源资源
     * @param targetFilePath 生成目标资源
     * @return 生成资源路径
     */
    @SneakyThrows({Exception.class})
    private static String baseToolForPath(String tool, String cmd, String sourceFilePath, String targetFilePath) {
        // 先创建父目录
        FileUtil.mkParentDirs(targetFilePath);
        String command = String.format("%s %s %s %s", getToolRootPath() + tool, cmd, sourceFilePath, targetFilePath);

        Process process = null;
        InputStream is = null;
        InputStream errIs = null;
        final CountDownLatch latch = new CountDownLatch(2);
        try {
            process = Runtime.getRuntime().exec(command);
            /**
             * process的InputStream和ErrorStream都要接收处理
             * 否则会出现问题
             */
            is = process.getInputStream();
            errIs = process.getErrorStream();

            new Thread(new ProcessInputSteramThread(is,latch)).start();
            new Thread(new ProcessInputSteramThread(errIs,latch)).start();

            // 等待当前命令执行完，再往下执行
            process.waitFor();
        } catch (Exception e) {
            e.printStackTrace();
            throw new Exception("工具丢失，请联系系统管理员！");
        } finally {
            if (null != process) {
                latch.await();
                process.destroy();
            }

        }

        log.info("=============== FINISH: [{}] ===============", command);
        return targetFilePath;
    }

    /**
     * 根据不同系统获取工具
     *
     * @return 工具位置
     */
    private static String getToolRootPath() {
        String system = System.getProperty("os.name");
        if (system.contains("Windows")) {
            return TOOL_WIN_ROOT_DIRECTORY;
        } else if (system.contains("Linux") || system.contains("Mac OS X")) {
            return "";
        }
        return "";
    }

    @SneakyThrows({Exception.class})
    public static void url2Pdf(String url, String saveFilePath) {
        String command = String.format("%s %s %s", "wkhtmltopdf", url, saveFilePath);
        Process process = null;
        try {
            process = Runtime.getRuntime().exec(command);
        } catch (IOException e) {
            throw new Exception("执行命令失败！");
        }

        // 等待当前命令执行完，再往下执行
        process.waitFor();
        log.info("=======【地址 {} 】======== FINISH: [{}] ===============", url,command);


        if(checkEmpty(saveFilePath)) {
            throw new ServiceException(30002, "文件内容为空");
        }

    }

    private static boolean checkEmpty(String saveFilePath) {
        try (PDDocument document = PDDocument.load(new File(saveFilePath))) {
            if (document.getNumberOfPages() == 0) {
                return true; // No pages in the PDF
            }

            PDFTextStripper stripper = new PDFTextStripper();
            String text = stripper.getText(document);
            return text.trim().isEmpty(); // Check if there's text content
        } catch (IOException e) {
            log.info(e.getMessage(), e);
            return true; // Assume empty or unreadable
        }
    }

//    public static void main(String[] args) throws Exception {
//        String sourceFilePath = "http://localhost:8088/mogo-valuation/v4.4/pdf/test";
//        String targetPngFilePath = Constants.DEFAULT_FOLDER_TMP_GENERATE + "/zhouyx.png";
//        String targetPdfFilePath =  "D:\\testPdf\\zhouyx.pdf";
//        // 设置宽高
//        String cmdByImage = "--crop-w 150 --crop-h 150 --quality 100";
//        byte[] imageBytes = html2ImageBytes(cmdByImage, sourceFilePath, targetPngFilePath);
//        byte[] imageBytesByCompress = html2ImageBytesByCompress(cmdByImage, sourceFilePath, targetPngFilePath);
//        byte[] pdfBytes = html2PdfBytes("", sourceFilePath, targetPdfFilePath);
//    }

}

