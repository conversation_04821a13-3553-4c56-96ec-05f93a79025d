package com.ai.application.task.controller;

import com.ai.application.task.api.dto.TaskAddDTO;
import com.ai.application.task.api.vo.TaskDetailVO;
import com.github.pagehelper.PageInfo;
import com.ai.application.task.service.ITaskService;
import com.ai.application.task.api.dto.TaskDTO;
import com.ai.application.task.api.dto.query.TaskQueryDTO;
import com.ai.application.task.api.vo.TaskVO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.io.ByteArrayInputStream;
import java.io.IOException;

/**
 * 计划任务表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Tag(name = "计划任务表", description = "计划任务表-相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1")
public class TaskController {

    @Resource
    private ITaskService  taskService;

    /**
     * 分页查询
     *
     * @param queryDto
     * @return
     */
    @Operation(summary = "计划任务表-分页查询", description = "查询所有计划任务表 信息")
    @PostMapping("/page")
    public ResultVo<PageInfo<TaskVO>> page(@Validated @RequestBody TaskQueryDTO queryDto){
        return ResultVo.data(taskService.page(queryDto));
    }

    /**
     * 保存
     *
     * @param dto
     * @return
     */
    @Operation(summary = "计划任务表-新增")
    @PostMapping("/add")
    public ResultVo<Void> add(@Validated @RequestBody TaskAddDTO dto){
        taskService.add(dto);
        return ResultVo.success("保存成功");
    }
    
    /**
     * 修改
     *
     * @param dto
     * @return
     */
    @Operation(summary = "计划任务表-修改")
    @PostMapping(value = "/update")
    public ResultVo<Void> update(@Validated @RequestBody TaskDTO dto){
        taskService.update(dto);
        return ResultVo.success("修改成功");
    }

    @Operation(summary = "计划任务表-删除")
    @PostMapping(value = "/delete/{taskSn}")
    public ResultVo<Void> delete(@PathVariable("taskSn") String taskSn){
        taskService.delete(taskSn);
        return ResultVo.success("删除成功");
    }

    @Operation(summary = "计划任务表-终止")
    @PostMapping(value = "/stop/{taskSn}")
    public ResultVo<Void> stop(@PathVariable("taskSn") String taskSn){
        taskService.stop(taskSn);
        return ResultVo.success("删除成功");
    }

    @Operation(summary = "计划任务表-修改")
    @PostMapping(value = "/dtail/{taskSn}")
    public ResultVo<TaskDetailVO> dtail(@PathVariable("taskSn") String taskSn){
        return ResultVo.data(taskService.detail(taskSn));
    }

    @Operation(summary = "任务创建-数据集模板下载")
    @GetMapping("/datasets/template/{agentSn}/{versionSn}")
    public void getDatasetTemplate(@PathVariable("agentSn") String agentSn, @PathVariable("versionSn") String versionSn,HttpServletResponse response) {
        taskService.getDataSetTemplate(agentSn,versionSn,response);
    }


}