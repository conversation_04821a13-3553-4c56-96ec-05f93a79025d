package com.ai.application.app.mapper;

import com.ai.application.app.api.entity.AppRole;
import com.ai.application.app.api.entity.AppRoleFunction;
import com.ai.application.app.api.vo.AppRoleVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 应用角色表 Mapper接口
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Mapper
public interface AppRoleMapper extends BaseMapper<AppRole> {
    /**
     * 查询应用角色表
     *
     * @return
     */
    List<AppRoleVO> selectAppRoleList();

    @Select("select * from app_role where tenant_id=#{tenantId} and role_code = #{roleCode} and app_id=100 and rf_status >= 0 limit 1")
    AppRole selectRoleByCode(@Param("tenantId") Integer tenantId,@Param("roleCode") String roleCode);
}
