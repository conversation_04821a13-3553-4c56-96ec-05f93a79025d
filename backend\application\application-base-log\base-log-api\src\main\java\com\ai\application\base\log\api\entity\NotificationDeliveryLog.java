package com.ai.application.base.log.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 通知发送日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("notification_delivery_log")
public class NotificationDeliveryLog implements Serializable {
        @Schema(description = "")
    @TableId(type = IdType.AUTO)
private Integer deliveryId;

    /**
    * 通知序列号
    */
    @Schema(description = "通知序列号")
    private String deliverySn;

    /**
    * 投递方式:10-邮件,20-短信,30-钉钉,40-企微,50-飞书,60-Slack,70-Webhook,80-站内消息
    */
    @Schema(description = "投递方式:10-邮件,20-短信,30-钉钉,40-企微,50-飞书,60-Slack,70-Webhook,80-站内消息")
    private Integer deliveryType;

    /**
    * 投递优先级:1-紧急,2-高,3-中,4-低
    */
    @Schema(description = "投递优先级:1-紧急,2-高,3-中,4-低")
    private Integer deliveryPriority;

    /**
    * 投递状态:1-待发送,2-发送中,3-部分成功,4-全部失败,5-全部成功,0-已取消
    */
    @Schema(description = "投递状态:1-待发送,2-发送中,3-部分成功,4-全部失败,5-全部成功,0-已取消")
    private Integer deliveryStatus;

    /**
    * 计划发送时间
    */
    @Schema(description = "计划发送时间")
    private Date deliveryPlanTime;

    /**
    * 发送处理时间
    */
    @Schema(description = "发送处理时间")
    private Date deliveryDoneTime;

    /**
    * 发送结果
    */
    @Schema(description = "发送结果")
    private String deliveryResult;

    /**
    * 消息类型:10-异常,20-一般通知,30-成功
    */
    @Schema(description = "消息类型:10-异常,20-一般通知,30-成功")
    private Integer messageType;

    /**
    * 消息标题
    */
    @Schema(description = "消息标题")
    private String messageTitle;

    /**
    * 消息内容
    */
    @Schema(description = "消息内容")
    private String messageContent;

    /**
    * 关联源记录id,比如错误日志id
    */
    @Schema(description = "关联源记录id,比如错误日志id")
    private Integer messageSourceId;

    /**
    * 接收人类型:10-所有者,20-使用者,30-指定用户,40-租户管理员,50-系统管理员
    */
    @Schema(description = "接收人类型:10-所有者,20-使用者,30-指定用户,40-租户管理员,50-系统管理员")
    private Integer recipientType;

    /**
    * 接收人信息:id,name,address等
    */
    @Schema(description = "接收人信息:id,name,address等")
    private String recipientIds;

    /**
    * 通知配置id
    */
    @Schema(description = "通知配置id")
    private Integer ntypeId;

    /**
    * 通知模板id
    */
    @Schema(description = "通知模板id")
    private Integer ntplId;

    /**
    * 租户id
    */
    @Schema(description = "租户id")
    private Integer tenantId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}