package com.ai.application.agent.base.api.feign;

import com.ai.application.agent.base.api.dto.AgentChatDTO;
import com.ai.application.agent.base.api.dto.query.AgentPageDTO;
import com.ai.application.agent.base.api.dto.query.AgentStatDetailQueryDTO;
import com.ai.application.agent.base.api.feign.fallback.IAgentClientFallback;
import com.ai.application.agent.base.api.vo.AgentChatVO;
import com.ai.application.agent.base.api.vo.AgentDetailVO;
import com.ai.application.agent.base.api.vo.AgentPageVO;
import com.ai.application.agent.base.api.vo.AgentStatDetailVO;
import com.ai.application.agent.base.api.vo.AgentVO;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.vo.ResultVo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

@Tag(name = "智能体Feign接口", description = "智能体基本操作")
@FeignClient(
        value = ServiceConstant.AGENT_BASE,
        fallback = IAgentClientFallback.class,
        contextId = "IAgentClient"
)
@Component
public interface IAgentClient {
    String API_PREFIX = "/v1/feign/agent";

    @PostMapping(API_PREFIX + "/queryAgentByModelId")
    ResultVo<List<AgentStatDetailVO>> queryAgentByModelId(@RequestBody @Validated AgentStatDetailQueryDTO dto);

    @GetMapping(API_PREFIX + "/getAgentBySn/{agentSn}")
    ResultVo<AgentVO> getAgentBySn(@PathVariable("agentSn") String agentSn);

    @GetMapping(API_PREFIX + "/queryGrantAgentIdList/{pageSource}")
    ResultVo<List<Integer>> queryGrantAgentIdList(@PathVariable("pageSource") Integer pageSource);

    @PostMapping(API_PREFIX + "/page")
    ResultVo<PageInfo<AgentPageVO>> page(@Validated @RequestBody AgentPageDTO dto);

    /**
     * 调用智能体进行对话
     *
     * @param chatDto 对话请求
     * @return 对话结果
     */
    // TODO 不同agent 待提供接口
    @PostMapping(API_PREFIX + "/chat")
    ResultVo<AgentChatVO> chat(@RequestBody @Valid AgentChatDTO chatDto);
}
