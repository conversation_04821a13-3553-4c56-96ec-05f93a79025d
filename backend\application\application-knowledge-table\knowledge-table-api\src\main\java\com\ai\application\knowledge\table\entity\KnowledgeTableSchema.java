package com.ai.application.knowledge.table.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 智能表格定义表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("knowledge_table_schema")
@Schema(description = "智能表格定义表")
public class KnowledgeTableSchema implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "field_id", type = IdType.AUTO)
    private Integer fieldId;

    @Schema(description = "字段sn")
    @TableField("field_sn")
    private String fieldSn;

    @Schema(description = "字段名称")
    @TableField("field_name")
    private String fieldName;

    @Schema(description = "字段类型")
    @TableField("field_type")
    private String fieldType;

    @Schema(description = "字段描述")
    @TableField("field_desc")
    private String fieldDesc;

    @Schema(description = "key标识 0否 1是")
    @TableField("field_key")
    private Integer fieldKey;

    @Schema(description = "排序值")
    @TableField("field_sort")
    private Integer fieldSort;

    @Schema(description = "状态 0失效 1有效")
    @TableField("field_status")
    private Integer fieldStatus;

    @Schema(description = "智能表格id")
    @TableField("table_id")
    private Integer tableId;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @Schema(description = "创建用户id")
    @TableField("create_user_id")
    private Integer createUserId;

    @Schema(description = "更新用户id")
    @TableField("update_user_id")
    private Integer updateUserId;

}
