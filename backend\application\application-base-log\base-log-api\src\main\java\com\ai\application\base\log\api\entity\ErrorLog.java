package com.ai.application.base.log.api.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 错误日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-07
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("error_log")
public class ErrorLog implements Serializable {
        /**
    * 日志id
    */
    @Schema(description = "日志id")
    @TableId(type = IdType.AUTO)
private Integer logId;

    /**
    * 错误类型:10-运行错误,20-LLM错误,30-工具错误,40-知识库错误,50-系统错误
    */
    @Schema(description = "错误类型:10-运行错误,20-LLM错误,30-工具错误,40-知识库错误,50-系统错误")
    private Integer errorType;

    /**
    * 错误级别:10-致命,20-严重,30-警告,40-信息
    */
    @Schema(description = "错误级别:10-致命,20-严重,30-警告,40-信息")
    private Integer errorLevel;

    /**
    * 错误模块
    */
    @Schema(description = "错误模块")
    private String errorModule;

    /**
    * 错误代码
    */
    @Schema(description = "错误代码")
    private String errorCode;

    /**
    * 错误信息
    */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
    * 错误堆栈
    */
    @Schema(description = "错误堆栈")
    private String errorStack;

    /**
    * 错误追踪
    */
    @Schema(description = "错误追踪")
    private String errorTrace;

    /**
    * 错误元数据
    */
    @Schema(description = "错误元数据")
    private String errorMetadata;

    /**
    * 影响对象类型:10-智能体,20-工具,30-知识库,40-用户,50-租户
    */
    @Schema(description = "影响对象类型:10-智能体,20-工具,30-知识库,40-用户,50-租户")
    private Integer affectedObjectType;

    /**
    * 影响对象id
    */
    @Schema(description = "影响对象id")
    private Integer affectedObjectId;

    /**
    * 租户id
    */
    @Schema(description = "租户id")
    private Integer tenantId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}