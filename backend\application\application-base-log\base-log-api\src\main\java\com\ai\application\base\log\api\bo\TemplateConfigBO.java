package com.ai.application.base.log.api.bo;

import com.ai.application.base.log.api.serializer.MessageReceiverTypeSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class TemplateConfigBO {
    @Schema(description = "接收类型：10：支持租户管理员:20：任务发起人:100：自定义")
    @JsonSerialize(using = MessageReceiverTypeSerializer.class)
    private Integer receiverType;

    @Schema(description = "接收方邮箱")
    private List<String> receiverEmails;

    @Schema(description = "通知类型id")
    private Integer noticeTypeId;

    @Schema(description = "通知类型名称")
    private String noticeTypeName;
}
