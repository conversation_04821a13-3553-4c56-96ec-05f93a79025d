package com.ai.application.agent.base.feign;

import com.ai.application.agent.base.api.dto.AgentUseDictListDTO;
import com.ai.application.agent.base.api.feign.IAgentDictClient;
import com.ai.application.agent.base.api.vo.*;
import com.ai.application.agent.base.service.IAgentUseDictService;
import com.ai.application.agent.base.service.IAgentVersionService;
import com.ai.framework.core.vo.ResultVo;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

@RestController
@AllArgsConstructor
public class AgentDictClient implements IAgentDictClient {
    private final IAgentUseDictService agentUseDictService;
    private final IAgentVersionService agentVersionService;
    
    /**
     * 根据版本号查词库列表
     * @param versionSn
     * @return
     */
    @Override
    public ResultVo<List<Integer>> selectDictByVersionSn(String versionSn) {
        AgentVersionVO agentVersionVO = agentVersionService.getBySn(versionSn);
        AgentUseDictListDTO agentUseDictListDTO = new AgentUseDictListDTO();
        agentUseDictListDTO.setVersionId(agentVersionVO.getVersionId());
        List<AgentUseDictListVO> list = agentUseDictService.list(agentUseDictListDTO);
        return ResultVo.data(list.stream().map(AgentUseDictListVO::getDictId).toList());
    }
}
