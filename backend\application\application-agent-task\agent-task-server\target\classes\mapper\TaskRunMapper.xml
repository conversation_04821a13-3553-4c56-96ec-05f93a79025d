<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.task.mapper.TaskRunMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.task.api.entity.TaskRun">
                    <id column="task_run_id" property="taskRunId" />
                    <result column="run_type" property="runType" />
                    <result column="run_status" property="runStatus" />
                    <result column="scheduled_time" property="scheduledTime" />
                    <result column="actual_start_time" property="actualStartTime" />
                    <result column="actual_end_time" property="actualEndTime" />
                    <result column="duration" property="duration" />
                    <result column="retry_attempt" property="retryAttempt" />
                    <result column="run_input" property="runInput" />
                    <result column="run_output" property="runOutput" />
                    <result column="run_error" property="runError" />
                    <result column="run_metadata" property="runMetadata" />
                    <result column="tokens_used" property="tokensUsed" />
                    <result column="next_retry_time" property="nextRetryTime" />
                    <result column="task_id" property="taskId" />
                    <result column="agent_run_id" property="agentRunId" />
                    <result column="trigger_user_id" property="triggerUserId" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        task_run_id, run_type, run_status, scheduled_time, actual_start_time, actual_end_time, duration, retry_attempt, run_input, run_output, run_error, run_metadata, tokens_used, next_retry_time, task_id, agent_run_id, trigger_user_id, create_time, update_time
    </sql>

    <select id="selectTaskRunList" resultType="com.ai.application.task.api.vo.TaskRunVO">
        select
        <include refid="com.ai.application.task.mapper.TaskRunMapper.Base_Column_List"></include>
        from task_run
        order by create_time desc limit 10;
    </select>
</mapper>