<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.admin.mapper.ResourceGrantMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.admin.api.entity.ResourceGrant">
                    <id column="grant_id" property="grantId" />
                    <result column="grant_type" property="grantType" />
                    <result column="grant_status" property="grantStatus" />
                    <result column="grant_object_type" property="grantObjectType" />
                    <result column="grant_object_id" property="grantObjectId" />
                    <result column="grant_config" property="grantConfig" />
                    <result column="resource_id" property="resourceId" />
                    <result column="resource_type" property="resourceType" />
                    <result column="resource_object_id" property="resourceObjectId" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        grant_id, grant_type, grant_status, grant_object_type, grant_object_id, grant_config, resource_id, resource_type, resource_object_id, create_time, update_time
    </sql>
</mapper>