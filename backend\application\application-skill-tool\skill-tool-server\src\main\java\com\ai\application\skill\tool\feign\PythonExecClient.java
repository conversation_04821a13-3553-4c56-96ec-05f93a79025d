package com.ai.application.skill.tool.feign;


import com.ai.application.skill.tool.api.dto.PythonCodeExecReq;
import com.ai.application.skill.tool.api.vo.PythonCodeExecResp;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


//@FeignClient( contextId = "PythonExecClient", url="http://192.168.7.55:6052/python/v1/",name = "skill-python-service")
@FeignClient( contextId = "PythonExecClient", value = "skill-python-service",path = "/python/v1/")
public interface PythonExecClient {

    /**
     *
     * @param req 利用工作流里面的python请求对象
     * @return
     */
    @PostMapping("execute")
    PythonCodeExecResp execPythonCode(@RequestBody PythonCodeExecReq req, Request.Options options);
}
