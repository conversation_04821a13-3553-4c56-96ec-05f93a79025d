package com.ai.application.task.mapper;

import com.ai.application.task.api.entity.Task;
import com.ai.application.task.api.vo.TaskVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 计划任务表-Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Mapper
public interface TaskMapper extends BaseMapper<Task> {
    /**
     * 查询计划任务表
     *
     * @return
     */
    List<TaskVO> selectTaskList();

    @Select("select * from task where task_sn = #{taskSn}")
    Task selectByTaskSn(@Param("taskSn") String taskSn);
}
