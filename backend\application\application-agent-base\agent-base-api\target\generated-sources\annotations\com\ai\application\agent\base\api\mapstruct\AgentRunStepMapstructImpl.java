package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.dto.AgentRunStepDTO;
import com.ai.application.agent.base.api.entity.AgentRunStep;
import com.ai.application.agent.base.api.vo.AgentRunStepVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-12T18:39:57+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentRunStepMapstructImpl implements AgentRunStepMapstruct {

    @Override
    public AgentRunStep toEntity(AgentRunStepDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentRunStep agentRunStep = new AgentRunStep();

        agentRunStep.setStepId( dto.getStepId() );
        agentRunStep.setStepType( dto.getStepType() );
        agentRunStep.setStepName( dto.getStepName() );
        agentRunStep.setStepOrder( dto.getStepOrder() );
        agentRunStep.setStepStatus( dto.getStepStatus() );
        agentRunStep.setStepInput( dto.getStepInput() );
        agentRunStep.setStepOutput( dto.getStepOutput() );
        agentRunStep.setStepError( dto.getStepError() );
        agentRunStep.setStepDuration( dto.getStepDuration() );
        agentRunStep.setStepTokens( dto.getStepTokens() );
        agentRunStep.setStepMetadata( dto.getStepMetadata() );
        agentRunStep.setStepStartTime( dto.getStepStartTime() );
        agentRunStep.setStepEndTime( dto.getStepEndTime() );
        agentRunStep.setRunId( dto.getRunId() );
        agentRunStep.setParentStepId( dto.getParentStepId() );
        agentRunStep.setCreateTime( dto.getCreateTime() );
        agentRunStep.setUpdateTime( dto.getUpdateTime() );

        return agentRunStep;
    }

    @Override
    public List<AgentRunStep> toEntityList(List<AgentRunStepDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<AgentRunStep> list = new ArrayList<AgentRunStep>( dtolist.size() );
        for ( AgentRunStepDTO agentRunStepDTO : dtolist ) {
            list.add( toEntity( agentRunStepDTO ) );
        }

        return list;
    }

    @Override
    public AgentRunStepVO toVo(AgentRunStep entity) {
        if ( entity == null ) {
            return null;
        }

        AgentRunStepVO agentRunStepVO = new AgentRunStepVO();

        agentRunStepVO.setStepId( entity.getStepId() );
        agentRunStepVO.setStepType( entity.getStepType() );
        agentRunStepVO.setStepName( entity.getStepName() );
        agentRunStepVO.setStepOrder( entity.getStepOrder() );
        agentRunStepVO.setStepStatus( entity.getStepStatus() );
        agentRunStepVO.setStepInput( entity.getStepInput() );
        agentRunStepVO.setStepOutput( entity.getStepOutput() );
        agentRunStepVO.setStepError( entity.getStepError() );
        agentRunStepVO.setStepDuration( entity.getStepDuration() );
        agentRunStepVO.setStepTokens( entity.getStepTokens() );
        agentRunStepVO.setStepMetadata( entity.getStepMetadata() );
        agentRunStepVO.setStepStartTime( entity.getStepStartTime() );
        agentRunStepVO.setStepEndTime( entity.getStepEndTime() );
        agentRunStepVO.setRunId( entity.getRunId() );
        agentRunStepVO.setParentStepId( entity.getParentStepId() );
        agentRunStepVO.setCreateTime( entity.getCreateTime() );
        agentRunStepVO.setUpdateTime( entity.getUpdateTime() );

        return agentRunStepVO;
    }

    @Override
    public List<AgentRunStepVO> toVoList(List<AgentRunStep> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AgentRunStepVO> list = new ArrayList<AgentRunStepVO>( entities.size() );
        for ( AgentRunStep agentRunStep : entities ) {
            list.add( toVo( agentRunStep ) );
        }

        return list;
    }
}
