package com.ai.application.agent.base.api.vo;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 智能体关联知识库表
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Data
@Schema(name = "")
public class AgentUseKbListVO {
    @Schema(description = "")
    private Integer akbId;

    /**
     * 状态 0失效 1有效
     */
    @Schema(description = "状态 0失效 1有效")
    private Integer akbStatus;

    /**
     * 智能体id
     */
    @Schema(description = "智能体id")
    private Integer agentId;

    /**
     * 智能体版本id
     */
    @Schema(description = "智能体版本id")
    private Integer versionId;

    /**
     * 知识库id
     */
    @Schema(description = "知识库id")
    private Integer kbId;

    /**
     * 额外属性
     */
    @Schema(description = "额外属性")
    private String kbExtend;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}