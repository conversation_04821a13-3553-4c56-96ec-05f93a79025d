package com.ai.application.agent.run.errors;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 执行器错误枚举单元测试
 */
class ExecutorErrorTest {

    @Test
    void testAgentNodeExecutorErrors() {
        // 测试 Agent 节点执行器错误
        assertEquals(51001, ExecutorError.AGENT_NODE_MISSING_INPUT_PARAMETERS.getCode());
        assertEquals("Agent节点缺少inputParameters配置", ExecutorError.AGENT_NODE_MISSING_INPUT_PARAMETERS.getMessage());

        assertEquals(51002, ExecutorError.AGENT_NODE_EXECUTOR_ERROR.getCode());
        assertEquals("Agent节点执行错误", ExecutorError.AGENT_NODE_EXECUTOR_ERROR.getMessage());

        assertEquals(51003, ExecutorError.AGENT_TYPE_IS_NULL.getCode());
        assertEquals("agentType 不能为空", ExecutorError.AGENT_TYPE_IS_NULL.getMessage());

        assertEquals(51004, ExecutorError.AGENT_MSG_CONTENT_IS_NULL.getCode());
        assertEquals("msgContent 不能为空", ExecutorError.AGENT_MSG_CONTENT_IS_NULL.getMessage());

        assertEquals(51005, ExecutorError.AGENT_RESPONSE_IS_NULL.getCode());
        assertEquals("Agent返回结果为空", ExecutorError.AGENT_RESPONSE_IS_NULL.getMessage());
    }

    @Test
    void testLlmNodeExecutorErrors() {
        // 测试 LLM 节点执行器错误
        assertEquals(51021, ExecutorError.LLM_NODE_MISSING_INPUT_PARAMETERS.getCode());
        assertEquals("LLM节点缺少inputParameters配置", ExecutorError.LLM_NODE_MISSING_INPUT_PARAMETERS.getMessage());

        assertEquals(51022, ExecutorError.LLM_PROMPT_IS_BLANK.getCode());
        assertEquals("prompt 不能为空", ExecutorError.LLM_PROMPT_IS_BLANK.getMessage());

        assertEquals(51023, ExecutorError.LLM_MODEL_IS_BLANK.getCode());
        assertEquals("model 不能为空", ExecutorError.LLM_MODEL_IS_BLANK.getMessage());

        assertEquals(51024, ExecutorError.LLM_CALL_FAILED.getCode());
        assertEquals("调用大模型失败", ExecutorError.LLM_CALL_FAILED.getMessage());

        assertEquals(51025, ExecutorError.LLM_RESPONSE_IS_NULL.getCode());
        assertEquals("大模型返回结果为空", ExecutorError.LLM_RESPONSE_IS_NULL.getMessage());
    }

    @Test
    void testKnowledgeAddExecutorErrors() {
        // 测试知识添加执行器错误
        assertEquals(51061, ExecutorError.KNOWLEDGE_INVENTORY_SN_IS_NULL.getCode());
        assertEquals("知识库编号不能为空", ExecutorError.KNOWLEDGE_INVENTORY_SN_IS_NULL.getMessage());

        assertEquals(51062, ExecutorError.KNOWLEDGE_INVENTORY_NOT_FOUND.getCode());
        assertEquals("知识库不存在", ExecutorError.KNOWLEDGE_INVENTORY_NOT_FOUND.getMessage());

        assertEquals(51063, ExecutorError.KNOWLEDGE_FILE_EMBEDDING_FAILED.getCode());
        assertEquals("文件嵌入失败", ExecutorError.KNOWLEDGE_FILE_EMBEDDING_FAILED.getMessage());

        assertEquals(51064, ExecutorError.KNOWLEDGE_FILE_STATUS_QUERY_FAILED.getCode());
        assertEquals("文件嵌入状态查询失败", ExecutorError.KNOWLEDGE_FILE_STATUS_QUERY_FAILED.getMessage());

        assertEquals(51065, ExecutorError.KNOWLEDGE_FILE_UPLOAD_FAILED.getCode());
        assertEquals("文件上传失败", ExecutorError.KNOWLEDGE_FILE_UPLOAD_FAILED.getMessage());

        assertEquals(51066, ExecutorError.KNOWLEDGE_SPLIT_RULE_CONFIG_ERROR.getCode());
        assertEquals("分段规则配置错误", ExecutorError.KNOWLEDGE_SPLIT_RULE_CONFIG_ERROR.getMessage());

        assertEquals(51067, ExecutorError.KNOWLEDGE_DEEP_PARSE_PARAM_ERROR.getCode());
        assertEquals("深度解析参数错误", ExecutorError.KNOWLEDGE_DEEP_PARSE_PARAM_ERROR.getMessage());

        assertEquals(51068, ExecutorError.KNOWLEDGE_FILE_DUPLICATE_CHECK_FAILED.getCode());
        assertEquals("文件重复检查失败", ExecutorError.KNOWLEDGE_FILE_DUPLICATE_CHECK_FAILED.getMessage());

        assertEquals(51069, ExecutorError.KNOWLEDGE_FILE_DELETE_FAILED.getCode());
        assertEquals("文件删除失败", ExecutorError.KNOWLEDGE_FILE_DELETE_FAILED.getMessage());

        assertEquals(51070, ExecutorError.KNOWLEDGE_EMBEDDING_POLLING_TIMEOUT.getCode());
        assertEquals("嵌入轮询超时", ExecutorError.KNOWLEDGE_EMBEDDING_POLLING_TIMEOUT.getMessage());
    }

    @Test
    void testKnowledgeQaExecutorErrors() {
        // 测试知识问答执行器错误
        assertEquals(51081, ExecutorError.KNOWLEDGE_QA_CONTENT_IS_BLANK.getCode());
        assertEquals("知识问答内容不能为空", ExecutorError.KNOWLEDGE_QA_CONTENT_IS_BLANK.getMessage());

        assertEquals(51082, ExecutorError.KNOWLEDGE_QA_MODEL_IS_BLANK.getCode());
        assertEquals("知识问答模型不能为空", ExecutorError.KNOWLEDGE_QA_MODEL_IS_BLANK.getMessage());

        assertEquals(51083, ExecutorError.KNOWLEDGE_QA_SUMMARY_PROMPT_IS_BLANK.getCode());
        assertEquals("知识问答总结提示词不能为空", ExecutorError.KNOWLEDGE_QA_SUMMARY_PROMPT_IS_BLANK.getMessage());

        assertEquals(51084, ExecutorError.KNOWLEDGE_SN_IS_NULL.getCode());
        assertEquals("知识编号不能为空", ExecutorError.KNOWLEDGE_SN_IS_NULL.getMessage());

        assertEquals(51085, ExecutorError.KNOWLEDGE_QA_RESULT_IS_NULL.getCode());
        assertEquals("知识问答结果为空", ExecutorError.KNOWLEDGE_QA_RESULT_IS_NULL.getMessage());

        assertEquals(51090, ExecutorError.KNOWLEDGE_QA_MODE_NOT_SUPPORTED.getCode());
        assertEquals("知识问答模式不支持", ExecutorError.KNOWLEDGE_QA_MODE_NOT_SUPPORTED.getMessage());
    }

    @Test
    void testDocumentSearchExecutorErrors() {
        // 测试文档知识检索执行器错误
        assertEquals(51101, ExecutorError.DOCUMENT_SEARCH_CONTENT_IS_BLANK.getCode());
        assertEquals("文档检索内容不能为空", ExecutorError.DOCUMENT_SEARCH_CONTENT_IS_BLANK.getMessage());

        assertEquals(51102, ExecutorError.DOCUMENT_SEARCH_RESULT_IS_NULL.getCode());
        assertEquals("文档检索结果为空", ExecutorError.DOCUMENT_SEARCH_RESULT_IS_NULL.getMessage());

        assertEquals(51103, ExecutorError.DOCUMENT_SEARCH_TYPE_NOT_SUPPORTED.getCode());
        assertEquals("文档检索类型不支持", ExecutorError.DOCUMENT_SEARCH_TYPE_NOT_SUPPORTED.getMessage());

        assertEquals(51104, ExecutorError.MULTI_RETRIEVER_REQUEST_FAILED.getCode());
        assertEquals("多检索器请求失败", ExecutorError.MULTI_RETRIEVER_REQUEST_FAILED.getMessage());

        assertEquals(51105, ExecutorError.EXPAND_SEARCH_PARAMS_ERROR.getCode());
        assertEquals("扩展检索参数错误", ExecutorError.EXPAND_SEARCH_PARAMS_ERROR.getMessage());

        assertEquals(51110, ExecutorError.FILE_SN_GET_FAILED.getCode());
        assertEquals("文件编号获取失败", ExecutorError.FILE_SN_GET_FAILED.getMessage());
    }

    @Test
    void testCommonExecutorErrors() {
        // 测试通用执行器错误
        assertEquals(51041, ExecutorError.NODE_DEFINITION_IS_NULL.getCode());
        assertEquals("节点定义为空", ExecutorError.NODE_DEFINITION_IS_NULL.getMessage());

        assertEquals(51042, ExecutorError.NODE_TYPE_NOT_SUPPORTED.getCode());
        assertEquals("节点类型不支持", ExecutorError.NODE_TYPE_NOT_SUPPORTED.getMessage());

        assertEquals(51043, ExecutorError.PARAMETER_PARSE_ERROR.getCode());
        assertEquals("参数解析错误", ExecutorError.PARAMETER_PARSE_ERROR.getMessage());

        assertEquals(51044, ExecutorError.VARIABLE_REPLACEMENT_ERROR.getCode());
        assertEquals("变量替换错误", ExecutorError.VARIABLE_REPLACEMENT_ERROR.getMessage());

        assertEquals(51045, ExecutorError.OUTPUT_PARAMETER_MAPPING_ERROR.getCode());
        assertEquals("输出参数映射错误", ExecutorError.OUTPUT_PARAMETER_MAPPING_ERROR.getMessage());

        assertEquals(51046, ExecutorError.NODE_EXECUTION_TIMEOUT.getCode());
        assertEquals("节点执行超时", ExecutorError.NODE_EXECUTION_TIMEOUT.getMessage());

        assertEquals(51047, ExecutorError.NODE_EXECUTION_INTERRUPTED.getCode());
        assertEquals("节点执行被中断", ExecutorError.NODE_EXECUTION_INTERRUPTED.getMessage());

        assertEquals(51048, ExecutorError.WORKFLOW_CONTEXT_IS_NULL.getCode());
        assertEquals("工作流上下文为空", ExecutorError.WORKFLOW_CONTEXT_IS_NULL.getMessage());

        assertEquals(51049, ExecutorError.NODE_CONTEXT_IS_NULL.getCode());
        assertEquals("节点上下文为空", ExecutorError.NODE_CONTEXT_IS_NULL.getMessage());

        assertEquals(51050, ExecutorError.EXECUTOR_INITIALIZATION_FAILED.getCode());
        assertEquals("执行器初始化失败", ExecutorError.EXECUTOR_INITIALIZATION_FAILED.getMessage());
    }

    @Test
    void testErrorCodeRanges() {
        // 验证错误码范围分配
        // Agent 错误: 51001-51020
        assertTrue(ExecutorError.AGENT_NODE_MISSING_INPUT_PARAMETERS.getCode() >= 51001);
        assertTrue(ExecutorError.AGENT_RESPONSE_IS_NULL.getCode() <= 51020);

        // LLM 错误: 51021-51040
        assertTrue(ExecutorError.LLM_NODE_MISSING_INPUT_PARAMETERS.getCode() >= 51021);
        assertTrue(ExecutorError.LLM_RESPONSE_IS_NULL.getCode() <= 51040);

        // 通用错误: 51041-51060
        assertTrue(ExecutorError.NODE_DEFINITION_IS_NULL.getCode() >= 51041);
        assertTrue(ExecutorError.EXECUTOR_INITIALIZATION_FAILED.getCode() <= 51060);

        // 知识添加错误: 51061-51080
        assertTrue(ExecutorError.KNOWLEDGE_INVENTORY_SN_IS_NULL.getCode() >= 51061);
        assertTrue(ExecutorError.KNOWLEDGE_EMBEDDING_POLLING_TIMEOUT.getCode() <= 51080);

        // 知识问答错误: 51081-51100
        assertTrue(ExecutorError.KNOWLEDGE_QA_CONTENT_IS_BLANK.getCode() >= 51081);
        assertTrue(ExecutorError.KNOWLEDGE_QA_MODE_NOT_SUPPORTED.getCode() <= 51100);

        // 文档知识检索错误: 51101-51120
        assertTrue(ExecutorError.DOCUMENT_SEARCH_CONTENT_IS_BLANK.getCode() >= 51101);
        assertTrue(ExecutorError.FILE_SN_GET_FAILED.getCode() <= 51120);
    }

    @Test
    void testErrorCodeUniqueness() {
        // 验证所有错误码都是唯一的
        ExecutorError[] errors = ExecutorError.values();
        for (int i = 0; i < errors.length; i++) {
            for (int j = i + 1; j < errors.length; j++) {
                assertNotEquals(errors[i].getCode(), errors[j].getCode(),
                    String.format("错误码重复: %s 和 %s 都使用了错误码 %d",
                        errors[i].name(), errors[j].name(), errors[i].getCode()));
            }
        }
    }

    @Test
    void testErrorMessageNotEmpty() {
        // 验证所有错误消息都不为空
        for (ExecutorError error : ExecutorError.values()) {
            assertNotNull(error.getMessage(), "错误消息不能为空: " + error.name());
            assertFalse(error.getMessage().trim().isEmpty(), "错误消息不能为空字符串: " + error.name());
        }
    }

    @Test
    void testErrorEnumImplementsIErrorCode() {
        // 验证枚举实现了 IErrorCode 接口
        assertTrue(ExecutorError.AGENT_NODE_MISSING_INPUT_PARAMETERS instanceof com.ai.framework.core.enums.IErrorCode);
        assertTrue(ExecutorError.LLM_PROMPT_IS_BLANK instanceof com.ai.framework.core.enums.IErrorCode);
        assertTrue(ExecutorError.NODE_DEFINITION_IS_NULL instanceof com.ai.framework.core.enums.IErrorCode);
        assertTrue(ExecutorError.KNOWLEDGE_INVENTORY_SN_IS_NULL instanceof com.ai.framework.core.enums.IErrorCode);
    }
}
