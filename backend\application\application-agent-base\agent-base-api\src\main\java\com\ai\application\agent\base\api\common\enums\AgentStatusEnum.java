package com.ai.application.agent.base.api.common.enums;

import java.util.Objects;

/**
 * 状态 0停用 1开发 5发布
 */
public enum AgentStatusEnum {
    DISABLE(0, "停用"),
    DEVELOP(1, "开发"),
    RELEASE(5, "发布"),
    ;

    private Integer code;
    private String title;

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.title;
    }

    private AgentStatusEnum(Integer code, String name) {
        this.code = code;
        this.title = name;
    }

    public static String getTitle(Integer code) {
        for(AgentStatusEnum vo :values() ) {
            if (Objects.equals(vo.code, code)) {
                return vo.title;
            }
        }
        return null;
    }

    public static Integer getCode(String title) {
        for(AgentStatusEnum vo :values() ) {
            if (Objects.equals(vo.title, title)) {
                return vo.code;
            }
        }
        return null;
    }
}
