<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.admin.mapper.TenantExtendMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.admin.api.entity.TenantExtend">
                    <id column="item_id" property="itemId" />
                    <result column="item_name" property="itemName" />
                    <result column="item_value" property="itemValue" />
                    <result column="item_status" property="itemStatus" />
                    <result column="tenant_id" property="tenantId" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        item_id, item_name, item_value, item_status, tenant_id, create_time, update_time
    </sql>
</mapper>