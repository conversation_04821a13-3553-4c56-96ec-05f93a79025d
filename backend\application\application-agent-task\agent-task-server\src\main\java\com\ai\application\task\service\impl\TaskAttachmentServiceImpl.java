package com.ai.application.task.service.impl;

import com.github.pagehelper.PageInfo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ai.application.task.mapper.TaskAttachmentMapper;
import com.ai.application.task.api.entity.TaskAttachment;
import com.ai.application.task.service.ITaskAttachmentService;
import com.ai.application.task.api.dto.TaskAttachmentDTO;
import com.ai.application.task.api.dto.query.TaskAttachmentQueryDTO;
import com.ai.application.task.api.vo.TaskAttachmentVO;
import com.ai.application.task.api.mapstruct.TaskAttachmentMapstruct;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Date;

/**
 * 任务附件表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Service
public class TaskAttachmentServiceImpl implements ITaskAttachmentService{

    @Resource
    private TaskAttachmentMapper taskAttachmentMapper;

    @Resource
    private TaskAttachmentMapstruct taskAttachmentMapstruct;

    @Transactional(readOnly = true)
    @Override
    public PageInfo<TaskAttachmentVO> page(TaskAttachmentQueryDTO queryDto) {
        QueryWrapper<TaskAttachment> queryWrapper = this.buildQuery(queryDto);
        Page<TaskAttachment> page = new Page<>(queryDto.getPageNo(), queryDto.getPageSize());
        Page<TaskAttachment> result = this.taskAttachmentMapper.selectPage(page, queryWrapper);
        return PageInfo.of(taskAttachmentMapstruct.toVoList(result.getRecords()));
    }

    @Transactional(readOnly = true)
    @Override
    public List<TaskAttachmentVO> list(TaskAttachmentQueryDTO queryDto) {
        QueryWrapper<TaskAttachment> queryWrapper = this.buildQuery(queryDto);
        return taskAttachmentMapstruct.toVoList(this.taskAttachmentMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(TaskAttachmentDTO dto) {
        dto.setTaskId(null);
        TaskAttachment entity = taskAttachmentMapstruct.toEntity(dto);
        entity.setCreateTime(new Date());

        taskAttachmentMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(TaskAttachmentDTO dto) {
        BusinessAssertUtil.notNull(dto.getTaskId(), "TaskId不能为空");

        // TODO 唯一性字段校验
        TaskAttachment entity = taskAttachmentMapper.selectById(dto.getTaskId());
        BusinessAssertUtil.notNull(entity, "找不到TaskId为 " + dto.getTaskId() + " 的记录");

        TaskAttachment entityList = taskAttachmentMapstruct.toEntity(dto);
        entityList.setUpdateTime(new Date());
        taskAttachmentMapper.updateById(entityList);
    }

    @Transactional(readOnly = true)
    @Override
    public TaskAttachmentVO get(Integer id) {
        BusinessAssertUtil.notNull(id, "TaskId不能为空");

        TaskAttachment entity = taskAttachmentMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到TaskId为 " + id + " 的记录");

        return taskAttachmentMapstruct.toVo(entity);
    }

    private QueryWrapper<TaskAttachment> buildQuery(TaskAttachmentQueryDTO queryDto) {
        QueryWrapper<TaskAttachment> queryWrapper = new QueryWrapper<>();
        return queryWrapper;
    }
}