# -*- coding: utf-8 -*-
"""
网络工具模块

网络相关的实用函数集合，提供：
- 本机IP地址获取（外部通信IP）
- 主机名解析和获取
- 端口可达性检测
- 多网卡IP地址枚举
- 域名解析功能
- 网络连通性检查

Project: Agent Memory Service
Author: 张小龙 <<EMAIL>>
Created: 2025-06-01
Version: 1.0.0
License: private
"""

import socket
from typing import Optional


def get_local_ip() -> str:
    """
    获取本机IP地址
    通过连接外部地址来获取本机的出口IP地址
    
    Returns:
        str: 本机IP地址
        
    Examples:
        >>> ip = get_local_ip()
        >>> print(f"本机IP: {ip}")
        本机IP: *************
    """
    try:
        # 创建一个socket连接到外部地址（不会真正连接）
        # 这样可以获取到本机用于外部通信的IP地址
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            # 连接到一个公共DNS服务器地址（*******）
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
        return local_ip
    except Exception:
        # 如果获取失败，降级到备用方法
        try:
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            # 避免返回回环地址
            if local_ip.startswith("127."):
                return "0.0.0.0"  # 使用通配符地址作为降级选项
            return local_ip
        except Exception:
            # 最后的降级选项
            return "127.0.0.1"


def get_hostname() -> str:
    """
    获取主机名
    
    Returns:
        str: 主机名
    """
    try:
        return socket.gethostname()
    except Exception:
        return "unknown"


def is_port_open(host: str, port: int, timeout: float = 3.0) -> bool:
    """
    检测指定主机和端口是否可连接
    
    Args:
        host: 主机地址
        port: 端口号
        timeout: 超时时间（秒）
        
    Returns:
        bool: 端口是否开放
    """
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(timeout)
            result = s.connect_ex((host, port))
            return result == 0
    except Exception:
        return False


def get_all_local_ips() -> list[str]:
    """
    获取本机所有网络接口的IP地址
    
    Returns:
        list[str]: 所有IP地址列表
    """
    try:
        hostname = socket.gethostname()
        ip_list = socket.gethostbyname_ex(hostname)[2]
        # 过滤掉回环地址
        return [ip for ip in ip_list if not ip.startswith("127.")]
    except Exception:
        return []


def resolve_hostname(hostname: str) -> Optional[str]:
    """
    解析主机名为IP地址
    
    Args:
        hostname: 主机名或域名
        
    Returns:
        Optional[str]: IP地址，解析失败返回None
    """
    try:
        return socket.gethostbyname(hostname)
    except Exception:
        return None 