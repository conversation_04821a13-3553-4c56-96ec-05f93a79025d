package com.ai.application.tenant.audit.api.mapstruct;

import com.ai.application.tenant.audit.api.dto.TenantAuditDTO;
import com.ai.application.tenant.audit.api.dto.query.TenantAddDTO;
import com.ai.application.tenant.audit.api.entity.TenantAudit;
import com.ai.application.tenant.audit.api.vo.TenantAuditVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 租户审核表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-03
 */

@Mapper(componentModel = "spring")
public interface TenantAuditMapstruct {
    TenantAudit toEntity(TenantAddDTO dto);
    TenantAudit toEntity(TenantAuditDTO dto);
    List<TenantAudit> toEntityList(List<TenantAuditDTO> dtolist);
    TenantAuditVO toVo(TenantAudit entity);
    List<TenantAuditVO> toVoList(List<TenantAudit> entities);
}
