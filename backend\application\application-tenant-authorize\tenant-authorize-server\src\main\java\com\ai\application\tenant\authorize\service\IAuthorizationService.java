package com.ai.application.tenant.authorize.service;

import com.ai.application.tenant.authorize.api.dto.ResourceAddReqDTO;
import com.ai.application.tenant.authorize.api.dto.ResourceGrantReqDTO;
import com.ai.application.tenant.authorize.api.dto.UserAuthorizationReqDTO;
import com.ai.application.tenant.authorize.api.dto.query.UserAuthorizationQueryDTO;
import com.ai.application.tenant.authorize.api.vo.ResourceVO;
import com.ai.application.tenant.authorize.vo.UserAuthorizationResultVO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface IAuthorizationService {
    @Transactional(readOnly = true)
    List<ResourceVO> queryGrantResourceList(ResourceGrantReqDTO dto);

    List<UserAuthorizationResultVO> list(UserAuthorizationQueryDTO queryDto);

    void authorization(UserAuthorizationReqDTO dto);

    void addResource(ResourceAddReqDTO dto);
}
