package com.ai.application.app.api.dto;

import com.ai.framework.core.sensitive.Sensitive;
import com.ai.framework.core.sensitive.SensitiveType;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @program: retail-system-center
 * @date 2024-11-28 09:19:31
 */
@Data
public class AppUserBatchImportData {
    @ExcelProperty("*登录名")
    private String userAccount;

    @ExcelProperty("姓名")
    private String userName;

    @ExcelProperty("*角色")
    private String roleName;

    @ExcelProperty("部门")
    private String deptName;

    @ExcelProperty("*手机号")
    private String userMobile;

    @ExcelProperty("邮箱")
    private String userEmail;

    @ExcelProperty("原因")
    private String failReson;
}
