package com.ai.application.admin.api.dto.query;

import com.ai.framework.core.vo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 租户表 查询条件
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(name = "租户表QueryDTO带分页")
public class TenantQueryPageDTO extends PageParam {
    /**
     * 租户名称/租户编码
     */
    @Schema(description = "租户名称/租户编码")
    private String keyword;

    @Schema(description = "开户时间-开始")
    private Date createDateStart;
    @Schema(description = "开户时间-结束")
    private Date createDateEnd;

    @Schema(description = "有效截止时间-开始")
    private Date expireDateStart;
    @Schema(description = "有效截止时间-结束")
    private Date expireDateEnd;

    /**
     * 租户状态：包括1:“正常”、2:“即将到期”、3:“已过期”、0:“已停用”
     */
    @Schema(description = "租户状态：包括1:“正常”、2:“即将到期”、3:“已过期”、0:“已停用”")
    private Integer tenantStatus;
}