package com.ai.application.app.auth.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(name = "用户登录返回")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GrantVO {
    /**
     * 令牌
     */
    @Schema(description = "accessToken")
    private String accessToken;

    /**
     * 刷新令牌
     */
    @Schema(description = "refreshToken")
    private String refreshToken;

    /**
     * 用户名称
     */
    @Schema(description = "用户名称")
    private String userName;

    /**
     * 用户编号
     */
    @Schema(description = "用户编号")
    private String userSn;

    /**
     * 租户编号
     */
    @Schema(description = "租户编码")
    private String tenantSn;

    /**
     * 租户域名
     */
    @Schema(description = "租户域名")
    private String tenantDomain;

    /**
     * 租户域名
     */
    @Schema(description = "租户名称")
    private String tenantName;
}
