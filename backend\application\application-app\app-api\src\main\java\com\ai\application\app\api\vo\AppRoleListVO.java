package com.ai.application.app.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 应用角色表
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Data
@Schema(name = "")
public class AppRoleListVO {

    @Schema(description = "roleId")
    private Integer roleId;

    /**
     * 角色code:SA,ADMIN,IT,USER
     */
    @Schema(description = "角色code:SA,ADMIN,IT,USER")
    private String roleCode;
    /**
     * 角色名称
     */
    @Schema(description = "角色名称")
    private String roleName;
    /**
     * 角色描述
     */
    @Schema(description = "角色描述")
    private String roleDesc;

    @Schema(description = "功能id集合")
    private List<Integer> functionIds;
}