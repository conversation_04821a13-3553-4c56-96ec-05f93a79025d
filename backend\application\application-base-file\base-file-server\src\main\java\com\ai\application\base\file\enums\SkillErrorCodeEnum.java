package com.ai.application.base.file.enums;


import com.ai.framework.core.enums.IErrorCode;

public enum SkillErrorCodeEnum implements IErrorCode {

    FILE_IS_NOT_EXISTS(41000, "文件不存在"),

    FILE_HAS_EXISTS(41001, "文件已存在"),

    EMBEDDING_INTERFACE_ERROR(41002, "embedding接口异常"),

    EMBEDDING_FILE_CAN_NOT_DELETE(41003, "embedding中的文件无法删除"),

    CAN_NOT_DELETE_EMBEDDING_FILE(41004, "知识学习中，无法删除"),

    CAN_NOT_DELETE_QUEUE_FILE(41005, "知识排队中，无法删除"),

    FILE_UPLOAD_FAILED(41006, "文件上传失败"),
    FILE_ID_IS_NULL(41007, "fileId为空"),
    FILE_GENERATE_FAILED(41008, "文件生成失败"),
    KNOWLEDGE_INVENTORY_NOT_EXISTS(41009, "知识库不存在"),
    EMBEDDING_COPY_ERROR(41010, "embedding复制失败"),
    TENANT_NOT_EXIST(41011, "租户不存在"),
    ;

    private Integer code;
    private String message;

    SkillErrorCodeEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    @Override
    public String getMessage() {
        return message;
    }

    @Override
    public Integer getCode() {
        return code;
    }
}
