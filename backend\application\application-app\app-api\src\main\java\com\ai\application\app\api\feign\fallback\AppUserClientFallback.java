package com.ai.application.app.api.feign.fallback;

import com.ai.application.app.api.dto.query.AppUserQueryDTO;
import com.ai.application.app.api.dto.query.AppUserVerifyPasswordDTO;
import com.ai.application.app.api.feign.IAppUserClient;
import com.ai.application.app.api.vo.AppUserDetailVO;
import com.ai.application.app.api.vo.AppUserVO;
import com.ai.framework.core.vo.ResultVo;

import java.util.List;

public class AppUserClientFallback implements IAppUserClient {

    @Override
    public ResultVo<AppUserVO> verifyPassword(AppUserVerifyPasswordDTO dto){
        return ResultVo.fail("验证密码错误");
    }

    @Override
    public ResultVo<AppUserDetailVO> getUserBySn(String userSn) {
        return ResultVo.fail("获取用户详情错误");
    }

   @Override
    public ResultVo<AppUserVO> getUserById(Integer userId) {
        return ResultVo.fail("获取用户详情错误");
    }

    @Override
    public ResultVo<List<AppUserVO>> list(AppUserQueryDTO queryDto) {
        return ResultVo.fail("查询用户列表失败");
    }

    @Override
    public ResultVo<List<AppUserVO>> ceshi(AppUserQueryDTO queryDto) {
        return null;
    }
}
