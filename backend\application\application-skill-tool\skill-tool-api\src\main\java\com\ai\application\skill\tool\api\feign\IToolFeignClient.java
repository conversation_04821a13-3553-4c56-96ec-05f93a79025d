package com.ai.application.skill.tool.api.feign;


import com.ai.application.skill.tool.api.vo.ToolDetailVo;
import com.ai.framework.core.constants.ServiceConstant;
import com.ai.framework.core.vo.ResultVo;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;


import java.io.IOException;
import java.util.List;

@FeignClient(value = ServiceConstant.SKILL_TOOL, contextId = "IToolFeignClient")
@Component
public interface IToolFeignClient {
    String API_PREFIX = "/v1/operate/feign";

    @GetMapping(API_PREFIX+"/getSkillDetailById/{skillSn}")
     ResultVo<ToolDetailVo> getSkillDetailById(@PathVariable("toolId") Integer toolId) ;



}
