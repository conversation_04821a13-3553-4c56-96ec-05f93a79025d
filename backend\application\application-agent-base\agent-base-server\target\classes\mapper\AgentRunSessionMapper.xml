<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.agent.base.mapper.AgentRunSessionMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.agent.base.api.entity.AgentRunSession">
                    <id column="session_id" property="sessionId" />
                    <result column="session_sn" property="sessionSn" />
                    <result column="session_title" property="sessionTitle" />
                    <result column="session_type" property="sessionType" />
                    <result column="session_status" property="sessionStatusEnum" />
                    <result column="session_metadata" property="sessionMetadata" />
                    <result column="agent_id" property="agentId" />
                    <result column="version_id" property="versionId" />
                    <result column="user_id" property="userId" />
                    <result column="tenant_id" property="tenantId" />
                    <result column="run_count" property="runCount" />
                    <result column="first_run_id" property="firstRunId" />
                    <result column="last_run_id" property="lastRunId" />
                    <result column="last_run_time" property="lastRunTime" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        session_id, session_sn, session_title, session_type, session_status, session_metadata, agent_id, version_id, user_id, tenant_id, run_count, first_run_id, last_run_id, last_run_time, create_time, update_time
    </sql>

    <select id="selectAgentRunSessionList" resultType="com.ai.application.agent.base.api.vo.AgentRunSessionVO">
        select
        <include refid="com.ai.application.agent.base.mapper.AgentRunSessionMapper.Base_Column_List"></include>
        from agent_run_session
        order by create_time desc limit 10;
    </select>
    <select id="querySessionCountAgentByUserId" resultType="com.ai.application.agent.base.api.vo.AgentStatVO">
        SELECT
            A.run_count session_count,
            B.agent_sn,
            B.agent_name,
            B.agent_desc,
            B.agent_type,
            B.create_time
        FROM
            (SELECT agent_id,SUM(run_count) run_count FROM `agent_run_session`
             WHERE tenant_id=#{tenantId} and user_id=#{userId} GROUP BY agent_id) A
                LEFT JOIN agent B ON(A.agent_id=B.agent_id)
        WHERE B.agent_status=5
    </select>

    <select id="getAgentTotalSessions" resultType="java.lang.Integer">
        SELECT
        SUM(session_id)
        FROM agent_run_session
        WHERE tenant_id=#{tenantId}
        <if test="userId!=null and userId >0">
          and user_id=#{userId}
        </if>
        <if test="days!=null and days = 7">
            and create_time>=DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        </if>
        <if test="days = 30">
            and create_time>=DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        </if>
        <if test="startDate!=null and endDate !=null">
            and create_time BETWEEN #{startDate} and #{endDate}
        </if>
        <if test="sessionStatus!=null">
            and session_status=#{sessionStatus}
        </if>
    </select>
</mapper>