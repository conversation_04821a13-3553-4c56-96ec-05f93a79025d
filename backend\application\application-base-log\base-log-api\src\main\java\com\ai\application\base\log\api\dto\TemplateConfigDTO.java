package com.ai.application.base.log.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class TemplateConfigDTO {
    @Schema(description = "接收类型：10：支持租户管理员:20：任务发起人:100：自定义")
    @NotNull(message = "接收类型不能为空")
    private Integer receiverType;

    @Schema(description = "接收方邮箱")
    private List<String> receiverEmails;

    @Schema(description = "通知类型id")
    @NotNull(message = "通知类型不能为空")
    private Integer noticeTypeId;

}
