package com.ai.application.app.service;

import com.ai.application.app.api.dto.AppRoleDTO;
import com.ai.application.app.api.dto.query.AppRoleQueryDTO;
import com.ai.application.app.api.vo.AppRoleListVO;
import com.ai.application.app.api.vo.AppRoleSimpleVO;
import com.ai.application.app.api.vo.AppRoleVO;
import com.github.pagehelper.PageInfo;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * 应用角色表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public interface IAppRoleService {

        /**
         * 分页
         *
         * @param queryDto
         * @return
         */
        PageInfo<AppRoleVO> page(AppRoleQueryDTO queryDto);

        /**
         * 列表
         *
         * @param queryDto
         * @return
         */
        List<AppRoleVO> list(AppRoleQueryDTO queryDto);

        @Transactional(readOnly = true)
        List<AppRoleSimpleVO> simpleList(AppRoleQueryDTO queryDto);

        @Transactional(readOnly = true)
        List<AppRoleListVO> queryRoles();

        /**
         * 保存
         *
         * @param dto
         */
        void save(AppRoleDTO dto);

        /**
         * 更新
         *
         * @param dto
         */
        void update(AppRoleDTO dto);

        /**
         * 查看
         *
         * @param id
         * @return
         */
        AppRoleVO get(Long id);

        /**
         * 删除
         *
         * @param ids
         */
        void delete(Set<Long> ids);
}