package com.ai.application.agent.run.executor;

import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import com.ai.framework.workflow.excutor.NodeExecutor;
import org.springframework.stereotype.Component;

@Component
public class StartNodeExecutor implements NodeExecutor {
    @Override
    public void execute(WorkflowContext context) {
        String nodeKey = context.getCurrentNodeKey();
        NodeContext nodeCtx = context.getNodeContexts().get(nodeKey);
        // 可在此初始化全局变量、上下文等
        // 例如：context.setVar("startTime", System.currentTimeMillis());
        nodeCtx.setStatus(NodeStatus.SUCCESS);
        nodeCtx.setEndTime(java.time.LocalDateTime.now());
    }

    @Override
    public String getType() { return "start"; }
}
