package com.ai.application.admin.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 资源定义表
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@Schema(name = "资源DTO")
public class GrantResourceDTO {
    /**
     * 资源对象id
     */
    @Schema(description = "资源id",hidden = true)
    private Integer resourceId;

    /**
     * 资源对象id
     */
    @Schema(description = "资源对象id(智能体/模型id)")
    private Integer objectId;

    /**
     * 资源对象id
     */
    @Schema(description = "资源对象名称(智能体/模型名称)")
    private Integer objectName;

}