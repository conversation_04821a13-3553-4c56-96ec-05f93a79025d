package com.ai.application.admin.api.enums;

import java.util.Objects;

/**
 * 资源类型:10-智能体,20-知识库,30-智能表格,40-字典,50-工具,60-MCP,70-模型
 */
public enum ResourceTypeEnum {
    AGENT(10, "智能体"),
    KNOWLEDGE(20, "知识库"),
    TABLE(30, "智能表格"),
    DICTIONARY(40, "字典"),
    TOOLS(50, "工具"),
    MCP(60, "MCP"),
    MODEL(70, "模型"),
    ;

    private Integer code;
    private String name;

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    ResourceTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
