package com.ai.application.agent.run.controller;

import com.ai.application.agent.run.service.IMasterRunService;
import com.ai.framework.core.vo.ResultVo;
import dto.MasterRunDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import vo.MasterRunVO;

@Tag(name = "智能规划", description = "智能规划")
@RestController
@RequestMapping("/v1/master")
public class MasterController {
    @Resource
    private IMasterRunService masterSessionService;

    /**
     * 智能规划执行
     * @return
     */
    @Operation(summary = "智能规划会话")
    @PostMapping(produces = "text/event-stream")
    public Flux<ResultVo<MasterRunVO>> run(@Validated @RequestBody MasterRunDTO dto) {
        return masterSessionService.run(dto);
    }
}