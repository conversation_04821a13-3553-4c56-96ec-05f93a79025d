package com.ai.application.app.service;

import com.github.pagehelper.PageInfo;
import com.ai.application.app.api.dto.AppDTO;
import com.ai.application.app.api.dto.query.AppQueryDTO;
import com.ai.application.app.api.vo.AppVO;
import java.util.List;
import java.util.Set;

/**
 * 应用表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-26
 */
public interface IAppService {

        /**
         * 分页
         *
         * @param queryDto
         * @return
         */
        PageInfo<AppVO> page(AppQueryDTO queryDto);

        /**
         * 列表
         *
         * @param sort
         * @param queryDto
         * @return
         */
        List<AppVO> list(AppQueryDTO queryDto);

        /**
         * 保存
         *
         * @param dto
         */
        void save(AppDTO dto);

        /**
         * 更新
         *
         * @param dto
         */
        void update(AppDTO dto);

        /**
         * 查看
         *
         * @param appId
         * @return
         */
        AppVO get(Integer appId);
}