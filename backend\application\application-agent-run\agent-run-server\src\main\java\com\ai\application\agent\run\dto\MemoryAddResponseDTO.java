package com.ai.application.agent.run.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 记忆添加响应DTO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(name = "MemoryAddResponseDTO")
public class MemoryAddResponseDTO {

    /**
     * 是否成功
     */
    @JsonProperty("success")
    @Schema(description = "是否成功")
    private Boolean success;

    /**
     * 响应消息
     */
    @JsonProperty("message")
    @Schema(description = "响应消息")
    private String message;

    /**
     * 响应时间
     */
    @JsonProperty("timestamp")
    @Schema(description = "响应时间")
    private LocalDateTime timestamp;

    /**
     * 响应数据
     */
    @JsonProperty("data")
    @Schema(description = "响应数据")
    private Map<String, Object> data;
}
