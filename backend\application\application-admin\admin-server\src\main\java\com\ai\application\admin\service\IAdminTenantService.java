package com.ai.application.admin.service;

import com.ai.application.admin.api.dto.TenantAddDTO;
import com.ai.application.admin.api.dto.TenantDTO;
import com.ai.application.admin.api.dto.TenantUpdateDTO;
import com.ai.application.admin.api.dto.TenantUpdatePasswordDTO;
import com.ai.application.admin.api.dto.query.TenantQueryDTO;
import com.ai.application.admin.api.dto.query.TenantQueryPageDTO;
import com.ai.application.admin.api.entity.Tenant;
import com.ai.application.admin.api.vo.AdminResourceVO;
import com.ai.application.admin.api.vo.TenantDetailVO;
import com.ai.application.admin.api.vo.TenantSimpleVO;
import com.ai.application.admin.api.vo.TenantVO;
import com.ai.framework.core.sensitive.SensitiveDecrypt;
import com.github.pagehelper.PageInfo;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

/**
 * 租户表 前端控制器
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public interface IAdminTenantService {

    /**
     * 分页
     *
     * @param queryDto
     * @return
     */
    PageInfo<TenantVO> page(TenantQueryPageDTO queryDto);

    /**
     * 列表
     *
     * @param queryDto
     * @return
     */
    List<TenantVO> list(TenantQueryDTO queryDto);

    /**
     * 保存
     *
     * @param dto
     */
    void save(TenantAddDTO dto);



    void updatePassword(String tenantSn, TenantUpdatePasswordDTO dto);

    TenantDetailVO detail(String tenantSn);

    void saveTenantStatus(String tenantSn, Boolean enable);

    /**
     * 查看
     *
     * @param id
     * @return
     */
    TenantVO get(Long id);

    void update(TenantUpdateDTO dto);

    List<TenantSimpleVO> valids();

    Tenant getTenantBySn(String tenantSn);

    Tenant getTenantById(Integer tenantId);

    List<AdminResourceVO> queryAgentList();

    List<AdminResourceVO> queryModelList();
}