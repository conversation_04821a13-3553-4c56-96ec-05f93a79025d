{"element": "serviceAction", "id": "smartTableSearch001", "name": "智能表格检索", "type": "SMART_TABLE_SEARCH", "inputParameters": {"intelligentTableSn": "table_sn_001", "ruleConditions": [{"definitionSn": "field1_sn", "condition": "==", "value": "value1", "filterType": "exact"}, {"definitionSn": "field2_sn", "condition": "contains", "value": "keyword", "filterType": "fuzzy"}], "semanticConditions": [{"definitionSn": ["field3_sn", "field4_sn"], "value": "semantic search text", "similarity": "0.8"}], "outputField": "1", "definitionSns": ["field1_sn", "field2_sn", "field3_sn"], "maxOutputRows": "100"}, "outputParameters": {"output": "searchResults"}}