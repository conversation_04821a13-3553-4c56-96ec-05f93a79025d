package com.ai.application.agent.base.api.mapstruct;
import com.ai.application.agent.base.api.entity.AgentRunLlm;
import com.ai.application.agent.base.api.dto.AgentRunLlmDTO;
import com.ai.application.agent.base.api.vo.AgentRunLlmVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 智能体LLM调用记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */

@Mapper(componentModel = "spring")
public interface AgentRunLlmMapstruct {

    AgentRunLlm toEntity(AgentRunLlmDTO dto);
    List<AgentRunLlm> toEntityList(List<AgentRunLlmDTO> dtolist);
    AgentRunLlmVO toVo(AgentRunLlm entity);
    List<AgentRunLlmVO> toVoList(List<AgentRunLlm> entities);
}
