package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.dto.AgentRunToolListDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ai.application.agent.base.mapper.AgentRunToolMapper;
import com.ai.application.agent.base.api.entity.AgentRunTool;
import com.ai.application.agent.base.service.IAgentRunToolService;
import com.ai.application.agent.base.api.dto.AgentRunToolDTO;
import com.ai.application.agent.base.api.vo.AgentRunToolVO;
import com.ai.application.agent.base.api.mapstruct.AgentRunToolMapstruct;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Date;

/**
 * 智能体工具执行记录表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
public class AgentRunToolServiceImpl implements IAgentRunToolService{

    @Resource
    private AgentRunToolMapper agentRunToolMapper;

    @Resource
    private AgentRunToolMapstruct agentRunToolMapstruct;

    @Transactional(readOnly = true)
    @Override
    public List<AgentRunToolVO> list(AgentRunToolListDTO queryDto) {
        LambdaQueryWrapper<AgentRunTool> queryWrapper = this.buildQuery(queryDto);
        return agentRunToolMapstruct.toVoList(this.agentRunToolMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(AgentRunToolDTO dto) {
        dto.setRunId(null);
        AgentRunTool entity = agentRunToolMapstruct.toEntity(dto);
        entity.setCreateTime(new Date());

        agentRunToolMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AgentRunToolDTO dto) {
        BusinessAssertUtil.notNull(dto.getToolRunId(), "RunId不能为空");

        AgentRunTool entity = agentRunToolMapper.selectById(dto.getToolRunId());
        BusinessAssertUtil.notNull(entity, "找不到ID为 " + dto.getToolRunId() + " 的记录");

        AgentRunTool entityList = agentRunToolMapstruct.toEntity(dto);
        entityList.setUpdateTime(new Date());
        agentRunToolMapper.updateById(entityList);
    }

    @Transactional(readOnly = true)
    @Override
    public AgentRunToolVO get(Integer id) {
        BusinessAssertUtil.notNull(id, "ID不能为空");

        AgentRunTool entity = agentRunToolMapper.selectById(id);
        BusinessAssertUtil.notNull(entity, "找不到ID为 " + id + " 的记录");

        return agentRunToolMapstruct.toVo(entity);
    }

    private LambdaQueryWrapper<AgentRunTool> buildQuery(AgentRunToolListDTO queryDto) {
        LambdaQueryWrapper<AgentRunTool> queryWrapper = new LambdaQueryWrapper<>();
        return queryWrapper;
    }
}