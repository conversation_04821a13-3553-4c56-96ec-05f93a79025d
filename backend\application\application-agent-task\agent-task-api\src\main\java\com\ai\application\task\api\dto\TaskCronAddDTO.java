package com.ai.application.task.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * 定时任务表
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Data
@Schema(name = "定时任务表创建DTO")
public class TaskCronAddDTO {
    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "关联智能体Sn")
    @NotBlank(message = "关联智能体Sn不能为空")
    private String agentSn;

    @Schema(description = "执行周期: 10：仅一次，20：每天重复，30：每周重复")
    private Integer executeCycle = 10;

    @Schema(description = "执行周(执行周期选【每周重复】时用)")
    private List<Integer> executeWeeks;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "消息模板id")
    private Integer notificationTemplateId;

    @Schema(description = "任务输入参数")
    private String taskInput;
}