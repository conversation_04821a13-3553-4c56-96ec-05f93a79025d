package com.ai.application.skill.tool.api.mapstruct;

import com.ai.application.skill.tool.api.dto.ToolDTO;
import com.ai.application.skill.tool.api.entity.Tool;
import com.ai.application.skill.tool.api.entity.Tool.ToolBuilder;
import com.ai.application.skill.tool.api.vo.ToolVO;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T09:54:03+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class ToolMapstructImpl implements ToolMapstruct {

    @Override
    public Tool toEntity(ToolDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ToolBuilder tool = Tool.builder();

        tool.toolId( dto.getToolId() );
        tool.toolSn( dto.getToolSn() );
        tool.toolName( dto.getToolName() );
        tool.toolDesc( dto.getToolDesc() );
        tool.toolLogo( dto.getToolLogo() );
        tool.toolType( dto.getToolType() );
        tool.toolStatus( dto.getToolStatus() );
        tool.toolWeight( dto.getToolWeight() );
        tool.toolAgents( dto.getToolAgents() );
        if ( dto.getCreateTime() != null ) {
            tool.createTime( new Timestamp( dto.getCreateTime().getTime() ) );
        }
        if ( dto.getUpdateTime() != null ) {
            tool.updateTime( new Timestamp( dto.getUpdateTime().getTime() ) );
        }

        return tool.build();
    }

    @Override
    public List<Tool> toEntityList(List<ToolDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<Tool> list = new ArrayList<Tool>( dtolist.size() );
        for ( ToolDTO toolDTO : dtolist ) {
            list.add( toEntity( toolDTO ) );
        }

        return list;
    }

    @Override
    public ToolVO toVo(Tool entity) {
        if ( entity == null ) {
            return null;
        }

        ToolVO toolVO = new ToolVO();

        toolVO.setToolId( entity.getToolId() );
        toolVO.setToolSn( entity.getToolSn() );
        toolVO.setToolName( entity.getToolName() );
        toolVO.setToolDesc( entity.getToolDesc() );
        toolVO.setToolLogo( entity.getToolLogo() );
        toolVO.setToolType( entity.getToolType() );
        toolVO.setToolStatus( entity.getToolStatus() );
        toolVO.setToolWeight( entity.getToolWeight() );
        toolVO.setToolAgents( entity.getToolAgents() );
        toolVO.setCreateTime( entity.getCreateTime() );
        toolVO.setUpdateTime( entity.getUpdateTime() );

        return toolVO;
    }

    @Override
    public List<ToolVO> toVoList(List<Tool> entities) {
        if ( entities == null ) {
            return null;
        }

        List<ToolVO> list = new ArrayList<ToolVO>( entities.size() );
        for ( Tool tool : entities ) {
            list.add( toVo( tool ) );
        }

        return list;
    }
}
