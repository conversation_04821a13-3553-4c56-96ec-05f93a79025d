package com.ai.application.base.log.api.mapstruct;
import com.ai.application.base.log.api.dto.NotificationTypeAddDTO;
import com.ai.application.base.log.api.dto.NotificationTypeUpdateDTO;
import com.ai.application.base.log.api.entity.NotificationType;
import com.ai.application.base.log.api.dto.NotificationTypeDTO;
import com.ai.application.base.log.api.vo.NotificationTypeDetailVO;
import com.ai.application.base.log.api.vo.NotificationTypePageVO;
import com.ai.application.base.log.api.vo.NotificationTypeVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 通知类型配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-09
 */

@Mapper(componentModel = "spring")
public interface NotificationTypeMapstruct {

    NotificationType toEntity(NotificationTypeDTO dto);
    NotificationType toEntity(NotificationTypeAddDTO dto);
    NotificationType toEntity(NotificationTypeUpdateDTO dto);
    List<NotificationType> toEntityList(List<NotificationTypeDTO> dtolist);
    NotificationTypeVO toVo(NotificationType entity);
    NotificationTypeDetailVO toDetailVo(NotificationType entity);
    List<NotificationTypeVO> toVoList(List<NotificationType> entities);
    List<NotificationTypePageVO> toPageVoList(List<NotificationType> entities);
}
