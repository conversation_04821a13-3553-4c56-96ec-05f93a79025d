<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.app.mapper.AppFunctionMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.app.api.entity.AppFunction">
                    <id column="fun_id" property="funId" />
                    <result column="fun_type" property="funType" />
                    <result column="fun_name" property="funName" />
                    <result column="fun_sort" property="funSort" />
                    <result column="res_code" property="resCode" />
                    <result column="fun_status" property="funStatus" />
                    <result column="parent_id" property="parentId" />
                    <result column="ref_ids" property="refIds" />
                    <result column="ref_tenant" property="refTenant" />
                    <result column="app_id" property="appId" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        fun_id, fun_type, fun_name, fun_sort, res_code, fun_status, parent_id, ref_ids, ref_tenant, app_id, create_time, update_time
    </sql>

    <select id="selectAppFunctionList" resultType="com.ai.application.app.api.vo.AppFunctionVO">
        select
        <include refid="com.ai.application.app.mapper.AppFunctionMapper.Base_Column_List"></include>
        from app_function
        order by create_time desc limit 10;
    </select>
</mapper>