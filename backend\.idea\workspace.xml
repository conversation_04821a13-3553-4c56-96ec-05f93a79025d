<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="5310e1aa-8300-4090-9b05-c9450f3e2499" name="Changes" comment="【工作流组件-添加知识】：待完善embedding 接口&#10;【工作流组件-知识问答】：待完善调用模型接口逻辑" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="feature/login" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\maven_repo" />
        <option name="userSettingsFile" value="D:\apache-maven-3.9.10\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2yOq4dQTubUPco4G00muCdcbFmA" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "JUnit.KnowledgeAddNodeExecutorIntegrationTest.testKnowledgeAddWithCustomSplitRule.executor": "Debug",
    "JUnit.KnowledgeAddNodeExecutorTest.testExecuteSuccessWithFileInput.executor": "Debug",
    "JUnit.KnowledgeAddNodeExecutorTest.testExecuteWithContinueWhenError.executor": "Debug",
    "JUnit.KnowledgeAddNodeExecutorTest.testExecuteWithVariableReplacement.executor": "Debug",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.AdminApplication.executor": "Debug",
    "Spring Boot.AgentBaseApplication.executor": "Debug",
    "Spring Boot.AgentRunApplication.executor": "Debug",
    "Spring Boot.AgentTaskApplication.executor": "Debug",
    "Spring Boot.AppApplication.executor": "Debug",
    "Spring Boot.AppSessionApplication.executor": "Debug",
    "Spring Boot.AuthServerApplication.executor": "Debug",
    "Spring Boot.BaseFileApplication.executor": "Debug",
    "Spring Boot.BaseLogApplication.executor": "Debug",
    "Spring Boot.BaseModelApplication.executor": "Debug",
    "Spring Boot.BaseNoticeApplication.executor": "Debug",
    "Spring Boot.BaseSearchApplication.executor": "Debug",
    "Spring Boot.KnowledgeDictApplication.executor": "Debug",
    "Spring Boot.KnowledgeDocApplication.executor": "Debug",
    "Spring Boot.KnowledgeTableApplication.executor": "Debug",
    "Spring Boot.MarketApplication.executor": "Debug",
    "Spring Boot.SkillMcpApplication.executor": "Debug",
    "Spring Boot.SkillToolApplication.executor": "Debug",
    "Spring Boot.TenantApplication.executor": "Debug",
    "Spring Boot.TenantAuditApplication.executor": "Debug",
    "Spring Boot.TenantAuthorizeApplication.executor": "Debug",
    "git-widget-placeholder": "feature/executor__from__login",
    "last_opened_file_path": "D:/idea_workspace/allbackend/backend",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.0",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "project.propVCSSupport.CommitDialog",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="JUnit.KnowledgeAddNodeExecutorTest.testExecuteSuccessWithFileInput">
    <configuration name="KnowledgeAddNodeExecutorIntegrationTest.testKnowledgeAddWithCustomSplitRule" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="agent-run-server" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ai.application.agent.run.executor.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.ai.application.agent.run.executor" />
      <option name="MAIN_CLASS_NAME" value="com.ai.application.agent.run.executor.KnowledgeAddNodeExecutorIntegrationTest" />
      <option name="METHOD_NAME" value="testKnowledgeAddWithCustomSplitRule" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="KnowledgeAddNodeExecutorTest.testExecuteSuccessWithFileInput" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="agent-run-server" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ai.application.agent.run.executor.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.ai.application.agent.run.executor" />
      <option name="MAIN_CLASS_NAME" value="com.ai.application.agent.run.executor.KnowledgeAddNodeExecutorTest" />
      <option name="METHOD_NAME" value="testExecuteSuccessWithFileInput" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="KnowledgeAddNodeExecutorTest.testExecuteWithContinueWhenError" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="agent-run-server" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ai.application.agent.run.executor.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.ai.application.agent.run.executor" />
      <option name="MAIN_CLASS_NAME" value="com.ai.application.agent.run.executor.KnowledgeAddNodeExecutorTest" />
      <option name="METHOD_NAME" value="testExecuteWithContinueWhenError" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="KnowledgeAddNodeExecutorTest.testExecuteWithVariableReplacement" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="agent-run-server" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.ai.application.agent.run.executor.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.ai.application.agent.run.executor" />
      <option name="MAIN_CLASS_NAME" value="com.ai.application.agent.run.executor.KnowledgeAddNodeExecutorTest" />
      <option name="METHOD_NAME" value="testExecuteWithVariableReplacement" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="admin-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.admin.AdminApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AgentApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="agent-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.agent.base.AgentApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AgentBaseApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="ACTIVE_PROFILES" value="-server.port=8090" />
      <module name="agent-base-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.agent.base.AgentBaseApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AgentRunApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="agent-run-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.agent.run.AgentRunApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AgentTaskApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="agent-task-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.task.AgentTaskApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AppApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="app-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.app.AppApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AppSessionApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="app-session-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.app.session.AppSessionApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AuthServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="app-auth-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.app.auth.AuthServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BaseFileApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="base-file-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.base.file.BaseFileApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BaseLogApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="base-log-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.base.log.BaseLogApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BaseModelApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="base-model-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.base.model.BaseModelApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BaseNoticeApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="base-notice-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.base.notice.BaseNoticeApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BaseSearchApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="base-search-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.base.search.BaseSearchApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="FileApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="skill-file-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.skill.file.FileApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="KnowledgeDictApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="knowledge-dict-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.knowledge.dict.KnowledgeDictApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="KnowledgeDocApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="knowledge-doc-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.knowledge.table.KnowledgeDocApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="KnowledgeTableApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="knowledge-table-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.knowledge.table.KnowledgeTableApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="MarketApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="market-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.market.MarketApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ProcessServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="process-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.process.ProcessServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SkillMcpApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="skill-mcp-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.skill.mcp.SkillMcpApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="SkillToolApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="skill-tool-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.skill.tool.SkillToolApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TenantApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="tenant-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.tenant.TenantApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TenantAuditApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="tenant-audit-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.tenant.audit.TenantAuditApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TenantAuthorizeApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="tenant-authorize-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.tenant.authorize.TenantAuthorizeApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="UserApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="user-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.user.UserApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="WebSearchApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="agent-web-search-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.ai.application.agent.web.search.WebSearchApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.KnowledgeAddNodeExecutorTest.testExecuteSuccessWithFileInput" />
        <item itemvalue="JUnit.KnowledgeAddNodeExecutorTest.testExecuteWithContinueWhenError" />
        <item itemvalue="JUnit.KnowledgeAddNodeExecutorIntegrationTest.testKnowledgeAddWithCustomSplitRule" />
        <item itemvalue="JUnit.KnowledgeAddNodeExecutorTest.testExecuteWithVariableReplacement" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="5310e1aa-8300-4090-9b05-c9450f3e2499" name="Changes" comment="" />
      <created>1749715777669</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749715777669</updated>
      <workItem from="1749715778844" duration="1475000" />
      <workItem from="1749717276945" duration="3984000" />
      <workItem from="1749721288193" duration="196000" />
      <workItem from="1749721490916" duration="168000" />
      <workItem from="1749721676684" duration="15321000" />
      <workItem from="1749778120540" duration="297000" />
      <workItem from="1749778796902" duration="41769000" />
    </task>
    <task id="LOCAL-00001" summary="【agent 组件代码暂提交】：待完善用户上下文+会话获取+不同类型agent api 接口">
      <option name="closed" value="true" />
      <created>1749792987287</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1749792987287</updated>
    </task>
    <task id="LOCAL-00002" summary="【LLM 模型代码暂提交】：待完善提供调用具体agent api 接口">
      <option name="closed" value="true" />
      <created>1749794746708</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1749794746708</updated>
    </task>
    <task id="LOCAL-00003" summary="【工作流组件-添加知识】：待完善embedding 接口&#10;【工作流组件-知识问答】：待完善调用模型接口逻辑">
      <option name="closed" value="true" />
      <created>1749872900008</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1749872900008</updated>
    </task>
    <option name="localTasksCounter" value="4" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="CUSTOM_BOOLEAN_PROPERTIES">
                <map>
                  <entry key="Show.Git.Branches" value="true" />
                </map>
              </option>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="feature/executor_from_login" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CODE_SMELLS_PROFILE" value="Default" />
    <MESSAGE value="agent 暂存" />
    <MESSAGE value="llm" />
    <MESSAGE value="【agent 组件代码暂提交】：待完善用户上下文+会话获取+不同类型agent api 接口" />
    <MESSAGE value="【LLM 模型代码暂提交】：待完善提供调用具体agent api 接口" />
    <MESSAGE value="【工作流组件-添加知识】：待完善embedding 接口&#10;【工作流组件-知识问答】：待完善调用模型接口逻辑" />
    <option name="LAST_COMMIT_MESSAGE" value="【工作流组件-添加知识】：待完善embedding 接口&#10;【工作流组件-知识问答】：待完善调用模型接口逻辑" />
  </component>
  <component name="XDebuggerManager">
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="com.ai.framework.core.vo.ResultVo" memberName="data" />
        <PinnedItemInfo parentTag="com.ai.framework.core.vo.ResultVo" memberName="success" />
      </pinned-members>
    </pin-to-top-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>