package com.ai.application.base.log.controller;

import com.ai.application.base.log.api.dto.query.NotificationParameterQueryDTO;
import com.ai.application.base.log.api.vo.NotificationParameterVO;
import com.ai.application.base.log.service.INotificationParameterService;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 通知参数配置表-前端控制器
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Tag(name = "通知参数配置表", description = "通知参数配置表-相关操作")
@Validated
@CrossOrigin
@RestController
@RequestMapping("/v1/parameter")
public class NotificationParameterController {

    @Resource
    private INotificationParameterService  notificationParameterService;

    /**
     * 分页查询
     *
     * @param queryDto
     * @return
     */
    @Operation(summary = "通知参数配置表-列表不带分页", description = "查询所有通知参数配置表 信息")
    @PostMapping("/list")
    public ResultVo<List<NotificationParameterVO>> list(@Validated @RequestBody NotificationParameterQueryDTO queryDto){
        return ResultVo.data(notificationParameterService.list(queryDto));
    }
}