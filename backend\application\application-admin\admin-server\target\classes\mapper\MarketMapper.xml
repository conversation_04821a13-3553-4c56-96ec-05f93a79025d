<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.admin.mapper.MarketMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.admin.api.entity.Market">
                    <id column="market_id" property="marketId" />
                    <result column="market_sn" property="marketSn" />
                    <result column="market_type" property="marketType" />
                    <result column="tenant_id" property="tenantId" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        market_id, market_sn, market_type, tenant_id, create_time, update_time
    </sql>
</mapper>