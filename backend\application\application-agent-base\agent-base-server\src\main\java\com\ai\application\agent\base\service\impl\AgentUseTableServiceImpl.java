package com.ai.application.agent.base.service.impl;

import com.ai.application.agent.base.api.dto.AgentUseTableUpdateDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ai.application.agent.base.mapper.AgentUseTableMapper;
import com.ai.application.agent.base.api.entity.AgentUseTable;
import com.ai.application.agent.base.service.IAgentUseTableService;
import com.ai.application.agent.base.api.dto.AgentUseTableAddDTO;
import com.ai.application.agent.base.api.dto.AgentUseTableListDTO;
import com.ai.application.agent.base.api.vo.AgentUseTableListVO;
import com.ai.application.agent.base.api.mapstruct.AgentUseTableMapstruct;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ai.framework.core.util.BusinessAssertUtil;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Date;

/**
 * 智能体关联智能表格表-服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-07
 */
@Service
public class AgentUseTableServiceImpl implements IAgentUseTableService{

    @Resource
    private AgentUseTableMapper agentUseTableMapper;

    @Resource
    private AgentUseTableMapstruct agentUseTableMapstruct;

    @Transactional(readOnly = true)
    @Override
    public List<AgentUseTableListVO> list(AgentUseTableListDTO queryDto) {
        LambdaQueryWrapper<AgentUseTable> queryWrapper = this.buildQuery(queryDto);
        return agentUseTableMapstruct.toVoList(this.agentUseTableMapper.selectList(queryWrapper));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(AgentUseTableAddDTO dto) {
        AgentUseTable entity = agentUseTableMapstruct.toAddEntity(dto);
        entity.setCreateTime(new Date());
        entity.setUpdateTime(new Date());

        agentUseTableMapper.insert(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(AgentUseTableUpdateDTO dto) {
        BusinessAssertUtil.notNull(dto.getTableId(), "AdId不能为空");

        AgentUseTable entity = agentUseTableMapper.selectById(dto.getTableId());
        BusinessAssertUtil.notNull(entity, "找不到为 " + dto.getTableId() + " 的记录");

        AgentUseTable entityList = agentUseTableMapstruct.toUpdateEntity(dto);
        entityList.setUpdateTime(new Date());
        agentUseTableMapper.updateById(entityList);
    }

    private LambdaQueryWrapper<AgentUseTable> buildQuery(AgentUseTableListDTO queryDto) {
        LambdaQueryWrapper<AgentUseTable> queryWrapper = new LambdaQueryWrapper<>();
        return queryWrapper;
    }
}