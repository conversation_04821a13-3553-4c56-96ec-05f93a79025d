package com.ai.application.agent.base.api.mapstruct;
import com.ai.application.agent.base.api.entity.AgentRunStep;
import com.ai.application.agent.base.api.dto.AgentRunStepDTO;
import com.ai.application.agent.base.api.vo.AgentRunStepVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 智能体运行步骤表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */

@Mapper(componentModel = "spring")
public interface AgentRunStepMapstruct {

    AgentRunStep toEntity(AgentRunStepDTO dto);
    List<AgentRunStep> toEntityList(List<AgentRunStepDTO> dtolist);
    AgentRunStepVO toVo(AgentRunStep entity);
    List<AgentRunStepVO> toVoList(List<AgentRunStep> entities);
}
