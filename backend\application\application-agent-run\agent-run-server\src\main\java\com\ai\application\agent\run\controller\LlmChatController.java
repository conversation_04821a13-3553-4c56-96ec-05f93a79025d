package com.ai.application.agent.run.controller;

import com.ai.application.agent.run.dto.ProcessChatDTO;
import com.ai.application.agent.run.service.ILlmChatService;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * LLM 聊天控制器
 */
@Tag(name = "大模型对话", description = "大模型对话接口")
@RestController
@RequestMapping("/v1/feign/llm")
@AllArgsConstructor
public class LlmChatController {

    private final ILlmChatService llmChatService;

    /**
     * 调用大模型进行对话
     */
    @Operation(summary = "调用大模型对话")
    @PostMapping("/chat")
    public ResultVo<String> chat(
            @Validated @RequestBody ProcessChatDTO dto,
            @RequestHeader(value = "Authorization", required = false) String authorization) {
        return llmChatService.chat(dto);
    }
}
