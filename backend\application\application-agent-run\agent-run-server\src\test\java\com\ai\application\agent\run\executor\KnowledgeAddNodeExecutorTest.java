package com.ai.application.agent.run.executor;

import com.ai.application.agent.run.errors.ExecutorError;
import com.ai.application.agent.run.feign.IFileProcessClient;
import com.ai.application.agent.run.service.IKnowledgeService;
import com.ai.application.base.file.api.dto.FileInfoDto;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.NodeContext;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.enums.NodeStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 知识添加节点执行器单元测试
 */
@ExtendWith(MockitoExtension.class)
class KnowledgeAddNodeExecutorTest {

    @Mock
    private IKnowledgeService knowledgeService;

    @Mock
    private IFileProcessClient fileProcessClient;

    @InjectMocks
    private KnowledgeAddNodeExecutor knowledgeAddNodeExecutor;

    private WorkflowContext workflowContext;
    private NodeContext nodeContext;
    private Map<String, Object> nodeDefinition;
    private Map<String, Object> inputParameters;
    private Map<String, Object> outputParameters;

    @BeforeEach
    void setUp() {
        // 初始化工作流上下文
        workflowContext = new WorkflowContext();
        workflowContext.setWorkflowInstanceId(12345L);
        workflowContext.setCurrentNodeKey("knowledge_add_node_001");
        workflowContext.setGlobalVars(new HashMap<>());
        workflowContext.getGlobalVars().put("authorization", "Bearer test-token");
        workflowContext.getGlobalVars().put("tenantName", "TEST_TENANT");

        // 初始化节点上下文
        nodeContext = new NodeContext();
        nodeContext.setNodeKey("knowledge_add_node_001");
        nodeContext.setStatus(NodeStatus.INIT);
        nodeContext.setOutput(new HashMap<>());

        // 初始化输入参数
        inputParameters = new HashMap<>();
        inputParameters.put("knowledgeInventorySn", "test_knowledge_base");
        inputParameters.put("continueWhenErr", "0");
        inputParameters.put("knowledgeType", "sn");
        inputParameters.put("input", "这是一个测试文本内容");
        inputParameters.put("deepParse", Arrays.asList("parseWord", "parseImage"));
        inputParameters.put("splitRuleType", "0");

        // 初始化输出参数
        outputParameters = new HashMap<>();
        outputParameters.put("embeddingFiles", "embeddingFilesVar");
        outputParameters.put("knowledgeInventorySn", "knowledgeInventorySnVar");
        outputParameters.put("resultMessage", "resultMessageVar");

        // 初始化节点定义
        nodeDefinition = new HashMap<>();
        nodeDefinition.put("inputParameters", inputParameters);
        nodeDefinition.put("outputParameters", outputParameters);
        nodeContext.setNodeDefinition(nodeDefinition);

        // 设置节点上下文
        workflowContext.getNodeContexts().put("knowledge_add_node_001", nodeContext);
    }

    @Test
    void testExecuteSuccessWithTextInput() {
        // 准备测试数据
        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
        inventoryInfo.setModelSn("test-model");
        inventoryInfo.setSplitRule(1);
        inventoryInfo.setSplitter(1);
        inventoryInfo.setWordCountLimit(500);
        inventoryInfo.setWordCountOverlap(50);
        inventoryInfo.setSeparatorContent("[\"\n\", \"\n\n\", \" \"]");

        FileInfoDto embeddedFile = new FileInfoDto();
        embeddedFile.setFileId(12345L);
        embeddedFile.setFileSn("test_file_sn");
        embeddedFile.setFileName("test_file.txt");
        embeddedFile.setFileType("text/plain");
        embeddedFile.setFileMd5("test_md5");

        IKnowledgeService.FileStatusInfo statusInfo = new IKnowledgeService.FileStatusInfo();
        statusInfo.setStatus(2); // 成功状态

        // 模拟服务调用
        when(knowledgeService.checkKnowledgeInventory(anyString())).thenReturn(inventoryInfo);
        when(fileProcessClient.processFileUpload(any(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(ResultVo.data("上传成功"));
        when(knowledgeService.findFilesByDatasetIdAndMd5(anyString(), anyString()))
                .thenReturn(ResultVo.data(new ArrayList<>()));
        when(knowledgeService.fileEmbedding(any(), anyString())).thenReturn(ResultVo.data(embeddedFile));
        when(knowledgeService.getFileStatus(anyLong())).thenReturn(ResultVo.data(statusInfo));

        // 执行测试
        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));

        // 验证结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        assertNotNull(nodeContext.getOutput().get("embeddingFiles"));
        assertNotNull(nodeContext.getOutput().get("resultMessage"));
        assertNotNull(nodeContext.getEndTime());

        // 验证服务调用
        verify(knowledgeService, times(1)).checkKnowledgeInventory("test_knowledge_base");
        verify(knowledgeService, times(1)).fileEmbedding(any(), eq("Bearer test-token"));
        verify(knowledgeService, times(1)).getFileStatus(12345L);
    }

    @Test
    void testExecuteSuccessWithFileInput() {
        // 准备测试数据 - 文件类型输入
        inputParameters.put("input", "test_file_sn::test_file.pdf::DOC");

        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
        inventoryInfo.setModelSn("test-model");
        inventoryInfo.setSplitRule(1);
        inventoryInfo.setSplitter(1);
        inventoryInfo.setWordCountLimit(500);
        inventoryInfo.setWordCountOverlap(50);

        IFileProcessClient.ProcessFileInfo processFileInfo = new IFileProcessClient.ProcessFileInfo();
        processFileInfo.setFileSn("test_file_sn");
        processFileInfo.setFileName("test_file.pdf");
        processFileInfo.setFileType("application/pdf");
        processFileInfo.setDataMd5("test_file_md5");

        FileInfoDto embeddedFile = new FileInfoDto();
        embeddedFile.setFileId(12345L);
        embeddedFile.setFileSn("test_file_sn");
        embeddedFile.setFileName("test_file.pdf");

        IKnowledgeService.FileStatusInfo statusInfo = new IKnowledgeService.FileStatusInfo();
        statusInfo.setStatus(2); // 成功状态

        // 模拟服务调用
        when(knowledgeService.checkKnowledgeInventory(anyString())).thenReturn(inventoryInfo);
        when(fileProcessClient.findProcessFileByFileSn("test_file_sn"))
                .thenReturn(ResultVo.data(Arrays.asList(processFileInfo)));
        when(knowledgeService.findFilesByDatasetIdAndMd5(anyString(), anyString()))
                .thenReturn(ResultVo.data(new ArrayList<>()));
        when(knowledgeService.fileEmbedding(any(), anyString())).thenReturn(ResultVo.data(embeddedFile));
        when(knowledgeService.getFileStatus(anyLong())).thenReturn(ResultVo.data(statusInfo));

        // 执行测试
        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));

        // 验证结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        verify(fileProcessClient, times(1)).findProcessFileByFileSn("test_file_sn");
    }

    @Test
    void testExecuteWithDuplicateFile() {
        // 准备测试数据 - 重复文件
        FileInfoDto existingFile = new FileInfoDto();
        existingFile.setFileId(99999L);
        existingFile.setFileSn("existing_file_sn");
        existingFile.setFileName("existing_file.txt");

        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
        inventoryInfo.setModelSn("test-model");

        IKnowledgeService.FileStatusInfo statusInfo = new IKnowledgeService.FileStatusInfo();
        statusInfo.setStatus(2); // 成功状态

        // 模拟服务调用
        when(knowledgeService.checkKnowledgeInventory(anyString())).thenReturn(inventoryInfo);
        when(fileProcessClient.processFileUpload(any(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(ResultVo.data("上传成功"));
        when(knowledgeService.findFilesByDatasetIdAndMd5(anyString(), anyString()))
                .thenReturn(ResultVo.data(Arrays.asList(existingFile)));
        when(knowledgeService.getFileStatus(99999L)).thenReturn(ResultVo.data(statusInfo));

        // 执行测试
        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));

        // 验证结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        // 验证没有调用文件嵌入（因为文件已存在）
        verify(knowledgeService, never()).fileEmbedding(any(), anyString());
    }

    @Test
    void testExecuteMissingInputParameters() {
        // 准备测试数据 - 缺少输入参数
        nodeDefinition.remove("inputParameters");

        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, 
            () -> knowledgeAddNodeExecutor.execute(workflowContext));

        assertEquals(ExecutorError.NODE_DEFINITION_IS_NULL.getCode(), exception.getCode());
        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
        assertNotNull(nodeContext.getErrorMsg());
        assertNotNull(nodeContext.getEndTime());
    }

    @Test
    void testExecuteBlankKnowledgeInventory() {
        // 准备测试数据 - 空的知识库编号
        inputParameters.put("knowledgeInventorySn", "");

        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, 
            () -> knowledgeAddNodeExecutor.execute(workflowContext));

        assertEquals(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), exception.getCode());
        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
    }

    @Test
    void testExecuteNullInput() {
        // 准备测试数据 - 空的输入
        inputParameters.put("input", null);

        // 执行测试并验证异常
        ServiceException exception = assertThrows(ServiceException.class, 
            () -> knowledgeAddNodeExecutor.execute(workflowContext));

        assertEquals(ExecutorError.PARAMETER_PARSE_ERROR.getCode(), exception.getCode());
        assertEquals(NodeStatus.FAILED, nodeContext.getStatus());
    }

    @Test
    void testExecuteWithVariableReplacement() {
        // 准备测试数据 - 使用变量替换
        workflowContext.getGlobalVars().put("knowledgeBase", "variable_knowledge_base");
        workflowContext.getGlobalVars().put("textContent", "这是来自变量的文本内容");
        
        inputParameters.put("knowledgeInventorySn", "$knowledgeBase");
        inputParameters.put("input", "$textContent");

        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
        inventoryInfo.setModelSn("test-model");

        FileInfoDto embeddedFile = new FileInfoDto();
        embeddedFile.setFileId(12345L);

        IKnowledgeService.FileStatusInfo statusInfo = new IKnowledgeService.FileStatusInfo();
        statusInfo.setStatus(2);

        // 模拟服务调用
        when(knowledgeService.checkKnowledgeInventory("variable_knowledge_base")).thenReturn(inventoryInfo);
        when(fileProcessClient.processFileUpload(any(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(ResultVo.data("上传成功"));
        when(knowledgeService.findFilesByDatasetIdAndMd5(anyString(), anyString()))
                .thenReturn(ResultVo.data(new ArrayList<>()));
        when(knowledgeService.fileEmbedding(any(), anyString())).thenReturn(ResultVo.data(embeddedFile));
        when(knowledgeService.getFileStatus(anyLong())).thenReturn(ResultVo.data(statusInfo));

        // 执行测试
        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));

        // 验证结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        verify(knowledgeService, times(1)).checkKnowledgeInventory("variable_knowledge_base");
    }

    @Test
    void testExecuteWithContinueWhenError() {
        // 准备测试数据 - 遇到错误继续执行
        inputParameters.put("continueWhenErr", "1");
        inputParameters.put("input", Arrays.asList("正常文本", "错误文本"));

        IKnowledgeService.KnowledgeInventoryInfo inventoryInfo = new IKnowledgeService.KnowledgeInventoryInfo();
        inventoryInfo.setModelSn("test-model");

        FileInfoDto embeddedFile = new FileInfoDto();
        embeddedFile.setFileId(12345L);
        embeddedFile.setFileName("success_file.txt");

        IKnowledgeService.FileStatusInfo statusInfo = new IKnowledgeService.FileStatusInfo();
        statusInfo.setStatus(2);

        // 模拟服务调用
        when(knowledgeService.checkKnowledgeInventory(anyString())).thenReturn(inventoryInfo);
        when(fileProcessClient.processFileUpload(any(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(ResultVo.data("上传成功"))
                .thenThrow(new RuntimeException("上传失败"));
        when(knowledgeService.findFilesByDatasetIdAndMd5(anyString(), anyString()))
                .thenReturn(ResultVo.data(new ArrayList<>()));
        when(knowledgeService.fileEmbedding(any(), anyString())).thenReturn(ResultVo.data(embeddedFile));
        when(knowledgeService.getFileStatus(anyLong())).thenReturn(ResultVo.data(statusInfo));

        // 执行测试
        assertDoesNotThrow(() -> knowledgeAddNodeExecutor.execute(workflowContext));

        // 验证结果
        assertEquals(NodeStatus.SUCCESS, nodeContext.getStatus());
        Map<String, String> errorMsgs = (Map<String, String>) nodeContext.getOutput().get("errorMsg");
        assertNotNull(errorMsgs);
        assertFalse(errorMsgs.isEmpty());
    }

    @Test
    void testGetType() {
        assertEquals("KNOWLEDGE_ADD", knowledgeAddNodeExecutor.getType());
    }
}
