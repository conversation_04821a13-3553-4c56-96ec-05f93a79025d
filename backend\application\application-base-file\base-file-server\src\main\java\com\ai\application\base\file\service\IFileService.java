package com.ai.application.base.file.service;



import com.ai.application.base.file.api.dto.AppFileBatchDto;
import com.ai.application.base.file.api.dto.DocFileDto;
import com.ai.application.base.file.api.entity.AppFile;
import com.ai.application.base.file.api.vo.DownloadBytesVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;

public interface IFileService extends IService<AppFile> {
    void batchFileSave(AppFileBatchDto dto);

    void uploadFile(com.ai.application.base.file.api.dto.FileUploadNoticeDto uploadInfo, boolean allowExists);

    com.ai.application.base.file.api.vo.FileMd5Vo historyMd5();



    String getUrl(String fileSn, String type, String fileName);



    void deleteFile(com.ai.application.base.file.api.dto.FileDeleteDto fileDeleteDto);





    void upload(MultipartFile file, String fileSn, String fileType);

    String uploadFileReturnUrl(MultipartFile file, String fileSn, String fileType);

   void processFileUpload(byte[] content, String fileSn, String fileType, String fileName,String source);

    DownloadBytesVo downloadAsBytes(String objectName);

    Optional<com.ai.application.base.file.api.dto.DocFileDto> getDocFileById(Long fileId);

    Optional<com.ai.application.base.file.api.dto.DocFileDto> getDocFileByFileSn(String  fileSn);

    Optional<com.ai.application.base.file.api.dto.DocFileDto> getDocFileByFileId(Integer fileId);

    List<DocFileDto> getDocFileByFileIds(List<Integer> fileIds);

    List<DocFileDto> getDocFileByFileSns(List<String> fileSns);
    Boolean  batchUpdateStatus(List<Integer> fileIds);


}
