package com.ai.application.knowledge.table.controller;

import com.ai.application.knowledge.table.dto.AgentByBbDto;
import com.ai.application.knowledge.table.dto.BaseCreateDto;
import com.ai.application.knowledge.table.dto.BaseListDto;
import com.ai.application.knowledge.table.service.IKnowledgeBaseService;
import com.ai.application.knowledge.table.vo.AgentByBbVo;
import com.ai.application.knowledge.table.vo.BaseDetailVo;
import com.ai.application.knowledge.table.vo.BaseListVo;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.vo.ResultVo;
import com.github.pagehelper.PageInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@Validated
@RestController
@RequestMapping("/v1/base")
@Tag(name = "知识库", description = "知识库控制器")
public class KnowledgeBaseController {

    @Autowired
    private IKnowledgeBaseService knowledgeBaseService;

    @Operation(summary = "列表")
    @PostMapping("/list")
    public ResultVo<PageInfo<BaseListVo>> list(@Valid @RequestBody BaseListDto dto) {
        return knowledgeBaseService.list(dto);
    }

    @Operation(summary = "列表（非分页）")
    @PostMapping("/allList")
    public List<BaseListVo> allList(@Valid @RequestBody BaseListDto dto) {
        return knowledgeBaseService.allList(dto);
    }


    @Operation(summary = "详情")
    @GetMapping("/detail/{kbSn}")
    public ResultVo<BaseDetailVo> detail(@PathVariable("kbSn") String kbSn) {
        return knowledgeBaseService.detail(kbSn);
    }

    @Operation(summary = "删除")
    @DeleteMapping("/delete/{kbSn}")
    public ResultVo<String> delete(@PathVariable("kbSn") String kbSn) {
        return knowledgeBaseService.delete(kbSn);
    }

    @Operation(summary = "编辑")
    @PostMapping("/update")
    public ResultVo<String> update(@Valid @RequestBody BaseCreateDto dto) {
        return knowledgeBaseService.update(dto);
    }

    @Operation(summary = "新增")
    @PostMapping("/create")
    public ResultVo<String> create(@Valid @RequestBody BaseCreateDto dto) {
        return knowledgeBaseService.create(dto);
    }

    @Operation(summary = "获取关联的应用")
    @PostMapping("/agentByBb")
    public ResultVo<List<AgentByBbVo>> agentByBb(@Valid @RequestBody AgentByBbDto dto) {
        return knowledgeBaseService.agentByBb(dto);
    }
}
