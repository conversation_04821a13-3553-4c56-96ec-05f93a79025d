package com.ai.application.agent.base.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

@Schema(name = "API接口生成VO")
@Data
public class ApiGenVO {
    @Schema(description = "域名")
    private String domain;

    @Schema(description = "Agent编码")
    private String agentSn;

    @Schema(description = "版本编码")
    private String versionSn;

    @Schema(description = "接口信息")
    private List<ApiInfo> apiInfos;

    @Data
    public static class ApiInfo {
        @Schema(description = "接口名称")
        private String apiName;

        @Schema(description = "接口描述")
        private String apiDesc;

        @Schema(description = "接口路径")
        private String apiPath;

        @Schema(description = "响应类型")
        private String contentType;

        @Schema(description = "请求类型 GET或POST")
        private String methodType;

        @Schema(description = "请求参")
        private List<ReqParam> reqParam;

        @Schema(description = "响应参数")
        private List<RespParam> respParam;

        @Schema(description = "请求参数示例")
        private String paramsExample;

        @Schema(description = "响应参数示例")
        private String responseExample;
    }

    @Schema(description = "请求参数")
    public static class ReqParam {

    }

    @Schema(description = "响应参数")
    public static class RespParam {}
}
