package com.ai.application.admin.api.mapstruct;

import com.ai.application.admin.api.dto.AdminUserCreateDTO;
import com.ai.application.admin.api.entity.AdminUser;
import com.ai.application.admin.api.vo.AdminUserDetailVO;
import com.ai.application.admin.api.vo.AdminUserVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <p>
 * 应用用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */

@Mapper(componentModel = "spring")
public interface AdminUserMapstruct {
    AdminUserMapstruct INSTANCE = Mappers.getMapper(AdminUserMapstruct.class);
    List<AdminUserVO> toVoList(List<AdminUser> entities);
    AdminUser toEntity(AdminUserCreateDTO dto);
    AdminUserDetailVO toDetailVo(AdminUser entity);
}
