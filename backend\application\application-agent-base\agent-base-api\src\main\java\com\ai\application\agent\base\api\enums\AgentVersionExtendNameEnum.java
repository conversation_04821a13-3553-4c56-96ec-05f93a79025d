package com.ai.application.agent.base.api.enums;

import lombok.Getter;

@Getter
public enum AgentVersionExtendNameEnum {
    MODEL("model", "模型编码"),
    TEMPERATURE("temperature", "创造性"),
    PROMPT("prompt", "提示词"),
    VAR("var", "变量"),
    SHORT_MEMORY("shortMemory", "短期记忆"),
    LONG_MEMORY("longMemory", "长期记忆"),
    ;

    private final String code;
    private final String desc;

    AgentVersionExtendNameEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AgentVersionExtendNameEnum ofCode(String value) {
        for (AgentVersionExtendNameEnum enums : AgentVersionExtendNameEnum.values()) {
            if (enums.code.equals(value)) {
                return enums;
            }
        }
        return null;
    }
}
