
package com.ai.framework.core.constants;

/**
 * 服务名
 * <AUTHOR>
 */
public interface ServiceConstant {
    String MARKET = "market-service";
    String SKILL_TOOL = "skill-tool-service";
    String SKILL_MCP = "skill-mcp-service";

    String TENANT = "tenant-service";
    String TENANT_AUDIT = "tenant-audit-service";
    String TENANT_AUTHORIZE = "tenant-authorize-service";

    String KNOWLEDGE_DOC = "knowledge-doc-service";
    String KNOWLEDGE_TABLE = "knowledge-table-service";
    String KNOWLEDGE_DICT = "knowledge-dict-service";

    String APP = "app-service";
    String APP_SESSION = "app-session-service";
    String APP_AUTH = "app-auth-service";

    String AGENT_BASE = "agent-base-service";
    String AGENT_RUN = "agent-run-service";
    String AGENT_TASK = "agent-task-service";
    String AGENT_MEMORY = "agent-memory-service";

    String BASE_NOTICE = "base-notice-service";
    String BASE_SEARCH = "base-search-service";
    String BASE_LOG = "base-log-service";
    String BASE_MODEL = "base-model-service";
    String BASE_FILE = "base-file-service";

    String ADMIN = "admin-service";
}