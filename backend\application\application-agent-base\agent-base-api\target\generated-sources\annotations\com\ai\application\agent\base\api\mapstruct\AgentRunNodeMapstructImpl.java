package com.ai.application.agent.base.api.mapstruct;

import com.ai.application.agent.base.api.dto.AgentRunNodeDTO;
import com.ai.application.agent.base.api.entity.AgentRunNode;
import com.ai.application.agent.base.api.vo.AgentRunNodeVO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-16T09:54:04+0800",
    comments = "version: 1.4.2.Final, compiler: javac, environment: Java 17.0.4 (Oracle Corporation)"
)
@Component
public class AgentRunNodeMapstructImpl implements AgentRunNodeMapstruct {

    @Override
    public AgentRunNode toEntity(AgentRunNodeDTO dto) {
        if ( dto == null ) {
            return null;
        }

        AgentRunNode agentRunNode = new AgentRunNode();

        agentRunNode.setNodeRunId( dto.getNodeRunId() );
        agentRunNode.setNodeId( dto.getNodeId() );
        agentRunNode.setNodeName( dto.getNodeName() );
        agentRunNode.setNodeType( dto.getNodeType() );
        agentRunNode.setNodeOrder( dto.getNodeOrder() );
        agentRunNode.setNodeStatus( dto.getNodeStatus() );
        agentRunNode.setNodeInput( dto.getNodeInput() );
        agentRunNode.setNodeOutput( dto.getNodeOutput() );
        agentRunNode.setNodeVariables( dto.getNodeVariables() );
        agentRunNode.setNodeConfig( dto.getNodeConfig() );
        agentRunNode.setNodeError( dto.getNodeError() );
        agentRunNode.setNodeProgress( dto.getNodeProgress() );
        agentRunNode.setNodeDuration( dto.getNodeDuration() );
        agentRunNode.setNodeTokens( dto.getNodeTokens() );
        agentRunNode.setNodeRetryCount( dto.getNodeRetryCount() );
        agentRunNode.setNodeStartTime( dto.getNodeStartTime() );
        agentRunNode.setNodeEndTime( dto.getNodeEndTime() );
        agentRunNode.setParentNodeId( dto.getParentNodeId() );
        agentRunNode.setNextNodeIds( dto.getNextNodeIds() );
        agentRunNode.setConditionResult( dto.getConditionResult() );
        agentRunNode.setLoopCurrent( dto.getLoopCurrent() );
        agentRunNode.setLoopTotal( dto.getLoopTotal() );
        agentRunNode.setRunId( dto.getRunId() );
        agentRunNode.setFlowId( dto.getFlowId() );
        agentRunNode.setCreateTime( dto.getCreateTime() );
        agentRunNode.setUpdateTime( dto.getUpdateTime() );

        return agentRunNode;
    }

    @Override
    public List<AgentRunNode> toEntityList(List<AgentRunNodeDTO> dtolist) {
        if ( dtolist == null ) {
            return null;
        }

        List<AgentRunNode> list = new ArrayList<AgentRunNode>( dtolist.size() );
        for ( AgentRunNodeDTO agentRunNodeDTO : dtolist ) {
            list.add( toEntity( agentRunNodeDTO ) );
        }

        return list;
    }

    @Override
    public AgentRunNodeVO toVo(AgentRunNode entity) {
        if ( entity == null ) {
            return null;
        }

        AgentRunNodeVO agentRunNodeVO = new AgentRunNodeVO();

        agentRunNodeVO.setNodeRunId( entity.getNodeRunId() );
        agentRunNodeVO.setNodeId( entity.getNodeId() );
        agentRunNodeVO.setNodeName( entity.getNodeName() );
        agentRunNodeVO.setNodeType( entity.getNodeType() );
        agentRunNodeVO.setNodeOrder( entity.getNodeOrder() );
        agentRunNodeVO.setNodeStatus( entity.getNodeStatus() );
        agentRunNodeVO.setNodeInput( entity.getNodeInput() );
        agentRunNodeVO.setNodeOutput( entity.getNodeOutput() );
        agentRunNodeVO.setNodeVariables( entity.getNodeVariables() );
        agentRunNodeVO.setNodeConfig( entity.getNodeConfig() );
        agentRunNodeVO.setNodeError( entity.getNodeError() );
        agentRunNodeVO.setNodeProgress( entity.getNodeProgress() );
        agentRunNodeVO.setNodeDuration( entity.getNodeDuration() );
        agentRunNodeVO.setNodeTokens( entity.getNodeTokens() );
        agentRunNodeVO.setNodeRetryCount( entity.getNodeRetryCount() );
        agentRunNodeVO.setNodeStartTime( entity.getNodeStartTime() );
        agentRunNodeVO.setNodeEndTime( entity.getNodeEndTime() );
        agentRunNodeVO.setParentNodeId( entity.getParentNodeId() );
        agentRunNodeVO.setNextNodeIds( entity.getNextNodeIds() );
        agentRunNodeVO.setConditionResult( entity.getConditionResult() );
        agentRunNodeVO.setLoopCurrent( entity.getLoopCurrent() );
        agentRunNodeVO.setLoopTotal( entity.getLoopTotal() );
        agentRunNodeVO.setRunId( entity.getRunId() );
        agentRunNodeVO.setFlowId( entity.getFlowId() );
        agentRunNodeVO.setCreateTime( entity.getCreateTime() );
        agentRunNodeVO.setUpdateTime( entity.getUpdateTime() );

        return agentRunNodeVO;
    }

    @Override
    public List<AgentRunNodeVO> toVoList(List<AgentRunNode> entities) {
        if ( entities == null ) {
            return null;
        }

        List<AgentRunNodeVO> list = new ArrayList<AgentRunNodeVO>( entities.size() );
        for ( AgentRunNode agentRunNode : entities ) {
            list.add( toVo( agentRunNode ) );
        }

        return list;
    }
}
