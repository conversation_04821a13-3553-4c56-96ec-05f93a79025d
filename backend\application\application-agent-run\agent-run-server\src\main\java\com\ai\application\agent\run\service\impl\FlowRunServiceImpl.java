package com.ai.application.agent.run.service.impl;

import com.ai.application.agent.run.service.IFlowRunService;
import com.ai.framework.core.vo.ResultVo;
import com.ai.framework.workflow.context.WorkflowContext;
import com.ai.framework.workflow.engine.WorkflowEngineFactory;
import com.googlecode.aviator.AviatorEvaluator;
import dto.FlowRunDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import vo.FlowRunVO;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class FlowRunServiceImpl implements IFlowRunService {
    @Resource
    private WorkflowEngineFactory workflowEngineFactory;

    @Override
    public Flux<ResultVo<FlowRunVO>> stream(FlowRunDTO dto) {
        return null;
    }

    @Override
    public ResultVo<FlowRunVO> noStream(FlowRunDTO dto) {
        Map<String, Map<String, Object>> nodeMap = new HashMap<>();
        Map<String, Object> startNode = new HashMap<>();
        startNode.put("key", "start");
        startNode.put("type", "start");
        startNode.put("timeout", 1);
        startNode.put("next", List.of("cond1"));
        nodeMap.put("start", startNode);

        // cond1 节点（条件节点，支持SPL表达式）
        Map<String, Object> cond1Node = new HashMap<>();
        cond1Node.put("key", "cond1");
        cond1Node.put("type", "condition");
        Map<String, String> cond1Next = new HashMap<>();
        cond1Next.put("a > b", "parallel1");
        cond1Next.put("c <= d", "n3");
        cond1Node.put("next", cond1Next);
        nodeMap.put("cond1", cond1Node);

        HashMap<String, Object> input = new HashMap<>();
        input.put("key", "111");
        WorkflowContext workflowContext = workflowEngineFactory.startWorkflow(nodeMap, input);

        // 2. 执行带变量的表达式
//        Map<String, Object> env = new HashMap<>();
//        env.put("a", 3);
//        env.put("b", 2);
//        env.put("c", 5);
//        env.put("d", 4);
//        String result = AviatorEvaluator.execute("a > b && c > d", env).toString();
//        System.out.println(result);

        log.info("flow run start...{}", workflowContext.getNodeContexts().get("cond1"));
        return ResultVo.success("ok");
    }
}