package com.ai.application.admin.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 租户部门表
 *
 * <AUTHOR>
 * @since 2025-05-28
 */
@Data
@Schema(name = "")
public class TenantDepartmentVO {
    /**
     * 部门id
     */
    @Schema(description = "部门id")
    private Integer deptId;
    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String deptName;
    /**
     * 所有父节点名称
     */
    @Schema(description = "所有父节点名称")
    private String deptUpName;
    /**
     * 所有父节点路径
     */
    @Schema(description = "所有父节点路径")
    private String deptUpPath;
    /**
     * 排序值
     */
    @Schema(description = "排序值")
    private Integer deptSort;
    /**
     * 状态 1启用 0禁用 -1删除
     */
    @Schema(description = "状态 1启用 0禁用 -1删除")
    private Integer deptStatus;
    /**
     * 父级ID
     */
    @Schema(description = "父级ID")
    private Integer parentId;
    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private Integer tenantId;
    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @Schema(description = "")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}