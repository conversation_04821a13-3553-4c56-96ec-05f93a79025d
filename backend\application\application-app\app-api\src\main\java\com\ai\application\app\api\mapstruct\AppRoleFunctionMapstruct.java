package com.ai.application.app.api.mapstruct;
import com.ai.application.app.api.entity.AppRoleFunction;
import com.ai.application.app.api.dto.AppRoleFunctionDTO;
import com.ai.application.app.api.vo.AppRoleFunctionVO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * <p>
 * 应用角色功能表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-30
 */

@Mapper(componentModel = "spring")
public interface AppRoleFunctionMapstruct {

    AppRoleFunction toEntity(AppRoleFunctionDTO dto);
    List<AppRoleFunction> toEntityList(List<AppRoleFunctionDTO> dtolist);
    AppRoleFunctionVO toVo(AppRoleFunction entity);
    List<AppRoleFunctionVO> toVoList(List<AppRoleFunction> entities);
}
