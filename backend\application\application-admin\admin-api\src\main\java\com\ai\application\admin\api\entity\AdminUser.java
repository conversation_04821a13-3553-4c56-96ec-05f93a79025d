package com.ai.application.admin.api.entity;

import com.ai.framework.core.sensitive.Sensitive;
import com.ai.framework.core.sensitive.SensitiveType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 应用用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("app_user")
public class AdminUser implements Serializable {
    /**
     * 用户id
     */
    @Schema(description = "用户id")
    @TableId(type = IdType.AUTO)
    private Integer userId;

    /**
     * 用户sn
     */
    @Schema(description = "用户sn")
    private String userSn;

    /**
     * 登录账号
     */
    @Schema(description = "登录账号")
    private String userAccount;

    /**
     * 用户名称
     */
    @Schema(description = "用户名称")
    private String userName;

    /**
     * 登录密码
     */
    @Schema(description = "登录密码")
    private String userPassword;

    /**
     * 用户手机号
     */
    @Schema(description = "用户手机号")
    @Sensitive(type = SensitiveType.MOBILE)
    private String userMobile;

    /**
     * 用户邮箱
     */
    @Schema(description = "用户邮箱")
    private String userEmail;

    /**
     * 头像链接
     */
    @Schema(description = "头像链接")
    private String userAvatar;

    /**
     * 工号
     */
    @Schema(description = "工号")
    private String userStaffSn;

    /**
     * 用户状态 1-启用 0-禁用 -1-删除
     */
    @Schema(description = "用户状态 1-启用 0-禁用 -1-删除")
    private Integer userStatus;

    /**
     * 应用id
     */
    @Schema(description = "应用id")
    private Integer appId;

    /**
     * 角色id
     */
    @Schema(description = "角色id")
    private Integer roleId;

    /**
     * 租户id
     */
    @Schema(description = "租户id")
    private Integer tenantId;

    /**
     * 部门id
     */
    @Schema(description = "部门id")
    private Integer deptId;

    @Schema(description = "")
    private Date createTime;

    @Schema(description = "")
    private Date updateTime;

}