<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ai.application.admin.mapper.AgentMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.ai.application.admin.api.entity.Agent">
                    <id column="agent_id" property="agentId" />
                    <result column="agent_sn" property="agentSn" />
                    <result column="agent_name" property="agentName" />
                    <result column="agent_desc" property="agentDesc" />
                    <result column="agent_type" property="agentType" />
                    <result column="agent_metadata" property="agentMetadata" />
                    <result column="agent_status" property="agentStatus" />
                    <result column="version_id" property="versionId" />
                    <result column="tenant_id" property="tenantId" />
                    <result column="user_id" property="userId" />
                    <result column="create_time" property="createTime" />
                    <result column="update_time" property="updateTime" />
        </resultMap>
        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        agent_id, agent_sn, agent_name, agent_desc, agent_type, agent_metadata, agent_status, version_id, tenant_id, user_id, create_time, update_time
    </sql>
    <select id="queryGrantTenantAgentList" resultType="com.ai.application.admin.api.vo.AgentPageVO">
        SELECT A.resource_id,B.* FROM `resource` A
        LEFT JOIN agent B ON(A.resource_object_id=B.agent_id)
        WHERE A.resource_status=1 AND A.resource_type=10
    </select>
</mapper>