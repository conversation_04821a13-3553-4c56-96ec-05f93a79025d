package com.ai.application.process.executor.impl;

import com.ai.application.skill.file.api.feign.ISkillFileClient;
import com.google.common.collect.Lists;
import com.ai.application.agent.base.api.dto.DocMultiRetrieverDto;
import com.ai.application.agent.base.api.dto.DocOrInventoryChatDto;
import com.ai.application.agent.base.api.util.JsonUtil;
import com.ai.application.agent.doc.api.dto.ExpandSearchDto;
import com.ai.application.agent.doc.api.dto.KnowledgeInventoryDto;
import com.ai.application.agent.doc.api.feign.IAgentDocClient;
import com.ai.application.process.api.dto.KnowledgeSearchReq;
import com.ai.application.process.enums.ProcessErrorCodeEnum;
import com.ai.application.process.executor.BaseExecutor;
import com.ai.application.process.executor.ExecutionContext;
import com.ai.application.process.service.KnowledgeService;
import com.ai.application.process.util.ProcessConvertUtils;
import com.ai.application.skill.file.api.dto.QueryByFileIdsDto;
import com.ai.application.skill.file.api.entity.DocFile;
import com.ai.application.skill.file.api.feign.ISkillFileClient;
import com.ai.framework.core.context.UserContext;
import com.ai.framework.core.exception.ServiceException;
import com.ai.framework.core.exception.enums.ErrorCodeEnum;
import com.ai.framework.core.util.json.JsonUtils;
import com.ai.framework.core.util.list.CollectionUtils;
import com.ai.framework.core.vo.ResultVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class KnowledgeSearchExecutor implements BaseExecutor {

    @Resource
    private IAgentDocClient agentDocClient;
    @Resource
    private KnowledgeService knowledgeService;
    @Resource
    private ISkillFileClient skillFileClient;

    @Override
    public Map<String, Object> execute(ExecutionContext executionContext) {
        String processInstanceId = executionContext.getProcessInstanceId();
        log.info("knowledgeSearchExecutor start processInstanceId:{}", processInstanceId);

        Map<String, Object> parameters = executionContext.getParameters();
        String searchModel = executionContext.getParameterAsString("searchModel");
        String searchContent = executionContext.getParameterAsString("content");
        String type = executionContext.getParameterAsString("type");
        String embedding = executionContext.getParameterAsString("embedding");
        String keyword = executionContext.getParameterAsString("keyword");
        String text = executionContext.getParameterAsString("text");
        Object knowledgeInventorySnObj = parameters.get("knowledgeInventorySn");
        String knowledgeInventorySn = "";
        List<String> knowledgeInventoryList = Lists.newArrayList();
        if (knowledgeInventorySnObj != null) {
            if (knowledgeInventorySnObj instanceof List) {
                knowledgeInventoryList = (List<String>) knowledgeInventorySnObj;
                knowledgeInventorySn = knowledgeInventoryList.get(0);
            } else {
                knowledgeInventorySn = String.valueOf(knowledgeInventorySnObj);
                knowledgeInventoryList.add(knowledgeInventorySn);
            }
        }
        log.info("=======KnowledgeSearchExecutor:" + knowledgeInventorySn);
        log.info("=======KnowledgeSearchExecutor:" + JsonUtils.toJsonString(knowledgeInventoryList));
        Object knowledgeSnObj = parameters.get("knowledgeSn");

//        ResultVo<String> docSearchResult = execAgentSearch(processInstanceId, searchContent, knowledgeAndKnowList.getKnowledgeInventorySn(), knowledgeAndKnowList.getKnowledgeSn());

        Map<String, Object> result = new HashMap<>();
//        Optional.ofNullable(docSearchResult.getData()).ifPresentOrElse(s -> result.put("output", s),
//                () -> {
//                    log.info("process instance id {}, knowledgeSearchExecutor received agentSearch empty", executionContext.getProcessInstanceId());
//                    result.put("output", ProcessErrorCodeEnum.KNOWLEDGE_SEARCH_EMPTY.getMessage());
//                });

        // expand 基于片段检索 content 基于内容检索 all返回全部片段
        if ("expand".equals(searchModel)) {
            this.expandSearch(executionContext, result);
        } else if ("all".equals(searchModel)) {
            var searchKnowledge = parameters.get("searchKnowledge");
            this.manualRetriever(searchKnowledge, result);
        } else {
//             KnowledgeSearchReq knowledgeAndKnowList = prepareSearchReq(type, knowledgeInventorySn, knowledgeSnObj);
            // 改成多级目录
            KnowledgeSearchReq knowledgeAndKnowList = prepareSearchReqForKnowledgeTree(type, knowledgeInventoryList, knowledgeSnObj);
            var docMultiRetrieverDto = new DocMultiRetrieverDto();
            // 使用knowledgeInventorySn获取知识库的modelSn，用于知识检索来召回知识片段
            // 这里只要模型sn 由于无论是知识库目录 还是知识，只能同模型，取第一个查询
            KnowledgeInventoryDto inventory = knowledgeService.checkKnowledgeInventory(knowledgeAndKnowList.getKnowledgeInventorySnList().get(0));
            docMultiRetrieverDto.setLlmSn(inventory.getModelSn());
            docMultiRetrieverDto.setOriginQuery(searchContent);
            if ("knowledgeInventorySn".equals(type)) {
                docMultiRetrieverDto.setDatasetIds(knowledgeInventoryList);
                docMultiRetrieverDto.setFileIds(Lists.newArrayList());
            } else if ("knowledgeSn".equals(type)) {
                List<String> knowledgeSnList = (List<String>) knowledgeSnObj;
                List<Long> fileIds = new ArrayList<>();
                knowledgeSnList.forEach(longKnowledge -> fileIds.add(Long.parseLong(longKnowledge.split("::")[0])));
                if (CollectionUtils.isEmpty(fileIds)) {
                    log.error("knowledgeSearchExecutor fail knowledgeSnObj :{}", knowledgeSnObj);
                    throw new ServiceException(ProcessErrorCodeEnum.KNOWLEDGE_IS_BLANK);
                }
                var queryByFileIdsDto = new QueryByFileIdsDto();
                queryByFileIdsDto.setFileIds(fileIds);
                var docFileResult = skillFileClient.queryByFileIds(queryByFileIdsDto);
                if (docFileResult.getCode() != 0) {
                    throw new ServiceException(docFileResult.getCode(), docFileResult.getMessage());
                }
                var fileSns = docFileResult.getData().stream().map(DocFile::getFileSn).toList();
                docMultiRetrieverDto.setDatasetIds(knowledgeAndKnowList.getKnowledgeInventorySnList());
                docMultiRetrieverDto.setFileIds(fileSns);
            }
            var retrievalPlanList = new ArrayList<DocMultiRetrieverDto.RetrievalPlan>();
            // 语义检索
            var embeddingRetrievalPlan = new DocMultiRetrieverDto.RetrievalPlan();
            embeddingRetrievalPlan.setMethod("embedding");
            embeddingRetrievalPlan.setTopK(StringUtils.isBlank(embedding) ? 0 : Integer.parseInt(embedding));
            retrievalPlanList.add(embeddingRetrievalPlan);
            // 关键词检索
            var keywordRetrievalPlan = new DocMultiRetrieverDto.RetrievalPlan();
            keywordRetrievalPlan.setMethod("keywords");
            keywordRetrievalPlan.setTopK(StringUtils.isBlank(keyword) ? 0 : Integer.parseInt(keyword));
            retrievalPlanList.add(keywordRetrievalPlan);
            // 文本词检索
            var textRetrievalPlan = new DocMultiRetrieverDto.RetrievalPlan();
            textRetrievalPlan.setMethod("text");
            textRetrievalPlan.setTopK(StringUtils.isBlank(text) ? 0 : Integer.parseInt(text));
            retrievalPlanList.add(textRetrievalPlan);
            // 兼容历史数据
            if (searchModel == null) {
                embeddingRetrievalPlan.setTopK(10);
                keywordRetrievalPlan.setTopK(10);
                textRetrievalPlan.setTopK(10);
            }
            docMultiRetrieverDto.setRetrievalPlan(retrievalPlanList);
            var docMultiRetrieverResult = agentDocClient.docMultiRetriever(docMultiRetrieverDto);
            if (docMultiRetrieverResult.getCode() != 0) {
                throw new ServiceException(docMultiRetrieverResult.getCode(), docMultiRetrieverResult.getMessage());
            }
            var output = docMultiRetrieverResult.getData();
            result.put("output", CollectionUtils.isEmpty(output) ? "[]" : JsonUtil.encode(output));
        }
        return result;
    }

    private void manualRetriever(Object searchKnowledge, Map<String, Object> result) {
        var manualRetriever = knowledgeService.getKnowledge(searchKnowledge);
        var resultManualRetriever = agentDocClient.manualRetriever(manualRetriever);
        if (resultManualRetriever.getCode() != 0) {
            throw new ServiceException(resultManualRetriever.getCode(), resultManualRetriever.getMessage());
        }
        var output = resultManualRetriever.getData();
        result.put("output", CollectionUtils.isEmpty(output) ? "" : JsonUtil.encode(output));
    }

    private void expandSearch(ExecutionContext executionContext, Map<String, Object> result) {
        var fragmentType = executionContext.getParameterAsString("fragmentType");
        var length = executionContext.getParameterAsString("length");
        var parameters = executionContext.getParameters();
        var object = parameters.get("expand");
        var forward = executionContext.getParameterAsString("forward");
        var backward = executionContext.getParameterAsString("backward");
        var expandAllChildren = false;
        ServiceException.throwIf(StringUtils.isBlank(fragmentType), ProcessErrorCodeEnum.KNOWLEDGE_FRAGMENT_TYPE_IS_BLANK);
        //基于片段检索 知识片段有可能是单个知识片段json 也有可能是多片段json数组
        var contentList = ProcessConvertUtils.buildContents(object);
        ServiceException.throwIf(CollectionUtils.isEmpty(contentList), ProcessErrorCodeEnum.KNOWLEDGE_FRAGMENT_IS_BLANK);

        if ("all".equals(fragmentType)) {
            expandAllChildren = true;
        }
        var expandSearchDto = new ExpandSearchDto(contentList, StringUtils.isBlank(backward) ? 0 : Integer.parseInt(backward),
                StringUtils.isBlank(forward) ? 0 : Integer.parseInt(forward), StringUtils.isBlank(length) ? 1000 : Integer.parseInt(length), expandAllChildren);
        var resultExpand = agentDocClient.faqExpandSearch(expandSearchDto);
        if (resultExpand.getCode() != 0) {
            throw new ServiceException(resultExpand.getCode(), resultExpand.getMessage());
        }
        var output = resultExpand.getData();
        result.put("output", CollectionUtils.isEmpty(output) ? "" : JsonUtil.encode(output));
    }

    public KnowledgeSearchReq prepareSearchReq(String type, String knowledgeInventorySn, Object knowledgeSn) {
        KnowledgeSearchReq knowledgeSearchReq = new KnowledgeSearchReq();

        //知识列表id
        List<String> knowledge = new ArrayList<>();
        if ("knowledgeInventorySn".equals(type)) {
            knowledgeSearchReq.setKnowledgeInventorySn(knowledgeInventorySn);
            knowledgeSearchReq.setKnowledgeSn(knowledge);

        } else if ("knowledgeSn".equals(type)) {
            //根据知识地址查询知识库相关配置
            ServiceException.throwIf(Objects.isNull(knowledgeSn), ProcessErrorCodeEnum.KNOWLEDGE_IS_BLANK);
            knowledgeInventorySn = knowledgeService.getKnowledgeAndAddKnowList(knowledgeSn, knowledge);
            knowledgeSearchReq.setKnowledgeInventorySn(knowledgeInventorySn);
            knowledgeSearchReq.setKnowledgeSn(knowledge);

        } else {
            throw new ServiceException(ProcessErrorCodeEnum.KNOWLEDGE_SEARCH_TYPE_UNSUPPORTED);
        }
        return knowledgeSearchReq;
    }

    public ResultVo<String> execAgentSearch(String processInstanceId, String searchContent, String knowledgeInventorySn, List<String> knowledge, String auth) {
        return execAgentSearch(processInstanceId, searchContent, knowledgeInventorySn, knowledge, false, Boolean.TRUE, auth);
    }

    public ResultVo<String> execAgentSearch(
            String processInstanceId, String searchContent, String knowledgeInventorySn,
            List<String> knowledge, Boolean questionFocus, Boolean responseMode, String auth) {
        ServiceException.throwIf(StringUtils.isBlank(searchContent), ProcessErrorCodeEnum.QUESTION_CONTENT_IS_BLANK);
        ServiceException.throwIf(StringUtils.isBlank(knowledgeInventorySn), ProcessErrorCodeEnum.KNOWLEDGE_INVENTORY_IS_BLANK);

        //使用knowledgeInventorySn获取知识库的modelSn，用于知识检索来召回知识片段
        KnowledgeInventoryDto inventory = knowledgeService.checkKnowledgeInventory(knowledgeInventorySn);
        List<String> knowledgeList = knowledgeService.getFileSnByFileId(knowledge).stream().map(DocFile::getFileSn).collect(Collectors.toList());

        DocOrInventoryChatDto chatDto = new DocOrInventoryChatDto();
        chatDto.setModel(inventory.getModelSn());
        chatDto.setFileOrInventoryId(knowledgeInventorySn);
        chatDto.setKnowledge(knowledgeList);
        chatDto.setRequestType(1);
        chatDto.setQuestionFocus(ObjectUtils.defaultIfNull(questionFocus, true));
        chatDto.setResponseMode(responseMode);
        chatDto.setMsgContent(searchContent);
        chatDto.setDelayInMs(20L);
        log.info("processInstanceId: [{}], knowledgeSearchExecutor call agent doc for fragment, request param: {}", processInstanceId, JsonUtils.toJsonString(chatDto));
        ResultVo<String> docSearchResult = agentDocClient.requestLLMWithDoc(chatDto, auth);
        if (docSearchResult.getCode() != 0) {
            throw new ServiceException(docSearchResult.getCode(), docSearchResult.getMessage());
        }
        log.info("processInstanceId: [{}], knowledgeSearchExecutor call agent doc for fragment, resp: {}", processInstanceId, docSearchResult.getData());
        return docSearchResult;
    }

    public ResultVo<String> execAgentSearchForKnowledgeInventorySnList(
            String processInstanceId, String searchContent, List<String> knowledgeInventorySnList,
            List<String> knowledge, Boolean questionFocus, Boolean responseMode, String auth, String llmModelSn) {
        ServiceException.throwIf(StringUtils.isBlank(searchContent), ProcessErrorCodeEnum.QUESTION_CONTENT_IS_BLANK);
        ServiceException.throwIf(CollectionUtils.isEmpty(knowledgeInventorySnList), ProcessErrorCodeEnum.KNOWLEDGE_INVENTORY_IS_BLANK);

        //使用knowledgeInventorySn获取知识库的modelSn，用于知识检索来召回知识片段
        KnowledgeInventoryDto inventory = knowledgeService.checkKnowledgeInventory(knowledgeInventorySnList.get(0));
        List<String> knowledgeList = knowledgeService.getFileSnByFileId(knowledge).stream().map(DocFile::getFileSn).collect(Collectors.toList());

        DocOrInventoryChatDto chatDto = new DocOrInventoryChatDto();
        chatDto.setModel(llmModelSn);
        chatDto.setInventorySns(knowledgeInventorySnList);
        chatDto.setKnowledge(knowledgeList);
        chatDto.setRequestType(1);
        chatDto.setQuestionFocus(ObjectUtils.defaultIfNull(questionFocus, true));
        chatDto.setResponseMode(responseMode);
        chatDto.setMsgContent(searchContent);
        chatDto.setDelayInMs(20L);
        log.info("processInstanceId: [{}], knowledgeSearchExecutor call agent doc for fragment, request param: {}", processInstanceId, JsonUtils.toJsonString(chatDto));
        ResultVo<String> docSearchResult = agentDocClient.requestLLMWithDoc(chatDto, auth);
        if (docSearchResult.getCode() != 0) {
            throw new ServiceException(docSearchResult.getCode(), docSearchResult.getMessage());
        }
        log.info("processInstanceId: [{}], knowledgeSearchExecutor call agent doc for fragment, resp: {}", processInstanceId, docSearchResult.getData());
        return docSearchResult;
    }



    @Override
    public String getId() {
        return "KNOWLEDGE_SEARCH";
    }


    public Optional<String> getKnowledgeSearchResult(KnowledgeSearchReq knowledgeSearchReq) {
        Long userId = UserContext.getUserId();
        if (Optional.ofNullable(userId).isEmpty()) {
            throw new ServiceException(ErrorCodeEnum.UNAUTHORIZED);
        }

        KnowledgeSearchReq builtSearchReq = prepareSearchReq(knowledgeSearchReq.getType(), knowledgeSearchReq.getKnowledgeInventorySn(), knowledgeSearchReq.getKnowledgeSn());
        ResultVo<String> docSearchResult = execAgentSearch("interfaceCall",
                knowledgeSearchReq.getContent(), builtSearchReq.getKnowledgeInventorySn(),
                builtSearchReq.getKnowledgeSn(), UserContext.getAuthorization());
        return Optional.ofNullable(docSearchResult.getData()).or(() -> Optional.of(docSearchResult.getMessage()));

    }


    /**
     * 预搜索多级知识库
     * @param type
     * @param knowledgeInventorySn
     * @param knowledgeSn
     * @return
     */
    public KnowledgeSearchReq prepareSearchReqForKnowledgeTree(String type, List<String> knowledgeInventorySn, Object knowledgeSn) {
        KnowledgeSearchReq knowledgeSearchReq = new KnowledgeSearchReq();

        //知识列表id
        List<String> knowledge = new ArrayList<>();
        if ("knowledgeInventorySn".equals(type)) {
            knowledgeSearchReq.setKnowledgeInventorySnList(knowledgeInventorySn);
            knowledgeSearchReq.setKnowledgeSn(knowledge);

        } else if ("knowledgeSn".equals(type)) {
            //根据知识地址查询知识库相关配置
            ServiceException.throwIf(Objects.isNull(knowledgeSn), ProcessErrorCodeEnum.KNOWLEDGE_IS_BLANK);
            List<String> knowledgeInventorySnList = knowledgeService.getKnowledgeTreeAndAddKnowList(knowledgeSn, knowledge);
            knowledgeSearchReq.setKnowledgeInventorySnList(knowledgeInventorySnList);
            knowledgeSearchReq.setKnowledgeSn(knowledge);

        } else {
            throw new ServiceException(ProcessErrorCodeEnum.KNOWLEDGE_SEARCH_TYPE_UNSUPPORTED);
        }
        return knowledgeSearchReq;
    }

}
