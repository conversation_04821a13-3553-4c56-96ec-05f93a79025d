package com.ai.application.admin.api.enums;

/**
 * 授权类型:10-使用,20-协作,30-管理,50-所有者
 */
public enum GrantTypeEnum {
    USE(10, "使用"),
    COORDINATION(20, "协作"),
    MANAGE(30, "管理"),
    OWNER(50, "所有者"),
    ;

    private int code;
    private String name;

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    GrantTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }
}
