package com.ai.application.agent.base.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(name = "智能体统计返回VO")
public class AgentStatResultVO {
    @Schema(description = "活跃应用数量")
    private AgentStatIndexVO agentMetrics;

    @Schema(description = "总会话量")
    private AgentStatIndexVO sessionMetrics;

    @Schema(description = "工作流调用次数")
    private AgentStatIndexVO workFlowMetrics;

    @Schema(description = "Token总消耗量")
    private AgentStatIndexVO tokenMetrics;
}
