package com.ai.application.agent.run.controller;

import com.ai.application.agent.base.api.dto.AgentChatDTO;
import com.ai.application.agent.base.api.vo.AgentChatVO;
import com.ai.application.agent.run.service.IAgentChatService;
import com.ai.framework.core.vo.ResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * Agent Chat 控制器
 */
@Tag(name = "智能体对话", description = "智能体对话接口")
@RestController
@RequestMapping("/v1/feign/agent")
@AllArgsConstructor
public class AgentChatController {

    private final IAgentChatService agentChatService;

    /**
     * 调用智能体进行对话
     */
    @Operation(summary = "调用智能体对话")
    @PostMapping("/chat")
    public ResultVo<AgentChatVO> requestLLMWithAgent(
            @Validated @RequestBody AgentChatDTO chatDto,
            @RequestHeader(value = "Authorization", required = false) String authorization) {
        return agentChatService.requestLLMWithAgent(chatDto);
    }

    /**
     * 检查智能体是否可用
     */
    @Operation(summary = "检查智能体可用性")
    @GetMapping("/check/available/{agentSn}")
    public ResultVo<Boolean> checkAgentAvailable(@PathVariable String agentSn) {
        return agentChatService.checkAgentAvailable(agentSn);
    }
}
